# Web3钱包登录功能部署指南

## 概述
本指南将帮助您部署Web3钱包登录功能到您的ThinkPHP 5.1项目中。

## 部署步骤

### 1. 数据库迁移
执行数据库迁移脚本以添加必要的表和字段：

```sql
-- 执行 web3_migration.sql 文件
mysql -u your_username -p your_database < web3_migration.sql
```

### 2. 验证文件结构
确保以下文件已正确放置：

```
su.xisum.xyz_LkCWDj/
├── application/
│   ├── admin/
│   │   ├── controller/
│   │   │   └── Web3Auth.php
│   │   └── view/
│   │       └── auth/
│   │           └── index.html (已修改)
│   └── common/
│       ├── library/
│       │   └── Web3Signature.php
│       ├── model/
│       │   └── User.php (已扩展)
│       └── service/
│           └── Web3Service.php
├── route/
│   └── route.php (已添加路由)
└── web3_migration.sql
```

### 3. 配置检查
确保以下配置正确：

#### 3.1 数据库配置
检查 `config/database.php` 中的数据库连接配置。

#### 3.2 路由配置
确认 `route/route.php` 中包含以下路由：
```php
route('admin/web3auth/getNonce', 'admin/Web3Auth/getNonce');
route('admin/web3auth/verify', 'admin/Web3Auth/verify');
route('admin/web3auth/bind', 'admin/Web3Auth/bind');
```

### 4. 前端依赖
登录页面已集成ethers.js库（通过CDN），无需额外安装。

### 5. 测试部署

#### 5.1 基本功能测试
1. 访问登录页面：`http://your-domain/admin/auth/index`
2. 检查是否显示"连接钱包登录"按钮
3. 点击按钮测试钱包连接功能

#### 5.2 API测试
使用Postman或类似工具测试API接口：

**获取Nonce：**
```
POST /admin/web3auth/getNonce
Content-Type: application/json

{
    "wallet_address": "******************************************"
}
```

**验证签名：**
```
POST /admin/web3auth/verify
Content-Type: application/json

{
    "wallet_address": "******************************************",
    "signature": "0x...",
    "nonce": "generated_nonce"
}
```

## 重要注意事项

### 🚨 生产环境警告
当前的签名验证使用的是简化实现，**不适用于生产环境**。在生产部署前，必须：

1. **安装专业的加密库**：
   ```bash
   composer require kornrunner/keccak
   composer require mdanter/ecc
   # 或者
   composer require web3p/web3.php
   ```

2. **替换签名验证逻辑**：
   更新 `application/common/library/Web3Signature.php` 中的验证方法。

3. **安全审计**：
   对整个Web3登录流程进行安全审计。

### 安全建议

1. **HTTPS必须**：Web3钱包连接必须在HTTPS环境下进行
2. **Nonce管理**：定期清理过期的nonce记录
3. **日志监控**：监控Web3登录日志，及时发现异常
4. **错误处理**：不要在错误信息中暴露敏感信息

### 支持的钱包
当前支持以下钱包：
- MetaMask
- WalletConnect（需要额外配置）
- Trust Wallet
- Coinbase Wallet

### 故障排除

#### 问题1：钱包连接失败
- 检查是否安装了MetaMask
- 确认网站运行在HTTPS下
- 检查浏览器控制台错误信息

#### 问题2：签名验证失败
- 确认nonce未过期
- 检查钱包地址格式
- 验证签名格式是否正确

#### 问题3：数据库错误
- 确认数据库迁移已执行
- 检查数据库连接配置
- 验证表结构是否正确

## 开发环境测试

为了便于开发测试，系统包含了一些测试地址：
- `******************************************`
- `0x8ba1f109551bD432803012645Hac136c5C1b4d8b6`

这些地址在开发环境中会自动通过签名验证。

## 后续扩展

### 多链支持
可以扩展支持其他区块链：
- Binance Smart Chain
- Polygon
- Arbitrum

### 高级功能
- 钱包地址绑定/解绑
- 多钱包管理
- 签名消息自定义
- Web3用户权限管理

## 技术支持

如果在部署过程中遇到问题，请检查：
1. PHP版本兼容性（推荐PHP 7.1+）
2. ThinkPHP 5.1框架完整性
3. 数据库权限设置
4. Web服务器配置

## 更新日志

- 2025-06-16: 初始版本发布
  - 基础Web3钱包登录功能
  - MetaMask集成
  - 用户自动创建
  - 登录日志记录
