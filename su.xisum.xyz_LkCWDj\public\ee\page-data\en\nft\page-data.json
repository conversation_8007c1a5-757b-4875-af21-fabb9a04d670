{"componentChunkName": "component---src-templates-use-cases-js", "path": "/en/nft/", "result": {"data": {"siteData": {"siteMetadata": {"editContentUrl": "https://github.com/ethereum/ethereum-org-website/tree/dev/"}}, "pageData": {"fields": {"slug": "/en/nft/"}, "frontmatter": {"title": "Non-fungible tokens (NFT)", "description": "An overview of NFTs on Ethereum", "lang": "en", "sidebar": true, "emoji": ":frame_with_picture:", "sidebarDepth": 2, "summaryPoint1": "A way to represent anything unique as an Ethereum-based asset.", "summaryPoint2": "NFTs are giving more power to content creators than ever before.", "summaryPoint3": "Powered by smart contracts on the Ethereum blockchain.", "alt": "An Eth logo being displayed via hologram.", "image": {"childImageSharp": {"gatsbyImageData": {"layout": "fullWidth", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAWCAYAAADAQbwGAAAACXBIWXMAAD2EAAA9hAHVrK90AAAFXklEQVQ4y8WVe0zTVxTHD4gadIv7R5NpotuyREeMMSZuM9lE3aAv2kKLtIA/nrblVV7aB4+WlpdAeehAQEFh8h4VoQXGUBBFBfExFZ1QZFE2Mw0+QNzUPu5ZW8yM2/5cspt8c/K7v9/95HvOPff+AP6vsWbNali+fDmsXPk+rFq1yjmHiODh8cm/LxgnxBmzVMmglseAWhHrok2NX6BJkb6RMs4tMy3eLTk6wi1CKFggpoJdQwN2QkSgACKDhBDo5wtA91fC11wZ8CI14O2b7hqV1eCaUlzvdkXvD9/kREOxJhJ0qnDITaGgQB0FGtVeEAaFA5cTDEJhJGzyigVVdCAoREHQk8sFaOk+D5fGJx32XTw2xgFfXAGy3Bo42UwtK8mO8SnUSvwKNOLgPPVuUUFGXHyWWqakdoWqeNygcEFA2LZ1W8KWRokjQS4SuDSp2QC1+j5o6TntrMfaDeKNgTEHvBILO+nhYcntMZIklIj3oFi0F3eL5EhRESgNZWBhIg8zo3mo3M3HhBDfRpmQA/BRBWRKGPOFdYyW7pGvFHmtc5V9lzHPeB3DVEYM1XaT8Oxem6Sw37pff9bKDc23qMJoZtMhf/OZfVzrYL6vbVDHxy4NRzFRRcHdGsrVCZxD1Nx+8AQP9V0lnbOPSd3d+7bikZu27N4hknNyiBSd+5EcH7tFqMRiEhEpIf05PqQ2nkbqk2jWxmRvPChl3YVP1QvXMaUAxA6cRWwbn55BXe8Nc+3kFGkx/UIaR38mh7pGsNwwTCoNF3F/Qx8yxPnI2UVhVwYLjytYxJjOtp1QMjBPRH+4nhbt7smOmAfO2GwNE8+eY/XoPUvNxD1sHpsizZP3sebibSw1DOHBjiEsqXMAdcij5oGtciYa0tg2QyoLC8T0hx94itw/o4fM13CWkKbbs8+xdMhkOeoAjk9h3fU7eGRwFEuNw1hmBxYfO4V0kQMY8hawI5WJOgljej09xn0bJ/K1QzvwzswcFvfftJRfG8P6yyasv2LCqt6rjpSx0jCCZfqzyI3OR9+QUOxSM/8GZD7ezJEu8eKJXjtEbPzpwSM8OnzdbHj6hNRN3CNlF0cx13iW7OsaxLzvz2OLyYQRqUdwp38IdqoZb4ApTCyMYj3Zyo97hyWQANgcDpG0mp7OYVHrAFZduoEHLozaEmt6SEJND9pF4ivbUF5Uh7zANJSFU9ih5qBeznAAiQOoi/L5wz8g9GOBMBxgxmqFabOlfNpqm7n1cPpXbVPXi4N6A7YZO4jeaMBWhzra8Vh1FWYo92CuSontGX5OoH2XidGRchT7JYcbsIHPDwKwmF/C1NyzhfbUF1/+7dp7O7TZ/t9W6bpPVOdgbWmWuak8y/pdZaa11a4ybby1QhNj7dJwSfMemq0thWXVy2m2gmiO1c9XsDFgZzDAi1cv4ZHZ7KylU+c0MFrms6XTvpOyYB88HOeNdYle6GjghmQ69mZxcLDAz/5Mw+a9dHs/MrA6kT3httZn6YpNPIBnVgs8sljsvUhg7Eq7y0wPzQ7ucxvQ8c+kh7MvVcTRzxyWel84mkAftsNHGmWMa0Ml/N+PK5kzegVzslXBulGX5C3v0AjhdH6Ii/Mcf5EQC5s16VBbEAZAyeBYbiQ0prIXxQ44XP/w13mfj4vdmlNpH2qDPn8v0NNjoTOrAQoOKMRQlaN8+5KtT2PBfnWgfeFq6NCyIXvdPy/iRe+uAIOWBaViT1AJt9hn3AH7pc53S5atePvj4TIBvBqQAt5Uwsl9fpBGeUF26HZID/oSMqmtoLbHIvF2aM9gulREb3epS6LBEak3lIh2wCkNGy7k+v73/54/AUaEKKCt+fV1AAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/a44134e541c72364beb121234ab5864e/5791f/infrastructure_transparent.png", "srcSet": "/static/a44134e541c72364beb121234ab5864e/a26f5/infrastructure_transparent.png 750w,\n/static/a44134e541c72364beb121234ab5864e/437be/infrastructure_transparent.png 1080w,\n/static/a44134e541c72364beb121234ab5864e/93b79/infrastructure_transparent.png 1366w,\n/static/a44134e541c72364beb121234ab5864e/5791f/infrastructure_transparent.png 1920w", "sizes": "100vw"}, "sources": [{"srcSet": "/static/a44134e541c72364beb121234ab5864e/eb410/infrastructure_transparent.webp 750w,\n/static/a44134e541c72364beb121234ab5864e/4319d/infrastructure_transparent.webp 1080w,\n/static/a44134e541c72364beb121234ab5864e/ff5f0/infrastructure_transparent.webp 1366w,\n/static/a44134e541c72364beb121234ab5864e/fd5c2/infrastructure_transparent.webp 1920w", "type": "image/webp", "sizes": "100vw"}]}, "width": 1, "height": 1.0927083333333332}}}, "isOutdated": null}, "body": "var _excluded = [\"components\"];\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* @jsxRuntime classic */\n\n/* @jsx mdx */\nvar _frontmatter = {\n  \"title\": \"Non-fungible tokens (NFT)\",\n  \"description\": \"An overview of NFTs on Ethereum\",\n  \"lang\": \"en\",\n  \"template\": \"use-cases\",\n  \"emoji\": \":frame_with_picture:\",\n  \"sidebar\": true,\n  \"sidebarDepth\": 2,\n  \"image\": \"../../assets/infrastructure_transparent.png\",\n  \"alt\": \"An Eth logo being displayed via hologram.\",\n  \"summaryPoint1\": \"A way to represent anything unique as an Ethereum-based asset.\",\n  \"summaryPoint2\": \"NFTs are giving more power to content creators than ever before.\",\n  \"summaryPoint3\": \"Powered by smart contracts on the Ethereum blockchain.\"\n};\n\nvar makeShortcode = function makeShortcode(name) {\n  return function MDXDefaultShortcode(props) {\n    console.warn(\"Component \" + name + \" was not imported, exported, or provided by MDXProvider as global scope\");\n    return mdx(\"div\", props);\n  };\n};\n\nvar YouTube = makeShortcode(\"YouTube\");\nvar Divider = makeShortcode(\"Divider\");\nvar InfoBanner = makeShortcode(\"InfoBanner\");\nvar ButtonLink = makeShortcode(\"ButtonLink\");\nvar layoutProps = {\n  _frontmatter: _frontmatter\n};\nvar MDXLayout = \"wrapper\";\nreturn function MDXContent(_ref) {\n  var components = _ref.components,\n      props = _objectWithoutProperties(_ref, _excluded);\n\n  return mdx(MDXLayout, _extends({}, layoutProps, props, {\n    components: components,\n    mdxType: \"MDXLayout\"\n  }), mdx(\"p\", null, \"NFTs are currently taking the digital art and collectibles world by storm. Digital artists are seeing their lives change thanks to huge sales to a new crypto-audience. And celebrities are joining in as they spot a new opportunity to connect with fans. But digital art is only one way to use NFTs. Really they can be used to represent ownership of any unique asset, like a deed for an item in the digital or physical realm.\"), mdx(\"p\", null, \"If Andy Warhol had been born in the late 90s, he probably would have minted Campbell's Soup as an NFT. It's only a matter of time before Kanye puts a run of Yeezys on Ethereum. And one day owning your car might be proved with an NFT.\"), mdx(\"h2\", {\n    \"id\": \"what-are-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#what-are-nfts\",\n    \"aria-label\": \"what are nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"What's an NFT?\"), mdx(\"p\", null, \"NFTs are tokens that we can use to represent ownership of unique items. They let us tokenise things like art, collectibles, even real estate. They can only have one official owner at a time and they're secured by the Ethereum blockchain \\u2013 no one can modify the record of ownership or copy/paste a new NFT into existence.\"), mdx(\"p\", null, \"NFT stands for non-fungible token. Non-fungible is an economic term that you could use to describe things like your furniture, a song file, or your computer. These things are not interchangeable for other items because they have unique properties.\"), mdx(\"p\", null, \"Fungible items, on the other hand, can be exchanged because their value defines them rather than their unique properties. For example, ETH or dollars are fungible because 1 ETH / $1 USD is exchangeable for another 1 ETH / $1 USD.\"), mdx(YouTube, {\n    id: \"Xdkkux6OxfM\",\n    mdxType: \"YouTube\"\n  }), mdx(\"h2\", {\n    \"id\": \"internet-of-assets\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#internet-of-assets\",\n    \"aria-label\": \"internet of assets permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"The internet of assets\"), mdx(\"p\", null, \"NFTs and Ethereum solve some of the problems that exist in the internet today. As everything becomes more digital, there's a need to replicate the properties of physical items like scarcity, uniqueness, and proof of ownership. Not to mention that digital items often only work in the context of their product. For example you can't re-sell an iTunes mp3 you've purchased, or you can't exchange one company's loyalty points for another platform's credit even if there's a market for it.\"), mdx(\"p\", null, \"Here's how an internet of NFTs compared to the internet most of us use today looks...\"), mdx(\"h3\", {\n    \"id\": \"nft-comparison\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-comparison\",\n    \"aria-label\": \"nft comparison permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"A comparison\"), mdx(\"table\", null, mdx(\"thead\", {\n    parentName: \"table\"\n  }, mdx(\"tr\", {\n    parentName: \"thead\"\n  }, mdx(\"th\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"An NFT internet\"), mdx(\"th\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"The internet today\"))), mdx(\"tbody\", {\n    parentName: \"table\"\n  }, mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"NFTs are digitally unique, no two NFTs are the same.\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"A copy of a file, like an .mp3 or .jpg, is the same as the original.\")), mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Every NFT must have an owner and this is of public record and easy for anyone to verify.\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Ownership records of digital items are stored on servers controlled by institutions \\u2013 you must take their word for it.\")), mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"NFTs are compatible with anything built using Ethereum. An NFT ticket for an event can be traded on every Ethereum marketplace, for an entirely different NFT. You could trade a piece of art for a ticket!\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Companies with digital items must build their own infrastructure. For example an app that issues digital tickets for events would have to build their own ticket exchange.\")), mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Content creators can sell their work anywhere and can access a global market.\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Creators rely on the infrastructure and distribution of the platforms they use. These are often subject to terms of use and geographical restrictions.\")), mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Creators can retain ownership rights over their own work, and claim resale royalties directly.\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Platforms, such as music streaming services, retain the majority of profits from sales.\")), mdx(\"tr\", {\n    parentName: \"tbody\"\n  }, mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  }, \"Items can be used in surprising ways. For example, you can use digital artwork as collateral in a decentralised loan.\"), mdx(\"td\", {\n    parentName: \"tr\",\n    \"align\": null\n  })))), mdx(\"h3\", {\n    \"id\": \"nft-examples\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-examples\",\n    \"aria-label\": \"nft examples permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"NFT examples\"), mdx(\"p\", null, \"The NFT world is relatively new. In theory, the scope for NFTs is anything that is unique that needs provable ownership. Here are some examples of NFTs that exist today, to help you get the idea:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://foundation.app/artworks\"\n  }, \"A unique digital artwork\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://www.metagrail.co/auctions/91cf83fb-3477-4155-aae8-6dcb9b853397\"\n  }, \"A unique sneaker in a limited-run fashion line\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://market.decentraland.org/\"\n  }, \"An in-game item\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://zora.co/******************************************/145\"\n  }, \"An essay\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://www.larvalabs.com/cryptopunks/details/1\"\n  }, \"A digital collectible\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://app.ens.domains/name/ethereum.eth\"\n  }, \"A domain name\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://www.yellowheart.io/\"\n  }, \"A ticket that gives you access to an event or a coupon\"))), mdx(\"h3\", {\n    \"id\": \"ethereum-org-examples\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#ethereum-org-examples\",\n    \"aria-label\": \"ethereum org examples permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"ethstake.exchange examples\"), mdx(\"p\", null, \"We use NFTs to give back to our contributors and we've even got our own NFT domain name.\"), mdx(\"h4\", {\n    \"id\": \"poaps\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#poaps\",\n    \"aria-label\": \"poaps permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"POAPs (Proof of attendance protocol)\"), mdx(\"p\", null, \"If you contribute to ethstake.exchange, you can claim a POAP NFT. These are collectibles that prove you participated in an event. Some crypto meetups have used POAPs as a form of ticket to their events. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/contributing/#poap\"\n  }, \"More on contributing\"), \".\"), mdx(\"p\", null, mdx(\"span\", {\n    parentName: \"p\",\n    \"className\": \"gatsby-resp-image-wrapper\",\n    \"style\": {\n      \"position\": \"relative\",\n      \"display\": \"block\",\n      \"marginLeft\": \"auto\",\n      \"marginRight\": \"auto\",\n      \"maxWidth\": \"1200px\"\n    }\n  }, \"\\n      \", mdx(\"a\", {\n    parentName: \"span\",\n    \"className\": \"gatsby-resp-image-link\",\n    \"href\": \"/static/aee7aca97ad8405722868827513c9937/0d390/poap.png\",\n    \"style\": {\n      \"display\": \"block\"\n    },\n    \"target\": \"_blank\",\n    \"rel\": \"noopener\"\n  }, \"\\n    \", mdx(\"span\", {\n    parentName: \"a\",\n    \"className\": \"gatsby-resp-image-background-image\",\n    \"style\": {\n      \"paddingBottom\": \"67%\",\n      \"position\": \"relative\",\n      \"bottom\": \"0\",\n      \"left\": \"0\",\n      \"backgroundImage\": \"url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAANCAYAAACpUE5eAAAACXBIWXMAAAsTAAALEwEAmpwYAAACuElEQVQ4y43Ry0sUcRwA8O+6dNJ/wLtYrVmma1imF0ui6JBQaZGUaFhGZF56HLwGdgkPdpLADoU2mqs7kW6uaA8JDUNF3cfs7szO7Mt9jLMzO/Nb59toDwI1+8L39IXP9wWwS+Tn50PZgS4oKXwKR4vuFlYWd9VUHnlQfbLkcfWZiq6aqvI7BafKO6HmWCeUHq+EPSMvLw86aj3mR2fdcKniDd18UsDL1gDWW1msKw7i6SLb0IX9aagrVM3Wyuq9wdzcXJidipnnPoWgt+eDne734bePWc1Jy9rz7gS23XRSPU8Qep9hzj8n5CUZgpIE814vcPHUvlUvC+8cDtrlYVCS0mQtESf+VBJf0fQoIoKROQWWou2QxWIBIaNsJa/IIMiqKYoacBEN+gccw36/jKwvTjhfgnD8Og7ZHMO/wYOHS7aD/W+HgDMmC8pp4GQJIioBIZ0pDytq3dTMwpzHKyLDxrO+oDEh9x/gZpGT1gHjCKKKBqZ0C+sqCikNw+sE2ZiiLwtJXA2niNuTQpt9kvoF7vwUzSiGVNUcIhoIitwaUglGGXZDWPISfk3TQ0kDTcg6ExSJixGRGpykstmfYNmJqu2gJxYFgmja7BrV1fdeXkVmIUp8rjj6WU3nQwryMVlngxJxuRM4SDmpDWMT1Dcn3AFcFnhIbqBpwwB5VRn/MiHhNCWSzw5FX7Qt6YnJcZ1d8uvG/UiAWUP7yNhfK1ftfENWVM28kgG/KLavGF+dn8+QpRmBeEans/Gvs1neH8tyfDrjZ5I4YnMO7Am6Yhy8HnsB9VfLTIIm9UU0FUPJDAaCIrpcIXQzEXR71/D7Yhj7XtL2aAQhEd/lhhRNQyAdg5UwD3Pe5a0GiwG+JpjK3KPoibbG600dTS237t9oud1+rbH14bnzF2vrG5qh4Uqz6VCp9Y/zA7b2DB7AT7KxAAAAAElFTkSuQmCC')\",\n      \"backgroundSize\": \"cover\",\n      \"display\": \"block\"\n    }\n  }), \"\\n  \", mdx(\"img\", {\n    parentName: \"a\",\n    \"className\": \"gatsby-resp-image-image\",\n    \"alt\": \"ethstake.exchange POAP\",\n    \"title\": \"ethstake.exchange POAP\",\n    \"src\": \"/static/aee7aca97ad8405722868827513c9937/c1b63/poap.png\",\n    \"srcSet\": [\"/static/aee7aca97ad8405722868827513c9937/5a46d/poap.png 300w\", \"/static/aee7aca97ad8405722868827513c9937/0a47e/poap.png 600w\", \"/static/aee7aca97ad8405722868827513c9937/c1b63/poap.png 1200w\", \"/static/aee7aca97ad8405722868827513c9937/0d390/poap.png 1472w\"],\n    \"sizes\": \"(max-width: 1200px) 100vw, 1200px\",\n    \"style\": {\n      \"width\": \"100%\",\n      \"height\": \"100%\",\n      \"margin\": \"0\",\n      \"verticalAlign\": \"middle\",\n      \"position\": \"absolute\",\n      \"top\": \"0\",\n      \"left\": \"0\"\n    },\n    \"loading\": \"lazy\",\n    \"decoding\": \"async\"\n  }), \"\\n  \"), \"\\n    \")), mdx(\"h4\", {\n    \"id\": \"ethereum-dot-eth\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#ethereum-dot-eth\",\n    \"aria-label\": \"ethereum dot eth permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"ethereum.eth\"), mdx(\"p\", null, \"This website has an alternative domain name powered by NFTs, \", mdx(\"strong\", {\n    parentName: \"p\"\n  }, \"ethereum.eth\"), \". Our \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \".org\"), \" address is centrally managed by a domain name system (DNS) provider, whereas ethereum\", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \".eth\"), \" is registered on Ethereum via the Ethereum Name Service (ENS). And its owned and managed by us. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://app.ens.domains/name/ethereum.eth\"\n  }, \"Check our ENS record\")), mdx(\"p\", null, mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://app.ens.domains\"\n  }, \"More on ENS\")), mdx(\"h2\", {\n    \"id\": \"how-nfts-work\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#how-nfts-work\",\n    \"aria-label\": \"how nfts work permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"How do NFTs work?\"), mdx(\"p\", null, \"NFTs are different from ERC-20 tokens, such as DAI or LINK, in that each individual token is completely unique and is not divisible. NFTs give the ability to assign or claim ownership of any unique piece of digital data, trackable by using Ethereum's blockchain as a public ledger. An NFT is minted from digital objects as a representation of digital or non-digital assets. For example, an NFT could represent:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Digital Art:\", mdx(\"ul\", {\n    parentName: \"li\"\n  }, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"GIFs\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Collectibles\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Music\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Videos\"))), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Real World Items:\", mdx(\"ul\", {\n    parentName: \"li\"\n  }, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Deeds to a car\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Tickets to a real world event\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Tokenized invoices\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Legal documents\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Signatures\"))), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Lots and lots more options to get creative with!\")), mdx(\"p\", null, \"An NFT can only have one owner at a time. Ownership is managed through the uniqueID and metadata that no other token can replicate. NFTs are minted through smart contracts that assign ownership and manage the transferability of the NFT's. When someone creates or mints an NFT, they execute code stored in smart contracts that conform to different standards, such as ERC-721. This information is added to the blockchain where the NFT is being managed. The minting process, from a high level, has the following steps that it goes through:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Creating a new block\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Validating information\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Recording information into the blockchain\")), mdx(\"p\", null, \"NFT's have some special properties:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Each token minted has a unique identifier that is directly linked to one Ethereum address.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"They're not directly interchangeable with other tokens 1:1. For example 1 ETH is exactly the same as another ETH. This isn't the case with NFTs.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Each token has an owner and this information is easily verifiable.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"They live on Ethereum and can be bought and sold on any Ethereum-based NFT market.\")), mdx(\"p\", null, \"In other words, if you \", mdx(\"em\", {\n    parentName: \"p\"\n  }, \"own\"), \" an NFT:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You can easily prove you own it.\", mdx(\"ul\", {\n    parentName: \"li\"\n  }, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Proving you own an NFT is very similar to proving you have ETH in your account.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"For example, let's say you purchase an NFT, and the ownership of the unique token is transferred to your wallet via your public address.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The token proves that your copy of the digital file is the original.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Your private key is proof-of-ownership of the original.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The content creator's public key serves as a certificate of authenticity for that particular digital artefact.\", mdx(\"ul\", {\n    parentName: \"li\"\n  }, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The creators public key is essentially a permanent part of the token's history. The creator's public key can demonstrate that the token you hold was created by a particular individual, thus contributing to its market value (vs a counterfeit).\"))), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Another way to think about proving you own the NFT is by signing messages to prove you own the private key behind the address.\", mdx(\"ul\", {\n    parentName: \"li\"\n  }, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"As mentioned above, your private key is proof-of-ownership of the original. This tells us that the private keys behind that address control the NFT.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"A signed message can be used as proof that you own your private keys without revealing them to anybody and thus proving you own the NFT as well!\"))))), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"No one can manipulate it in any way.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You can sell it, and in some cases this will earn the original creator resale royalties.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Or, you can hold it forever, resting comfortably knowing your asset is secured by your wallet on Ethereum.\")), mdx(\"p\", null, \"And if you \", mdx(\"em\", {\n    parentName: \"p\"\n  }, \"create\"), \" an NFT:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You can easily prove you're the creator.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You determine the scarcity.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You can earn royalties every time it's sold.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You can sell it on any NFT market or peer-to-peer. You're not locked in to any platform and you don't need anyone to intermediate.\")), mdx(\"h3\", {\n    \"id\": \"scarcity\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#scarcity\",\n    \"aria-label\": \"scarcity permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Scarcity\"), mdx(\"p\", null, \"The creator of an NFT gets to decide the scarcity of their asset.\"), mdx(\"p\", null, \"For example, consider a ticket to a sporting event. Just as an organizer of an event can choose how many tickets to sell, the creator of an NFT can decide how many replicas exist. Sometimes these are exact replicas, such as 5000 General Admission tickets. Sometimes several are minted that are very similar, but each slightly different, such as a ticket with an assigned seat. In another case, the creator may want to create an NFT where only one is minted as a special rare collectible.\"), mdx(\"p\", null, \"In these cases, each NFT would still have a unique identifier (like a bar code on a traditional \\\"ticket\\\"), with only one owner. The intended scarcity of the NFT matters, and is up to the creator. A creator may intend to make each NFT completely unique to create scarcity, or have reasons to produce several thousand replicas. Remember, this information is all public.\"), mdx(\"h3\", {\n    \"id\": \"royalties\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#royalties\",\n    \"aria-label\": \"royalties permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Royalties\"), mdx(\"p\", null, \"Some NFTs will automatically pay out royalties to their creators when they're sold. This is still a developing concept but it's one of the most powerful. Original owners of \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://eulerbeats.com/\"\n  }, \"EulerBeats Originals\"), \" earn an 8% royalty every time the NFT is sold on. And some platforms, like \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://foundation.app\"\n  }, \"Foundation\"), \" and \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://zora.co/\"\n  }, \"Zora\"), \", support royalties for their artists.\"), mdx(\"p\", null, \"This is completely automatic so creators can just sit back and earn royalties as their work is sold from person to person. At the moment, figuring out royalties is very manual and lacks accuracy \\u2013 a lot of creators don't get paid what they deserve. If your NFT has a royalty programmed into it, you'll never miss out.\"), mdx(\"h2\", {\n    \"id\": \"nft-use-cases\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#nft-use-cases\",\n    \"aria-label\": \"nft use cases permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"What are NFTs used for?\"), mdx(\"p\", null, \"Here's more information of some of the better developed use-cases and visions for NFTs on Ethereum.\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"#nfts-for-creators\"\n  }, \"Digital content\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"#nft-gaming\"\n  }, \"Gaming items\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"#nft-domains\"\n  }, \"Domain names\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"#nft-physical-items\"\n  }, \"Physical items\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"#nfts-and-defi\"\n  }, \"Investments and collateral\"))), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"nfts-for-creators\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nfts-for-creators\",\n    \"aria-label\": \"nfts for creators permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Maximising earnings for creators\"), mdx(\"p\", null, \"The biggest use of NFTs today is in the digital content realm. That's because that industry today is broken. Content creators see their profits and earning potential swallowed by platforms.\"), mdx(\"p\", null, \"An artist publishing work on a social network makes money for the platform who sell ads to the artists followers. They get exposure in return, but exposure doesn't pay the bills.\"), mdx(\"p\", null, \"NFTs power a new creator economy where creators don't hand ownership of their content over to the platforms they use to publicise it. Ownership is baked into the content itself.\"), mdx(\"p\", null, \"When they sell their content, funds go directly to them. If the new owner then sells the NFT, the original creator can even automatically receive royalties. This is guaranteed every time it's sold because the creator's address is part of the token's metadata \\u2013 metadata which can't be modified.\"), mdx(InfoBanner, {\n    shouldSpaceBetween: true,\n    emoji: \":eyes:\",\n    mdxType: \"InfoBanner\"\n  }, mdx(\"div\", null, \"Explore, buy or create your own NFT art/collectibles...\"), mdx(ButtonLink, {\n    to: \"/dapps/?category=collectibles\",\n    mdxType: \"ButtonLink\"\n  }, \"Explore NFT art\")), mdx(\"h4\", {\n    \"id\": \"nfts-copy-paste\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#nfts-copy-paste\",\n    \"aria-label\": \"nfts copy paste permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"The copy/paste problem\"), mdx(\"p\", null, \"Naysayers often bring up the fact that NFTs \\\"are dumb\\\" usually alongside a picture of them screenshotting an NFT artwork. \\\"Look, now I have that image for free!\\\" they say smugly.\"), mdx(\"p\", null, \"Well, yes. But does googling an image of Picasso's Guernica make you the proud new owner of a multi-million dollar piece of art history?\"), mdx(\"p\", null, \"Ultimately owning the real thing is as valuable as the market makes it. The more a piece of content is screen-grabbed, shared, and generally used the more value it gains.\"), mdx(\"p\", null, \"Owning the verifiably real thing will always have more value than not.\"), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"nft-gaming\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-gaming\",\n    \"aria-label\": \"nft gaming permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Boosting gaming potential\"), mdx(\"p\", null, \"NFTs have seen a lot of interest from game developers. NFTs can provide records of ownership for in-game items, fuel in-game economies, and bring a host of benefits to the players.\"), mdx(\"p\", null, \"In a lot of regular games you can buy items for you to use in your game. But if that item was an NFT you could recoup your money by selling it on when you're done with the game. You might even make a profit if that item becomes more desirable.\"), mdx(\"p\", null, \"For game developers \\u2013 as issuers of the NFT \\u2013 they could earn a royalty every time an item is re-sold in the open marketplace. This creates a more mutually-beneficial business model where both players and developers earn from the secondary NFT market.\"), mdx(\"p\", null, \"This also means that if a game is no longer maintained by the developers, the items you've collected remain yours.\"), mdx(\"p\", null, \"Ultimately the items you grind for in-game can outlive the games themselves. Even if a game is no longer maintained, your items will always be under your control. This means in-game items become digital memorabilia and have a value outside of the game.\"), mdx(\"p\", null, \"Decentraland, a virtual reality game, even lets you buy NFTs representing virtual parcels of land that you can use as you see fit.\"), mdx(InfoBanner, {\n    shouldSpaceBetween: true,\n    emoji: \":eyes:\",\n    mdxType: \"InfoBanner\"\n  }, mdx(\"div\", null, \"Check out Ethereum games, powered by NFTs...\"), mdx(ButtonLink, {\n    to: \"/dapps/?category=gaming\",\n    mdxType: \"ButtonLink\"\n  }, \"Explore NFT games\")), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"nft-domains\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-domains\",\n    \"aria-label\": \"nft domains permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Making Ethereum addresses more memorable\"), mdx(\"p\", null, \"The Ethereum Name Service uses NFTs to provide your Ethereum address with an easier-to-remember name like \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"mywallet.eth\"), \". This means you could ask someone to send you ETH via \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"mywallet.eth\"), \" rather than \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"0x123456789.....\"), \".\"), mdx(\"p\", null, \"This works in a similar way to a website domain name which makes an IP address more memorable. And like domains, ENS names have value, usually based on length and relevance. With ENS you don't need a domain registry to facilitate the transfer of ownership. Instead, you can trade your ENS names on an NFT marketplace.\"), mdx(\"p\", null, \"Your ENS name can:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Receive cryptocurrency and other NFTs.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Point to a decentralized website, like \", mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://ethereum.eth.link\"\n  }, \"ethereum.eth\"), \". \", mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://docs.ipfs.io/how-to/websites-on-ipfs/link-a-domain/#domain-name-service-dns\"\n  }, \"More on decentralizing your website\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Store any arbitrary information, including profile information like email addresses and Twitter handles.\")), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"nft-physical-items\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-physical-items\",\n    \"aria-label\": \"nft physical items permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Physical items\"), mdx(\"p\", null, \"The tokenisation of physical items isn't yet as developed as their digital counterparts. But there are plenty of projects exploring the tokenisation of real estate, one-of-a-kind fashion items, and more.\"), mdx(\"p\", null, \"As NFTs are essentially deeds, one day you could buy a car or home using ETH and receive the deed as an NFT in return (in the same transaction). As things become increasingly high-tech, it's not hard to imagine a world where your Ethereum wallet becomes the key to your car or home \\u2013 your door being unlocked by the cryptographic proof of ownership.\"), mdx(\"p\", null, \"With valuable assets like cars and property representable on Ethereum, you can use NFTs as collateral in decentralized loans. This is particularly helpful if you're not cash or crypto-rich but own physical items of value. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/defi/\"\n  }, \"More on DeFi\")), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"nfts-and-defi\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nfts-and-defi\",\n    \"aria-label\": \"nfts and defi permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"NFTs and DeFi\"), mdx(\"p\", null, \"The NFT world and the \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/defi/\"\n  }, \"decentralized finance (DeFi)\"), \" world are starting to work together in a number of interesting ways.\"), mdx(\"h4\", {\n    \"id\": \"nft-backed-loans\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#nft-backed-loans\",\n    \"aria-label\": \"nft backed loans permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"NFT-backed loans\"), mdx(\"p\", null, \"There are DeFi applications that let you borrow money by using collateral. For example you collateralise 10 ETH so you can borrow 5000 DAI (\", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/stablecoins/\"\n  }, \"a stablecoin\"), \"). This guarantees that the lender gets paid back \\u2013 if the borrower doesn't pay back the DAI, the collateral is sent to the lender. However not everyone has enough crypto to use as collateral.\"), mdx(\"p\", null, \"Projects are beginning to explore using NFTs as collateral instead. Imagine you bought a rare CryptoPunk NFT back in the day \\u2013 they can fetch $1000s at today's prices. By putting this up as collateral, you can access a loan with the same rule set. If you don't pay back the DAI, your CryptoPunk will be sent to the lender as collateral. This could eventually work with anything you tokenise as an NFT.\"), mdx(\"p\", null, \"And this isn't hard on Ethereum, because both worlds (NFT and DeFi) share the same infrastructure.\"), mdx(\"h4\", {\n    \"id\": \"fractional-ownership\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#fractional-ownership\",\n    \"aria-label\": \"fractional ownership permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Fractional ownership\"), mdx(\"p\", null, \"NFT creators can also create \\\"shares\\\" for their NFT. This gives investors and fans the opportunity to own a part of an NFT without having to buy the whole thing. This adds even more opportunities for NFT minters and collectors alike.\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Fractionalised NFTs can be traded on \", mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"/defi/#dex\"\n  }, \"DEXs\"), \" like Uniswap, not just \", mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"/dapps?category=collectibles\"\n  }, \"NFT marketplaces\"), \". That means more buyers and sellers.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"An NFT's overall price can be defined by the price of its fractions.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"You have more of an opportunity to own and profit from items you care about. It's harder to be priced out of owning NFTs.\")), mdx(\"p\", null, \"This is still experimental but you can learn more about fractional NFT ownership at the following exchanges:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://landing.niftex.com/\"\n  }, \"NIFTEX\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://gallery.nftx.org/\"\n  }, \"NFTX\"))), mdx(\"p\", null, \"In theory, this would unlock the possibility to do things like own a piece of a Picasso. You would become a shareholder in a Picasso NFT, meaning you would have a say in things like revenue sharing. It's very likely that one day soon owning a fraction of an NFT will enter you into a decentralised autonomous organisation (DAO) for managing that asset.\"), mdx(\"p\", null, \"These are Ethereum-powered organisations that allow strangers, like global shareholders of an asset, to coordinate securely without necessarily having to trust the other people. That's because not a single penny can be spent without group approval.\"), mdx(\"p\", null, \"As we mentioned, this is an emerging space. NFTs, DAOs, fractionalised tokens are all developing at different paces. But all their infrastructure exists and can work together easily because they all speak the same language: Ethereum. So watch this space.\"), mdx(\"p\", null, mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/dao/\"\n  }, \"More on DAOs\")), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h2\", {\n    \"id\": \"ethereum-and-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#ethereum-and-nfts\",\n    \"aria-label\": \"ethereum and nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Ethereum and NFTs\"), mdx(\"p\", null, \"Ethereum makes it possible for NFTs to work for a number of reasons:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Transaction history and token metadata is publicly verifiable \\u2013 it's simple to prove ownership history.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Once a transaction is confirmed, it's nearly impossible to manipulate that data to \\\"steal\\\" ownership.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Trading NFTs can happen peer-to-peer without needing platforms that can take large cuts as compensation.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"All Ethereum products share the same \\\"backend\\\". Put another way, all Ethereum products can easily understand each other \\u2013 this makes NFTs portable across products. You can buy an NFT on one product and sell it on another easily. As a creator you can list your NFTs on multiple products at the same time \\u2013 every product will have the most up-to-date ownership information.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Ethereum never goes down, meaning your tokens will always be available to sell.\")), mdx(\"h2\", {\n    \"id\": \"environmental-impact-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#environmental-impact-nfts\",\n    \"aria-label\": \"environmental impact nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"The environmental impact of NFTs\"), mdx(\"p\", null, \"NFTs are growing in popularity which means they're also coming under increased scrutiny \\u2013 especially over their carbon footprint.\"), mdx(\"p\", null, \"To clarify a few things:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"NFTs aren't directly increasing the carbon footprint of Ethereum.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The way Ethereum keeps your funds and assets secure is currently energy-intensive but it's about to improve.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"Once improved, Ethereum's carbon footprint will be 99.95% better, making it more energy efficient than many existing industries.\")), mdx(\"p\", null, \"To explain further we're going to have to get a little more technical so bear with us...\"), mdx(\"h3\", {\n    \"id\": \"nft-qualities\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#nft-qualities\",\n    \"aria-label\": \"nft qualities permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Don't blame it on the NFTs\"), mdx(\"p\", null, \"The whole NFT ecosystem works because Ethereum is decentralized and secure.\"), mdx(\"p\", null, \"Decentralized meaning you and everyone else can verify you own something. All without trusting or granting custody to a third party who can impose their own rules at will. It also means your NFT is portable across many different products and markets.\"), mdx(\"p\", null, \"Secure meaning no one can copy/paste your NFT or steal it.\"), mdx(\"p\", null, \"These qualities of Ethereum makes digitally owning unique items and getting a fair price for your content possible. But it comes at a cost. Blockchains like Bitcoin and Ethereum are energy intensive right now because it takes a lot of energy to preserve these qualities. If it was easy to rewrite Ethereum's history to steal NFTs or cryptocurrency, the system collapses.\"), mdx(\"h4\", {\n    \"id\": \"minting-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#minting-nfts\",\n    \"aria-label\": \"minting nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"The work in minting your NFT\"), mdx(\"p\", null, \"When you mint an NFT, a few things have to happen:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"It needs to be confirmed as an asset on the blockchain.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The owner's account balance must be updated to include that asset. This makes it possible for it to then be traded or verifiably \\\"owned\\\".\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The transactions that confirm the above need to be added to a block and \\\"immortalised\\\" on the chain.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, \"The block needs to be confirmed by everyone in the network as \\\"correct\\\". This consensus removes the need for intermediaries because the network agrees that your NFT exists and belongs to you. And it's on chain so anyone can check it. This is one of the ways Ethereum helps NFT creators to maximise their earnings.\")), mdx(\"p\", null, \"All these tasks are done by miners. And they let the rest of the network know about your NFT and who owns it. This means mining needs to be sufficiently difficult, otherwise anyone could just claim that they own the NFT you just minted and fraudulently transfer ownership. There are lots of incentives in place to make sure miners are acting honestly.\"), mdx(\"p\", null, mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/developers/docs/consensus-mechanisms/pow/\"\n  }, \"More on mining\")), mdx(\"h4\", {\n    \"id\": \"securing-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#securing-nfts\",\n    \"aria-label\": \"securing nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Securing your NFT with mining\"), mdx(\"p\", null, \"Mining difficulty comes from the fact that it takes a lot of computing power to create new blocks in the chain. Importantly, blocks are created consistently, not just when they're needed. They're created every 12 seconds or so.\"), mdx(\"p\", null, \"This is important for making Ethereum tamper-proof, one of the qualities that makes NFTs possible. The more blocks the more secure the chain. If your NFT was created in block #600 and a hacker were to try and steal your NFT by modifying its data, the digital fingerprint of all subsequent blocks would change. That means anyone running Ethereum software would immediately be able to detect and prevent it from happening.\"), mdx(\"p\", null, \"However this means that computing power needs to be used constantly. It also means that a block that contains 0 NFT transactions will still have roughly the same carbon footprint, because computing power will still be consumed to create it. Other non-NFT transactions will fill the blocks.\"), mdx(\"h4\", {\n    \"id\": \"blockchains-intensive\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#blockchains-intensive\",\n    \"aria-label\": \"blockchains intensive permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Blockchains are energy intensive, right now\"), mdx(\"p\", null, \"So yes, there is a carbon footprint associated with creating blocks by mining \\u2013 and this is a problem for chains like Bitcoin too \\u2013 but it's not directly the fault of NFTs.\"), mdx(\"p\", null, \"A lot of mining uses renewable energy sources or untapped energy in remote locations. And there is the argument that the industries that NFTs and cryptocurrencies are disrupting have huge carbon footprints too. But just because existing industries are bad, doesn't mean we shouldn't strive to be better.\"), mdx(\"p\", null, \"And we are. Ethereum is evolving to make using Ethereum (and by virtue, NFTs) more energy efficient. And that's always been the plan.\"), mdx(\"p\", null, \"We're not here to defend the environmental footprint of mining, instead we want to explain how things are changing for the better.\"), mdx(\"h3\", {\n    \"id\": \"a-greener-future\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#a-greener-future\",\n    \"aria-label\": \"a greener future permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"A greener future...\"), mdx(\"p\", null, \"For as long as Ethereum has been around, the energy-consumption of mining has been a huge focus area for developers and researchers. And the vision has always been to replace it as soon as possible. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/upgrades/vision/\"\n  }, \"More on Ethereum's vision\")), mdx(\"p\", null, \"This vision is being delivered right now.\"), mdx(\"h4\", {\n    \"id\": \"greener-ethereum\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#greener-ethereum\",\n    \"aria-label\": \"greener ethereum permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"A greener Ethereum\"), mdx(\"p\", null, \"Ethereum is currently going through a series of upgrades that will replace mining with \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/staking/\"\n  }, \"staking\"), \". This will remove computing power as a security mechanism, and reduce Ethereum's carbon footprint by ~99.95%\", mdx(\"sup\", null, mdx(\"sup\", {\n    parentName: \"p\",\n    \"id\": \"fnref-1\"\n  }, mdx(\"a\", {\n    parentName: \"sup\",\n    \"href\": \"#fn-1\",\n    \"className\": \"footnote-ref\"\n  }, \"1\"))), \". In this world, stakers commit funds instead of computing power to secure the network.\"), mdx(\"p\", null, \"The energy-cost of Ethereum will become the cost of running a home computer multiplied by the number of nodes in the network. If there are 10,000 nodes in the network and the cost of running a home computer is roughly 525kWh per year. That's 5,250,000kWh\", mdx(\"sup\", null, mdx(\"sup\", {\n    parentName: \"p\",\n    \"id\": \"fnref-1\"\n  }, mdx(\"a\", {\n    parentName: \"sup\",\n    \"href\": \"#fn-1\",\n    \"className\": \"footnote-ref\"\n  }, \"1\"))), \" per year for the entire network.\"), mdx(\"p\", null, \"We can use this to compare the future of Ethereum to a global service like Visa. 100,000 Visa transactions uses 149kWh of energy\", mdx(\"sup\", null, mdx(\"sup\", {\n    parentName: \"p\",\n    \"id\": \"fnref-2\"\n  }, mdx(\"a\", {\n    parentName: \"sup\",\n    \"href\": \"#fn-2\",\n    \"className\": \"footnote-ref\"\n  }, \"2\"))), \". In proof-of-stake Ethereum, that same number of transactions would cost 17.4kWh of energy or ~11% of the total energy\", mdx(\"sup\", null, mdx(\"sup\", {\n    parentName: \"p\",\n    \"id\": \"fnref-3\"\n  }, mdx(\"a\", {\n    parentName: \"sup\",\n    \"href\": \"#fn-3\",\n    \"className\": \"footnote-ref\"\n  }, \"3\"))), \". That's without considering the many optimizations being worked on in parallel to the consensus layer and shard chains, like \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/glossary/#rollups\"\n  }, \"rollups\"), \". It could be as little as 0.1666666667kWh of energy for 100,000 transactions.\"), mdx(\"p\", null, \"Importantly this improves the energy efficiency while preserving Ethereum's decentralization and security. Many other blockchains out there might already use some form of staking, but they're secured by a select few stakers, not the thousands that Ethereum will have. The more decentralization, the more secure the system.\"), mdx(\"p\", null, mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"#footnotes-and-sources\"\n  }, \"More on energy estimates\")), mdx(\"p\", null, mdx(\"em\", {\n    parentName: \"p\"\n  }, \"We\\u2019ve provided the basic comparison to Visa to baseline your understanding of proof-of-stake Ethereum energy consumption against a familiar name. However, in practice, it\\u2019s not really correct to compare based on number of transactions. Ethereum\\u2019s energy output is time-based. If Ethereum did more or less transactions from one minute to the next, the energy output would stay the same.\")), mdx(\"p\", null, mdx(\"em\", {\n    parentName: \"p\"\n  }, \"It\\u2019s also important to remember that Ethereum does more than just financial transactions, it\\u2019s a platform for applications, so a fairer comparison might be to many companies/industries including Visa, AWS and more!\")), mdx(\"h4\", {\n    \"id\": \"timelines\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#timelines\",\n    \"aria-label\": \"timelines permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Timelines\"), mdx(\"p\", null, \"The process has already started. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/upgrades/beacon-chain/\"\n  }, \"The Beacon Chain\"), \", the first upgrade, shipped in December 2020. This provides the foundation for staking by allowing stakers to join the system. The next step relevant to energy efficiency is to merge the current chain, the one secured by miners, into the Beacon Chain where mining isn't needed. Timelines can't be exact at this stage, but it's estimated that this will happen sometime in 2022. This process is known as The Merge (formerly referred to as the docking). \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/upgrades/merge/\"\n  }, \"More on The Merge\"), \".\"), mdx(ButtonLink, {\n    to: \"/upgrades/\",\n    mdxType: \"ButtonLink\"\n  }, \"More on Ethereum upgrades\"), mdx(\"h2\", {\n    \"id\": \"build-with-nfts\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#build-with-nfts\",\n    \"aria-label\": \"build with nfts permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Build with NFTs\"), mdx(\"p\", null, \"Most NFTs are built using a consistent standard known as \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/developers/docs/standards/tokens/erc-721/\"\n  }, \"ERC-721\"), \". However there are other standards that you might want to look into. The \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://blog.enjincoin.io/erc-1155-the-crypto-item-standard-ac9cf1c5a226\"\n  }, \"ERC-1155\"), \" standard allows for semi-fungible tokens which is particularly useful in the realm of gaming. And more recently, \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://eips.ethstake.exchange/EIPS/eip-2309\"\n  }, \"EIP-2309\"), \" has been proposed to make minting NFTs a lot more efficient. This standard lets you mint as many as you like in one transaction!\"), mdx(\"h2\", {\n    \"id\": \"further-reading\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h2\",\n    \"href\": \"#further-reading\",\n    \"aria-label\": \"further reading permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Further reading\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://cryptoart.io/data\"\n  }, \"Crypto art data\"), \" \\u2013 Richard Chen, updated automatically\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://opensea.io/blog/guides/non-fungible-tokens/\"\n  }, \"OpenSea: the NFT Bible\"), \" \\u2013 \", mdx(\"em\", {\n    parentName: \"li\"\n  }, \"Devin Fizner, January 10 2020\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://linda.mirror.xyz/df649d61efb92c910464a4e74ae213c4cab150b9cbcc4b7fb6090fc77881a95d\"\n  }, \"A beginner's guide to NFTs\"), \" \\u2013 \", mdx(\"em\", {\n    parentName: \"li\"\n  }, \"Linda Xie, January 2020\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://foundation.app/blog/enter-the-metaverse\"\n  }, \"Everything you need to know about the metaverse\"), \" \\u2013 \", mdx(\"em\", {\n    parentName: \"li\"\n  }, \"Foundation team, foundation.app\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://medium.com/superrare/no-cryptoartists-arent-harming-the-planet-43182f72fc61\"\n  }, \"No, CryptoArtists Aren\\u2019t Harming the Planet\")), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"a\", {\n    parentName: \"li\",\n    \"href\": \"https://blog.ethstake.exchange/2021/05/18/country-power-no-more/\"\n  }, \"A country's worth of power, no more\"), \" \\u2013 \", mdx(\"em\", {\n    parentName: \"li\"\n  }, \"Carl Beekhuizen, May 18 2021\"))), mdx(Divider, {\n    mdxType: \"Divider\"\n  }), mdx(\"h3\", {\n    \"id\": \"footnotes-and-sources\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h3\",\n    \"href\": \"#footnotes-and-sources\",\n    \"aria-label\": \"footnotes and sources permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"Footnotes and sources\"), mdx(\"p\", null, \"This explains how we arrived at our energy estimates above. These estimates apply to the network as a whole and are not just reserved for the process of creating, buying, or selling NFTs.\"), mdx(\"h4\", {\n    \"id\": \"fn-1\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#fn-1\",\n    \"aria-label\": \"fn 1 permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"1. 99.95% energy reduction from mining\"), mdx(\"p\", null, \"The 99.95% reduction in energy consumption from a system secured by mining to a system secured by staking is calculated using the following data sources:\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"p\", {\n    parentName: \"li\"\n  }, \"44.49 TWh of annualized electrical energy is consumed by mining Ethereum - \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://digiconomist.net/ethereum-energy-consumption\"\n  }, \"Digiconomist\"))), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"p\", {\n    parentName: \"li\"\n  }, \"The average desktop computer, all that's needed to run proof-of-stake, uses 0.06kWh of energy per hour \\u2013 \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://www.siliconvalleypower.com/residents/save-energy/appliance-energy-use-chart\"\n  }, \"Silicon Valley power chart\"), \" (Some estimates are a little higher at 0.15kWh)\"))), mdx(\"p\", null, \"At the time of writing, there are 140 592 validators from 16 405 unique addresses.\\nOf those, 87 897 validators are assumed to be staking from home.\"), mdx(\"p\", null, \"It is assumed the average person staking from home uses a 100 watt desktop personal computer setup to run an average of 5.4 validator clients.\"), mdx(\"p\", null, \"The 87 897 validators running from home gives us 16 300 users consuming ~1.64 megawatt of energy.\"), mdx(\"p\", null, \"The rest of the validators are run by custodial stakers such as exchanges and staking services.\\nIt can be assumed that they use 100w per 5.5 validators. This is a gross overestimation to be on the safe side.\"), mdx(\"p\", null, \"In total, Ethereum on proof-of-stake therefore consumes something on the order of 2.62 megawatt, which is about the same as a small American town.\"), mdx(\"p\", null, \"This is a reduction of at least 99.95% in total energy usage from the Digiconomist estimate of 44.94 TWh per year that the Ethereum miners currently consume.\"), mdx(\"h4\", {\n    \"id\": \"fn-2\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#fn-2\",\n    \"aria-label\": \"fn 2 permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"2. Visa energy consumption\"), mdx(\"p\", null, \"The cost of 100,000 Visa transactions is 149 kwH - \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://www.statista.com/statistics/881541/bitcoin-energy-consumption-transaction-comparison-visa/\"\n  }, \"Bitcoin network average energy consumption per transaction compared to VISA network as of 2020, Statista\")), mdx(\"p\", null, \"Year-ending September 2020 they processed 140,839,000,000 transactions \\u2013 \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://s1.q4cdn.com/050606653/files/doc_financials/2020/q4/Visa-Inc.-Q4-2020-Operational-Performance-Data.pdf\"\n  }, \"Visa financials report Q4 2020\")), mdx(\"h4\", {\n    \"id\": \"fn-3\",\n    \"style\": {\n      \"position\": \"relative\"\n    }\n  }, mdx(\"a\", {\n    parentName: \"h4\",\n    \"href\": \"#fn-3\",\n    \"aria-label\": \"fn 3 permalink\",\n    \"className\": \"header-anchor before\"\n  }, mdx(\"svg\", {\n    parentName: \"a\",\n    \"aria-hidden\": \"true\",\n    \"focusable\": \"false\",\n    \"height\": \"16\",\n    \"version\": \"1.1\",\n    \"viewBox\": \"0 0 16 16\",\n    \"width\": \"16\"\n  }, mdx(\"path\", {\n    parentName: \"svg\",\n    \"fillRule\": \"evenodd\",\n    \"d\": \"M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z\"\n  }))), \"3. Energy usage for 100,000 transactions on a sharded proof-of-stake network\"), mdx(\"p\", null, \"It's estimated that scalability upgrades will allow the network to process between 25,000 and 100,000 transactions per second, with \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://ethereum-magicians.org/t/a-rollup-centric-ethereum-roadmap/4698\"\n  }, \"100,000 as the theoretical maximum right now\"), \".\"), mdx(\"p\", null, mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"https://twitter.com/VitalikButerin/status/1312905884549300224\"\n  }, \"Vitalik Buterin on transactions per second potential with sharding\")), mdx(\"p\", null, \"At the bare minimum, sharding will allow 64 times the amount of transactions as today which sits at around 15 transactions. That's the amount of shard chains (extra data and capacity) being introduced. \", mdx(\"a\", {\n    parentName: \"p\",\n    \"href\": \"/upgrades/shard-chains/\"\n  }, \"More on shard chains\")), mdx(\"p\", null, \"That means we can estimate how long it will take to process 100,000 transactions so we can compare it to the Visa example above.\"), mdx(\"ul\", null, mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"inlineCode\", {\n    parentName: \"li\"\n  }, \"15 * 64 = 960\"), \" transactions per second.\"), mdx(\"li\", {\n    parentName: \"ul\"\n  }, mdx(\"inlineCode\", {\n    parentName: \"li\"\n  }, \"100,000 / 960 = 104.2\"), \" seconds to process 100,000 transactions.\")), mdx(\"p\", null, \"In 104.2 seconds, the Ethereum network will use the following amount of energy:\"), mdx(\"p\", null, mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"1.44kWh daily usage * 10,000 network nodes = 14,400kWh\"), \" per day.\"), mdx(\"p\", null, \"There are 86,400 seconds in a day, so \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"14,400 / 86,400 = 0.1666666667kWh\"), \" per second.\"), mdx(\"p\", null, \"If we multiply that by the amount of time it takes to process 100,000 transaction: \", mdx(\"inlineCode\", {\n    parentName: \"p\"\n  }, \"0.1666666667 * 104.2 = 17.3666666701 kWh\"), \".\"), mdx(\"p\", null, \"That is \", mdx(\"strong\", {\n    parentName: \"p\"\n  }, \"11.6554809866%\"), \" of the energy consumed by the same amount of transactions on Visa.\"), mdx(\"p\", null, \"And remember, this is based on the minimum amount of transactions that Ethereum will be able to handle per second. If Ethereum reaches its potential of 100,000 transactions per second, 100,000 transactions would consume 0.1666666667kWh.\"), mdx(\"p\", null, \"To put it another way, if Visa handled 140,839,000,000 transactions at a cost of 149 kWh per 100,000 transactions that's 209,850,110 kWh energy consumed for the year.\"), mdx(\"p\", null, \"Ethereum in a single year stands to consume 5,256,000 kWh. With a potential of 788,940,000,000 - 3,153,600,000,000 transactions processed in that time.\"), mdx(InfoBanner, {\n    emoji: \":evergreen_tree:\",\n    mdxType: \"InfoBanner\"\n  }, \"If you think these stats are incorrect or can be made more accurate, please raise an issue or PR. These are estimates by the ethstake.exchange team made using publicly accessible information and the planned Ethereum design. This doesn't represent an official promise from the Ethereum Foundation.\"));\n}\n;\nMDXContent.isMDXComponent = true;", "tableOfContents": {"items": [{"url": "#whats-an-nft-what-are-nfts", "title": "What's an NFT? {#what-are-nfts}"}, {"url": "#the-internet-of-assets-internet-of-assets", "title": "The internet of assets {#internet-of-assets}", "items": [{"url": "#a-comparison-nft-comparison", "title": "A comparison {#nft-comparison}"}, {"url": "#nft-examples-nft-examples", "title": "NFT examples {#nft-examples}"}, {"url": "#ethstakeexchange-examples-ethereum-org-examples", "title": "ethstake.exchange examples {#ethereum-org-examples}", "items": [{"url": "#poaps-proof-of-attendance-protocol-poaps", "title": "POAPs (Proof of attendance protocol) {#poaps}"}, {"url": "#ethereumeth-ethereum-dot-eth", "title": "ethereum.eth {#ethereum-dot-eth}"}]}]}, {"url": "#how-do-nfts-work-how-nfts-work", "title": "How do NFTs work? {#how-nfts-work}", "items": [{"url": "#scarcity-scarcity", "title": "Scarcity {#scarcity}"}, {"url": "#royalties-royalties", "title": "Royalties {#royalties}"}]}, {"url": "#what-are-nfts-used-for-nft-use-cases", "title": "What are NFTs used for? {#nft-use-cases}", "items": [{"url": "#maximising-earnings-for-creators-nfts-for-creators", "title": "Maximising earnings for creators {#nfts-for-creators}", "items": [{"url": "#the-copypaste-problem-nfts-copy-paste", "title": "The copy/paste problem {#nfts-copy-paste}"}]}, {"url": "#boosting-gaming-potential-nft-gaming", "title": "Boosting gaming potential {#nft-gaming}"}, {"url": "#making-ethereum-addresses-more-memorable-nft-domains", "title": "Making Ethereum addresses more memorable {#nft-domains}"}, {"url": "#physical-items-nft-physical-items", "title": "Physical items {#nft-physical-items}"}, {"url": "#nfts-and-defi-nfts-and-defi", "title": "NFTs and DeFi {#nfts-and-defi}", "items": [{"url": "#nft-backed-loans-nft-backed-loans", "title": "NFT-backed loans {#nft-backed-loans}"}, {"url": "#fractional-ownership-fractional-ownership", "title": "Fractional ownership {#fractional-ownership}"}]}]}, {"url": "#ethereum-and-nfts-ethereum-and-nfts", "title": "Ethereum and NFTs {#ethereum-and-nfts}"}, {"url": "#the-environmental-impact-of-nfts-environmental-impact-nfts", "title": "The environmental impact of NFTs {#environmental-impact-nfts}", "items": [{"url": "#dont-blame-it-on-the-nfts-nft-qualities", "title": "Don't blame it on the NFTs {#nft-qualities}", "items": [{"url": "#the-work-in-minting-your-nft-minting-nfts", "title": "The work in minting your NFT {#minting-nfts}"}, {"url": "#securing-your-nft-with-mining-securing-nfts", "title": "Securing your NFT with mining {#securing-nfts}"}, {"url": "#blockchains-are-energy-intensive-right-now-blockchains-intensive", "title": "Blockchains are energy intensive, right now {#blockchains-intensive}"}]}, {"url": "#a-greener-future-a-greener-future", "title": "A greener future... {#a-greener-future}", "items": [{"url": "#a-greener-ethereum-greener-ethereum", "title": "A greener Ethereum {#greener-ethereum}"}, {"url": "#timelines-timelines", "title": "Timelines {#timelines}"}]}]}, {"url": "#build-with-nfts-build-with-nfts", "title": "Build with NFTs {#build-with-nfts}"}, {"url": "#further-reading-further-reading", "title": "Further reading {#further-reading}", "items": [{"url": "#footnotes-and-sources-footnotes-and-sources", "title": "Footnotes and sources {#footnotes-and-sources}", "items": [{"url": "#1-9995-energy-reduction-from-mining-fn-1", "title": "1. 99.95% energy reduction from mining {#fn-1}"}, {"url": "#2-visa-energy-consumption-fn-2", "title": "2. Visa energy consumption {#fn-2}"}, {"url": "#3-energy-usage-for-100000-transactions-on-a-sharded-proof-of-stake-network-fn-3", "title": "3. Energy usage for 100,000 transactions on a sharded proof-of-stake network {#fn-3}"}]}]}]}, "parent": {"mtime": "2022-05-16T19:17:04.635Z", "fields": {"gitLogLatestDate": "2022-05-17 03:16:37 +0800"}}}}, "pageContext": {"slug": "/en/nft/", "isOutdated": false, "relativePath": "src/content/nft/index.md", "intl": {"language": "en", "defaultLanguage": "en", "languages": ["en", "ar", "bg", "bn", "ca", "cs", "de", "el", "es", "fa", "fi", "fr", "hi", "hr", "hu", "id", "ig", "it", "ja", "ka", "ko", "lt", "ml", "mr", "ms", "nl", "nb", "pl", "pt", "pt-br", "ro", "ru", "se", "sk", "sl", "sr", "sw", "th", "tr", "uk", "vi", "zh", "zh-tw"], "messages": {"1inch-logo": "1inch logo", "aave-logo": "Aave logo", "acknowledgements": "Acknowledgements", "about": "About", "about-ethereum-org": "About ethstake.exchange", "about-us": "About us", "alt-eth-blocks": "Illustration of blocks being organized like an ETH symbol", "aria-toggle-search-button": "Toggle search button", "aria-toggle-menu-button": "Toggle menu button", "zen-mode": "Zen Mode", "back-to-top": "Back to top", "banner-page-incomplete": "This page is incomplete and we'd love your help. Edit this page and add anything that you think might be useful to others.", "beacon-chain": "Beacon Chain", "binance-logo": "Binance logo", "bittrex-logo": "Bittrex logo", "brand-assets": "Brand assets", "bridges": "Blockchain bridges", "bug-bounty": "Bug bounty", "coinbase-logo": "Coinbase logo", "coinmama-logo": "Coinmama logo", "community": "Community", "community-hub": "Community hub", "community-menu": "Community Menu", "compound-logo": "Compound logo", "cons": "Cons", "contact": "Contact", "content-versions": "Content Versions", "contributing": "Contributing", "contributors": "Contributors", "contributors-thanks": "Everyone who has contributed to this page – thank you!", "cookie-policy": "Cookie policy", "copied": "<PERSON>pied", "copy": "Copy", "dark-mode": "Dark", "data-provided-by": "Data source:", "decentralized-applications-dapps": "Decentralized applications (dapps)", "deposit-contract": "Deposit contract", "devcon": "Devcon", "developers": "Developers", "developers-home": "Developers' home", "docs": "Docs", "documentation": "Documentation", "dydx-logo": "Dydx logo", "ecosystem": "Ecosystem", "edit-page": "Edit page", "ef-blog": "Ethereum Foundation Blog", "eips": "Ethereum Improvement Proposals", "energy-consumption": "Ethereum energy consumption", "enterprise": "Enterprise", "enterprise-menu": "Enterprise Menu", "esp": "Ecosystem Support Program", "eth-current-price": "Current ETH price (USD)", "consensus-beaconcha-in-desc": "Open source Beacon Chain explorer", "consensus-beaconscan-desc": "Beacon Chain explorer - Etherscan for the consensus layer", "consensus-become-staker": "Become a staker", "consensus-become-staker-desc": "Staking is live! If you want to stake your ETH to help secure the network, make sure you’re aware of the risks.", "consensus-explore": "Explore the data", "consensus-run-beacon-chain": "Run a consensus client", "consensus-run-beacon-chain-desc": "Ethereum needs as many clients running as possible. Help with this Ethereum public good!", "consensus-when-shipping": "When's it shipping?", "eth-upgrade-what-happened": "What happened to 'Eth2?'", "eth-upgrade-what-happened-description": "The term 'Eth2' has been deprecated as we approach The Merge. The 'consensus layer' encapsulates what was formerly known as 'Eth2.'", "ethereum": "Ethereum", "ethereum-upgrades": "Ethereum upgrades", "ethereum-brand-assets": "Ethereum brand assets", "ethereum-community": "Ethereum Community", "ethereum-online": "Online communities", "ethereum-events": "Ethereum events", "ethereum-foundation": "Ethereum Foundation", "ethereum-foundation-logo": "Ethereum Foundation logo", "ethereum-glossary": "Ethereum glossary", "ethereum-governance": "Ethereum governance", "ethereum-logo": "Ethereum logo", "ethereum-security": "Ethereum security and scam prevention", "ethereum-support": "Ethereum support", "ethereum-studio": "Ethereum studio", "ethereum-wallets": "Ethereum wallets", "ethereum-whitepaper": "Ethereum Whitepaper", "events": "Events", "example-projects": "Example projects", "feedback-prompt": "Did this page help answer your question?", "feedback-title-helpful": "Thanks for your feedback!", "feedback-title-not-helpful": "Join our public <a href=\"https://discord.gg/rZz26QWfCg\" target=\"_blank\">Discord</a> and get the answer you're looking for.", "find-wallet": "Find wallet", "foundation": "Foundation", "gemini-logo": "Gemini logo", "get-eth": "Get ETH", "get-involved": "Get involved", "get-started": "Get started", "gitcoin-logo": "Gitcoin logo", "glossary": "Glossary", "governance": "Governance", "grants": "<PERSON>s", "grant-programs": "Ecosystem Grant Programs", "guides-and-resources": "Community guides and resources", "history": "History", "history-of-ethereum": "History of Ethereum", "home": "Home", "how-ethereum-works": "How Ethereum works", "image": "image", "in-this-section": "In this section", "individuals": "Individuals", "individuals-menu": "Individual's Menu", "jobs": "Jobs", "kraken-logo": "Kraken logo", "language-ar": "Arabic", "language-bg": "Bulgarian", "language-bn": "Bengali", "language-ca": "Catalan", "language-cs": "Czech", "language-de": "German", "language-el": "Greek", "language-en": "English", "language-es": "Spanish", "language-fa": "<PERSON><PERSON>", "language-fi": "Finnish", "language-fr": "French", "language-hi": "Hindi", "language-hr": "Croatian", "language-hu": "Hungarian", "language-id": "Indonesian", "language-ig": "Igbo", "language-it": "Italian", "language-ja": "Japanese", "language-ka": "Georgian", "language-ko": "Korean", "language-lt": "Lithuanian", "language-ml": "Malayalam", "language-mr": "Marathi", "language-ms": "Malay", "language-nb": "Norwegian", "language-nl": "Dutch", "language-pl": "Polish", "language-pt": "Portuguese", "language-pt-br": "Portuguese (Brazilian)", "language-resources": "Language resources", "language-ro": "Romanian", "language-ru": "Russian", "language-se": "Swedish", "language-sk": "Slovak", "language-sl": "Slovenian", "language-sr": "Serbian", "language-sw": "Swahili", "language-th": "Thai", "language-support": "Language support", "language-tr": "Turkish", "language-uk": "Ukrainian", "language-vi": "Vietnamese", "language-zh": "Chinese Simplified", "language-zh-tw": "Chinese Traditional", "languages": "Languages", "last-24-hrs": "Last 24 hours", "last-edit": "Last edit", "learn": "Learn", "learn-by-coding": "Learn by coding", "learn-menu": "Learn menu", "learn-more": "Learn more", "less": "Less", "light-mode": "Light", "listing-policy-disclaimer": "All products listed on this page are not official endorsements, and are provided for informational purposes only. If you want to add a product or provide feedback on the policy raise an issue in GitHub.", "listing-policy-raise-issue-link": "Raise issue", "live-help": "Live help", "live-help-menu": "Live help menu", "loading": "Loading...", "loading-error": "Loading error.", "loading-error-refresh": "Error, please refresh.", "logo": "logo", "loopring-logo": "Loopring logo", "mainnet-ethereum": "Mainnet Ethereum", "makerdao-logo": "MakerDao logo", "matcha-logo": "Matcha logo", "medalla-data-challenge": "Medalla data challenge", "merge": "<PERSON><PERSON>", "more": "More", "more-info": "More info", "nav-beginners": "Beginners", "next": "Next", "no": "No", "oasis-logo": "Oasis logo", "online": "Online", "on-this-page": "On this page", "page-content": "Page content", "page-enterprise": "Enterprise", "page-last-updated": "Page last updated", "previous": "Previous", "privacy-policy": "Privacy policy", "private-ethereum": "Private Ethereum", "pros": "Pros", "read-more": "Read more", "refresh": "Please refresh the page.", "return-home": "return home", "run-a-node": "Run a node", "review-progress": "Review progress", "rollup-component-website": "Website", "rollup-component-developer-docs": "Developer docs", "rollup-component-technology-and-risk-summary": "Technology and risk summary", "search": "Search", "search-box-blank-state-text": "Search away!", "search-eth-address": "This looks like an Ethereum address. We don't provide data specific to addresses. Try searching for it on a block explorer like", "search-no-results": "No results for your search", "security": "Security", "see-contributors": "See contributors", "set-up-local-env": "Set up local environment", "shard-chains": "Shard chains", "show-all": "Show all", "show-less": "Show less", "site-description": "Ethereum is a global, decentralized platform for money and new kinds of applications. On Ethereum, you can write code that controls money, and build applications accessible anywhere in the world.", "site-title": "ethstake.exchange", "skip-to-main-content": "Skip to main content", "smart-contracts": "Smart contracts", "stablecoins": "Stablecoins", "staking": "Staking", "staking-community-grants": "Staking community grants", "academic-grants-round": "Academic grants round", "summary": "Summary", "support": "Support", "terms-of-use": "Terms of use", "transaction-fees": "What are transaction fees?", "translation-banner-body-new": "You’re viewing this page in English because we haven’t translated it yet. Help us translate this content.", "translation-banner-body-update": "There’s a new version of this page but it’s only in English right now. Help us translate the latest version.", "translation-banner-button-join-translation-program": "Join Translation Program", "translation-banner-button-learn-more": "Learn more", "translation-banner-button-see-english": "See English", "translation-banner-button-translate-page": "Translate page", "translation-banner-title-new": "Help translate this page", "translation-banner-title-update": "Help update this page", "translation-program": "Translation Program", "translation-progress": "Translation progress", "translation-banner-no-bugs-title": "No bugs here!", "translation-banner-no-bugs-content": "This page is not being translated. We've intentionally left this page in English for now.", "translation-banner-no-bugs-dont-show-again": "Don't show again", "try-using-search": "Try using search to find what you're looking for or", "tutorials": "Tutorials", "uniswap-logo": "Uniswap logo", "upgrades": "Upgrades", "use": "Use", "use-ethereum": "Use Ethereum", "use-ethereum-menu": "Use Ethereum menu", "vision": "Vision", "wallets": "Wallets", "we-couldnt-find-that-page": "We couldn't find that page", "web3": "What is Web3?", "website-last-updated": "Website last updated", "what-is-ether": "What is ether (ETH)?", "what-is-ethereum": "What is Ethereum?", "whitepaper": "Whitepaper", "defi-page": "Decentralized finance (DeFi)", "dao-page": "Decentralized autonomous organisations (DAOs)", "nft-page": "Non-fungible tokens (NFTs)", "yes": "Yes", "page-about-h2": "Request a feature", "page-about-h3": "Work in progress", "page-about-h3-1": "Implemented features", "page-about-h3-2": "Planned features", "page-about-li-1": "in progress", "page-about-li-2": "planned", "page-about-li-3": "implemented", "page-about-li-4": "implemented", "page-about-link-1": "The source code of this repository is licensed under the MIT License", "page-about-link-2": "GitHub", "page-about-link-3": "View the full list of tasks in progress on GitHub", "page-about-link-4": "Join our Discord server", "page-about-link-5": "Reach out to us on Twitter", "page-about-link-6": "View the full list of implemented tasks on GitHub", "page-about-link-7": "Create an issue on GitHub", "page-about-p-1": "Ever since the launch of ethstake.exchange, we strive to be transparent about how we operate. This is one of our core values because we believe transparency is crucial to Ethereum's success.", "page-about-p-2": "We use", "page-about-p-3": "as our primary project management tool. We organize our tasks in 3 categories:", "page-about-p-4": "We do our best to keep the community informed what the status is of a specific task.", "page-about-p-5": "Tasks that we're implementing.", "page-about-p-6": "Tasks we've queued up to implement next.", "page-about-p-7": "Recently completed tasks.", "page-about-p-8": "Do you have an idea for how to improve ethstake.exchange? We'd love to collaborate with you!", "page-assets-bazaar": "Ethereum bazaar", "page-assets-beacon-chain": "Beacon Chain", "page-assets-blocks": "Building blocks", "page-assets-dao": "DAO", "page-assets-defi": "<PERSON><PERSON><PERSON>", "page-assets-merge": "The Merge", "page-assets-doge": "<PERSON><PERSON> using dapps", "page-assets-download-artist": "Artist:", "page-assets-download-download": "Download", "page-assets-enterprise": "Enterprise Ethereum", "page-assets-eth": "<PERSON>ther (ETH)", "page-assets-eth-diamond-color": "ETH diamond (color)", "page-assets-eth-diamond-glyph": "ETH diamond (glyph)", "page-assets-eth-diamond-gray": "ETH diamond (gray)", "page-assets-eth-diamond-purple": "ETH diamond (purple)", "page-assets-eth-diamond-white": "ETH diamond (white)", "page-assets-eth-diamond-colored": "ETH diamond (color filled)", "page-assets-eth-diamond-colored-svg": "ETH diamond (color filled, SVG)", "page-assets-eth-glyph-video-dark": "ETH glyph video (dark)", "page-assets-eth-glyph-video-light": "ETH glyph video (light)", "page-assets-eth-logo-landscape-gray": "ETH logo landscape (gray)", "page-assets-eth-logo-landscape-purple": "ETH logo landscape (purple)", "page-assets-eth-logo-landscape-white": "ETH logo landscape (white)", "page-assets-eth-logo-portrait-gray": "ETH logo portrait (gray)", "page-assets-eth-logo-portrait-purple": "ETH logo portrait (purple)", "page-assets-eth-logo-portrait-white": "ETH logo portrait (white)", "page-assets-eth-wordmark-gray": "ETH wordmark (gray)", "page-assets-eth-wordmark-purple": "ETH wordmark (purple)", "page-assets-eth-wordmark-white": "ETH wordmark (white)", "page-assets-ethereum-brand-assets": "Ethereum \"brand\" assets", "page-assets-finance": "Finance", "page-assets-future": "Future", "page-assets-h1": "ethstake.exchange assets", "page-assets-hero": "ethstake.exchange hero", "page-assets-hero-particles": "ETH particles image", "page-assets-historical-artwork": "Historical artwork", "page-assets-illustrations": "Illustrations", "page-assets-impact": "Impact", "page-assets-infrastructure": "Infrastructure", "page-assets-meta-desc": "Explore and download Ethereum and ethstake.exchange brand assets, illustrations and media.", "page-assets-meta-title": "Ethereum brand assets", "page-assets-mainnet": "Mainnet", "page-assets-page-assets-solid-background": "Solid background", "page-assets-page-assets-transparent-background": "Transparent background", "page-assets-robot": "Robot wallet", "page-assets-sharding": "Sharding", "page-assets-hackathon": "<PERSON><PERSON><PERSON><PERSON>", "page-community-card-1-title": "Join an online community", "page-community-card-1-description": "Find your tribe and participate in community with other Ethereum enthusiasts.", "page-community-card-2-title": "Ethereum events", "page-community-card-2-description": "Find and participate in an Ethereum conference, hackathon, or meetup.", "page-community-card-3-title": "Contribute to a project", "page-community-card-3-description": "Check out how to get involved for a list of ways that you can contribute based on your skills and professional background.", "page-community-card-4-title": "Search for grants", "page-community-card-4-description": "Funding grants are available to help you get a project off the ground.", "page-community-contribute": "Contribute to ethstake.exchange", "page-community-contribute-button": "More on contributing", "page-community-contribute-description": "For many people, ethstake.exchange is their first step into the ecosystem. It is kept up-to-date and accurate by thousands of open-source contributors. Want to help? Read our guide on contributing, or take up an issue on our GitHub.", "page-community-contribute-secondary-button": "View on GitHub", "page-community-daos-callout-title": "Decentralized Autonomous Organizations (DAOs)", "page-community-daos-callout-description": "These groups leverage Ethereum technology to facilitate organization and collaboration. For instance, for controlling membership, voting on proposals, or managing pooled assets.", "page-community-explore-dapps": "Explore dapps", "page-community-explore-dapps-alt": "Explore dapps", "page-community-explore-dapps-description": "Dapps are applications built on Ethereum. Dapps are disrupting current business models and inventing new ones.", "page-community-explore-dapps-title": "Try some dapps", "page-community-explore-grants": "Explore grants", "page-community-find-a-job": "Find a job", "page-community-get-eth": "Get ETH", "page-community-get-eth-alt": "Get some ETH", "page-community-get-eth-description": "ETH is the native currency of Ethereum. You'll need some ETH in your wallet to use Ethereum applications.", "page-community-get-eth-title": "Get some ETH", "page-community-get-involved-title": "How can I get involved?", "page-community-get-involved-description": "There are many ways to get involved in the fast-growing Ethereum community; you can join one of the popular online communities, attend an event, join a meetup group, contribute to a project, or participate in one of the many online forums about Ethereum.", "page-community-get-involved-image-alt": "Get Involved", "page-community-hero-alt": "Ethereum community hub", "page-community-hero-header": "Welcome to the Ethereum community hub", "page-community-hero-subtitle": "The Ethereum community is home to hundreds of thousands of developers, technologists, designers, users, HODLers and enthusiasts.", "page-community-hero-title": "Join the community", "page-community-meetuplist-no-meetups": "We don't have any meetups matching this search. Know of one?", "page-community-meta-title": "Community Hub", "page-community-meta-description": "Community homepage description", "page-community-open-source": "Creator? Builder? Get paid for your work.", "page-community-open-source-description": "Are you building on Ethereum, or do you want to? Companies are hiring for thousands of technical and non-technical roles. Got an idea of your own? Try finding a grant to get your project off the ground.", "page-community-open-source-image-alt": "Get paid for your work", "page-community-please-add-to-page": "Please add it to this page!", "page-community-support": "Ethereum support", "page-community-support-alt": "Support", "page-community-support-button": "Get support", "page-community-support-description": "Need support? There is no official Ethereum support, but hundreds of helpful communities are available to help you prosper on Ethereum.", "page-community-try-ethereum": "Try Ethereum for yourself", "page-community-upcoming-events-no-events": "We're not aware of any upcoming events. Know of one?", "page-community-upcoming-events-load-more": "Load more", "page-community-why-get-involved-title": "Why get involved?", "page-community-why-get-involved-card-1-title": "Find your tribe", "page-community-why-get-involved-card-1-description": "There is a tribe for everyone. Find and connect with like minded individuals to discuss, ponder, and celebrate Ethereum together.", "page-community-why-get-involved-card-2-title": "Earn a living", "page-community-why-get-involved-card-2-description": "Everyone has bills to pay. Ethereum allows you to find meaningful work, and get paid well to do it.", "page-community-why-get-involved-card-3-title": "Make a difference", "page-community-why-get-involved-card-3-description": "Getting involved with Ethereum allows you to be an active stakeholder in a technology that is having a positive impact on millions of people.", "page-contributing-translation-program-acknowledgements-acknowledgement-page-title": "Contributor acknowledgements", "page-contributing-translation-program-acknowledgements-acknowledgement-page-1": "The Translation Program is a collaborative effort and thousands of contributors have gotten involved by volunteering their time to help us make the website accessible in as many languages as possible.", "page-contributing-translation-program-acknowledgements-acknowledgement-page-2": "This page is dedicated to acknowledging our translators and their efforts, highlighting our most outstanding contributors, and supporting them on their professional journeys.", "page-contributing-translation-program-acknowledgements-acknowledgement-page-3": "All translators active in our project in Crowdin are featured on our contributors page.", "page-contributing-translation-program-acknowledgements-acknowledgement-page-link": "View all ethstake.exchange translators", "page-contributing-translation-program-acknowledgements-acknowledgement-page-4": "The most active translators in a given period will also earn their place on the Translation Leaderboard.", "page-contributing-translation-program-acknowledgements-acknowledgement-page-5": "Professional or upcoming translators, as well as translations students and linguists looking to add a new field of expertise, can request a Translator certificate, to certify their contributions to the website.", "page-contributing-translation-program-acknowledgements-cert-title": "Certificate", "page-contributing-translation-program-acknowledgements-cert-1": "We want to acknowledge our translators and support them on their career paths. With this in mind, we have designed the ethstake.exchange translator certificate.", "page-contributing-translation-program-acknowledgements-cert-2": "The certificate is intended for professional and upcoming translators who want to use it as a reference, prove their expertise in translating technical content or simply show their dedication to Ethereum.", "page-contributing-translation-program-acknowledgements-cert-3": "If you have contributed to the Translation Program and at least 5,000 of your translated words have been approved, you can request your translator certificate by writing to <NAME_EMAIL>. Your message should include the link to your Crowdin account and your full name (or alias, if you prefer), which we will add to the certificate.", "page-contributing-translation-program-acknowledgements-hero-image-alt": "Translation Program hero shiba image", "page-contributing-translation-program-acknowledgements-meta-description": "Acknowledgement of all the great work our translators do", "page-contributing-translation-program-acknowledgements-meta-title": "Translators Acknowledgement", "page-contributing-translation-program-acknowledgements-our-translators-cta": "Check out our full list of translators who dedicate their time and skills to help make Ethereum content available to everyone.", "page-contributing-translation-program-acknowledgements-our-translators-title": "Our translators", "page-contributing-translation-program-acknowledgements-our-translators-view-all": "View all translators", "page-contributing-translation-program-acknowledgements-our-translators-1": "Community is at the heart of the ethstake.exchange Translation Program. Explore our full community of translators below.", "page-contributing-translation-program-acknowledgements-translation-leaderboard-title": "Translation Leaderboard", "page-contributing-translation-program-acknowledgements-translation-leaderboard-cta": "Help us translate ethstake.exchange and earn a place on the Translator Leaderboard!", "page-contributing-translation-program-acknowledgements-translation-leaderboard-1": "We want to feature outstanding translators based on recent activity as well as highlight our most impactful all-time contributors. Our leaderboard tracks data on the most active translators using a monthly, quarterly and all-time view, and are updated at the start of each month. Translators are placed on the leaderboards based on their number of ''winning'' words (number of translated words that are approved during the review).", "page-contributing-translation-program-acknowledgements-translation-leaderboard-all-time-view": "All-time View", "page-contributing-translation-program-acknowledgements-translation-leaderboard-month-view": "Month View", "page-contributing-translation-program-acknowledgements-translation-leaderboard-quarter-view": "Quarter View", "page-contributing-translation-program-acknowledgements-translation-leaderboard-show-less": "Show Less", "page-contributing-translation-program-acknowledgements-translation-leaderboard-show-more": "Show More", "page-contributing-translation-program-acknowledgements-translator": "Translator", "page-contributing-translation-program-acknowledgements-language": "Language", "page-contributing-translation-program-acknowledgements-total-words": "Total Words", "page-contributing-translation-program-acknowledgements-poaps-title": "POAPs", "page-contributing-translation-program-acknowledgements-1": "All of our translators are eligible for a POAP (Proof of Attendance Protocol) – a non-fungible token that proves their participation in the ethstake.exchange Translation Program.", "page-contributing-translation-program-acknowledgements-2": "We have a number of different POAPs available for translators, based on their activity", "page-contributing-translation-program-acknowledgements-3": "If you have contributed to the translation effort in Crowdin, you have a POAP waiting for you!", "page-contributing-translation-program-acknowledgements-how-to-claim-title": "How to claim", "page-contributing-translation-program-acknowledgements-how-to-claim-1": "Join our", "page-contributing-translation-program-acknowledgements-how-to-claim-1-discord": "Discord server", "page-contributing-translation-program-acknowledgements-how-to-claim-2": "Paste a link to your Crowdin account in the #poaps-🏆 channel.", "page-contributing-translation-program-acknowledgements-how-to-claim-3": "Wait for a member of our team to send you a link to your POAP.", "page-contributing-translation-program-acknowledgements-how-to-claim-4": "Claim your POAP!", "page-contributing-translation-program-acknowledgements-4": "You should only use self-custody wallets to claim POAPs. Do not use exchange accounts or other accounts you do not hold the private keys to, as these will not allow you to access and manage your POAPs.", "page-contributing-translation-program-contributors-thank-you": "We'd like to thank all of our contributors!", "page-contributing-translation-program-contributors-title": "Our translators", "page-contributing-translation-program-contributors-our-translators-1": "Community is at the heart of the ethstake.exchange Translation Program.", "page-contributing-translation-program-contributors-our-translators-2": "With thousands of community members contributing translations to our project, it is difficult to acknowledge everyone.", "page-contributing-translation-program-contributors-our-translators-3": "All translators are listed alphabetically based on their chosen name in Crowdin. If you are a translator and would like to use your real name, nickname, ENS domain, etc., you can change your Full Name in Crowdin.", "page-contributing-translation-program-contributors-meta-title": "Our Translations", "page-contributing-translation-program-contributors-meta-description": "A list of our translation contributors.", "page-contributing-translation-program-contributors-number-of-contributors": "Number of contributors:", "page-dapps-1inch-logo-alt": "1inch logo", "page-dapps-aave-logo-alt": "Aave logo", "page-dapps-add-button": "Suggest dapp", "page-dapps-add-title": "Add dapp", "page-dapps-audius-logo-alt": "Audius logo", "page-dapps-augur-logo-alt": "Augur logo", "page-dapps-axie-infinity-logo-alt": "Axie Infinity logo", "page-dapps-brave-logo-alt": "Brave logo", "page-dapps-category-arts": "Art and fashion", "page-dapps-category-browsers": "Browsers", "page-dapps-category-collectibles": "Digital collectibles", "page-dapps-category-competitive": "Competition", "page-dapps-category-computing": "Developer tools", "page-dapps-category-dex": "Token swaps", "page-dapps-category-investments": "Investments", "page-dapps-category-lending": "Lending and borrowing", "page-dapps-category-lottery": "Crowdfunding", "page-dapps-category-marketplaces": "Marketplaces", "page-dapps-category-music": "Music", "page-dapps-category-payments": "Payments", "page-dapps-category-insurance": "Insurance", "page-dapps-category-portfolios": "Portfolios", "page-dapps-category-trading": "Trading and prediction markets", "page-dapps-category-utilities": "Utilities", "page-dapps-category-worlds": "Virtual worlds", "page-dapps-choose-category": "Choose category", "page-dapps-collectibles-benefits-1-description": "When art is tokenised on Ethereum, ownership can be proved for all to see. You can trace the artwork's journey from creation to its current holder. This prevents forgeries.", "page-dapps-collectibles-benefits-1-title": "Ownership is provable", "page-dapps-collectibles-benefits-2-description": "Paying to stream music or buy artwork is far fairer to the artists. With Ethereum there's less need for intermediaries. And if intermediaries are needed, their costs are not as high because platforms don't need to pay for the infrastructure of the network.", "page-dapps-collectibles-benefits-2-title": "Fairer for creators", "page-dapps-collectibles-benefits-3-description": "Tokenised collectibles are tied to your Ethereum address, not the platform. So you can sell things like in-game items on any Ethereum marketplace, not just in the game itself.", "page-dapps-collectibles-benefits-3-title": "Collectibles go with you", "page-dapps-collectibles-benefits-4-description": "The tools and products already exist for you to tokenise your art and sell it! And your tokens can be sold on any and all Ethereum collectibles platform.", "page-dapps-collectibles-benefits-4-title": "Infrastructure already in place", "page-dapps-collectibles-benefits-description": "These are applications that focus on digital ownership, increasing earning potential for creators, and inventing new ways to invest in your favourite creators and their work.", "page-dapps-collectibles-benefits-title": "decentralized collectibles and streaming", "page-dapps-collectibles-button": "Arts and collectibles", "page-dapps-collectibles-description": "These are applications that focus on digital ownership, increasing earning potential for creators, and inventing new ways to invest in your favourite creators and their work.", "page-dapps-collectibles-title": "Decentralized arts and collectibles", "page-dapps-compound-logo-alt": "Compound logo", "page-dapps-cryptopunks-logo-alt": "CryptoPunks logo", "page-dapps-cryptovoxels-logo-alt": "Cryptovoxels logo", "page-dapps-dapp-description-1inch": "Helps you avoid high price slippage by aggregating best prices.", "page-dapps-dapp-description-aave": "Lend your tokens to earn interest and withdraw any time.", "page-dapps-dapp-description-async-art": "Create, collect, and trade #ProgrammableArt - digital paintings split into “Layers” which you can use to affect the overall image. Each Master and Layer is an ERC721 token.", "page-dapps-dapp-description-audius": "Decentralized streaming platform. Listens = money for creators, not labels.", "page-dapps-dapp-description-augur": "Bet on outcomes of sports, economics, and more world events.", "page-dapps-dapp-description-axie-infinity": "Trade and battle creatures called Axies. And earn as you play – available on mobile", "page-dapps-dapp-description-brave": "Earn tokens for browsing and support your favorite creators with them.", "page-dapps-dapp-description-cent": "A social network where you earn money by posting NFTs.", "page-dapps-dapp-description-compound": "Lend your tokens to earn interest and withdraw any time.", "page-dapps-dapp-description-cryptopunks": "Buy, bid on, and offer punks for sale – one of the first token collectibles on Ethereum.", "page-dapps-dapp-description-cryptovoxels": "Create art galleries, build stores, and buy land – an Ethereum virtual world.", "page-dapps-dapp-description-dark-forest": "Conquer planets in an infinite, procedurally-generated, cryptographically-specified universe.", "page-dapps-dapp-description-decentraland": "Collect, trade virtual land in a virtual world you can explore.", "page-dapps-dapp-description-dydx": "Open short or leveraged positions with leverage up to 10x. Lending and borrowing available too.", "page-dapps-dapp-description-ens": "User-friendly names for Ethereum addresses and decentralized sites.", "page-dapps-dapp-description-foundation": "Invest in unique editions of digital artwork and trade pieces with other buyers.", "page-dapps-dapp-description-gitcoin": "<PERSON><PERSON><PERSON> crypt<PERSON> working on open-source software.", "page-dapps-dapp-description-gitcoin-grants": "Crowdfunding for Ethereum community projects with amplified contributions", "page-dapps-dapp-description-gods-unchained": "Strategic trading card game. Earn cards by playing that you can sell in real life.", "page-dapps-dapp-description-golem": "Access shared computing power or rent your own resources.", "page-dapps-dapp-description-radicle": "Secure peer-to-peer code collaboration without intermediaries.", "page-dapps-dapp-description-loopring": "Peer-to-peer trading platform built for speed.", "page-dapps-dapp-description-marble-cards": "Create and trade unique digital cards based on URLs.", "page-dapps-dapp-description-matcha": "Searches multiple exchanges to help find you the best prices.", "page-dapps-dapp-description-nifty-gateway": "Buy works on-chain from top artists, athletes, brands, and creators.", "page-dapps-dapp-description-oasis": "Trade, borrow, and save with Dai, an Ethereum stablecoin.", "page-dapps-dapp-description-opensea": "Buy, sell, discover, and trade limited-edition goods.", "page-dapps-dapp-description-opera": "Send crypto from your browser to merchants, other users and apps.", "page-dapps-dapp-description-poap": "Collect NFTs proving you were at different virtual or in-person events. Use them to join raffles, vote, collaborate, or just to brag.", "page-dapps-dapp-description-polymarket": "Bet on outcomes. Trade on information markets.", "page-dapps-dapp-description-pooltogether": "A lottery you can't lose. Prizes every week.", "page-dapps-dapp-description-index-coop": "A crypto index fund that gives your portfolio exposure to top DeFi tokens.", "page-dapps-dapp-description-nexus-mutual": "Coverage without the insurance company. Get protected against smart contract bugs and hacks.", "page-dapps-dapp-description-etherisc": "A decentralized insurance template anyone can use to create their own insurance coverage.", "page-dapps-dapp-description-zapper": "Track your portfolio and use a range of DeFi products from one interface.", "page-dapps-dapp-description-zerion": "Manage your portfolio and simply evaluate every single DeFi asset on the market.", "page-dapps-dapp-description-rotki": "Open source portfolio tracking, analytics, accounting and tax reporting tool that respects your privacy.", "page-dapps-dapp-description-rarible": "Create, sell and buy tokenised collectibles.", "page-dapps-dapp-description-sablier": "Stream money in real-time.", "page-dapps-dapp-description-superrare": "Buy digital artworks direct from artists or in secondary markets.", "page-dapps-dapp-description-token-sets": "Crypto investment strategies that automatically rebalance.", "page-dapps-dapp-description-tornado-cash": "Send anonymous transactions on Ethereum.", "page-dapps-dapp-description-uniswap": "Swap tokens simply or provide tokens for % rewards.", "page-dapps-docklink-dapps": "Intro to dapps", "page-dapps-docklink-smart-contracts": "Smart contracts", "page-dapps-dark-forest-logo-alt": "Dark Forest logo", "page-dapps-decentraland-logo-alt": "Decentraland logo", "page-dapps-index-coop-logo-alt": "Index Coop logo", "page-dapps-nexus-mutual-logo-alt": "Nexus Mutual logo", "page-dapps-etherisc-logo-alt": "Etherisc logo", "page-dapps-zapper-logo-alt": "Zapper logo", "page-dapps-zerion-logo-alt": "Zerion logo", "page-dapps-rotki-logo-alt": "Rotki logo", "page-dapps-desc": "Find an Ethereum application to try.", "page-dapps-doge-img-alt": "Illustration of a doge using a computer", "page-dapps-dydx-logo-alt": "dYdX logo", "page-dapps-editors-choice-dark-forest": "Play against others to conquer planets and try out bleeding-edge Ethereum scaling/privacy technology. Maybe one for those already familiar with Ethereum.", "page-dapps-editors-choice-description": "A few dapps the ethstake.exchange team are loving right now. Explore more dapps below.", "page-dapps-editors-choice-foundation": "Invest in culture. Buy, trade, and sell unique digital artwork and fashion from some incredible artists, musicians, and brands.", "page-dapps-editors-choice-header": "Editors' choices", "page-dapps-editors-choice-pooltogether": "Buy a ticket for the no-loss lottery. Each week, the interest generated from the entire ticket pool is sent to one lucky winner. Get your money back whenever you like.", "page-dapps-editors-choice-uniswap": "Swap your tokens with ease. A community favourite that allows you to trade tokens with folks across the network.", "page-dapps-ens-logo-alt": "Ethereum Name Service logo", "page-dapps-explore-dapps-description": "A lot of dapps are still experimental, testing the possibilties of decentralized networks. But there have been some successful early movers in the technology, financial, gaming and collectibles categories.", "page-dapps-explore-dapps-title": "Explore dapps", "page-dapps-features-1-description": "Once deployed to Ethereum, dapp code can’t be taken down. And anyone can use the dapp’s features. Even if the team behind the dapp disbanded you could still use it. Once on Ethereum, it stays there.", "page-dapps-features-1-title": "No owners", "page-dapps-features-2-description": "You can't be blocked from using a dapp or submitting transactions. For example, if Twitter was on Ethereum, no one could block your account or stop you from tweeting.", "page-dapps-features-2-title": "Free from censorship", "page-dapps-features-3-description": "Because Ethereum has ETH, payments are native to Ethereum. Developers don't need to spend time integrating with third-party payment providers.", "page-dapps-features-3-title": "Built-in payments", "page-dapps-features-4-description": "Dapp code is often in the open and compatible by default. Teams regularly build using other teams' work. If you want to let users swap tokens in your dapp, you can just plug in another dapp's code.", "page-dapps-features-4-title": "Plug and play", "page-dapps-features-5-description": "With most dapps, you don't need to share your real-world identity. Your Ethereum account is your login and you just need a wallet.", "page-dapps-features-5-title": "One anonymous login", "page-dapps-features-6-description": "Cryptography ensures that attackers can't forge transactions and other dapp interactions on your behalf. You authorize dapp actions with your Ethereum account - usually via your wallet - so that your credentials stay safe.", "page-dapps-features-6-title": "Backed by cryptography", "page-dapps-features-7-description": "Once the dapp is live on Ethereum, it will only go down if Ethereum itself goes down. Networks of Ethereum's size are notoriously difficult to attack.", "page-dapps-features-7-title": "No down time", "page-dapps-finance-benefits-1-description": "Financial services running on Ethereum have no sign up requirements. If you have funds and an internet connection, you’re good to go.", "page-dapps-finance-benefits-1-title": "Open access", "page-dapps-finance-benefits-2-description": "There’s a whole world of tokens that you can interact with across these financial products. People are building new tokens on top of Ethereum all the time.", "page-dapps-finance-benefits-2-title": "A new token economy", "page-dapps-finance-benefits-3-description": "Teams have built stablecoins – a less volatile cryptocurrency. These allow you to experiment and use crypto without the risk and uncertainty.", "page-dapps-finance-benefits-3-title": "Stablecoins", "page-dapps-finance-benefits-4-description": "Financial products in the Ethereum space are all modular and compatible with one another. New configurations of these modules are hitting the market all the time, increasing what you can do with your crypto.", "page-dapps-finance-benefits-4-title": "Interconnected financial services", "page-dapps-finance-benefits-description": "What is it about Ethereum that allows decentalized finance applications to thrive?", "page-dapps-finance-benefits-title": "decentralized finance", "page-dapps-finance-button": "Finance", "page-dapps-finance-description": "These are applications that focus on building out financial services using cryptocurrencies. They offer the likes of lending, borrowing, earning interest, and private payments – no personal data required.", "page-dapps-finance-title": "Decentralized finance", "page-dapps-foundation-logo-alt": "Foundation logo", "page-dapps-gaming-benefits-1-description": "Whether it's virtual land or trading cards, your items are tradeable on collectibles markets. Your in-game items have real-world value.", "page-dapps-gaming-benefits-1-title": "Game items double as tokens", "page-dapps-gaming-benefits-2-description": "You own your items, and in some cases your progress, not game companies. So you won't lose anything if the company behind the game is attacked, suffers a server malfunction, or disbands.", "page-dapps-gaming-benefits-2-title": "Your saves are safe", "page-dapps-gaming-benefits-3-description": "In the same way Ethereum payments are available to anyone to verify, games can use this quality to ensure fairness. In theory, everything is verifiable from the number of critical hits to the size of an opponent's war chest.", "page-dapps-gaming-benefits-3-title": "Provable fairness", "page-dapps-gaming-benefits-description": "What is it about Ethereum that allows decentralized gaming to thrive?", "page-dapps-gaming-benefits-title": "decentralized gaming", "page-dapps-gaming-button": "Gaming", "page-dapps-gaming-description": "These are applications that focus on the creation of virtual worlds and battling other players using collectibles that hold real-world value.", "page-dapps-gaming-title": "Decentralized gaming", "page-dapps-get-some-eth-description": "Dapp actions cost a transaction fee", "page-dapps-get-started-subtitle": "To try a dapp, you'll need a wallet and some ETH. A wallet will allow you to connect, or log in. And you'll need ETH to pay any transaction fees.", "page-dapps-get-started-title": "Get started", "page-dapps-gitcoin-grants-logo-alt": "Gitcoin Grants logo", "page-dapps-gitcoin-logo-alt": "Gitcoin logo", "page-dapps-gods-unchained-logo-alt": "Gods Unchained logo", "page-dapps-golem-logo-alt": "Golem logo", "page-dapps-radicle-logo-alt": "Radicle logo", "page-dapps-hero-header": "Ethereum-powered tools and services", "page-dapps-hero-subtitle": "Dapps are a growing movement of applications that use Ethereum to disrupt business models or invent new ones.", "page-dapps-how-dapps-work-p1": "Dapps have their backend code (smart contracts) running on a decentralized network and not a centralized server. They use the Ethereum blockchain for data storage and smart contracts for their app logic.", "page-dapps-how-dapps-work-p2": "A smart contract is like a set of rules that live on-chain for all to see and run exactly according to those rules. Imagine a vending machine: if you supply it with enough funds and the right selection, you'll get the item you want. And like vending machines, smart contracts can hold funds much like your Ethereum account. This allows code to mediate agreements and transactions.", "page-dapps-how-dapps-work-p3": "Once dapps are deployed on the Ethereum network you can't change them. Dapps can be decentralized because they are controlled by the logic written into the contract, not an individual or a company.", "page-dapps-how-dapps-work-title": "How dapps work", "page-dapps-learn-callout-button": "Start building", "page-dapps-learn-callout-description": "Our community developer portal has docs, tools, and frameworks to help you start building a dapp.", "page-dapps-learn-callout-image-alt": "Illustration of a hand building an ETH symbol out of lego bricks.", "page-dapps-learn-callout-title": "Learn to build a dapp", "page-dapps-loopring-logo-alt": "Loopring logo", "page-dapps-magic-behind-dapps-description": "Dapps might feel like regular apps. But behind the scenes they have some special qualities because they inherit all of Ethereum’s superpowers. Here's what makes dapps different from apps.", "page-dapps-magic-behind-dapps-link": "What makes Ethereum great?", "page-dapps-magic-behind-dapps-title": "The magic behind dapps", "page-dapps-magic-title-1": "The magic", "page-dapps-magic-title-2": "behind", "page-dapps-magician-img-alt": "Illustration of magicians", "page-dapps-marble-cards-logo-alt": "marble.cards logo", "page-dapps-matcha-logo-alt": "Matcha logo", "page-dapps-mobile-options-header": "Browse another category", "page-dapps-nifty-gateway-logo-alt": "Nifty Gateway logo", "page-dapps-oasis-logo-alt": "Oasis logo", "page-dapps-opensea-logo-alt": "OpenSea logo", "page-dapps-opera-logo-alt": "Opera logo", "page-dapps-polymarket-logo-alt": "Polymarket logo", "page-dapps-poap-logo-alt": "Proof of Attendance Protocol logo", "page-dapps-pooltogether-logo-alt": "PoolTogether logo", "page-dapps-rarible-logo-alt": "Rarible logo", "page-dapps-ready-button": "Go", "page-dapps-ready-description": "Choose a dapp to try out", "page-dapps-ready-title": "Ready?", "page-dapps-sablier-logo-alt": "Sablier logo", "page-dapps-set-up-a-wallet-button": "Find wallet", "page-dapps-set-up-a-wallet-description": "A wallet is your “login” for a dapp", "page-dapps-set-up-a-wallet-title": "Set up a wallet", "page-dapps-superrare-logo-alt": "SuperRare logo", "page-dapps-technology-button": "Technology", "page-dapps-technology-description": "These are applications that focus on decentralizing developer tools, incorporating cryptoeconomic systems into existing technology, and creating marketplaces for open-source development work.", "page-dapps-technology-title": "Decentralized technology", "page-dapps-token-sets-logo-alt": "Token Sets logo", "page-dapps-tornado-cash-logo-alt": "Tornado cash logo", "page-dapps-uniswap-logo-alt": "Uniswap logo", "page-dapps-wallet-callout-button": "Find wallet", "page-dapps-wallet-callout-description": "Wallets are dapps too. Find one based on the features that suit you.", "page-dapps-wallet-callout-image-alt": "Illustration of a robot.", "page-dapps-wallet-callout-title": "View wallets", "page-dapps-warning-header": "Always do your own research", "page-dapps-warning-message": "Ethereum is a new technology and most applications are new. Before depositing any large quantities of money, make sure you understand the risks.", "page-dapps-what-are-dapps": "What are dapps?", "page-dapps-more-on-defi-button": "More on decentralized finance", "page-dapps-more-on-nft-button": "More on tokenised collectibles", "page-dapps-more-on-nft-gaming-button": "More on tokenised in-game items", "docs-nav-accounts": "Accounts", "docs-nav-accounts-description": "Entities in the network that can hold a balance and send transactions", "docs-nav-advanced": "Advanced", "docs-nav-backend-apis": "Backend APIs", "docs-nav-block-explorers": "Block explorers", "docs-nav-blocks": "Blocks", "docs-nav-blocks-description": "The way transactions are batched to ensure state is synchronised across all actors", "docs-nav-compiling-smart-contracts": "Compiling smart contracts", "docs-nav-composability": "Composability", "docs-nav-consensus-mechanisms": "Consensus mechanisms", "docs-nav-consensus-mechanisms-description": "How the individual nodes of a distributed network agree on the current state of the system", "docs-nav-data-and-analytics": "Data and analytics", "docs-nav-data-and-analytics-description": "How blockchain data is aggregated, organized and implemented into dapps", "docs-nav-dart": "Dart", "docs-nav-delphi": "Delphi", "docs-nav-deploying-smart-contracts": "Deploying smart contracts", "docs-nav-development-frameworks": "Development frameworks", "docs-nav-development-frameworks-description": "Tools that make developing with Ethereum easier", "docs-nav-development-networks": "Development networks", "docs-nav-development-networks-description": "Local blockchain environments used to test dapps before deployment", "docs-nav-dot-net": ".NET", "docs-nav-erc-20": "ERC-20: Fungible Tokens", "docs-nav-erc-721": "ERC-721: NFTs", "docs-nav-erc-777": "ERC-777", "docs-nav-erc-1155": "ERC-1155", "docs-nav-ethereum-client-apis": "Ethereum client APIs", "docs-nav-ethereum-client-apis-description": "Convenience libraries that allow your web app to interact with Ethereum and smart contracts", "docs-nav-ethereum-stack": "Ethereum stack", "docs-nav-evm": "Ethereum virtual machine (EVM)", "docs-nav-evm-description": "The EVM handles all the computation on the Ethereum network", "docs-nav-foundational-topics": "Foundational topics", "docs-nav-gas": "Gas", "docs-nav-gas-description": "Computational power required to process transactions, paid for in ETH by transaction senders", "docs-nav-golang": "Golang", "docs-nav-integrated-development-environments-ides": "Integrated Development Environments (IDEs)", "docs-nav-integrated-development-environments-ides-description": "The best environments to write dapp code", "docs-nav-intro-to-dapps": "Intro to dapps", "docs-nav-intro-to-dapps-description": "An introduction to decentralized applications", "docs-nav-intro-to-ether": "Intro to <PERSON><PERSON>", "docs-nav-intro-to-ether-description": "A quick overview of <PERSON><PERSON>", "docs-nav-intro-to-ethereum": "Intro to Ethereum", "docs-nav-intro-to-ethereum-description": "A quick overview of Ethereum", "docs-nav-intro-to-the-stack": "Intro to the stack", "docs-nav-intro-to-the-stack-description": "An overview of the Ethereum/web3 stack", "docs-nav-java": "Java", "docs-nav-java-script-apis": "JavaScript APIs", "docs-nav-javascript": "JavaScript", "docs-nav-json-rpc": "JSON-RPC", "docs-nav-mev": "Miner extractable value (MEV)", "docs-nav-mev-description": "How value is extracted from the Ethereum blockchain beyond the block reward", "docs-nav-mining": "Mining", "docs-nav-networks": "Networks", "docs-nav-networks-description": "Implementations of Ethereum including test networks", "docs-nav-nodes-and-clients": "Nodes and clients", "docs-nav-nodes-and-clients-description": "The individuals participating in the network and the software they run to verify transactions", "docs-nav-opcodes": "Opcodes", "docs-nav-run-a-node": "Run a node", "docs-nav-client-diversity": "Client diversity", "docs-nav-nodes-as-a-service": "Nodes as a service", "docs-nav-oracles": "Oracles", "docs-nav-oracles-description": "How information is injected into the Ethereum blockchain", "docs-nav-programming-languages": "Programming languages", "docs-nav-programming-languages-description": "How to get started with Ethereum using languages you may already know", "docs-nav-proof-of-stake": "Proof-of-stake", "docs-nav-proof-of-work": "Proof-of-work", "docs-nav-python": "Python", "docs-nav-readme": "Overview", "docs-nav-ruby": "<PERSON>", "docs-nav-rust": "Rust", "docs-nav-scaling": "Sc<PERSON>", "docs-nav-scaling-description": "Methods for preserving decentralization and security as Ethereum grows", "docs-nav-scaling-optimistic-rollups": "Optimistic rollups", "docs-nav-scaling-zk-rollups": "Zero-knowledge rollups", "docs-nav-scaling-channels": "State channels", "docs-nav-scaling-sidechains": "Sidechains", "docs-nav-scaling-plasma": "Plasma", "docs-nav-scaling-validium": "Validium", "docs-nav-smart-contract-security": "Smart contract security", "docs-nav-smart-contract-security-description": "Best practices for managing smart contract attacks and vulnerabilities", "docs-nav-smart-contract-anatomy": "Smart contract anatomy", "docs-nav-smart-contract-languages": "Smart contract languages", "docs-nav-smart-contracts": "Smart contracts", "docs-nav-smart-contracts-description": "Programs that reside at an Ethereum address and run functions when triggered by transactions", "docs-nav-smart-contracts-libraries": "Smart contracts libraries", "docs-nav-standards": "Standards", "docs-nav-standards-description": "Agreed upon protocols for maintaining efficiency and accessibility of projects to the community", "docs-nav-storage": "Storage", "docs-nav-storage-description": "Decentralized storage structures and mechanism", "docs-nav-testing-smart-contracts": "Testing smart contracts", "docs-nav-token-standards": "Token standards", "docs-nav-transactions": "Transactions", "docs-nav-transactions-description": "Transfers and other actions that cause Ethereum's state to change", "docs-nav-web2-vs-web3": "Web2 vs Web3", "docs-nav-web2-vs-web3-description": "The fundamental differences that blockchain-based applications provide", "page-calltocontribute-desc-1": "If you're an expert on the topic and want to contribute, edit this page and sprinkle it with your wisdom.", "page-calltocontribute-desc-2": "You'll be credited and you'll be helping the Ethereum community!", "page-calltocontribute-desc-3": "Use this flexible", "page-calltocontribute-desc-4": "Questions? Ask us in the #content channel on our", "page-calltocontribute-link": "documentation template", "page-calltocontribute-link-2": "Discord server", "page-calltocontribute-span": "Edit page", "page-calltocontribute-title": "Help us with this page", "page-developer-meta-title": "Ethereum Developer Resources", "page-developers-about": "About these developer resources", "page-developers-about-desc": "ethstake.exchange is here to help you build with Ethereum with documentation on foundational concepts as well as the development stack. Plus there are tutorials to get you up and running.", "page-developers-about-desc-2": "Inspired by the Mozilla Developer Network, we thought Ethereum needed a place to house great developer content and resources. Like our friends at Mozilla, everything here is open-source and ready for you to extend and improve.", "page-developers-account-desc": "Contracts or people on the network", "page-developers-accounts-link": "Accounts", "page-developers-advanced": "Advanced", "page-developers-api-desc": "Using libraries to interact with smart contracts", "page-developers-api-link": "Backend APIs", "page-developers-aria-label": "Developers' Menu", "page-developers-block-desc": "Batches of transactions added to the blockchain", "page-developers-block-explorers-desc": "Your portal to Ethereum data", "page-developers-block-explorers-link": "Block explorers", "page-developers-blocks-link": "Blocks", "page-developers-browse-tutorials": "Browse tutorials", "page-developers-choose-stack": "Choose your stack", "page-developers-contribute": "Contribute", "page-developers-dev-env-desc": "IDEs that are suitable for dapp development", "page-developers-dev-env-link": "Development environments", "page-developers-discord": "Join <PERSON>", "page-developers-docs-introductions": "Introductions", "page-developers-evm-desc": "The computer that processes transactions", "page-developers-evm-link": "The Ethereum virtual machine (EVM)", "page-developers-explore-documentation": "Explore the documentation", "page-developers-feedback": "If you have any feedback, reach out to us via a GitHub issue or on our Discord server.", "page-developers-frameworks-desc": "Tools for helping speed up development", "page-developers-frameworks-link": "Development frameworks", "page-developers-fundamentals": "Fundamentals", "page-developers-gas-desc": "<PERSON><PERSON> needed to power transactions", "page-developers-gas-link": "Gas", "page-developers-get-started": "How would you like to get started?", "page-developers-improve-ethereum": "Help us make ethstake.exchange better", "page-developers-improve-ethereum-desc": "Like ethstake.exchange, these docs are a community effort. Create a PR if you see mistakes, room for improvement, or new opportunities to help Ethereum developers.", "page-developers-into-eth-desc": "An introduction to blockchain and Ethereum", "page-developers-intro-ether-desc": "An introduction to cryptocurrency and Ether", "page-developers-intro-dapps-desc": "An introduction to decentralized applications", "page-developers-intro-dapps-link": "Intro to dapps", "page-developers-intro-eth-link": "Intro to Ethereum", "page-developers-intro-ether-link": "Intro to <PERSON><PERSON>", "page-developers-intro-stack": "Intro to the stack", "page-developers-intro-stack-desc": "An introduction to the Ethereum stack", "page-developers-js-libraries-desc": "Using JavaScript to interact with smart contracts", "page-developers-js-libraries-link": "JavaScript libraries", "page-developers-language-desc": "Using Ethereum with familiar languages", "page-developers-languages": "Programming languages", "page-developers-learn": "Learn Ethereum development", "page-developers-learn-desc": "Read up on core concepts and the Ethereum stack with our docs", "page-developers-learn-tutorials": "Learn through tutorials", "page-developers-learn-tutorials-cta": "View tutorials", "page-developers-learn-tutorials-desc": "Learn Ethereum development step-by-step from builders who have already done it.", "page-developers-meta-desc": "Documentation, tutorials, and tools for developers building on Ethereum.", "page-developers-mev-desc": "An introduction to miner extractable value (MEV)", "page-developers-mev-link": "Miner extractable value (MEV)", "page-developers-mining-desc": "How new blocks are created and consensus is reached", "page-developers-mining-link": "Mining", "page-developers-networks-desc": "An overview of Mainnet and the test networks", "page-developers-networks-link": "Networks", "page-developers-node-clients-desc": "How blocks and transactions are verified in the network", "page-developers-node-clients-link": "Nodes and clients", "page-developers-oracle-desc": "Getting off-chain data into your smart contracts", "page-developers-oracles-link": "Oracles", "page-developers-play-code": "Play with code", "page-developers-read-docs": "Read the docs", "page-developers-scaling-desc": "Solutions for faster transactions", "page-developers-scaling-link": "Sc<PERSON>", "page-developers-smart-contract-security-desc": "Security measures to consider during development of smart contracts", "page-developers-smart-contract-security-link": "Smart contract security", "page-developers-set-up": "Set up local environment", "page-developers-setup-desc": "Get your stack ready for building by configuring a development environment.", "page-developers-smart-contracts-desc": "The logic behind dapps – self-executing agreements", "page-developers-smart-contracts-link": "Smart contracts", "page-developers-stack": "The stack", "page-developers-start": "Start experimenting", "page-developers-start-desc": "Want to experiment first, ask questions later?", "page-developers-storage-desc": "How to handle dapp storage", "page-developers-storage-link": "Storage", "page-developers-subtitle": "A builders manual for Ethereum. By builders, for builders.", "page-developers-title-1": "Ethereum", "page-developers-title-2": "developer", "page-developers-title-3": "resources", "page-developers-token-standards-desc": "An overview of accepted token standards", "page-developers-token-standards-link": "Token standards", "page-developers-transactions-desc": "The way Ethereum state changes", "page-developers-transactions-link": "Transactions", "page-developers-web3-desc": "How the web3 world of development is different", "page-developers-web3-link": "Web2 vs Web3", "page-learning-tools-bloomtech-description": "The BloomTech Web3 course will teach you the skills employers look for in engineers.", "page-learning-tools-bloomtech-logo-alt": "BloomTech logo", "page-learning-tools-bootcamps": "Developer bootcamps", "page-learning-tools-bootcamps-desc": "Paid online courses to get you up to speed, fast.", "page-learning-tools-browse-docs": "Browse docs", "page-learning-tools-capture-the-ether-description": "Capture the Ether is a game in which you hack Ethereum smart contracts to learn about security.", "page-learning-tools-capture-the-ether-logo-alt": "Capture the Ether logo", "page-learning-tools-chainshot-description": "Remote, instructor-led Ethereum developer bootcamp and additional courses.", "page-learning-tools-chainshot-logo-alt": "ChainShot logo", "page-learning-tools-coding": "Learn by coding", "page-learning-tools-coding-subtitle": "These tools will help you experiment with Ethereum if you prefer a more interactive learning experience.", "page-learning-tools-consensys-academy-description": "Online Ethereum developer bootcamp.", "page-learning-tools-consensys-academy-logo-alt": "ConsenSys Academy logo", "page-learning-tools-buildspace-description": "Learn about crypto by building cool projects.", "page-learning-tools-buildspace-logo-alt": "_buildspace Logo", "page-learning-tools-cryptozombies-description": "Learn Solidity building your own Zombie game.", "page-learning-tools-cryptozombies-logo-alt": "CryptoZombies logo", "page-learning-tools-documentation": "Learn with documentation", "page-learning-tools-documentation-desc": "Want to learn more? Go to our documentation to find the explanations you need.", "page-learning-tools-eth-dot-build-description": "An educational sandbox for web3, including drag-and-drop programming and open-source building blocks.", "page-learning-tools-eth-dot-build-logo-alt": "Eth.build logo", "page-learning-tools-ethernauts-description": "Complete levels by hacking smart contracts.", "page-learning-tools-ethernauts-logo-alt": "Ethernauts logo", "page-learning-tools-game-tutorials": "Interactive game tutorials", "page-learning-tools-game-tutorials-desc": "Learn while you play. These tutorials get you through the basics using gameplay.", "page-learning-tools-meta-desc": "Web-based coding tools and interactive learning experiences to help you experiment with Ethereum development.", "page-learning-tools-meta-title": "Developer learning tools", "page-learning-tools-questbook-description": "Self paced tutorials to learn Web 3.0 by building", "page-learning-tools-questbook-logo-alt": "Questbook Logo", "page-learning-tools-remix-description": "Develop, deploy and administer smart contracts for Ethereum. Follow tutorials with the Learneth plugin.", "page-learning-tools-remix-description-2": "Remix and Replit aren't just sandboxes—developers can write, compile and deploy their smart contracts using them.", "page-learning-tools-replit-description": "A customizable development environment for Ethereum with hot reloading, error checking, and first-class testnet support.", "page-learning-tools-replit-logo-alt": "Replit logo", "page-learning-tools-remix-logo-alt": "Remix logo", "page-learning-tools-sandbox": "Code sandboxes", "page-learning-tools-sandbox-desc": "These sandboxes will give you a space to experiment with writing smart contracts and understanding Ethereum.", "page-learning-tools-studio-description": "A web-based IDE where you can follow tutorials to create and test smart contracts, and build a frontend for them.", "page-learning-tools-vyperfun-description": "Learn Vyper building your own Pokémon game.", "page-learning-tools-vyperfun-logo-alt": "Vyper.fun logo", "page-learning-tools-nftschool-description": "Explore what's going on with non-fungible tokens, or NFTs from the technical side.", "page-learning-tools-nftschool-logo-alt": "NFT school Logo", "page-learning-tools-pointer-description": "Learn web3 dev skills with fun interactive tutorials. Earn crypto rewards along the way", "page-learning-tools-pointer-logo-alt": "<PERSON><PERSON>", "page-local-environment-brownie-desc": "A Python-based development and testing framework for smart contracts targeting the Ethereum Virtual Machine.", "page-local-environment-brownie-logo-alt": "Brownie logo", "page-local-environment-dapptools-desc": "A suite of Ethereum focused CLI tools following the Unix design philosophy, favoring composability, configurability and extensibility.", "page-local-environment-dapptools-logo-alt": "Dapptools logo", "page-local-environment-embark-desc": "The all-in-one developer platform for building and deploying decentralized applications.", "page-local-environment-embark-logo-alt": "Embark logo", "page-local-environment-epirus-desc": "A platform for developing, deploying and monitoring blockchain applications on the Java Virtual Machine", "page-local-environment-epirus-logo-alt": "Epirus logo", "page-local-environment-eth-app-desc": "Create Ethereum-powered apps with one command. Comes with a wide offerring of UI frameworks and DeFi templates to choose from.", "page-local-environment-eth-app-logo-alt": "Create Eth App logo", "page-local-environment-framework-feature-1": "Features to spin up a local blockchain instance.", "page-local-environment-framework-feature-2": "Utilities to compile and test your smart contracts.", "page-local-environment-framework-feature-3": "Client development add-ons to build your user-facing application within the same project/repository.", "page-local-environment-framework-feature-4": "Configuration to connect to Ethereum networks and deploy contracts, whether to a locally running instance, or one of Ethereum's public networks.", "page-local-environment-framework-feature-5": "Decentralized app distribution - integrations with storage options like IPFS.", "page-local-environment-framework-features": "These frameworks come with a lot of out-of-the-box functionality, like:", "page-local-environment-frameworks-desc": "We recommend picking a framework, particularly if you're just getting started. Building a full-fledged dapp requires different pieces of technology. Frameworks include many of the needed features or provide easy plugin systems to pick the tools you desire.", "page-local-environment-frameworks-title": "Frameworks and pre-made stacks", "page-local-environment-hardhat-desc": "Hardhat is an Ethereum development environment for professionals.", "page-local-environment-hardhat-logo-alt": "Hardhat logo", "page-local-environment-openZeppelin-desc": "Save hours of development time by compiling, upgrading, deploying, and interacting with smart contracts with our CLI.", "page-local-environment-openZeppelin-logo-alt": "OpenZeppelin logo", "page-local-environment-scaffold-eth-desc": "Ethers + Hardhat + React: everything you need to get started building decentralized applications powered by smart contracts", "page-local-environment-scaffold-eth-logo-alt": "scaffold-eth logo", "page-local-environment-setup-meta-desc": "Guide on how to choose your software stack for Ethereum development.", "page-local-environment-setup-meta-title": "Ethereum local development setup", "page-local-environment-setup-subtitle": "If you're ready to start building, it's time to choose your stack.", "page-local-environment-setup-subtitle-2": "Here are the tools and frameworks you can use to help you build your Ethereum application.", "page-local-environment-setup-title": "Set up your local development environment", "page-local-environment-solidity-template-desc": "A GitHub template for a pre-built setup for your Solidity smart contracts. Includes a Hardhat local network, Waffle for tests, Ethers for wallet implementation, and more.", "page-local-environment-solidity-template-logo-alt": "Solidity template logo", "page-local-environment-truffle-desc": "The Truffle Suite gets developers from idea to dapp as comfortably as possible.", "page-local-environment-truffle-logo-alt": "Truffle logo", "page-local-environment-waffle-desc": "The most advanced testing lib for smart contracts. Use alone or with Scaffold-eth or Hardhat.", "page-local-environment-waffle-logo-alt": "Waffle logo", "comp-tutorial-metadata-minute-read": "minute read", "page-tutorial-listing-policy-intro": "Before you submit a tutorial please read our listing policy.", "comp-tutorial-metadata-tip-author": "Tip author", "page-tutorial-listing-policy": "article listing policy", "page-tutorial-new-github": "New to GitHub?", "page-tutorial-new-github-desc": "Raise an issue – just fill in the requested information and paste your tutorial.", "page-tutorial-pull-request": "Create a pull request", "page-tutorial-pull-request-btn": "Create pull request", "page-tutorial-pull-request-desc-1": "Please follow the", "page-tutorial-pull-request-desc-2": "tutorials/your-tutorial-name/index.md", "page-tutorial-pull-request-desc-3": "naming structure.", "page-tutorial-raise-issue-btn": "Raise issue", "page-tutorial-read-time": "min", "page-tutorial-submit-btn": "Submit a tutorial", "page-tutorial-submit-tutorial": "To submit a tutorial, you'll need to use GitHub. We welcome you to create an issue or a pull request.", "page-tutorial-subtitle": "Welcome to our curated list of community tutorials.", "page-tutorial-tags-error": "There are no tutorials with all your chosen tags yet", "page-tutorial-title": "Ethereum Development Tutorials", "page-tutorials-meta-description": "Browse and filter vetted Ethereum community tutorials by topic.", "page-tutorial-external-link": "External", "page-tutorials-meta-title": "Ethereum Development Tutorials", "page-eth-buy-some": "Want to buy some Ethereum?", "page-eth-buy-some-desc": "It's common to mix up Ethereum and ETH. Ethereum is the blockchain and ETH is the primary asset of Ethereum. ETH is what you're probably looking to buy.", "page-eth-cat-img-alt": "Graphic of ETH glyph with a kaleidoscope of cats", "page-eth-collectible-tokens": "Collectible tokens", "page-eth-collectible-tokens-desc": "Tokens that represent a collectible game item, piece of digital art, or other unique assets. Commonly known as non-fungible tokens (NFTs).", "page-eth-cryptography": "Secured by cryptography", "page-eth-cryptography-desc": "Internet money may be new but it's secured by proven cryptography. This protects your wallet, your ETH, and your transactions. ", "page-eth-currency-for-apps": "It's the currency of Ethereum apps.", "page-eth-currency-for-future": "Currency for our digital future", "page-eth-description": "ETH is a cryptocurrency. It is scarce digital money that you can use on the internet – similar to Bitcoin. If you’re new to crypto, here's how ETH is different from traditional money.", "page-eth-earn-interest-link": "Earn interest", "page-eth-ethhub-caption": "Updated often", "page-eth-ethhub-overview": "EthHub has a great overview if you want", "page-eth-flexible-amounts": "Available in flexible amounts", "page-eth-flexible-amounts-desc": "ETH is divisible up to 18 decimal places so you don't have to buy 1 whole ETH. You can buy fractions at a time – as little as 0.000000000000000001 ETH if you want.", "page-eth-fuels": "ETH fuels and secures Ethereum", "page-eth-fuels-desc": "ETH is the lifeblood of Ethereum. When you send ETH or use an Ethereum application, you'll pay a small fee in ETH to use the Ethereum network. This fee is an incentive for a miner to process and verify what you're trying to do.", "page-eth-fuels-desc-2": "Miners are like the record-keepers of Ethereum – they check and prove that no one is cheating. Miners who do this work are also rewarded with small amounts of newly-issued ETH.", "page-eth-fuels-desc-3": "The work miners do keeps Ethereum secure and free of centralized control. In other words,", "page-eth-fuels-more-staking": "More on staking", "page-eth-fuels-staking": "ETH will become even more important with staking. When you stake your ETH you'll be able to help secure Ethereum and earn rewards. In this system, the threat of losing your ETH disincentivises attacks.", "page-eth-get-eth-btn": "Get ETH", "page-eth-gov-tokens": "Governance tokens", "page-eth-gov-tokens-desc": "Tokens that represent voting power in decentralized organisations.", "page-eth-has-value": "Why does ETH have value?", "page-eth-has-value-desc": "ETH's valuable in different ways to different people.", "page-eth-has-value-desc-2": "For users of Ethereum, ETH is valuable because it lets you pay transaction fees.", "page-eth-has-value-desc-3": "Others see it as a digital store of value because the creation of new ETH slows down over time.", "page-eth-has-value-desc-4": "More recently, ETH has become valuable to users of financial apps on Ethereum. That's because you can use ETH as collateral for crypto loans, or as a payment system.", "page-eth-has-value-desc-5": "Of course many also see it as an investment, similar to Bitcoin or other cryptocurrencies.", "page-eth-how-to-buy": "How to buy Ether", "page-eth-how-to-buy-caption": "Updated often", "page-eth-is-money": "ETH is digital, global money.", "page-eth-last-updated": "January 2019", "page-eth-mining-link": "More on Mining", "page-eth-monetary-policy": "Ethereum's monetary policy", "page-eth-more-on-ethereum-link": "More on Ethereum", "page-eth-no-centralized": "No centralized control ", "page-eth-no-centralized-desc": "ETH is decentralized and global. There's no company or bank that can decide to print more ETH, or change the terms of use.", "page-eth-non-fungible-tokens-link": "Non-fungible tokens", "page-eth-not-only-crypto": "ETH isn't the only crypto on Ethereum", "page-eth-not-only-crypto-desc": "Anyone can create new kinds of assets and trade them on Ethereum. These are known as 'tokens'. People have tokenised traditional currencies, their real estate, their art, and even themselves!", "page-eth-not-only-crypto-desc-2": "Ethereum is home to thousands of tokens – some more useful and valuable than others. Developers are constantly building new tokens that unlock new possibilities and open new markets.", "page-eth-not-only-crypto-desc-3": "If you'd like to learn more about tokens, our friends at EthHub have written a couple of great overviews:", "page-eth-open": "Open to anyone", "page-eth-open-desc": "You only need an internet connection and a wallet to accept ETH. You don't need access to a bank account to accept payments. ", "page-eth-p2p-payments": "Peer-to-peer payments", "page-eth-p2p-payments-desc": "You can send your ETH without any intermediary service like a bank. It's like handing cash over in-person, but you can do it securely with anyone, anywhere, anytime.", "page-eth-period": ".", "page-eth-popular-tokens": "Popular types of token", "page-eth-powers-ethereum": "ETH powers Ethereum", "page-eth-shit-coins": "Sh*t coins", "page-eth-shit-coins-desc": "Because making new tokens is easy, anyone can do it - even people with bad or misguided intentions. Always do your research before using them!", "page-eth-stablecoins": "Stablecoins", "page-eth-stablecoins-desc": "Tokens that mirror the value of traditional currency like dollars. This solves the volatility problem with many cryptocurrencies.", "page-eth-stablecoins-link": "Get stablecoins", "page-eth-stream-link": "Stream ETH", "page-eth-tokens-link": "Ethereum tokens", "page-eth-trade-link-2": "Swap tokens", "page-eth-underpins": "ETH underpins the Ethereum financial system", "page-eth-underpins-desc": "Not satisfied with payments, the Ethereum community is building a whole financial system that's peer-to-peer and accessible to everyone.", "page-eth-underpins-desc-2": "You can use ETH as collateral to generate entirely different cryptocurrency tokens on Ethereum. Plus you can borrow, lend and earn interest on ETH and other ETH-backed tokens.", "page-eth-uses": "Uses for ETH grow every day", "page-eth-uses-desc": "Because Ethereum is programmable, developers can shape ETH in countless ways.", "page-eth-uses-desc-2": "Back in 2015, all you could do was send ETH from one Ethereum account to another. Here are just some of things you can do today.", "page-eth-uses-desc-3": "pay someone or receive funds in real time.", "page-eth-uses-desc-4": "you can trade ETH with other tokens including Bitcoin.", "page-eth-uses-desc-5": "on ETH and other Ethereum-based tokens.", "page-eth-uses-desc-6": "access the world of cryptocurrencies with a steady, less-volatile value.", "page-eth-value": "Why ether is valuable", "page-eth-video-alt": "ETH glyph video", "page-eth-whats-eth": "What is ether (ETH)?", "page-eth-whats-eth-hero-alt": "Illustration of a group of people marvelling at an ether (ETH) glyph in awe", "page-eth-whats-eth-meta-desc": "What you need to know to understand ETH and its place in Ethereum.", "page-eth-whats-eth-meta-title": "What is ether (ETH)?", "page-eth-whats-ethereum": "What is Ethereum?", "page-eth-whats-ethereum-desc": "If you'd like to learn more about Ethereum, the technology behind ETH, check out our introduction.", "page-eth-whats-unique": "What's unique about ETH?", "page-eth-whats-unique-desc": "There are many cryptocurrencies and lots of other tokens on Ethereum, but there are some things that only ETH can do.", "page-eth-where-to-buy": "Where to get ETH", "page-eth-where-to-buy-desc": "You can get ETH from an exchange or a wallet but different countries have different policies. Check to see the services that will let you buy ETH.", "page-eth-yours": "It's really yours", "page-eth-yours-desc": "ETH lets you be your own bank. You can control your own funds with your wallet as proof of ownership – no third parties necessary.", "page-eth-more-on-tokens": "More on tokens and their uses", "page-eth-button-buy-eth": "Get ETH", "page-eth-tokens-stablecoins": "Stablecoins", "page-eth-tokens-defi": "Decentralized finance (DeFi)", "page-eth-tokens-nft": "Non-fungible tokens (NFTs)", "page-eth-tokens-dao": "Decentralized autonomous organisations (DAOs)", "page-eth-tokens-stablecoins-description": "More on the least volatile of Ethereum tokens.", "page-eth-tokens-defi-description": "The financial system for Ethereum tokens.", "page-eth-tokens-nft-description": "Tokens that represent ownership of items on Ethereum.", "page-eth-tokens-dao-description": "Internet communities often goverened by token holders.", "page-eth-whats-defi": "More on DeFi", "page-eth-whats-defi-description": "DeFi is the decentralized financial system built on Ethereum. This overview explains what you can do.", "page-get-eth-article-keeping-crypto-safe": "The keys to keeping your crypto safe", "page-get-eth-article-protecting-yourself": "Protecting yourself and your funds", "page-get-eth-article-store-digital-assets": "How to store digital assets on Ethereum", "page-get-eth-cex": "Centralized exchanges", "page-get-eth-cex-desc": "Exchanges are businesses that let you buy crypto using traditional currencies. They have custody over any ETH you buy until you send it to a wallet you control.", "page-get-eth-checkout-dapps-btn": "Check out dapps", "page-get-eth-community-safety": "Community posts on security", "page-get-eth-description": "Ethereum and ETH aren't controlled by any government or company - they are decentralized. This means ETH's open to everyone to use.", "page-get-eth-dex": "Decentralized exchanges (DEXs)", "page-get-eth-dex-desc": "If you want more control, buy ETH peer-to-peer. With a DEX you can trade without giving control of your funds to a centralized company.", "page-get-eth-dexs": "Decentralized exchanges (DEXs)", "page-get-eth-dexs-desc": "Decentralized exchanges are open marketplaces for ETH and other tokens. They connect buyers and sellers directly.", "page-get-eth-dexs-desc-2": "Instead of using a trusted third party to safeguard funds in the transaction, they use code. The seller's ETH will only be transferred when payment is guaranteed. This type of code is known as a smart contract.", "page-get-eth-dexs-desc-3": "This means there are fewer geographical restrictions than with centralized alternatives. If someone is selling what you want and accepting a payment method you can provide, you’re good to go. DEXs can let you buy ETH with other tokens, PayPal or even in-person cash deliveries.", "page-get-eth-do-not-copy": "Example: Do not copy", "page-get-eth-exchanges-disclaimer": "We collected this information manually. If you spot something wrong, let us know at", "page-get-eth-exchanges-empty-state-text": "Enter your country of residence to see a list of wallets and exchanges you can use to buy ETH", "page-get-eth-exchanges-except": "Except", "page-get-eth-exchanges-header": "What country do you live in?", "page-get-eth-exchanges-header-exchanges": "Exchanges", "page-get-eth-exchanges-header-wallets": "Wallets", "page-get-eth-exchanges-intro": "Exchanges and wallets have restrictions on where they can sell crypto.", "page-get-eth-exchanges-no-exchanges": "Sorry, we don’t know any exchanges that let you buy ETH from this country. If you do, tell us at", "page-get-eth-exchanges-no-exchanges-or-wallets": "Sorry, we don’t know any exchanges or wallets that let you buy ETH from this country. If you do, tell us at", "page-get-eth-exchanges-no-wallets": "Sorry, we don’t know any wallets that let you buy ETH from this country. If you do, tell us at", "page-get-eth-exchanges-search": "Type where you live...", "page-get-eth-exchanges-success-exchange": "It can take a number of days to register with an exchange because of their legal checks.", "page-get-eth-exchanges-success-wallet-link": "wallets", "page-get-eth-exchanges-success-wallet-paragraph": "Where you live, you can buy ETH directly from these wallets. Learn more about", "page-get-eth-exchanges-usa": "United States of America (USA)", "page-get-eth-get-wallet-btn": "Get a wallet", "page-get-eth-hero-image-alt": "Get ETH hero image", "page-get-eth-keep-it-safe": "Keeping your ETH safe", "page-get-eth-meta-description": "How to buy ETH based on where you live and advice on how to look after it.", "page-get-eth-meta-title": "How to buy ETH", "page-get-eth-need-wallet": "You will need a wallet to use a DEX.", "page-get-eth-new-to-eth": "New to ETH? Here's an overview to get you started.", "page-get-eth-other-cryptos": "Buy with other crypto", "page-get-eth-protect-eth-desc": "If you plan on buying a lot of ETH you may want to keep it in a wallet you control, not an exchange. That's because an exchange is a likely target for hackers. If a hacker gains access, you could lose your funds. Alternatively, only you have control of your wallet.", "page-get-eth-protect-eth-in-wallet": "Protect your ETH in a wallet", "page-get-eth-search-by-country": "Search by country", "page-get-eth-security": "But this also means you need to take the security of your funds seriously. With ETH, you’re not trusting a bank to look after your money, you’re trusting yourself.", "page-get-eth-smart-contract-link": "More on smart contracts", "page-get-eth-swapping": "Swap your tokens for other people's ETH. And vice versa.", "page-get-eth-traditional-currencies": "Buy with traditional currencies", "page-get-eth-traditional-payments": "Buy ETH with traditional payment types directly from sellers.", "page-get-eth-try-dex": "Try a Dex", "page-get-eth-use-your-eth": "Use your ETH", "page-get-eth-use-your-eth-dapps": "Now that you own some ETH, check out some Ethereum applications (dapps). There are dapps for finance, social media, gaming and lots of other categories.", "page-get-eth-wallet-instructions": "Follow wallet instructions", "page-get-eth-wallet-instructions-lost": "If you lose access to your wallet, you’ll lose access to your funds. Your wallet should give you instructions on protecting against this. Be sure to follow them carefully – in most cases, no one can help you if you lose access to your wallet.", "page-get-eth-wallets": "Wallets", "page-get-eth-wallets-link": "More on wallets", "page-get-eth-wallets-purchasing": "Some wallets let you buy crypto with a debit/credit card, bank transfer or even Apple Pay. Geographical restrictions apply.", "page-get-eth-warning": "These DEXs aren't for beginners as you'll need some ETH to use them.", "page-get-eth-what-are-DEX's": "What are DEXs?", "page-get-eth-whats-eth-link": "What's ETH?", "page-get-eth-where-to-buy-desc": "You can buy ETH from exchanges or from wallets directly.", "page-get-eth-where-to-buy-desc-2": "Check which services you can use based on where you live.", "page-get-eth-where-to-buy-title": "Where to buy ETH", "page-get-eth-your-address": "Your ETH address", "page-get-eth-your-address-desc": "When you download a wallet it will create a public ETH address for you. Here's what one looks like:", "page-get-eth-your-address-desc-3": "Think of this like your email address, but instead of mail it can receive ETH. If you want to transfer ETH from an exchange to your wallet, use your address as the destination. Be sure to always double check before you send!", "page-get-eth-your-address-wallet-link": "Check out wallets", "page-index-hero-image-alt": "An illustration of a futuristic city, representing the Ethereum ecosystem.", "page-index-meta-description": "Ethereum is a global, decentralized platform for money and new kinds of applications. On Ethereum, you can write code that controls money, and build applications accessible anywhere in the world.", "page-index-meta-title": "Home", "page-index-title": "Welcome to Ethereum", "page-index-description": "Ethereum is the community-run technology powering the cryptocurrency ether (ETH) and thousands of decentralized applications.", "page-index-title-button": "Explore Ethereum", "page-index-get-started": "Get started", "page-index-get-started-description": "ethstake.exchange is your portal into the world of Ethereum. The tech is new and ever-evolving – it helps to have a guide. Here's what we recommend you do if you want to dive in.", "page-index-get-started-image-alt": "Illustration of a person working on a computer.", "page-index-get-started-wallet-title": "Pick a wallet", "page-index-get-started-wallet-description": "A wallet lets you connect to Ethereum and manage your funds.", "page-index-get-started-wallet-image-alt": "Illustration of a robot with a vault for a body, representing an Ethereum wallet.", "page-index-get-started-eth-title": "Get ETH", "page-index-get-started-eth-description": "ETH is the currency of Ethereum – you can use it in applications.", "page-index-get-started-eth-image-alt": "Illustration of a group of people marvelling at an ether (ETH) glyph in awe.", "page-index-get-started-dapps-title": "Use a dapp", "page-index-get-started-dapps-description": "Dapps are applications powered by Ethereum. See what you can do.", "page-index-get-started-dapps-image-alt": "Illustration of a doge using a computer.", "page-index-get-started-devs-title": "Start building", "page-index-get-started-devs-description": "If you want to start coding with Ethereum, we have documentation, tutorials, and more in our developer portal.", "page-index-get-started-devs-image-alt": "An illustration of a hand creating an ETH logo made of lego bricks.", "page-index-what-is-ethereum": "What is Ethereum?", "page-index-what-is-ethereum-description": "Ethereum is a technology that's home to digital money, global payments, and applications. The community has built a booming digital economy, bold new ways for creators to earn online, and so much more. It's open to everyone, wherever you are in the world – all you need is the internet.", "page-index-what-is-ethereum-button": "What is Ethereum?", "page-index-what-is-ethereum-secondary-button": "More on digital money", "page-index-what-is-ethereum-image-alt": "Illustration of a person peering into a bazaar, meant to represent Ethereum.", "page-index-defi": "A fairer financial system", "page-index-defi-description": "Today, billions of people can’t open bank accounts, others have their payments blocked. Ethereum's decentralized finance (DeFi) system never sleeps or discriminates. With just an internet connection, you can send, receive, borrow, earn interest, and even stream funds anywhere in the world.", "page-index-defi-button": "Explore DeFi", "page-index-defi-image-alt": "Illustration of hands offering an ETH symbol.", "page-index-internet": "An open internet", "page-index-internet-description": "Today, we gain access to 'free' internet services by giving up control of our personal data. Ethereum services are open by default – you just need a wallet. These are free and easy to set up, controlled by you, and work without any personal info.", "page-index-internet-button": "Explore the open internet", "page-index-internet-secondary-button": "More on wallets", "page-index-internet-image-alt": "Illustration of a futuristic computer set up, powered by Ethereum crystals.", "page-index-developers": "A new frontier for development", "page-index-developers-description": "Ethereum and its apps are transparent and open source. You can fork code and re-use functionality others have already built. If you don't want to learn a new language you can just interact with open-sourced code using JavaScript and other existing languages.", "page-index-developers-button": "Developer portal", "page-index-developers-code-examples": "Code examples", "page-index-developers-code-example-title-0": "Your own bank", "page-index-developers-code-example-description-0": "You can build a bank run by logic you've programmed.", "page-index-developers-code-example-title-1": "Your own currency", "page-index-developers-code-example-description-1": "You can create tokens that you can transfer and use across applications.", "page-index-developers-code-example-title-2": "A JavaScript Ethereum wallet", "page-index-developers-code-example-description-2": "You can use existing languages to interact with Ethereum and other applications.", "page-index-developers-code-example-title-3": "An open, permissionless DNS", "page-index-developers-code-example-description-3": "You can reimagine existing services as decentralized, open applications.", "page-index-network-stats-title": "Ethereum today", "page-index-network-stats-subtitle": "The latest network statistics", "page-index-network-stats-eth-price-description": "ETH price (USD)", "page-index-network-stats-eth-price-explainer": "The latest price for 1 ether. You can buy as little as 0.000000000000000001 – you don't need to buy 1 whole ETH.", "page-index-network-stats-tx-day-description": "Transactions today", "page-index-network-stats-tx-day-explainer": "The number of transactions successfully processed on the network in the last 24 hours.", "page-index-network-stats-value-defi-description": "Value locked in DeFi (USD)", "page-index-network-stats-value-defi-explainer": "The amount of money in decentralized finance (DeFi) applications, the Ethereum digital economy.", "page-index-network-stats-nodes-description": "Nodes", "page-index-network-stats-nodes-explainer": "Ethereum is run by thousands of volunteers around the globe, known as nodes.", "page-index-touts-header": "Explore ethstake.exchange", "page-index-contribution-banner-title": "Contribute to ethstake.exchange", "page-index-contribution-banner-description": "This website is open source with hundreds of community contributors. You can propose edits to any of the content on this site, suggest awesome new features, or help us squash bugs.", "page-index-contribution-banner-image-alt": "An Ethereum logo made of lego bricks.", "page-index-contribution-banner-button": "More on contributing", "page-index-tout-upgrades-title": "Level up your upgrade knowledge", "page-index-tout-upgrades-description": "Ethereum consists of interconnected upgrades designed to make the network more scalable, secure, and sustainable.", "page-index-tout-upgrades-image-alt": "Illustration of a spaceship representing the increased power after Ethereum upgrades.", "page-index-tout-enterprise-title": "Ethereum for enterprise", "page-index-tout-enterprise-description": "See how Ethereum can open up new business models, reduce your costs and future-proof your business.", "page-index-tout-enterprise-image-alt": "Illustration of a futuristic computer/device.", "page-index-tout-community-title": "The Ethereum community", "page-index-tout-community-description": "Ethereum is all about community. It's made up of people from all different backgrounds and interests. See how you can join in.", "page-index-tout-community-image-alt": "Illustration of a group of builders working together.", "page-index-nft": "The internet of assets", "page-index-nft-description": "Ethereum isn't just for digital money. Anything you can own can be represented, traded and put to use as non-fungible tokens (NFTs). You can tokenise your art and get royalties automatically every time it's re-sold. Or use a token for something you own to take out a loan. The possibilities are growing all the time.", "page-index-nft-button": "More on NFTs", "page-index-nft-alt": "An Eth logo being displayed via hologram.", "page-languages-h1": "Language Support", "page-languages-interested": "Interested in contributing?", "page-languages-learn-more": "Learn more about our Translation Program", "page-languages-meta-desc": "Resources to all supported languages of ethstake.exchange and ways to get involved as a translator.", "page-languages-meta-title": "ethstake.exchange Language Translations", "page-languages-p1": "Ethereum is a global project, and it is critical that ethstake.exchange is accessible to everyone, regardless of their nationality or language. Our community has been working hard to make this vision a reality.", "page-languages-translations-available": "ethstake.exchange is available in the following languages", "page-languages-resources-paragraph": "In addition to translating ethstake.exchange content, we also maintain a", "page-languages-resources-link": "curated list of Ethereum resources in many languages", "page-languages-want-more-header": "Want to see ethstake.exchange in a different language?", "page-languages-want-more-link": "Translation Program", "page-languages-want-more-paragraph": "ethstake.exchange translators are always translating pages in as many languages as possible. To see what they're working on right now or to sign up to join them, read about our", "page-languages-filter-placeholder": "Filter", "layer-2-arbitrum-note": "Fraud proofs only for whitelisted users, whitelist not open yet", "layer-2-boba-note": "State validation in development", "layer-2-metis-note": "Fraud proofs in development", "layer-2-optimism-note": "Fault proofs in development", "page-run-a-node-build-your-own-title": "Build your own", "page-run-a-node-build-your-own-hardware-title": "Step 1 – Hardware", "page-run-a-node-build-your-own-minimum-specs": "Minimum specs", "page-run-a-node-build-your-own-min-ram": "4 - 8 GB RAM", "page-run-a-node-build-your-own-ram-note-1": "See note on staking", "page-run-a-node-build-your-own-ram-note-2": "See note on Raspberry Pi", "page-run-a-node-build-your-own-min-ssd": "2 TB SSD", "page-run-a-node-build-your-own-ssd-note": "SSD necessary for required write speeds.", "page-run-a-node-build-your-own-min-internet": "Internet connection", "page-run-a-node-build-your-own-recommended": "Recommended", "page-run-a-node-build-your-own-nuc": "Intel NUC, 7th gen or higher", "page-run-a-node-build-your-own-nuc-small": "x86 processor", "page-run-a-node-build-your-own-connection": "Wired internet connection", "page-run-a-node-build-your-own-connection-small": "Not required, but provides easier setup and most consistent connection", "page-run-a-node-build-your-own-peripherals": "Display screen and keyboard", "page-run-a-node-build-your-own-peripherals-small": "Unless you're using DAppNode, or ssh/headless setup", "page-run-a-node-build-your-own-software": "Step 2 – Software", "page-run-a-node-build-your-own-software-option-1-title": "Option 1 – DAppNode", "page-run-a-node-build-your-own-software-option-1-description": "When you're ready with your hardware, the DAppNode operating system can be downloaded using any computer and installed onto a fresh SSD via a USB drive.", "page-run-a-node-build-your-own-software-option-1-button": "DAppNode Setup", "page-run-a-node-build-your-own-software-option-2-title": "Option 2 – Command line", "page-run-a-node-build-your-own-software-option-2-description-1": "For maximum control, experienced users may prefer using the command line instead.", "page-run-a-node-build-your-own-software-option-2-description-2": "See our developer docs for more information on getting started with client selection.", "page-run-a-node-build-your-own-software-option-2-button": "Command line setup", "page-run-a-node-buy-fully-loaded-title": "Buy fully loaded", "page-run-a-node-buy-fully-loaded-description": "Order a plug and play option from vendors for the simplest onboarding experience.", "page-run-a-node-buy-fully-loaded-note-1": "No building needed.", "page-run-a-node-buy-fully-loaded-note-2": "App-like setup with a GUI.", "page-run-a-node-buy-fully-loaded-note-3": "No command-line required.", "page-run-a-node-buy-fully-loaded-plug-and-play": "These solutions are small in size, but come fully loaded.", "page-run-a-node-censorship-resistance-title": "Censorship Resistance", "page-run-a-node-censorship-resistance-preview": "Ensure access when you need it, and don't be censored.", "page-run-a-node-censorship-resistance-1": "A 3rd-party node could choose to refuse transactions from specific IP addresses, or transactions that involve specific accounts, potentially blocking you from using the network when you need it. ", "page-run-a-node-censorship-resistance-2": "Having your own node to submit transactions to guarantees that you can broadcast your transaction to the rest of the peer-to-peer network at any time.", "page-run-a-node-community-title": "Find some helpers", "page-run-a-node-community-description-1": "Online platforms such as Discord or Reddit are home to a large number of community builders willing to help you with any questions you may encounter.", "page-run-a-node-community-description-2": "Don't go at it alone. If you have a question it's likely someone here can help you find an answer.", "page-run-a-node-community-link-1": "Join the DAppNode Discord", "page-run-a-node-community-link-2": "Find online communities", "page-run-a-node-choose-your-adventure-title": "Choose your adventure", "page-run-a-node-choose-your-adventure-1": "You'll need some hardware to get started. Although running node software is possible on a personal computer, having a dedicated machine can greatly enhance the performance of your node while minimizing its impact on your primary computer.", "page-run-a-node-choose-your-adventure-2": "When selecting hardware, consider that the chain is continually growing, and maintenance will inevitably be needed. Increasing specs can help delay the need for node maintenance.", "page-run-a-node-choose-your-adventure-build-1": "A cheaper and more customizable option for slightly more technical users.", "page-run-a-node-choose-your-adventure-build-bullet-1": "Source your own parts.", "page-run-a-node-choose-your-adventure-build-bullet-2": "Install DAppNode.", "page-run-a-node-choose-your-adventure-build-bullet-3": "Or, choose your own OS and clients.", "page-run-a-node-choose-your-adventure-build-start": "Start building", "page-run-a-node-decentralized-title": "Decentralization", "page-run-a-node-decentralized-preview": "Resist strengthening centralized points of failure.", "page-run-a-node-decentralized-1": "Centralized cloud servers can provide a lot of computing power, but they provide a target for nation-states or attackers looking to disrupt the network.", "page-run-a-node-decentralized-2": "Network resilience is achieved with more nodes, in geographically diverse locations, operated by more people of diverse backgrounds. As more people run their own node, reliance on centralized points of failure diminishes, making the network stronger.", "page-run-a-node-feedback-prompt": "Did you find this page helpful?", "page-run-a-node-further-reading-title": "Further reading", "page-run-a-node-further-reading-1-link": "Mastering Ethereum - Should I Run a Full Node", "page-run-a-node-further-reading-1-author": "<PERSON>", "page-run-a-node-further-reading-2-link": "Ethereum on ARM - Quick Start Guide", "page-run-a-node-further-reading-3-link": "The Limits to Blockchain Scalability", "page-run-a-node-further-reading-3-author": "<PERSON><PERSON>", "page-run-a-node-getting-started-title": "Getting started", "page-run-a-node-getting-started-software-section-1": "In the earlier days of the network, users needed to have the ability to interface with the command-line in order to operate an Ethereum node.", "page-run-a-node-getting-started-software-section-1-alert": "If this is your preference, and you've got the skills, feel free to check out our technical docs.", "page-run-a-node-getting-started-software-section-1-link": "Spin up an Ethereum node", "page-run-a-node-getting-started-software-section-2": "Now we have DAppNode, which is <b>free and open-source software</b> that gives users an <b>app-like experience</b> while managing their node.", "page-run-a-node-getting-started-software-section-3a": "In just a few taps you can have your node up and running.", "page-run-a-node-getting-started-software-section-3b": "DAppNode makes it easy for users to run full nodes, as well as dapps and other P2P networks, with no need to touch the command-line. This makes it easier for everyone to participate and create a more decentralized network.", "page-run-a-node-getting-started-software-title": "Part 2: Software", "page-run-a-node-glyph-alt-terminal": "Terminal glyph", "page-run-a-node-glyph-alt-phone": "Phone tap glyph", "page-run-a-node-glyph-alt-dappnode": "DAppNode glyph", "page-run-a-node-glyph-alt-pnp": "Plug-n-play glyph", "page-run-a-node-glyph-alt-hardware": "Hardware glyph", "page-run-a-node-glyph-alt-software": "Software download glyph", "page-run-a-node-glyph-alt-privacy": "Privacy glyph", "page-run-a-node-glyph-alt-censorship-resistance": "Censorship resistant megaphone glyph", "page-run-a-node-glyph-alt-earth": "Earth glyph", "page-run-a-node-glyph-alt-decentralization": "Decentralization glyph", "page-run-a-node-glyph-alt-vote": "Voice your vote glyph", "page-run-a-node-glyph-alt-sovereignty": "Sovereignty glyph", "page-run-a-node-hero-alt": "Graphic of node", "page-run-a-node-hero-header": "Take full control.<br />Run your own node.", "page-run-a-node-hero-subtitle": "Become fully sovereign while helping secure the network. Become Ethereum.", "page-run-a-node-hero-cta-1": "Learn more", "page-run-a-node-hero-cta-2": "Let's dive in!", "page-run-a-node-install-manually-title": "Install manually", "page-run-a-node-install-manually-1": "If you're a more technical user and have decided to build your own device, DAppNode can be downloaded from any computer and installed onto a fresh SSD via a USB drive.", "page-run-a-node-meta-description": "An introduction on what, why, and how to run an Ethereum node.", "page-run-a-node-participate-title": "Participate", "page-run-a-node-participate-preview": "The decentralization revolution starts with <strong>you</strong>.", "page-run-a-node-participate-1": "By running a node you become part of a global movement to decentralize control and power over a world of information.", "page-run-a-node-participate-2": "If you're a holder, bring value to your ETH by supporting the health and decentralization of the network, and ensure you have a say in its future.", "page-run-a-node-privacy-title": "Privacy & Security", "page-run-a-node-privacy-preview": "Stop leaking your personal information to third party nodes.", "page-run-a-node-privacy-1": "When sending transactions using public nodes, personal information can be leaked to these third-party services such as your IP address and which Ethereum addresses you own.", "page-run-a-node-privacy-2": "By pointing compatible wallets to your own node you can use your wallet to privately and securely interact with the blockchain.", "page-run-a-node-privacy-3": "Also, if a malicious node distributes an invalid transaction, your node will simply disregard it.  Every transaction is verified locally on your own machine, so you don't need to trust anyone.", "page-run-a-node-rasp-pi-title": "A note on Raspberry Pi (ARM processor)", "page-run-a-node-rasp-pi-description": "Raspberry Pis are lightweight and affordable computers, but they have limitations that may impact the performance of your node. Though not currently recommended for staking, these can be an excellent and inexpensive option for running a node for personal use, with as little as 4 - 8 GB of RAM.", "page-run-a-node-rasp-pi-note-1-link": "DAppNode on ARM", "page-run-a-node-rasp-pi-note-1-description": "See these instructions if you plan on running DAppNode on a Raspberry Pi", "page-run-a-node-rasp-pi-note-2-link": "Ethereum on ARM documentation", "page-run-a-node-rasp-pi-note-2-description": "Learn how to set up a node via the command line on a Raspberry Pi", "page-run-a-node-rasp-pi-note-3-link": "Run a node with Raspberry Pi", "page-run-a-node-rasp-pi-note-3-description": "Follow along here if tutorials are your preference", "page-run-a-node-shop": "Shop", "page-run-a-node-shop-avado": "Shop Avado", "page-run-a-node-shop-dappnode": "Shop DAppNode", "page-run-a-node-staking-title": "Stake your ETH", "page-run-a-node-staking-description": "Though not required, with a node up and running you're one step closer to staking your ETH to earn rewards and help contribute to a different component of Ethereum security.", "page-run-a-node-staking-link": "Stake ETH", "page-run-a-node-staking-plans-title": "Plan on staking?", "page-run-a-node-staking-plans-description": "To maximize the efficiency of your validator, a minimum of 16 GB RAM is recommended, but 32 GB is better, with a CPU benchmark score of 6667+ on <a href=\"https://cpubenchmark.net\" target=\"_blank\">cpubenchmark.net</a>. It is also recommended that stakers have access to unlimited high-speed internet bandwidth, though this is not an absolute requirement.", "page-run-a-node-staking-plans-ethstaker-link-label": "How to shop for Ethereum validator hardware", "page-run-a-node-staking-plans-ethstaker-link-description": "<PERSON>th<PERSON><PERSON> goes into more detail in this hour long special", "page-run-a-node-sovereignty-title": "Sovereignty", "page-run-a-node-sovereignty-preview": "Think of running a node like the next step beyond getting your own Ethereum wallet.", "page-run-a-node-sovereignty-1": "An Ethereum wallet allows you to take full custody and control of your digital assets by holding the private keys to your addresses, but those keys don't tell you the current state of the blockchain, such as your wallet balance.", "page-run-a-node-sovereignty-2": "By default, Ethereum wallets typically reach out to a 3rd-party node, such as Infura or Alchemy, when looking up your balances. Running your own node allows you to have your own copy of the Ethereum blockchain.", "page-run-a-node-title": "Run a node", "page-run-a-node-voice-your-choice-title": "Voice your choice", "page-run-a-node-voice-your-choice-preview": "Don't give up control in the event of a fork.", "page-run-a-node-voice-your-choice-1": "In the event of a chain fork, where two chains emerge with two different sets of rules, running your own node guarantees your ability to choose which set of rules you support. It's up to you to upgrade to new rules and support proposed changes, or not.", "page-run-a-node-voice-your-choice-2": "If you're staking ETH, running your own node allows you to chose your own client, to minimize your risk of slashing and to react to fluctuating demands of the network over time. Staking with a third party forfeits your vote on which client you think is the best choice.", "page-run-a-node-what-title": "<i>What</i> does it mean to \"run a node\"?", "page-run-a-node-what-1-subtitle": "Run software.", "page-run-a-node-what-1-text": "Known as a 'client', this software downloads a copy of the Ethereum blockchain and verifies the validity of every block, then keeps it up-to-date with new blocks and transactions, and helps others download and update their own copies.", "page-run-a-node-what-2-subtitle": "With hardware.", "page-run-a-node-what-2-text": "Ethereum is designed to run a node on average consumer-grade computers. You can use any personal computer, but most users opt to run their node on dedicated hardware to eliminate the performance impact on their machine and minimize node downtime.", "page-run-a-node-what-3-subtitle": "While online.", "page-run-a-node-what-3-text": "Running an Ethereum node may sound complicated at first, but it's merely the act of continuously running client software on a computer while connected to the internet. While offline, your node will simply be inactive until it gets back online and catches up with the latest changes.", "page-run-a-node-who-title": "<i>Who</i> should run a node?", "page-run-a-node-who-preview": "Everyone! Nodes are not just for miners and validators. <i>Anyone</i> can run a node—you don't even need ETH.", "page-run-a-node-who-copy-1": "You don't need to stake ETH or be a miner to run a node. In fact, it's every other node on Ethereum that holds miners and validators accountable.", "page-run-a-node-who-copy-2": "You may not get the financial rewards that validators and miners earn, but there are many other benefits of running a node for any Ethereum user to consider, including privacy, security, reduced reliance on third-party servers, censorship resistance and improved health and decentralization of the network.", "page-run-a-node-who-copy-3": "Having your own node means you don't need to trust information about the state of the network provided by a third party.", "page-run-a-node-who-copy-bold": "Don't trust. Verify.", "page-run-a-node-why-title": "<i>Why</i> run a node?", "page-stablecoins-accordion-borrow-crypto-collateral": "Crypto collateral", "page-stablecoins-accordion-borrow-crypto-collateral-copy": "With Ethereum you can borrow directly from other users without trading away your ETH. This can give you leverage – some do this to try to accumulate more ETH.", "page-stablecoins-accordion-borrow-crypto-collateral-copy-p2": "But because ETH’s price is volatile, you’ll need to overcollateralise. That means if you want to borrow 100 stablecoins you’ll probably need at least $150 worth of ETH. This protects the system and the lenders.", "page-stablecoins-accordion-borrow-crypto-collateral-link": "More on crypto-backed stablecoins", "page-stablecoins-accordion-borrow-pill": "Advanced", "page-stablecoins-accordion-borrow-places-intro": "These dapps let you borrow stablecoins using crypto as collateral. Some accept other tokens as well as ETH.", "page-stablecoins-accordion-borrow-places-title": "Places to borrow stablecoins", "page-stablecoins-accordion-borrow-requirement-1": "An Ethereum wallet", "page-stablecoins-accordion-borrow-requirement-1-description": "You’ll need a wallet to use a dapp", "page-stablecoins-accordion-borrow-requirement-2": "<PERSON>ther (ETH)", "page-stablecoins-accordion-borrow-requirement-2-description": "You'll need ETH for collateral and/or transaction fees", "page-stablecoins-accordion-borrow-requirements-description": "To borrow stablecoins you'll need to use the right dapp. You'll also need a wallet and some ETH.", "page-stablecoins-accordion-borrow-risks-copy": "If you use ETH as collateral and its value drops, your collateral won’t cover the stablecoins you generated. This will cause your ETH to liquidate and you may face a penalty. So if you borrow stablecoins you’ll need to watch the ETH price.", "page-stablecoins-accordion-borrow-risks-link": "Latest ETH price", "page-stablecoins-accordion-borrow-risks-title": "Risks", "page-stablecoins-accordion-borrow-text-preview": "You can borrow some stablecoins by using crypto as collateral, which you have to pay back.", "page-stablecoins-accordion-borrow-title": "Borrow", "page-stablecoins-accordion-buy-exchanges-title": "Popular exchanges", "page-stablecoins-accordion-buy-requirement-1": "Crypto exchanges and wallets", "page-stablecoins-accordion-buy-requirement-1-description": "Check the services you can use where you live", "page-stablecoins-accordion-buy-requirements-description": "An account with an exchange or a wallet you can buy crypto from directly. You may have already used one to get some ETH. Check to see which services you can use where you live.", "page-stablecoins-accordion-buy-text-preview": "A lot of exchanges and wallets let you buy stablecoins directly. Geographical restrictions will apply.", "page-stablecoins-accordion-buy-title": "Buy", "page-stablecoins-accordion-buy-warning": "Centralized exchanges may only list fiat-backed stablecoins like USDC, Tether, and others. You may not be able to buy them directly, but you should be able to exchange them from ETH or other cryptocurrencies that you can buy on the platform.", "page-stablecoins-accordion-earn-project-1-description": "Mostly technical work for the open-source software movement.", "page-stablecoins-accordion-earn-project-2-description": "Technology, content, and other work for the MakerDao community (the team that brought you <PERSON>).", "page-stablecoins-accordion-earn-project-3-description": "When you really know your stuff, find bugs to earn Dai.", "page-stablecoins-accordion-earn-project-bounties": "Gitcoin bounties", "page-stablecoins-accordion-earn-project-bug-bounties": "Consensus layer bug bounties", "page-stablecoins-accordion-earn-project-community": "MakerDao community", "page-stablecoins-accordion-earn-projects-copy": "These are platforms that will pay you in stablecoins for your work.", "page-stablecoins-accordion-earn-projects-title": "Where to earn stablecoins", "page-stablecoins-accordion-earn-requirement-1": "An Ethereum wallet", "page-stablecoins-accordion-earn-requirement-1-description": "You’ll need a wallet to receive your earned stablecoins", "page-stablecoins-accordion-earn-requirements-description": "Stablecoins are a great method of payment for work and services because the value is stable. But you'll need a wallet to be paid.", "page-stablecoins-accordion-earn-text-preview": "You can earn stablecoins by working on projects within the Ethereum ecosystem.", "page-stablecoins-accordion-earn-title": "<PERSON><PERSON><PERSON>", "page-stablecoins-accordion-less": "Less", "page-stablecoins-accordion-more": "More", "page-stablecoins-accordion-requirements": "What you'll need", "page-stablecoins-accordion-swap-dapp-intro": "If you’ve already got ETH and a wallet, you can use these dapps to swap for stablecoins.", "page-stablecoins-accordion-swap-dapp-link": "More on decentralized exchanges", "page-stablecoins-accordion-swap-dapp-title": "Dapps for swapping tokens", "page-stablecoins-accordion-swap-editors-tip": "Editors' tip", "page-stablecoins-accordion-swap-editors-tip-button": "Find wallets", "page-stablecoins-accordion-swap-editors-tip-copy": "Get yourself a wallet that will let you buy ETH and swap it for tokens, including stablecoins, directly.", "page-stablecoins-accordion-swap-pill": "Recommended", "page-stablecoins-accordion-swap-requirement-1": "An Ethereum wallet", "page-stablecoins-accordion-swap-requirement-1-description": "You’ll need a wallet to authorise the swap and store your coins", "page-stablecoins-accordion-swap-requirement-2": "<PERSON>ther (ETH)", "page-stablecoins-accordion-swap-requirement-2-description": "To pay for the swap", "page-stablecoins-accordion-swap-text-preview": "You can pick up most stablecoins on decentralized exchanges. So you can swap any tokens you might have for a stablecoin you want.", "page-stablecoins-accordion-swap-title": "<PERSON><PERSON><PERSON>", "page-stablecoins-algorithmic": "Algorithmic", "page-stablecoins-algorithmic-con-1": "You need to trust (or be able to read) the algorithm.", "page-stablecoins-algorithmic-con-2": "Your balance of coins will change based on total supply.", "page-stablecoins-algorithmic-description": "These stablecoins aren't backed by any other asset. Instead an algorithm will sell tokens if the price falls below the desired value and supply tokens if the value goes beyond the desired amount. Because the number of these tokens in circulation changes regularly, the number of tokens you own will change, but will always reflect your share.", "page-stablecoins-algorithmic-pro-1": "No collateral needed.", "page-stablecoins-algorithmic-pro-2": "Controlled by a public algorithm.", "page-stablecoins-bank-apy": "0.05%", "page-stablecoins-bank-apy-source": "The average rate paid by banks on basic, federally insured savings accounts, USA.", "page-stablecoins-bank-apy-source-link": "Source", "page-stablecoins-bitcoin-pizza": "The infamous Bitcoin pizza", "page-stablecoins-bitcoin-pizza-body": "In 2010, someone bought 2 pizzas for 10,000 bitcoin. At the time these were worth ~$41 USD. In today’s market that’s millions of dollars. There are many similar regretful transactions in Ethereum’s history. Stablecoins solve this problem, so you can enjoy your pizza and hold on to your ETH.", "page-stablecoins-coin-price-change": "Coin price change (last 30 days)", "page-stablecoins-crypto-backed": "Crypto backed", "page-stablecoins-crypto-backed-con-1": "Less stable than fiat-backed stablecoins.", "page-stablecoins-crypto-backed-con-2": "You need to keep an eye on the value of the crypto collateral.", "page-stablecoins-crypto-backed-description": "These stablecoins are backed by other crypto assets, like ETH. Their price depends on the value of the underlying asset (or collateral), which can be volatile. Because ETH's value can fluctuate, these stablecoins are overcollateralised to ensure the price stays as stable as possible. This means it's closer to say that a $1 crypto backed stablecoin has an underlying crypto asset worth at least $2. So if the price of ETH drops, more ETH must be used to back the stablecoin, else the stablecoins will lose their value.", "page-stablecoins-crypto-backed-pro-1": "Transparent and fully decentralized.", "page-stablecoins-crypto-backed-pro-2": "Quick to turn into other crypto assets.", "page-stablecoins-crypto-backed-pro-3": "No external custodians – all assets are controlled by Ethereum accounts.", "page-stablecoins-dai-banner-body": "Dai is probably the most famous decentralized stablecoin. Its value is roughly a dollar and it’s accepted widely across dapps.", "page-stablecoins-dai-banner-learn-button": "Learn about Dai", "page-stablecoins-dai-banner-swap-button": "Swap ETH for Dai", "page-stablecoins-dai-banner-title": "Dai", "page-stablecoins-dai-logo": "The Dai logo", "page-stablecoins-editors-choice": "Editors' choices", "page-stablecoins-editors-choice-intro": "These are probably the best-known examples of stablecoins right now and the coins we've found useful when using dapps.", "page-stablecoins-explore-dapps": "Explore dapps", "page-stablecoins-fiat-backed": "Fiat backed", "page-stablecoins-fiat-backed-con-1": "Centralized – someone must issue the tokens.", "page-stablecoins-fiat-backed-con-2": "Requires auditing to ensure company has sufficient reserves.", "page-stablecoins-fiat-backed-description": "Basically an IOU (I owe you) for a traditional fiat currency (usually dollars). You use your fiat currency to purchase a stablecoin that you can later cash-in and redeem for your original currency.", "page-stablecoins-fiat-backed-pro-1": "Safe against crypto volatility.", "page-stablecoins-fiat-backed-pro-2": "Changes in price are minimal.", "page-stablecoins-find-stablecoin": "Find a stablecoin", "page-stablecoins-find-stablecoin-how-to-get-them": "How to get stablecoins", "page-stablecoins-find-stablecoin-intro": "There are hundreds of stablecoins available. Here are some to help you get started. If you're new to Ethereum, we recommend doing some research first.", "page-stablecoins-find-stablecoin-types-link": "Different stablecoin types", "page-stablecoins-get-stablecoins": "How to get stablecoins", "page-stablecoins-hero-alt": "The three biggest stablecoins by market cap: Dai, USDC, and Tether.", "page-stablecoins-hero-button": "Get stablecoins", "page-stablecoins-hero-header": "Digital money for everyday use", "page-stablecoins-hero-subtitle": "Stablecoins are Ethereum tokens designed to stay at a fixed value, even when the price of ETH changes.", "page-stablecoins-interest-earning-dapps": "Interest-earning dapps", "page-stablecoins-meta-description": "An introduction to Ethereum stablecoins: what they are, how to get them, and why they're important.", "page-stablecoins-precious-metals": "Precious metals", "page-stablecoins-precious-metals-con-1": "Centralized – someone must issue the tokens.", "page-stablecoins-precious-metals-con-2": "You need to trust the token issuer and the precious metal reserves.", "page-stablecoins-precious-metals-description": "Like fiat-backed coins, instead these stablecoins use resources like gold to maintain their value.", "page-stablecoins-precious-metals-pro-1": "Safe against crypto volatility.", "page-stablecoins-prices": "Stablecoin prices", "page-stablecoins-prices-definition": "Stablecoins are cryptocurrencies without the volatility. They share a lot of the same powers as ETH but their value is steady, more like a traditional currency. So you have access to stable money that you can use on Ethereum. ", "page-stablecoins-prices-definition-how": "How stablecoins get their stability", "page-stablecoins-research-warning": "Ethereum is a new technology and most applications are new. Make sure you're aware of the risk and only deposit what you can afford to lose.", "page-stablecoins-research-warning-title": "Always do your own research", "page-stablecoins-save-stablecoins": "Save with stablecoins", "page-stablecoins-save-stablecoins-body": "Stablecoins often have an above-average interest rate because there’s a lot of demand for borrowing them. There are dapps that let you earn interest on your stablecoins in real time by depositing them into a lending pool. Just like in the banking world, you're supplying tokens for borrowers but you can withdraw your tokens and your interest at any time.", "page-stablecoins-saving": "Put your stablecoin savings to good use and earn some interest. Like everything in crypto, the predicted Annual Percentage Yields (APY) can change day-to-day dependent on real-time supply/demand.", "page-stablecoins-stablecoins-dapp-callout-description": "Check out Ethereum’s dapps – stablecoins are often more useful for everyday transactions.", "page-stablecoins-stablecoins-dapp-callout-image-alt": "Illustration of a doge.", "page-stablecoins-stablecoins-dapp-callout-title": "Use your stablecoins", "page-stablecoins-stablecoins-dapp-description-1": "Markets for lots of stablecoins, including Dai, USDC, TUSD, USDT, and more. ", "page-stablecoins-stablecoins-dapp-description-2": "Lend stablecoins and earn interest and $COMP, Compound's own token.", "page-stablecoins-stablecoins-dapp-description-3": "A trading platform where you can earn interest on your Dai and USDC.", "page-stablecoins-stablecoins-dapp-description-4": "An app designed for saving Dai.", "page-stablecoins-stablecoins-feature-1": "Stablecoins are global, and can be sent over the internet. They're easy to receive or send once you have an Ethereum account.", "page-stablecoins-stablecoins-feature-2": "Demand for stablecoins is high, so you can earn interest for lending yours. Make sure you're aware of the risks before lending.", "page-stablecoins-stablecoins-feature-3": "Stablecoins are exchangeable for ETH and other Ethereum tokens. Lots of dapps rely on stablecoins.", "page-stablecoins-stablecoins-feature-4": "Stablecoins are secured by cryptography. No one can forge transactions on your behalf.", "page-stablecoins-stablecoins-meta-description": "An introduction to Ethereum stablecoins: what they are, how to get them, and why they're important.", "page-stablecoins-stablecoins-table-header-column-1": "<PERSON><PERSON><PERSON><PERSON>", "page-stablecoins-stablecoins-table-header-column-2": "Market capitalization", "page-stablecoins-stablecoins-table-header-column-3": "Collateral type", "page-stablecoins-stablecoins-table-type-crypto-backed": "Crypto", "page-stablecoins-stablecoins-table-type-fiat-backed": "Fiat", "page-stablecoins-stablecoins-table-type-precious-metals-backed": "Precious metals", "page-stablecoins-table-error": "Couldn't load stablecoins. Try refreshing the page.", "page-stablecoins-table-loading": "Loading stablecoin data...", "page-stablecoins-title": "Stablecoins", "page-stablecoins-top-coins": "Top stablecoins by market capitalisation", "page-stablecoins-top-coins-intro": "Market capitalisation is", "page-stablecoins-top-coins-intro-code": "the total number of tokens that exist multiplied by the value per token. This list is dynamic and the projects listed here are not necessarily endorsed by the ethstake.exchange team.", "page-stablecoins-types-of-stablecoin": "How they work: types of stablecoin", "page-stablecoins-usdc-banner-body": "USDc is probably the most famous fiat-backed stablecoin. Its value is roughly a dollar and it’s backed by Circle and Coinbase.", "page-stablecoins-usdc-banner-learn-button": "Learn about USDC", "page-stablecoins-usdc-banner-swap-button": "Swap ETH for USDC", "page-stablecoins-usdc-banner-title": "USDC", "page-stablecoins-usdc-logo": "The USDC logo", "page-stablecoins-why-stablecoins": "Why stablecoins?", "page-stablecoins-how-they-work-button": "How they work", "page-stablecoins-why-stablecoins-body": "ETH, like Bitcoin, has a volatile price because it's new technology. So you may not want to spend it regularly. Stablecoins mirror the value of traditional currencies to give you access to stable money that you can use on Ethereum.", "page-stablecoins-more-defi-button": "More on decentralized finance (DeFi)", "page-staking-deposit-contract-address": "Staking deposit contract address", "page-staking-deposit-contract-address-caption": "We've added spaces to make the address easier to read", "page-staking-deposit-contract-address-check-btn": "Check deposit contract address", "page-staking-deposit-contract-checkbox1": "I’ve already used the launchpad to set up my Ethereum validator.", "page-staking-deposit-contract-checkbox2": "I understand that I need to use the launchpad to stake. Simple transfers to this address won’t work.", "page-staking-deposit-contract-checkbox3": "I'm going to check the deposit contract address with other sources.", "page-staking-deposit-contract-confirm-address": "Confirm to reveal address", "page-staking-deposit-contract-copied": "Copied address", "page-staking-deposit-contract-copy": "Copy address", "page-staking-deposit-contract-etherscan": "View contract on Etherscan", "page-staking-deposit-contract-h2": "This is not where you stake", "page-staking-deposit-contract-launchpad": "Stake using launchpad", "page-staking-deposit-contract-launchpad-2": "Use launchpad", "page-staking-deposit-contract-meta-desc": "Verify the deposit contract address for Ethereum staking.", "page-staking-deposit-contract-meta-title": "Ethereum staking deposit contract address", "page-staking-deposit-contract-read-aloud": "Read address aloud", "page-staking-deposit-contract-reveal-address-btn": "Reveal address", "page-staking-deposit-contract-staking": "To stake your ETH you must use the dedicated launchpad product and follow the instructions. Sending ETH to the address on this page will not make you a staker and will result in a failed transaction.", "page-staking-deposit-contract-staking-check": "Check these sources", "page-staking-deposit-contract-staking-check-desc": "We expect there to be a lot of fake addresses and scams out there. To be safe, check the staking contract address you're using against the address on this page. We recommend checking it with other trustworthy sources too.", "page-staking-deposit-contract-staking-more-link": "More on staking", "page-staking-deposit-contract-stop-reading": "Stop reading", "page-staking-deposit-contract-subtitle": "This is the address for the Ethereum staking contract. Use this page to confirm you’re sending funds to the correct address when you stake.", "page-staking-deposit-contract-warning": "Check each character carefully.", "page-staking-deposit-contract-warning-2": "Sending funds to this address won’t work and won’t make you a staker. You must follow the launchpad instructions.", "page-staking-just-staking": "Staking", "page-staking-image-alt": "Image of the Rhino mascot for the staking launchpad.", "page-staking-51-desc-1": "Staking makes joining the network as a validator more accessible so it’s likely that there’ll be more validators in the network than exists today. This will make this kind of attack even harder as the cost of an attack will increase.", "page-staking-accessibility": "More accessible", "page-staking-accessibility-desc": "With easier hardware requirements and the opportunity to pool if you don’t have 32 ETH, more people will be able to join the network. This will make Ethereum more decentralized and secure by decreasing the attack surface area.", "page-staking-at-stake": "Your ETH is at stake", "page-staking-at-stake-desc": "Because you have to stake your ETH in order to validate transactions and create new blocks, you can lose it if you decide to try and cheat the system.", "page-staking-benefits": "Benefits of staking to Ethereum", "page-staking-check-address": "Check deposit address", "page-staking-consensus": "More on consensus mechanisms", "page-staking-deposit-address": "Check the deposit address", "page-staking-deposit-address-desc": "If you’ve already followed the setup instructions on the launchpad, you’ll know you need to send a transaction to the staking deposit contract. We recommend you check the address very carefully. You can find the official address on ethstake.exchange and a number of other trusted sites.", "page-staking-desc-1": "Rewards are given for actions that help the network reach consensus. You'll get rewards for batching transactions into a new block or checking the work of other validators because that's what keeps the chain running securely.", "page-staking-desc-2": "Although you can earn rewards for doing work that benefits the network, you can lose ETH for malicious actions, going offline, and failing to validate.", "page-staking-desc-3": "You'll need 32 ETH to become a full validator or some ETH to join a staking pool. You'll also need to run an execution client (formerly 'Eth1 client'). The launchpad will walk you through the process and hardware requirements. Alternatively, you can use a backend API.", "page-staking-description": "Staking is the act of depositing 32 ETH to activate validator software. As a validator you’ll be responsible for storing data, processing transactions, and adding new blocks to the blockchain. This will keep Ethereum secure for everyone and earn you new ETH in the process. This process, known as proof-of-stake, is being introduced by the Beacon Chain.", "page-staking-docked": "More on The Merge", "page-staking-dyor": "Do your own research", "page-staking-dyor-desc": "None of the listed staking services are official endorsements. Be sure to do some research to figure out which service might be best for you.", "page-staking-header-1": "Stake your ETH to become an Ethereum validator", "page-staking-how-much": "How much are you willing to stake?", "page-staking-how-to-stake": "How to stake", "page-staking-how-to-stake-desc": "It all depends on how much you are willing to stake. You'll need 32 to become a full validator, but it is possible to stake less.", "page-staking-join": "Join", "page-staking-join-community": "Join the staker community", "page-staking-join-community-desc": "r/ethstaker is a community for everyone to discuss staking on Ethereum – join for advice, support, and to talk all thing staking.", "page-staking-less-than": "Less than", "page-staking-link-1": "View backend APIs", "page-staking-meta-description": "An overview of Ethereum staking: the risks, rewards, requirements, and where to do it.", "page-staking-meta-title": "Ethereum staking", "page-staking-more-sharding": "More on sharding", "page-staking-pool": "Stake in a pool", "page-staking-pool-desc": "If you have less than 32 ETH, you will be able to add a smaller stake to staking pools. Some companies can do it all on your behalf so you won't have to worry about staying online. Here are some companies to check out.", "page-staking-rocket-pool": "You can stake on Rocket Pool by swapping ETH for Rocket Pool's liquid token rETH with as little as 0.01 ETH. Want to run a node? You can also run a staking node on Rocket Pool starting from 16 ETH. The remaining ETH gets assigned from the Rocket Pool protocol, using ETH users have deposited to the pool.", "page-staking-pos-explained": "Proof-of-stake explained", "page-staking-pos-explained-desc": "Staking is what you need to do to become a validator in a proof-of-stake system. This is a consensus mechanism that is going to replace the proof-of-work system currently in place. Consensus mechanisms are what keep blockchains like Ethereum secure and decentralized.", "page-staking-pos-explained-desc-1": "Proof-of-stake helps secure the network in a number of ways:", "page-staking-services": "See all staking services", "page-staking-sharding": "Unlocks sharding", "page-staking-sharding-desc": "Sharding is only possible with a proof-of-stake system. Sharding a proof-of-work system would dilute the amount of computing power needed to corrupt the network, making it easier for malicious miners to control shards. This isn’t the case with randomly-assigned stakers in proof-of-stake.", "page-staking-solo": "Stake solo and run a validator", "page-staking-solo-desc": "To begin the staking process, you’ll need to use the staking launchpad. This will walk you through all the setup. Part of staking is running a consensus client, which is a local copy of the blockchain. This can take a while to download onto your computer.", "page-staking-start": "Get Staking Certificate", "page-staking-subtitle": "Staking is a public good for the Ethereum ecosystem. You can help secure the network and earn rewards in the process.", "page-staking-sustainability": "More sustainable", "page-staking-sustainability-desc": "Validators don’t need energy-intensive computers in order to participate in a proof-of-stake system – just a laptop or smartphone. This will make Ethereum better for the environment.", "page-staking-the-beacon-chain": "More on the Beacon Chain", "page-staking-title-1": "Rewards", "page-staking-title-2": "Risks", "page-staking-title-3": "Requirements", "page-staking-title-4": "How to stake your ETH", "page-staking-upgrades-li": "Proof-of-stake is managed by the Beacon Chain.", "page-staking-upgrades-li-2": "Ethereum will have a proof-of-stake Beacon Chain and a proof-of-work Mainnet for the foreseeable future. Mainnet is the Ethereum we've been using for years.", "page-staking-upgrades-li-3": "During this time, stakers will be adding new blocks to the Beacon Chain but not processing mainnet transactions.", "page-staking-upgrades-li-4": "Ethereum will fully transition to a proof-of-stake system once the Ethereum mainnet merges with the Beacon Chain.", "page-staking-upgrades-li-5": "A minor upgrade will follow to enable withdraw of staked funds.", "page-staking-upgrades-title": "Proof-of-stake and consensus upgrades", "page-staking-validators": "More validators, more security", "page-staking-validators-desc": "In a blockchain like Ethereum it is possible to censor and reorder transactions to suit you if you control a majority of the network. But, to control a majority of the network, you need a majority of validators, and for this you’d need to control a majority of the ETH in the system – that’s a lot! This amount of ETH grows every time a new validator enters the system, bolstering the security of the network. Proof-of-work, the security model proof-of-stake will replace, requires the validators (miners) to have specialist hardware and lots of physical space – entering the system as a miner is difficult so security against majority attacks doesn't increase as much. Proof-of-stake doesn't have these requirements, which should grow the network (and its resistance to majority attacks) to sizes that aren't possible with proof-of-work.", "page-staking-withdrawals": "Withdrawals won't be live right away", "page-staking-withdrawals-desc": "You won't be able to withdraw your stake until future upgrades are deployed. Withdrawals will be available in a minor upgrade following The Merge of Mainnet with the Beacon Chain.", "page-upgrades-bug-bounty-annotated-specs": "annotated spec", "page-upgrades-bug-bounty-annotations": "It might be helpful to check out the following annotations:", "page-upgrades-bug-bounty-client-bugs": "Consensus layer client bugs", "page-upgrades-bug-bounty-client-bugs-desc": "The clients will run the Beacon Chain once the upgrade has been deployed. Clients will need to follow the logic set out in the specification and be secure against potential attacks. The bugs we want to find are related to the implementation of the protocol.", "page-upgrades-bug-bounty-client-bugs-desc-2": "Currently Lighthouse, Nimbus, Teku, and Prysm bugs are eligible for the full bounty rewards. Lodestar is also eligible, but until further audits have been completed the points and rewards are limited to 10% (max payout is 5,000 DAI). More clients may be added as they complete audits and become production ready.", "page-upgrades-bug-bounty-clients": "Clients featured in the bounties", "page-upgrades-bug-bounty-clients-type-1": "Spec non-compliance issues", "page-upgrades-bug-bounty-clients-type-2": "Unexpected crashes or denial of service (DOS) vulnerabilities", "page-upgrades-bug-bounty-clients-type-3": "Any issues causing irreparable consensus splits from the rest of the network", "page-upgrades-bug-bounty-docking": "merge", "page-upgrades-bug-bounty-email-us": "Email us:", "page-upgrades-bug-bounty-help-links": "Helpful links", "page-upgrades-bug-bounty-hunting": "Bug hunting rules", "page-upgrades-bug-bounty-hunting-desc": "The bug bounty program is an experimental and discretionary rewards program for our active Ethereum community to encourage and reward those who are helping to improve the platform. It is not a competition. You should know that we can cancel the program at any time, and awards are at the sole discretion of Ethereum Foundation bug bounty panel. In addition, we are not able to issue awards to individuals who are on sanctions lists or who are in countries on sanctions lists (e.g. North Korea, Iran, etc). You are responsible for all taxes. All awards are subject to applicable law. Finally, your testing must not violate any law or compromise any data that is not yours.", "page-upgrades-bug-bounty-hunting-leaderboard": "Bug hunting leaderboard", "page-upgrades-bug-bounty-hunting-leaderboard-subtitle": "Find consensus layer bugs to get added to this leaderboard", "page-upgrades-bug-bounty-hunting-li-1": "Issues that have already been submitted by another user or are already known to spec and client maintainers are not eligible for bounty rewards.", "page-upgrades-bug-bounty-hunting-li-2": "Public disclosure of a vulnerability makes it ineligible for a bounty.", "page-upgrades-bug-bounty-hunting-li-3": "Ethereum Foundation researchers and employees of consensus layer client teams are not eligible for rewards.", "page-upgrades-bug-bounty-hunting-li-4": "Ethereum bounty program considers a number of variables in determining rewards. Determinations of eligibility, score and all terms related to an award are at the sole and final discretion of the Ethereum Foundation bug bounty panel.", "page-upgrades-bug-bounty-leaderboard": "See full leaderboard", "page-upgrades-bug-bounty-leaderboard-points": "points", "page-upgrades-bug-bounty-ledger-desc": "The Beacon Chain specification details the design rationale and proposed changes to Ethereum via the Beacon Chain upgrade.", "page-upgrades-bug-bounty-ledger-title": "The Beacon Chain specification bugs", "page-upgrades-bug-bounty-meta-description": "An overview of the consensus layer bug bounty program: how to get involved and reward information.", "page-upgrades-bug-bounty-meta-title": "Consensus layer bug hunting bounty program", "page-upgrades-bug-bounty-not-included": "Not included", "page-upgrades-bug-bounty-not-included-desc": "The Merge and shard chain upgrades are still in active development and so are not yet included as part of this bounty program.", "page-upgrades-bug-bounty-owasp": "View OWASP method", "page-upgrades-bug-bounty-points": "The EF will also award points based on:", "page-upgrades-bug-bounty-points-error": "Error loading data... please refresh.", "page-upgrades-bug-bounty-points-exchange": "Points Exchange", "page-upgrades-bug-bounty-points-loading": "Loading data...", "page-upgrades-bug-bounty-points-payout-desc": "The Ethereum Foundation will pay out the value of USD in ETH or DAI.", "page-upgrades-bug-bounty-points-point": "1 point", "page-upgrades-bug-bounty-points-rights-desc": "The Ethereum Foundation reserves the right to change this without prior notice.", "page-upgrades-bug-bounty-points-usd": "2 USD", "page-upgrades-bug-bounty-quality": "Quality of description", "page-upgrades-bug-bounty-quality-desc": ": Higher rewards are paid for clear, well-written submissions.", "page-upgrades-bug-bounty-quality-fix": "<strong>Quality of fix</strong>, if included: Higher rewards are paid for submissions with clear description of how to fix the issue.", "page-upgrades-bug-bounty-quality-repro": "Quality of reproducibility", "page-upgrades-bug-bounty-quality-repro-desc": ": Please include test code, scripts and detailed instructions. The easier it is for us to reproduce and verify the vulnerability, the higher the reward.", "page-upgrades-bug-bounty-questions": "Questions?", "page-upgrades-bug-bounty-rules": "Read rules", "page-upgrades-bug-bounty-shard-chains": "shard chains", "page-upgrades-bug-bounty-slogan": "Consensus layer bug bounties", "page-upgrades-bug-bounty-specs": "Read the full spec", "page-upgrades-bug-bounty-specs-docs": "Specification documents", "page-upgrades-bug-bounty-submit": "Submit a bug", "page-upgrades-bug-bounty-submit-desc": "For each bug you find you’ll be rewarded points. The points you earn depend on the severity of the bug. Lodestar bugs are currently being awarded 10% of points listed below, as additional audits are under way to be completed. The Ethereum Foundation (EF) determine severity using the OWASP method.", "page-upgrades-bug-bounty-subtitle": "Earn up to $50,000 USD and a place on the leaderboard by finding consensus layer protocol and client bugs.", "page-upgrades-bug-bounty-title": "Open for submissions", "page-upgrades-bug-bounty-title-1": "Beacon Chain", "page-upgrades-bug-bounty-title-2": "Fork choice", "page-upgrades-bug-bounty-title-3": "Solidity deposit contract", "page-upgrades-bug-bounty-title-4": "Peer-to-peer networking", "page-upgrades-bug-bounty-type-1": "Safety/finality-breaking bugs", "page-upgrades-bug-bounty-type-2": "Denial of service (DOS) vectors", "page-upgrades-bug-bounty-type-3": "Inconsistencies in assumptions, like situations where honest validators can be slashed", "page-upgrades-bug-bounty-type-4": "Calculation or parameter inconsistencies", "page-upgrades-bug-bounty-types": "Types of bugs", "page-upgrades-bug-bounty-validity": "Valid bugs", "page-upgrades-bug-bounty-validity-desc": "This bug bounty program is focused on finding bugs in the core consensus layer Beacon Chain specification and the Lighthouse, Nimbus, Teku, Prysm, and Lodestar client implementations.", "page-upgrades-bug-bounty-card-critical": "Critical", "page-upgrades-bug-bounty-card-critical-risk": "Submit critical risk bug", "page-upgrades-bug-bounty-card-h2": "Medium", "page-upgrades-bug-bounty-card-high": "High", "page-upgrades-bug-bounty-card-high-risk": "Submit high risk bug", "page-upgrades-bug-bounty-card-label-1": "Up to 1,000 points", "page-upgrades-bug-bounty-card-label-2": "Up to 2,000 DAI", "page-upgrades-bug-bounty-card-label-3": "Up to 5,000 points", "page-upgrades-bug-bounty-card-label-4": "Up to 10,000 DAI", "page-upgrades-bug-bounty-card-label-5": "Up to 10,000 points", "page-upgrades-bug-bounty-card-label-6": "Up to 20,000 DAI", "page-upgrades-bug-bounty-card-label-7": "Up to 25,000 points", "page-upgrades-bug-bounty-card-label-8": "Up to 50,000 DAI", "page-upgrades-bug-bounty-card-li-1": "Low impact, medium likelihood", "page-upgrades-bug-bounty-card-li-2": "Medium impact, low likelihood", "page-upgrades-bug-bounty-card-li-3": "High impact, low likelihood", "page-upgrades-bug-bounty-card-li-4": "Medium impact, medium likelihood", "page-upgrades-bug-bounty-card-li-5": "Low impact, high likelihood", "page-upgrades-bug-bounty-card-li-6": "High impact, medium likelihood", "page-upgrades-bug-bounty-card-li-7": "Medium impact, high likelihood", "page-upgrades-bug-bounty-card-li-8": "High impact, high likelihood", "page-upgrades-bug-bounty-card-low": "Low", "page-upgrades-bug-bounty-card-low-risk": "Submit low risk bug", "page-upgrades-bug-bounty-card-medium-risk": "Submit medium risk bug", "page-upgrades-bug-bounty-card-subheader": "Severity", "page-upgrades-bug-bounty-card-subheader-2": "Example", "page-upgrades-bug-bounty-card-text": "Attacker can sometimes put a node in a state that causes it to drop one out of every one hundred attestations made by a validator", "page-upgrades-bug-bounty-card-text-1": "Attacker can successfully conduct eclipse attacks on nodes with peer-ids with 4 leading zero bytes", "page-upgrades-bug-bounty-card-text-2": "There is a consensus bug between two clients, but it is difficult or impractical for the attacker to trigger the event.", "page-upgrades-bug-bounty-card-text-3": "There is a consensus bug between two clients, and it is trivial for an attacker to trigger the event.", "page-upgrades-get-involved-btn-1": "See clients", "page-upgrades-get-involved-btn-2": "More on staking", "page-upgrades-get-involved-btn-3": "Find bugs", "page-upgrades-get-involved-bug": "A bug might be:", "page-upgrades-get-involved-bug-hunting": "Go bug hunting", "page-upgrades-get-involved-bug-hunting-desc": "Find and report bugs in consensus layer upgrade specifications or the clients themselves. You can earn up to $50,000 USD and earn a place on the leaderboard.", "page-upgrades-get-involved-bug-li": "specification non-compliance issues", "page-upgrades-get-involved-bug-li-2": "finality breaking bugs", "page-upgrades-get-involved-bug-li-3": "denial of service (DOS) vectors", "page-upgrades-get-involved-bug-li-4": "and more...", "page-upgrades-get-involved-date": "Closing date: December 23, 2020", "page-upgrades-get-involved-desc-1": "Running a client means you'll be an active participant in Ethereum. Your client will help keep track of transactions and check new blocks.", "page-upgrades-get-involved-desc-2": "If you have ETH, you can stake it to become a validator and help secure the network. As a validator you can earn ETH rewards.", "page-upgrades-get-involved-desc-3": "Join the community testing effort! Help test Ethereum upgrades before they're shipped, find bugs, and earn rewards.", "page-upgrades-get-involved-ethresearch-1": "Sharding", "page-upgrades-get-involved-ethresearch-2": "The Merge", "page-upgrades-get-involved-ethresearch-3": "Sharded execution", "page-upgrades-get-involved-ethresearch-4": "All research topics", "page-upgrades-get-involved-grants": "Staking community grants", "page-upgrades-get-involved-grants-desc": "Help create tooling and educational content for the staking community", "page-upgrades-get-involved-how": "How do you want to get involved?", "page-upgrades-get-involved-how-desc": "The Ethereum community will always benefit from more folks running clients, staking, and bug hunting.", "page-upgrades-get-involved-join": "Join the research", "page-upgrades-get-involved-join-desc": "Like most things with Ethereum, a lot of the research is public. This means you can take part in the discussions or just read through what the Ethereum researchers have to say. ethresear.ch covers a number of topics including consensus upgrades, sharding, rollups and more.", "page-upgrades-get-involved-meta-description": "How to participate in Ethereum upgrades: run nodes, stake, hunt bugs and more.", "page-upgrades-get-involved-more": "More info", "page-upgrades-get-involved-run-clients": "Run consensus clients", "page-upgrades-get-involved-run-clients-desc": "Key to Ethereum's long term security is a strong distribution of clients. A client is software that runs the blockchain, checking transactions and the creation of new blocks. Each client has its own features, so choose one based on what you're comfortable with.", "page-upgrades-get-involved-run-clients-desc-2": "These clients were formerly referred to as 'Eth2' clients, but this term is being deprecated in favor of \"consensus layer clients.\"", "page-upgrades-get-involved-run-clients-production": "Production consensus clients", "page-upgrades-get-involved-run-clients-experimental": "Experimental consensus clients", "page-upgrades-get-involved-stake": "Stake your ETH", "page-upgrades-get-involved-stake-desc": "You can now stake your ETH to help secure the beacon chain.", "page-upgrades-get-involved-stake-eth": "Stake ETH", "page-upgrades-get-involved-subtitle": "Here's all the ways you can help with Ethereum and future upgrade-related efforts.", "page-upgrades-get-involved-title-1": "Run a client", "page-upgrades-get-involved-title-2": "Stake your ETH", "page-upgrades-get-involved-title-3": "Find bugs", "page-upgrades-get-involved-written-go": "Written in Go", "page-upgrades-get-involved-written-java": "Written in Java", "page-upgrades-get-involved-written-javascript": "Written in JavaScript", "page-upgrades-get-involved-written-net": "Written in .NET", "page-upgrades-get-involved-written-nim": "Written in Nim", "page-upgrades-get-involved-written-python": "Written in Python", "page-upgrades-get-involved-written-rust": "Written in Rust", "consensus-client-lighthouse-logo-alt": "Lighthouse logo", "consensus-client-lodestar-logo-alt": "Lodestar logo", "consensus-client-nimbus-logo-alt": "Nimbus logo", "consensus-client-prysm-logo-alt": "Prysm logo", "consensus-client-teku-logo-alt": "Teku logo", "consensus-client-under-review": "Review and audit in progress", "page-upgrades-answer-1": "Think of the changes being rolled out as a set of upgrades being added to improve the Ethereum we use today. These upgrades included the creation of a new chain called the Beacon Chain, and will introduce new chains known as 'shards' in the future.", "page-upgrades-answer-2": "Some upgrades are separate from the Ethereum Mainnet we use today but won't replace it. Instead Mainnet will 'merge' with this parallel system that's being added over time.", "page-upgrades-answer-4": "In other words the Ethereum we use today will eventually embody all the features that we're aiming towards in the Ethereum vision.", "page-upgrade-article-title-two-point-oh": "Two Point Oh: The Beacon Chain", "page-upgrade-article-title-beacon-chain-explainer": "The Beacon Chain Ethereum 2.0 explainer you need to read first", "page-upgrade-article-title-sharding-consensus": "Sharding consensus", "page-upgrade-article-author-ethereum-foundation": "Ethereum Foundation", "page-upgrade-article-title-sharding-is-great": "Why sharding is great: demystifying the technical properties", "page-upgrade-article-title-rollup-roadmap": "A rollup-centric roadmap", "page-upgrades-beacon-chain-btn": "More on the Beacon Chain", "page-upgrades-beacon-chain-date": "The Beacon Chain went live on December 1, 2020.", "page-upgrades-beacon-chain-desc": "The Beacon Chain brought staking to Ethereum, laid the groundwork for future upgrades, and will eventually coordinate the new system.", "page-upgrades-beacon-chain-estimate": "The Beacon Chain is live", "page-upgrades-beacon-chain-title": "The Beacon Chain", "page-upgrades-bug-bounty": "View the bug bounty program", "page-upgrades-clients": "Check out the consensus clients (previously known as 'Eth2' clients)", "page-staking-deposit-contract-title": "Check the deposit contract address", "page-upgrades-diagram-ethereum-mainnet": "Ethereum Mainnet", "page-upgrades-diagram-h2": "How the upgrades fit together", "page-upgrades-diagram-link-1": "More on proof of work", "page-upgrades-diagram-link-2": "More on shard chains", "page-upgrades-diagram-mainnet": "Mainnet", "page-upgrades-diagram-p": "Ethereum Mainnet will continue to exist in its current form for a while. This means the Beacon Chain and shard upgrades won't disrupt the network.", "page-upgrades-diagram-p-1": "Mainnet will eventually merge with the new system introduced by the Beacon Chain.", "page-upgrades-diagram-p-2": "The <PERSON> Chain will become the conductor of Ethereum, coordinating validators and setting the pace for block creation.", "page-upgrades-diagram-p-3": "At first, it will exist separately from Mainnet and manage validators - it will have nothing to do with smart contracts, transactions, or accounts.", "page-upgrades-diagram-p-4": "Shards will provide lots of extra data to help increase the amount of transactions Mainnet can handle. They'll be coordinated by the Beacon Chain.", "page-upgrades-diagram-p-5": "But all transactions will still rely on Mainnet, which will continue to exist as we know it today – secured by proof-of-work and miners.", "page-upgrades-diagram-p-6": "Mainnet will merge with the proof-of-stake system, coordinated by the Beacon Chain.", "page-upgrades-diagram-p-8": "This will turn Mainnet into a shard within the new system. Miners will no longer be needed as all of Ethereum will be secured by validators.", "page-upgrades-diagram-p10": "Ethereum scaling is not a migration or a single thing. It describes a set of upgrades being worked on to unlock Ethereum's true potential. This is how they all fit together.", "page-upgrades-diagram-scroll": "Scroll to explore – tap for more information.", "page-upgrades-diagram-shard": "Shard (1)", "page-upgrades-diagram-shard-1": "<PERSON><PERSON> (...)", "page-upgrades-diagram-shard-2": "<PERSON><PERSON> (2)", "page-upgrades-diagram-shard-3": "<PERSON><PERSON> (...)", "page-upgrades-diagram-validators": "More on validators", "page-upgrades-dive": "Dive into the vision", "page-upgrades-dive-desc": "How are we going to make Ethereum more scalable, secure, and sustainable? All while keeping Ethereum's core ethos of decentralization.", "page-upgrades-docking": "The Merge", "page-upgrades-merge-answer-1": "The Merge is when Mainnet begins using the Beacon Chain for consensus, and proof-of-work is turned off, sometime in 2022.", "page-upgrades-merge-btn": "More on The Merge", "page-upgrades-merge-desc": "Mainnet Ethereum will need to 'merge' with the Beacon Chain at some point. This will enable staking for the entire network and signal the end of energy-intensive mining.", "page-upgrades-merge-estimate": "Estimate: 2022", "page-upgrades-merge-mainnet": "What's Mainnet?", "page-upgrades-merge-mainnet-eth2": "Merging Mainnet and the Beacon Chain", "page-upgrades-eth-blog": "ethstake.exchange blog", "page-upgrades-explore-btn": "Explore upgrades", "page-upgrades-get-involved": "Get involved in upgrading Ethereum", "page-upgrades-get-involved-2": "Get involved", "page-upgrades-head-to": "Head to", "page-upgrades-help": "Want to help with the Ethereum upgrades?", "page-upgrades-help-desc": "There are plenty of opportunities to weigh in on the Ethereum upgrades, help with testing, and even earn rewards.", "page-upgrades-index-staking": "Staking is here", "page-upgrades-index-staking-desc": "Key to the Ethereum upgrades is the introduction of staking. If you want to use your ETH to help secure the Ethereum network, make sure you follow these steps.", "page-upgrades-index-staking-learn": "Learn about staking", "page-upgrades-index-staking-learn-desc": "The Beacon Chain will bring staking to Ethereum. This means if you have ETH, you can do a public good by securing the network and earn more ETH in the process.", "page-upgrades-index-staking-step-1": "1. Set up with the launchpad", "page-upgrades-index-staking-step-1-btn": "Visit staking launchpad", "page-upgrades-index-staking-step-1-desc": "To stake on Ethereum you'll need to use the launchpad - this will walk you through the process.", "page-upgrades-index-staking-step-2": "2. Confirm staking address", "page-upgrades-index-staking-step-2-btn": "Confirm deposit contract address", "page-upgrades-index-staking-step-2-desc": "Before you stake your ETH, be sure to check you've got the right address. You must have gone through the launchpad before doing this.", "page-upgrades-index-staking-sustainability": "More sustainable", "page-upgrades-just-docking": "More on The Merge", "page-upgrades-meta-desc": "An overview of the Ethereum upgrades and the vision they hope to make a reality.", "page-upgrades-meta-title": "Ethereum upgrades (formerly 'Eth2')", "page-upgrades-miners": "More on miners", "page-upgrades-more-on-upgrades": "More on the Ethereum upgrades", "page-upgrades-proof-of-stake": "proof-of-stake", "page-upgrades-proof-of-work": "proof-of-work", "page-upgrades-proof-stake-link": "More on proof of stake", "page-upgrades-question-1-title": "When will the upgrades ship?", "page-upgrades-question-1-desc": "Ethereum is being upgraded progressively; the upgrades are distinct with different ship dates.", "page-upgrades-question-2-title": "Is the Beacon Chain a separate blockchain?", "page-upgrades-question-2-desc": "It's not accurate to think of these upgrades as a separate blockchain.", "page-upgrades-question-3-answer-2": "The Merge and shard chain upgrades may impact dapp developers. But the specifications have not been finalized yet, so there's no immediate action required.", "page-upgrades-question-3-answer-3": "Talk to the Ethereum research and development team over at ethresear.ch.", "page-upgrades-question-3-answer-3-link": "Visit ethresear.ch", "page-upgrades-question-3-desc": "You don't have to do anything right now to prepare for the upgrades.", "page-upgrades-question-3-title": "How do I prepare for the upgrades?", "page-upgrades-question-4-answer-1": "Whenever you send a transaction or use a dapp today, you're using the execution layer, or Mainnet. This is the Ethereum that is secured by miners.", "page-upgrades-question-4-answer-2": "Mainnet will continue to run as normal until The Merge.", "page-upgrades-question-4-answer-3": "After The Merge, validators will secure the entire network via proof-of-stake.", "page-upgrades-question-4-answer-6": "Anyone can become a validator by staking their ETH.", "page-upgrades-question-4-answer-7": "More on staking", "page-upgrades-question-4-answer-8": "The Beacon Chain and shard chain upgrades will not disrupt the execution layer (Mainnet) as they are being built out separately.", "page-upgrades-question-4-title": "What is the execution layer?", "page-upgrades-question-4-desc": "The Ethereum Mainnet you transact on today has previously been referred to as 'Eth1.' This term is being phased out in favor of the 'execution layer'.", "page-upgrades-question-5-answer-1": "To become a full validator on the network, you'll need to stake 32 ETH. If you don't have that much, or aren't willing to stake that much, you can join staking pools. These pools will let you stake less and earn fractions of the total rewards.", "page-upgrades-question-5-desc": "You'll need to use the staking launchpad or join a staking pool.", "page-upgrades-question-5-title": "How do I stake?", "page-upgrades-question-6-answer-1": "For now, there's no actions to take. But we recommend you stay up to date with developments on The Merge and shard chain upgrades.", "page-upgrades-question-6-answer-3": "<PERSON> of the Ethereum foundation regularly updates the community:", "page-upgrades-question-6-answer-4": "<PERSON> of ConsenSys has a weekly newsletter about the Ethereum upgrades:", "page-upgrades-question-6-answer-5": "You can also join the discussion on Ethereum research and development at ethresear.ch.", "page-upgrades-question-6-desc": "Your dapp won't be affected by any imminent upgrades. However future upgrades may require some changes.", "page-upgrades-question-6-title": "What do I need to do with my dapp?", "page-upgrades-question-7-desc": "Many different teams from all over the community are working on the various Ethereum upgrades.", "page-upgrades-question-7-lighthouse": "Lighthouse", "page-upgrades-question-7-lighthouse-lang": "(Rust implementation)", "page-upgrades-question-7-lodestar": "Lodestar", "page-upgrades-question-7-lodestar-lang": "(JavaScript implementation)", "page-upgrades-question-7-nimbus": "Nimbus", "page-upgrades-question-7-nimbus-lang": "(Nim implementation)", "page-upgrades-question-7-prysm": "Prysm", "page-upgrades-question-7-prysm-lang": "(Go implementation)", "page-upgrades-question-7-teams": "The Ethereum consensus client teams:", "page-upgrades-question-7-teku": "<PERSON><PERSON>", "page-upgrades-question-7-teku-lang": "(Java implementation)", "page-upgrades-question-7-title": "Who's building the Ethereum upgrades?", "page-upgrades-question-7-clients": "Learn more about Ethereum clients", "page-upgrades-question-8-answer-1": "The Ethereum upgrades will help Ethereum scale in a decentralized way, while maintaining security, and increasing sustainability.", "page-upgrades-question-8-answer-2": "Perhaps the most obvious problem is that Ethereum needs to be able to handle more than 15-45 transactions per second. But the upgrades also address some other problems with Ethereum today.", "page-upgrades-question-8-answer-3": "The network is in such high demand that it's making Ethereum expensive to use. Nodes in the network are struggling under the size of Ethereum and the amount of data their computers are having to process. And the underlying algorithm that keeps Ethereum secure and decentralized is energy intensive and needs to be greener.", "page-upgrades-question-8-answer-4": "A lot of what's changing has always been on the Ethereum roadmap, even since 2015. But current conditions are making the need for the upgrades even greater.", "page-upgrades-question-8-answer-6": "Explore the Ethereum vision", "page-upgrades-question-8-desc": "The Ethereum we use today needs to offer a better experience to end users and network participants.", "page-upgrades-question-8-title": "Why are upgrades needed?", "page-upgrades-question-9-answer-1": "The most active role you can play is to stake your ETH.", "page-upgrades-question-9-answer-2": "You may also want to run a second client to help improve client diversity.", "page-upgrades-question-9-answer-3": "If you're more technical, you can help catch bugs in the new clients.", "page-upgrades-question-9-answer-4": "You can also weigh in on the technical discussions with Ethereum researchers at ethresear.ch.", "page-upgrades-question-9-desc": "You don't have to be technical to contribute. The community is looking for contributions from all kinds of skill sets.", "page-upgrades-question-9-stake-eth": "Stake ETH", "page-upgrades-question-9-title": "How can I contribute to Ethereum upgrades?", "page-upgrades-question-9-more": "Find more general ways to get involved with Ethereum", "page-upgrades-question-10-title": "What are the 'Eth2 phases?'", "page-upgrades-question-10-desc": "Some things have changed here.", "page-upgrades-question-10-answer-0": "The term 'Eth2' itself is being phased out, as it does not represent a single upgrade or new network. It is more accurately a set of multiple upgrades that all do their part to make Ethereum more scalable, secure, and sustainable. The network you know and love will simply be referred to as Ethereum.", "page-upgrades-question-10-answer-1": "We're reluctant to talk too much in terms of a technical roadmap because this is software: things can change. We think it's easier to understand what's happening when you read about the outcomes.", "page-upgrades-question-10-answer-1-link": "View the upgrades", "page-upgrades-question-10-answer-2": "But if you've followed the discussions, here's how the upgrades fit into technical roadmaps, and a bit on how they're changing.", "page-upgrades-question-10-answer-3": "Phase 0 described the work to get the Beacon Chain live.", "page-upgrades-question-10-answer-5": "Phase 1 originally focused on implementing the shard chains, but prioritization has shifted to 'The Merge' which is the next planned upgrade.", "page-upgrades-question-10-answer-6": "Phase 1.5 was originally planned to follow shard implementations, when Mainnet would be added as the last shard to the Beacon Chain. To expedite the transition away from proof-of-work mining, Mainnet will instead represent the first shard to connect with the Beacon Chain. This is now known as 'The Merge' and will be a significant step towards a greener Ethereum.", "page-upgrades-question-10-answer-7": "Though the plans around Phase 2 have been a point of intense research and discussion, with The Merge planned before shard chains, this will allow for continued reassessment as to the needs of Ethereum development moving forward. Given a rollup-centric roadmap, the immediate necessity of shard chains is debatable.", "page-upgrades-question-10-answer-8": "More on a rollup-centric roadmap", "page-upgrades-question-11-title": "Can I buy Eth2?", "page-upgrades-question-11-desc": "No. There is no Eth2 token and your ETH will not change after the merge.", "page-upgrades-question-11-answer-1": "One of the driving forces behind the Eth2 rebrand was the common misconception that ETH holders would have to migrate their ETH after 'Ethereum 2.0'. This has never been true. It is ", "page-upgrades-question-11-answer-2": "a common technique used scammers.", "page-upgrades-question-title": "Frequently asked questions", "page-upgrades-question3-answer-1": "ETH holders don't need to do anything. Your ETH will not need changing or upgrading. There's almost certain to be scams telling you otherwise, so be careful.", "page-upgrades-scalable": "More scalable", "page-upgrades-scalable-desc": "Ethereum needs to support 1000s of transactions per second, to make applications faster and cheaper to use.", "page-upgrades-secure": "More secure", "page-upgrades-secure-desc": "Ethereum needs to be more secure. As the adoption of Ethereum grows, the protocol needs to become more secure against all forms of attack.", "page-upgrades-shard-button": "More on the shard chains", "page-upgrades-shard-date": "Shard chains should follow The Merge, sometime in 2023.", "page-upgrades-shard-desc": "Shard chains will expand Ethereum's capacity to process transactions and store data. The shards themselves will gain more features over time, rolled out in multiple phases.", "page-upgrades-shard-estimate": "Estimate: 2023", "page-upgrades-shard-lower": "More on shard chains", "page-upgrades-shard-title": "Shard chains", "page-upgrades-stay-up-to-date": "Stay up to date", "page-upgrades-stay-up-to-date-desc": "Get the latest from the researchers and developers working on the Ethereum upgrades.", "page-upgrades-sustainable-desc": "Ethereum needs to be better for the environment. The technology today requires too much computing power and energy.", "page-upgrades-take-part": "Take part in the research", "page-upgrades-take-part-desc": "Ethereum researchers and enthusiasts alike meet here to discuss research efforts, including everything related to Ethereum upgrades.", "page-upgrades-the-upgrades": "The Ethereum upgrades", "page-upgrades-the-upgrades-desc": "Ethereum consists of a set of upgrades that improve the scalability, security, and sustainability of the network. Although each is being worked on in parallel, they have certain dependencies that determine when they will be deployed.", "page-upgrades-unofficial-roadmap": "This is not the official roadmap. This is how we view what's happening based on the information out there. But this is technology, things can change in an instant. So please don't read this as a commitment.", "page-upgrades-upgrade-desc": "The Ethereum we know and love, just more scalable, more secure, and more sustainable...", "page-upgrades-upgrades": "The Ethereum upgrades", "page-upgrades-upgrades-aria-label": "Ethereum upgrades menu", "page-upgrades-upgrades-beacon-chain": "The Beacon Chain", "page-upgrades-upgrades-docking": "The Merge", "page-upgrades-upgrades-guide": "Guide to Ethereum upgrades", "page-upgrades-upgrades-shard-chains": "The shard chains", "page-upgrades-upgrading": "Upgrading Ethereum to radical new heights", "page-upgrades-vision": "The vision", "page-upgrades-vision-btn": "More on the Ethereum vision", "page-upgrades-vision-desc": "To bring Ethereum into the mainstream and serve all of humanity, we have to make Ethereum more scalable, secure, and sustainable.", "page-upgrades-vision-upper": "Ethereum vision", "page-upgrades-what-happened-to-eth2-title": "What happened to 'Eth2'?", "page-upgrades-what-happened-to-eth2-1": "The term 'Eth2' is being phased out in preparation for The Merge.", "page-upgrades-what-happened-to-eth2-1-more": "More on The Merge.", "page-upgrades-what-happened-to-eth2-2": "After merging 'Eth1' and 'Eth2' into a single chain, there will no longer be two distinct Ethereum networks; there will only be Ethereum.", "page-upgrades-what-happened-to-eth2-3": "To limit confusion, the community has updated these terms:", "page-upgrades-what-happened-to-eth2-3-1": "'Eth1' is now the 'execution layer', which handles transactions and execution.", "page-upgrades-what-happened-to-eth2-3-2": "'Eth2' is now the 'consensus layer', which handles proof-of-stake consensus.", "page-upgrades-what-happened-to-eth2-4": "These terminology updates only change naming conventions; this does not alter Ethereum's goals or roadmap.", "page-upgrades-what-happened-to-eth2-5": "Learn more about the 'Eth2' renaming", "page-upgrades-why-cant-we-just-use-eth2-title": "Why can't we just use Eth2?", "page-upgrades-why-cant-we-just-use-eth2-mental-models-title": "Mental models", "page-upgrades-why-cant-we-just-use-eth2-mental-models-description": "One major problem with the Eth2 branding is that it creates a broken mental model for new users of Ethereum. They intuitively think that Eth1 comes first and Eth2 comes after. Or that Eth1 ceases to exist once Eth2 exists. Neither of these is true. By removing Eth2 terminology, we save all future users from navigating this confusing mental model.", "page-upgrades-why-cant-we-just-use-eth2-inclusivity-title": "Inclusivity", "page-upgrades-why-cant-we-just-use-eth2-inclusivity-description": "As the roadmap for Ethereum has evolved, Ethereum 2.0 has become an inaccurate representation of Ethereum’s roadmap. Being careful and accurate in our word choice allows content on Ethereum to be understood by the broadest audience possible.", "page-upgrades-why-cant-we-just-use-eth2-scam-prevention-title": "Scam Prevention", "page-upgrades-why-cant-we-just-use-eth2-scam-prevention-description": "Unfortunately, malicious actors have attempted to use the Eth2 misnomer to scam users by telling them to swap their ETH for ‘ETH2’ tokens or that they must somehow migrate their ETH before the Eth2 upgrade. We hope this updated terminology will bring clarity to eliminate this scam vector and help make the ecosystem safer.", "page-upgrades-why-cant-we-just-use-eth2-staking-clarity-title": "Staking Clarity", "page-upgrades-why-cant-we-just-use-eth2-staking-clarity-description": "Some staking operators have also represented ETH staked on the Beacon Chain with the ‘ETH2’ ticker. This creates potential confusion, given that users of these services are not actually receiving an ‘ETH2’ token. No ‘ETH2’ token exists; it simply represents their share in that specific providers’ stake.", "page-upgrades-what-to-do": "What do you need to do?", "page-upgrades-what-to-do-desc": "If you're a dapp user or ETH holder, you don't need to do anything. If you're a developer or want to start staking, there are ways you can get involved today.", "page-upgrades-whats-next": "What are Ethereum upgrades?", "page-upgrades-whats-next-desc": "The Ethereum roadmap involves interconnected protocol upgrades that will make the network more scalable, more secure, and more sustainable. These upgrades are being built by multiple teams from across the Ethereum ecosystem.", "page-upgrades-whats-next-history": "Learn about previous Ethereum upgrades", "page-upgrades-whats-ethereum": "Wait, what's Ethereum?", "page-upgrades-whats-new": "What's next for Ethereum?", "page-upgrades-security-link": "More on security and scam prevention", "page-upgrades-vision-2014": "View a 2014 blog post detailing proof of stake", "page-upgrades-vision-2021": "View a 2021 blog post on the Ethereum roadmap evolution", "page-upgrades-vision-2021-updates": "View a 2021 blog post on the Ethereum Protocol Updates", "page-upgrades-vision-beacon-chain": "The beacon chain", "page-upgrades-vision-beacon-chain-btn": "More on the Beacon Chain", "page-upgrades-vision-beacon-chain-date": "The Beacon Chain is live", "page-upgrades-vision-beacon-chain-desc": "The Beacon Chain brought staking to Ethereum, laid the groundwork for future upgrades, and will eventually coordinate the new system.", "page-upgrades-vision-beacon-chain-upper": "Beacon Chain", "page-upgrades-vision-desc-1": "Ethereum needs to reduce network congestion and improve speeds to better service a global user base.", "page-upgrades-vision-desc-2": "Running a node is getting harder as the network grows. This will only get harder with efforts to scale the network.", "page-upgrades-vision-desc-3": "Ethereum uses too much electricity. The technology that keeps the network secure needs to be more sustainable.", "page-upgrades-vision-merge-date": "Estimate: 2021", "page-upgrades-vision-merge-desc": "Mainnet Ethereum will need to \"merge\" with the Beacon Chain at some point. This will enable staking for the entire network and signal the end of energy-intensive mining.", "page-upgrades-vision-ethereum-node": "More on nodes", "page-upgrades-vision-explore-upgrades": "Explore the upgrades", "page-upgrades-vision-future": "A digital future on a global scale", "page-upgrades-vision-meta-desc": "An overview of the impact upgrades will have on Ethereum, and the challenges they must overcome.", "page-upgrades-vision-meta-title": "Ethereum vision", "page-upgrades-vision-mining": "More on mining", "page-upgrades-vision-problems": "Today's problems", "page-upgrades-vision-scalability": "Scalability", "page-upgrades-vision-scalability-desc": "Ethereum needs to be able to handle more transactions per second without increasing the size of the nodes in the network. Nodes are vital network participants who store and run the blockchain. Increasing node size isn't practical because only those with powerful and expensive computers could do it. To scale, Ethereum needs more transactions per second, coupled with more nodes. More nodes means more security.", "page-upgrades-vision-scalability-desc-3": "The shard chains upgrade will spread the load of the network into 64 new chains. This will give Ethereum room to breathe by reducing congestion and improving speeds beyond the current 15-45 transactions per second limit.", "page-upgrades-vision-scalability-desc-4": "And even though there will be more chains, this will actually require less work from validators - the maintainers of the network. Validators will only need to 'run' their shard and not the entire Ethereum chain. This makes nodes more lightweight, allowing Ethereum to scale and remain decentralized.", "page-upgrades-vision-security": "Security", "page-upgrades-vision-security-desc": "The planned upgrades improve Ethereum's security against coordinated attacks, like a 51% attack. This is a type of attack where if someone controls the majority of the network they can force through fraudulent changes.", "page-upgrades-vision-security-desc-3": "The transition to proof-of-stake means that the Ethereum protocol has greater disincentives against attack. This is because in proof-of-stake, the validators who secure the network must stake significant amounts of ETH into the protocol. If they try and attack the network, the protocol can automatically destroy their ETH.", "page-upgrades-vision-security-desc-5": "This isn't possible in proof-of-work, where the best a protocol can do is force entities who secure the network (the miners) to lose mining rewards they would have otherwise earned. To achieve the equivalent effect in proof-of-work, the protocol would have to be able to destroy all of a miner's equipment if they try and cheat.", "page-upgrades-vision-security-desc-5-link": "More on proof of work", "page-upgrades-vision-security-desc-8": "Ethereum's security model also needs to change because of the introduction of shard chains. The Beacon Chain will randomly assign validators to different shards - this makes it virtually impossible for validators to ever collude by attacking a specific shard. Sharding isn't as secure on a proof-of-work blockchain, because miners can't be controlled by the protocol in this way.", "page-upgrades-vision-security-desc-10": "Staking also means you don't need to invest in elite hardware to 'run' an Ethereum node. This should encourage more people to become a validator, increasing the network's decentralization and decreasing the attack surface area.", "page-upgrades-vision-security-staking": "Stake ETH", "page-upgrades-vision-security-validator": "You can become a validator by staking your ETH.", "page-upgrades-vision-shard-chains": "shard chains", "page-upgrades-vision-shard-date": "Estimate: 2021", "page-upgrades-vision-shard-desc-4": "Shard chains will spread the load of the network into 64 new blockchains. Shards have the potential to drastically improve transaction speed - up to 100,000 per second.", "page-upgrades-vision-shard-upgrade": "More on shard chains", "page-upgrades-vision-staking-lower": "More on staking", "page-upgrades-vision-subtitle": "Grow Ethereum until it's powerful enough to help all of humanity.", "page-upgrades-vision-sustainability": "Sustainability", "page-upgrades-vision-sustainability-desc-1": "It's no secret that Ethereum and other blockchains like Bitcoin are energy intensive because of mining.", "page-upgrades-vision-sustainability-desc-2": "But Ethereum is moving towards being secured by ETH, not computing power - via staking.", "page-upgrades-vision-sustainability-desc-3": "Although staking has already been introduced by the Beacon Chain, the Ethereum we use today will run in parallel for a period of time. One system secured by ETH, the other by computing power. This is until The Merge.", "page-upgrades-vision-sustainability-desc-8": "With the Beacon Chain up and running, work has begun on merging Mainnet with the new consensus layer. Mainnet will then be secured by staked ETH and far less energy intensive.", "page-upgrades-vision-sustainability-subtitle": "Ethereum needs to be greener.", "page-upgrades-vision-title": "The Ethereum Vision", "page-upgrades-vision-title-1": "Clogged network", "page-upgrades-vision-title-2": "Disk space", "page-upgrades-vision-title-3": "Too much energy", "page-upgrades-vision-trilemma-cardtext-1": "Ethereum upgrades will make Ethereum scalable, secure, and decentralized. Sharding will make Ethereum more scalable by increasing transactions per second while decreasing the power needed to run a node and validate the chain. The beacon chain will make Ethereum secure by coordinating validators across shards. And staking will lower the barrier to participation, creating a larger - more decentralized - network.", "page-upgrades-vision-trilemma-cardtext-2": "Secure and decentralized blockchain networks require every node to verify every transaction processed by the chain. This amount of work limits the number of transactions that can happen at any one given time. Decentralized and secure reflects the Ethereum chain today.", "page-upgrades-vision-trilemma-cardtext-3": "Decentralized networks work by sending information about transactions across nodes - the whole network needs to know about any state change. Scaling transactions per second across a decentralized network poses security risks because the more transactions, the longer the delay, the higher the probability of attack while information is in flight.", "page-upgrades-vision-trilemma-cardtext-4": "Increasing the size and power of Ethereum's nodes could increase transactions per second in a secure way, but the hardware requirement would restrict who could do it - this threatens decentralization. It's hoped that sharding and proof-of-stake will allow Ethereum to scale by increasing the amount of nodes, not node size.", "page-upgrades-vision-trilemma-h2": "The challenge of decentralized scaling", "page-upgrades-vision-trilemma-modal-tip": "Tap the circles below to better understand the problems of decentralized scaling", "page-upgrades-vision-trilemma-p": "A naive way to solve Ethereum's problems would be to make it more centralized. But decentralization is too important. It's decentralization that gives Ethereum censorship resistance, openness, data privacy and near-unbreakable security.", "page-upgrades-vision-trilemma-p-1": "Ethereum's vision is to be more scalable and secure, but also to remain decentralized. Achieving these 3 qualities is a problem known as the scalability trilemma.", "page-upgrades-vision-trilemma-p-2": "Ethereum upgrades aim to solve the trilemma but there are significant challenges.", "page-upgrades-vision-trilemma-press-button": "Press the buttons on the triangle to better understand the problems of decentralized scaling.", "page-upgrades-vision-trilemma-text-1": "Decentralization", "page-upgrades-vision-trilemma-text-2": "Security", "page-upgrades-vision-trilemma-text-3": "Scalability", "page-upgrades-vision-trilemma-title-1": "Explore the scalability trilemma", "page-upgrades-vision-trilemma-title-2": "Ethereum upgrades and decentralized scaling", "page-upgrades-vision-trilemma-title-3": "Secure and decentralized", "page-upgrades-vision-trilemma-title-4": "Decentralized and scalable", "page-upgrades-vision-trilemma-title-5": "Scalable and secure", "page-upgrades-vision-understanding": "Understanding the Ethereum vision", "page-upgrades-vision-upgrade-needs": "The need for upgrades", "page-upgrades-vision-upgrade-needs-desc": "The Ethereum protocol that launched in 2015 has had incredible success. But the Ethereum community always expected that a few key upgrades would be necessary to unlock Ethereum's full potential.", "page-upgrades-vision-upgrade-needs-desc-2": "High demand is driving up transaction fees that make Ethereum expensive for the average user. The disk space needed to run an Ethereum client is growing at a fast rate. And the underlying proof-of-work consensus algorithm that keeps Ethereum secure and decentralized has a big environmental impact.", "page-upgrades-vision-upgrade-needs-desc-3": "Ethereum has a set of upgrades that address these problems and more. This set of upgrades was originally called 'Serenity' and 'Eth2,' and they've been an active area of research and development since 2014.", "page-upgrades-vision-upgrade-needs-desc-5": "Now that the technology is ready, these upgrades will re-architect Ethereum to make it more scalable, secure, and sustainable - to make life better for existing users and entice new ones. All while preserving Ethereum's core value of decentralization.", "page-upgrades-vision-upgrade-needs-desc-6": "This means there's no on-switch for scalability. Improvements will ship incrementally over time.", "page-upgrades-vision-upgrade-needs-serenity": "View a 2015 blog post discussing 'Serenity'", "page-upgrades-beacon-date": "Shipped!", "page-upgrades-merge-date": "~Q2 2022", "page-upgrades-shards-date": "~2023", "page-find-wallet-add-wallet": ". If you'd like us to add a wallet,", "page-find-wallet-airgap-logo-alt": "AirGap logo", "page-find-wallet-alpha-logo-alt": "AlphaWallet logo", "page-find-wallet-ambo-logo-alt": "Ambo logo", "page-find-wallet-argent-logo-alt": "Argent logo", "page-find-wallet-buy-card": "Buy crypto with a card", "page-find-wallet-buy-card-desc": "Buy ETH directly from your wallet with a bank card. Geographical restrictions may apply.", "page-find-wallet-card-yes": "Yes", "page-find-wallet-card-no": "No", "page-find-wallet-card-go": "Go", "page-find-wallet-card-hardware": "Hardware", "page-find-wallet-card-mobile": "Mobile", "page-find-wallet-card-desktop": "Desktop", "page-find-wallet-card-web": "Web", "page-find-wallet-card-more-info": "More info", "page-find-wallet-card-features": "Features", "page-find-wallet-card-has-bank-withdraws": "Withdraw to bank", "page-find-wallet-card-has-card-deposits": "Buy ETH with card", "page-find-wallet-card-has-defi-integration": "Access to DeFi", "page-find-wallet-card-has-explore-dapps": "Explore dapps", "page-find-wallet-card-has-dex-integrations": "Swap tokens", "page-find-wallet-card-has-high-volume-purchases": "Buy in high volume", "page-find-wallet-card-has-limits-protection": "Transaction limits", "page-find-wallet-card-has-multisig": "Multi-sig protection", "page-find-wallet-checkout-dapps": "Check out dapps", "page-find-wallet-clear": "Clear filters", "page-find-wallet-coinbase-logo-alt": "Coinbase logo", "page-find-wallet-coinomi-logo-alt": "Coinomi logo", "page-find-wallet-coin98-logo-alt": "Coin98 logo", "page-find-wallet-dcent-logo-alt": "D'CENT logo", "page-find-wallet-desc-2": "So choose your wallet based on the features you want.", "page-find-wallet-description": "Wallets have lots of optional features which you might like.", "page-find-wallet-description-airgap": "Sign transactions completely offline on a device without any network connectivity using AirGap Vault. Then you can broadcast them with your every-day smartphone with the AirGap Wallet app.", "page-find-wallet-description-alpha": "Fully open-source Ethereum wallet that leverages the secure enclave on mobile, offers full testnet support and embraces TokenScript standard.", "page-find-wallet-description-ambo": "Cut straight to investing and get your first investment within minutes of downloading the app", "page-find-wallet-description-argent": "One tap to earn interest & invest; borrow, store and send. Own it.", "page-find-wallet-description-bitcoindotcom": "The Bitcoin.com Wallet now supports Ethereum! Buy, hold, send, and trade ETH using a fully non-custodial wallet trusted by millions.", "page-find-wallet-description-coinbase": "The secure app to store crypto yourself", "page-find-wallet-description-coinomi": "Coinomi is the oldest multi-chain, defi-ready, cross-platform wallet for bitcoin, altcoins & tokens - never hacked, with millions of users.", "page-find-wallet-description-coin98": "A Non-Custodial, Multi-Chain Wallet & DeFi Gateway", "page-find-wallet-description-dcent": "D'CENT Wallet is the über convenient multi-cryptocurrency wallet with built-in DApp browser for easy access to DeFi, NFT, and variety of services.", "page-find-wallet-description-enjin": "Impenetrable, feature-packed, and convenient—built for traders, gamers, and developers", "page-find-wallet-description-fortmatic": "Access Ethereum apps from anywhere with just an email or phone number. No more browser extensions and seed phrases.", "page-find-wallet-description-gnosis": "The most trusted platform to store digital assets on Ethereum", "page-find-wallet-description-guarda": "Secure, feature-packed, non-custodial crypto wallet with support for over 50 blockchains. Easy stakings, exchange and purchase of crypto assets.", "page-find-wallet-description-hyperpay": "HyperPay is a multi-platform universal crypto wallet that supports 50+ blockchains and 2000+ dApps.", "page-find-wallet-description-imtoken": "imToken is an easy and secure digital wallet trusted by millions", "page-find-wallet-description-keystone": "The Keystone wallet is a 100% air-gapped hardware wallet with open-source firmware and uses a QR code protocol.", "page-find-wallet-description-ledger": "Keep your assets safe with the highest security standards", "page-find-wallet-description-linen": "Mobile smart contract wallet: earn yield, buy crypto, and participate in DeFi. Earn rewards and governance tokens.", "page-find-wallet-description-loopring": "The first ever Ethereum smart contract wallet with zkRollup-based trading, transfers, and AMM. Gas-free, secure, and simple.", "page-find-wallet-description-mathwallet": "MathWallet is a multi-platform (mobile/extension/web) universal crypto wallet that supports 50+ blockchains and 2000+ dApps.", "page-find-wallet-description-metamask": "Start exploring blockchain applications in seconds. Trusted by over 1 million users worldwide.", "page-find-wallet-description-monolith": "The world's only self-custodial wallet paired with Visa Debit Card. Available in the UK & EU and usable globally.", "page-find-wallet-description-multis": "Multis is a cryptocurrency account designed for businesses. With Multis, companies can store with access controls, earn interest on their savings, and streamline payments and accounting.", "page-find-wallet-description-mycrypto": "MyCrypto is an interface for managing all of your accounts. Swap, send, and buy crypto with wallets like MetaMask, Ledger, Trezor, and more.", "page-find-wallet-description-myetherwallet": "A free, client-side interface helping you interact with the Ethereum blockchain", "page-find-wallet-description-numio": "Numio is a non-custodial, Layer 2 Ethereum wallet, powered by zkRollups for fast and cheap ERC-20 transactions and token swaps. Numio is available on Android and iOS.", "page-find-wallet-description-opera": "Built-in crypto wallet in Opera Touch on iOS and Opera for Android. The first major browser to integrate a crypto wallet, enabling seamless access to the emerging web of tomorrow (Web 3).", "page-find-wallet-description-pillar": "Non-custodial, community-owned wallet with its own L2 Payment Network.", "page-find-wallet-description-portis": "The non-custodial blockchain wallet that makes apps simple for everyone", "page-find-wallet-description-rainbow": "A better home for your Ethereum assets", "page-find-wallet-description-samsung": "Keep your valuables safe and secure with Samsung Blockchain.", "page-find-wallet-description-status": "Secure messaging app, crypto wallet, and Web3 browser built with state-of-the-art technology", "page-find-wallet-description-tokenpocket": "TokenPocket：A secure and convenient world-leading digital currency wallet and a portal to DApps, with Multi-chain supported.", "page-find-wallet-description-bitkeep": "BitKeep is a decentralized multi-chain digital wallet, dedicated to providing safe and convenient one-stop digital asset management services to users around the world.", "page-find-wallet-description-torus": "One-Click login for Web 3.0", "page-find-wallet-description-trezor": "The First and Original Hardware Wallet", "page-find-wallet-description-trust": "Trust Wallet is a decentralized multi-coin cryptocurrency wallet. Buy crypto, explore dapps, swap assets, and more while keeping control of your keys.", "page-find-wallet-description-unstoppable": "Unstoppable Wallet is an open-source, non-custodial storage solution known for its intuitive design and frictionless user experience. Natively integrates decentralized trading/exchange capabilities.", "page-find-wallet-description-zengo": "ZenGo is the first keyless crypto wallet. With ZenGo, there are no private keys, passwords or seed phrases to manage or lose. Buy, trade, earn and store Ethereum with unprecedented simplicity and safety", "page-find-wallet-description-walleth": "100% open source (GPLv3) and native Android Ethereum Wallet for your service since 2017. Connect to your favorite dapp via WalletConnect and use it directly with hardware wallets.", "page-find-wallet-description-safepal": "SafePal's wallet is a secure, decentralized, easy-to-use, and free application to manage more than 10,000 cryptocurrencies.", "page-find-wallet-enjin-logo-alt": "Enjin logo", "page-find-wallet-Ethereum-wallets": "Ethereum Wallets", "page-find-wallet-explore-dapps": "Explore dapps", "page-find-wallet-explore-dapps-desc": "These wallets are designed to help you connect to Ethereum dapps.", "page-find-wallet-feature-h2": "Choose the wallet features you care about", "page-find-wallet-fi-tools": "Access to DeFi", "page-find-wallet-fi-tools-desc": "Borrow, lend and earn interest directly from your wallet.", "page-find-wallet-following-features": "with the following features:", "page-find-wallet-fortmatic-logo-alt": "Fortmatic logo", "page-find-wallet-gnosis-logo-alt": "Gnosis Safe logo", "page-find-wallet-guarda-logo-alt": "Guarda logo", "page-find-wallet-hyperpay-logo-alt": "HyperPay logo", "page-find-wallet-image-alt": "Find wallet hero image", "page-find-wallet-imtoken-logo-alt": "imToken logo", "page-find-wallet-keystone-logo-alt": "Keystone logo", "page-find-wallet-last-updated": "Last updated", "page-find-wallet-ledger-logo-alt": "Ledger logo", "page-find-wallet-limits": "Limits protection", "page-find-wallet-limits-desc": "Safeguard your assets by setting limits that prevent your account being drained.", "page-find-wallet-linen-logo-alt": "Linen logo", "page-find-wallet-listing-policy": "listing policy", "page-find-wallet-loopring-logo-alt": "Loopring logo", "page-find-wallet-mathwallet-logo-alt": "MathWallet logo", "page-find-wallet-meta-description": "Find and compare Ethereum wallets based on the features you want.", "page-find-wallet-meta-title": "Find an Ethereum Wallet", "page-find-wallet-metamask-logo-alt": "MetaMask logo", "page-find-wallet-monolith-logo-alt": "Monolith logo", "page-find-wallet-multis-logo-alt": "Multis logo", "page-find-wallet-multisig": "Multi-signature accounts", "page-find-wallet-multisig-desc": "For extra security, multi-signature wallets require more than one account to authorise certain transactions.", "page-find-wallet-mycrypto-logo-alt": "MyCrypto logo", "page-find-wallet-myetherwallet-logo-alt": "MyEtherWallet logo", "page-find-wallet-new-to-wallets": "New to wallets? Here's an overview to get you started.", "page-find-wallet-new-to-wallets-link": "Ethereum wallets", "page-find-wallet-not-all-features": "No wallet has all of these features yet", "page-find-wallet-not-endorsements": "Wallets listed on this page are not official endorsements, and are provided for informational purposes only. Their descriptions have been provided by the wallet companies themselves. We add products to this page based on criteria in our", "page-find-wallet-numio-logo-alt": "Numio logo", "page-find-wallet-overwhelmed": "Ethereum wallets below. Overwhelmed? Try filtering by features.", "page-find-wallet-opera-logo-alt": "Opera logo", "page-find-wallet-pillar-logo-alt": "Pillar logo", "page-find-wallet-portis-logo-alt": "Portis logo", "page-find-wallet-rainbow-logo-alt": "Rainbow logo", "page-find-wallet-raise-an-issue": "raise an issue in GitHub", "page-find-wallet-search-btn": "Search chosen features", "page-find-wallet-showing": "Showing ", "page-find-wallet-samsung-logo-alt": "Samsung Blockchain Wallet logo", "page-find-wallet-status-logo-alt": "Status logo", "page-find-wallet-swaps": "Decentralized token swaps", "page-find-wallet-swaps-desc": "Trade between ETH and other tokens directly from your wallet.", "page-find-wallet-title": "Find a wallet", "page-find-wallet-tokenpocket-logo-alt": "TokenPocket logo", "page-find-wallet-bitkeep-logo-alt": "BitKeep logo", "page-find-wallet-torus-logo-alt": "Torus logo", "page-find-wallet-trezor-logo-alt": "Trezor logo", "page-find-wallet-trust-logo-alt": "Trust logo", "page-find-wallet-safepal-logo-alt": "SafePal logo", "page-find-wallet-try-removing": "Try removing a feature or two", "page-find-wallet-unstoppable-logo-alt": "Unstoppable logo", "page-find-wallet-use-wallet-desc": "Now that you have a wallet, check out some Ethereum applications (dapps). There are dapps for finance, social media, gaming and lots of other categories.", "page-find-wallet-use-your-wallet": "Use your wallet", "page-find-wallet-voluem-desc": "If you want to hold a lot of ETH, choose a wallet that lets you buy more than $2000 ETH at a time.", "page-find-wallet-volume": "High-volume purchases", "page-find-wallet-we-found": "We found", "page-find-wallet-withdraw": "Withdraw to bank", "page-find-wallet-withdraw-desc": "You can cash out your ETH straight to your bank account without going through an exchange.", "page-find-wallet-zengo-logo-alt": "ZenGo logo", "page-find-wallet-walleth-logo-alt": "WallETH logo", "page-stake-eth": "Stake ETH", "page-wallets-accounts-addresses": "Wallets, accounts, and addresses", "page-wallets-accounts-addresses-desc": "It's worth understanding the differences between some key terms.", "page-wallets-accounts-ethereum-addresses": "An Ethereum account has an Ethereum address, like an inbox has an email address. You can use this to send funds to an account.", "page-wallets-alt": "Illustration of a robot with a vault for a body, representing an Ethereum wallet", "page-wallets-ethereum-account": "An Ethereum account is an entity that can send transactions and has a balance.", "page-wallets-blog": "Coinbase blog", "page-wallets-bookmarking": "Bookmark your wallet", "page-wallets-bookmarking-desc": "If you use a web wallet, bookmark the site to protect yourself against phishing scams.", "page-wallets-cd": "Physical hardware wallets that let you keep your crypto offline – very secure", "page-wallets-converted": "Crypto converted?", "page-wallets-converted-desc": "If you’re looking to hold some serious value, we recommend a hardware wallet as these are the most secure. Or a wallet with fraud alerts and withdrawal limits.", "page-wallets-curious": "Crypto curious?", "page-wallets-curious-desc": "If you’re new to crypto and just want to get a feel for it, we recommend something that will give you the opportunity to explore Ethereum applications or buy your first ETH directly from the wallet.", "page-wallets-desc-2": "You need a wallet to send funds and manage your ETH.", "page-wallets-desc-2-link": "More on ETH", "page-wallets-desc-3": "Your wallet is only a tool for managing your Ethereum account. That means you can swap wallet providers at any time. Many wallets also let you manage several Ethereum accounts from one application.", "page-wallets-desc-4": "That's because wallets don't have custody of your funds, you do. They're just a tool for managing what's really yours.", "page-wallets-description": "Ethereum wallets are applications that let you interact with your Ethereum account. Think of it like an internet banking app – without the bank. Your wallet lets you read your balance, send transactions and connect to applications.", "page-wallets-desktop": "Desktop applications if you prefer to manage your funds via macOS, Windows or Linux", "page-wallets-ethereum-wallet": "A wallet is a product that lets you manage your Ethereum account. It allows you to view your account balance, send transactions, and more.", "page-wallets-explore": "Explore Ethereum", "page-wallets-features-desc": "We can help you choose your wallet based on the features you care about.", "page-wallets-features-title": "Prefer to choose based on features?", "page-wallets-find-wallet-btn": "Find a wallet", "page-wallets-find-wallet-link": "Find a wallet", "page-wallets-get-some": "Get some ETH", "page-wallets-get-some-alt": "An illustration of a hand creating an ETH logo made of lego bricks", "page-wallets-get-some-btn": "Get some ETH", "page-wallets-get-some-desc": "ETH is the native crypto of Ethereum. You’ll need some ETH in your wallet to use Ethereum applications.", "page-wallets-get-wallet": "Get a wallet", "page-wallets-get-wallet-desc": "There are lots of different wallets to choose from. We want to help you choose the best one for you.", "page-wallets-get-wallet-desc-2": "Remember: this decision isn’t forever – your Ethereum account is not tied to your wallet provider.", "page-wallets-how-to-store": "How to store digital assets on Ethereum", "page-wallets-keys-to-safety": "The keys to keeping your crypto safe", "page-wallets-manage-funds": "An app for managing your funds", "page-wallets-manage-funds-desc": "Your wallet shows your balances, transaction history and gives you a way to send/receive funds. Some wallets may offer more.", "page-wallets-meta-description": "What you need to know to use Ethereum wallets.", "page-wallets-meta-title": "Ethereum wallets", "page-wallets-mobile": "Mobile applications that make your funds accessible from anywhere", "page-wallets-more-on-dapps-btn": "More on Dapps", "page-wallets-most-wallets": "Most wallet products will let you generate an Ethereum account. So you don't need one before you download a wallet.", "page-wallets-protecting-yourself": "Protecting yourself and your funds", "page-wallets-seed-phrase": "Write down your seed phrase", "page-wallets-seed-phrase-desc": "Wallets will often give you a seed phrase that you must write down somewhere safe. This is the only way you’ll be able to recover your wallet.", "page-wallets-seed-phrase-example": "Here's an example:", "page-wallets-seed-phrase-snippet": "there aeroplane curve vent formation doge possible product distinct under spirit lamp", "page-wallets-seed-phrase-write-down": "Don’t store it on a computer. Write it down and keep it safe.", "page-wallets-slogan": "The key to your digital future", "page-wallets-stay-safe": "How to stay safe", "page-wallets-stay-safe-desc": "Wallets require a bit of a different mindset when it comes to safety. Financial freedom and the ability to access and use funds anywhere comes with a bit of responsibility – there’s no customer support in crypto.", "page-wallets-subtitle": "Wallets give access to your funds and Ethereum applications. Only you should have access to your wallet.", "page-wallets-take-responsibility": "Take responsibility for your own funds", "page-wallets-take-responsibility-desc": "Centralized exchanges will link your wallet to a username and password that you can recover in a traditional way. Just remember you’re trusting that exchange with custody over your funds. If that company is attacked or folds, your funds are at risk.", "page-wallets-tips": "More tips on staying safe", "page-wallets-tips-community": "From the community", "page-wallets-title": "Ethereum wallets", "page-wallets-triple-check": "Triple check everything", "page-wallets-triple-check-desc": "Remember transactions can’t be reversed and wallets can’t be easily recovered so take precautions and always be careful.", "page-wallets-try-dapps": "Try some dapps", "page-wallets-try-dapps-alt": "An illustration of Ethereum community members working together", "page-wallets-try-dapps-desc": "Dapps are applications built on Ethereum. They’re cheaper, fairer and kinder on your data than most traditional applications.", "page-wallets-types": "Types of wallet", "page-wallets-web-browser": "Web wallets that let you interact with your account via a web browser", "page-wallets-whats-a-wallet": "What's an Ethereum wallet?", "page-wallets-your-ethereum-account": "Your Ethereum account", "page-wallets-your-ethereum-account-desc": "Your wallet is your window into your Ethereum account – your balance, transaction history and more. But you can swap wallet providers at any time.", "page-wallets-your-login": "Your login for Ethereum apps", "page-wallets-your-login-desc": "Your wallet lets you connect to any decentralized application using your Ethereum account. It's like a login you can use across many dapps.", "page-what-is-ethereum-101": "Ethereum 101", "page-what-is-ethereum-101-desc": "Ethereum is a technology that lets you send cryptocurrency to anyone for a small fee. It also powers applications that everyone can use and no one can take down.", "page-what-is-ethereum-101-desc-2": "Ethereum builds on Bitcoin's innovation, with some big differences.", "page-what-is-ethereum-101-desc-3": "Both let you use digital money without payment providers or banks. But Ethereum is programmable, so you can also use it for lots of different digital assets – even Bitcoin!", "page-what-is-ethereum-101-desc-4": "This also means Ethereum is for more than payments. It's a marketplace of financial services, games and apps that can't steal your data or censor you.", "page-what-is-ethereum-101-italic": "the world's programmable blockchain.", "page-what-is-ethereum-101-strong": "It's ", "page-what-is-ethereum-accessibility": "Ethereum is open to everyone.", "page-what-is-ethereum-adventure": "Choose your adventure!", "page-what-is-ethereum-alt-img-bazaar": "Illustration of a person peering into a bazaar, meant to represent Ethereum", "page-what-is-ethereum-alt-img-comm": "An illustration of Ethereum community members working together", "page-what-is-ethereum-alt-img-lego": "An illustration of a hand creating an ETH logo made of lego bricks", "page-what-is-ethereum-alt-img-social": "An illustration of characters in a social space dedicated to Ethereum with a large ETH logo", "page-what-is-ethereum-banking-card": "Banking for everyone", "page-what-is-ethereum-banking-card-desc": "Not everyone has access to financial services. But all you need to access Ethereum and its lending, borrowing and savings products is an internet connection.", "page-what-is-ethereum-build": "Make something with Ethereum", "page-what-is-ethereum-build-desc": "If you want to try building with Ethereum, read our docs, try some tutorials, or check out the tools you need to get started.", "page-what-is-ethereum-censorless-card": "Censorship-resistant", "page-what-is-ethereum-censorless-card-desc": "No government or company has control over Ethereum. This decentralization makes it nearly impossible for anyone to stop you from receiving payments or using services on Ethereum.", "page-what-is-ethereum-comm-desc": "Our community includes people from all backgrounds, including artists, crypto-anarchists, fortune 500 companies, and now you. Find out how you can get involved today.", "page-what-is-ethereum-commerce-card": "Commerce guarantees", "page-what-is-ethereum-commerce-card-desc": "Ethereum creates a more level playing field. Customers have a secure, built-in guarantee that funds will only change hands if you provide what was agreed. You don’t need large company clout to do business.", "page-what-is-ethereum-community": "The Ethereum community", "page-what-is-ethereum-compatibility-card": "Compatibility for the win", "page-what-is-ethereum-compatibility-card-desc": "Better products and experiences are being built all the time because Ethereum products are compatible by default. Companies can build on each other's success.", "page-what-is-ethereum-dapps-desc": "Products and services that run on Ethereum. There are dapps for finance, work, social media, gaming and more – meet the apps for our digital future.", "page-what-is-ethereum-dapps-img-alt": "An illustration of a doge using an Ethereum application on a computer", "page-what-is-ethereum-dapps-title": "Ethereum da<PERSON>", "page-what-is-ethereum-desc": "The foundation for our digital future", "page-what-is-ethereum-explore": "Explore Ethereum", "page-what-is-ethereum-get-started": "The best way to learn more is to download a wallet, get some ETH and try an Ethereum dapp.", "page-what-is-ethereum-in-depth-description": "Ethereum is open access to digital money and data-friendly services for everyone – no matter your background or location. It's a community-built technology behind the cryptocurrency ether (ETH) and thousands of applications you can use today.", "page-what-is-ethereum-internet-card": "A more private internet", "page-what-is-ethereum-internet-card-desc": "You don't need to provide all your personal details to use an Ethereum app. Ethereum is building an economy based on value, not surveillance.", "page-what-is-ethereum-meet-comm": "Meet the community", "page-what-is-ethereum-meta-description": "Learn about Ethereum, what it does and how to try it for yourself.", "page-what-is-ethereum-meta-title": "What is Ethereum?", "page-what-is-ethereum-native-alt": "The symbol for ether (ETH)", "page-what-is-ethereum-native-crypto": "Ethereum's native cryptocurrency and equivalent to Bitcoin. You can use ETH on Ethereum applications or for sending value to friends and family.", "page-what-is-ethereum-native-img-alt": "An illustration of a robot with a safe for a torso, used to represent Ethereum wallets", "page-what-is-ethereum-native-title": "ETH", "page-what-is-ethereum-p2p-card": "A peer-to-peer network", "page-what-is-ethereum-p2p-card-desc": "Ethereum allows you to move money, or make agreements, directly with someone else. You don't need to go through intermediary companies.", "page-what-is-ethereum-singlecard-desc": "If you're interested in blockchain and the technical side of Ethereum, we've got you covered.", "page-what-is-ethereum-singlecard-link": "How Ethereum works", "page-what-is-ethereum-singlecard-title": "How Ethereum works", "page-what-is-ethereum-start-building-btn": "Start building", "page-what-is-ethereum-title": "What is Ethereum?", "page-what-is-ethereum-tools-needed": "All you need is a wallet to take part.", "page-what-is-ethereum-try": "Try Ethereum", "page-what-is-ethereum-tryit": "So step into the bazaar and give it a try...", "page-what-is-ethereum-wallets": "Wallets", "page-what-is-ethereum-wallets-desc": "How you manage your ETH and your Ethereum account. You'll need a wallet to get started – we'll help you choose one.", "page-what-is-ethereum-welcome": "Welcome to Ethereum", "page-what-is-ethereum-welcome-2": "We hope you stay.", "page-what-is-ethereum-defi-title": "Decentralized finance (DeFi)", "page-what-is-ethereum-defi-description": "A more open financial system that gives you more control over your money and unlocks new possibilities.", "page-what-is-ethereum-defi-alt": "An Eth logo made of lego bricks.", "page-what-is-ethereum-nft-title": "Non-fungible tokens (NFTs)", "page-what-is-ethereum-nft-description": "A way to represent unique items as Ethereum assets that can be traded, used as proof of ownership, and create new opportunities for creators.", "page-what-is-ethereum-nft-alt": "An Eth logo being displayed via hologram.", "page-what-is-ethereum-dao-title": "Decentralized autonomous organisations (DAOs)", "page-what-is-ethereum-dao-description": "A new way to collaborate and set up online communities with shared goals and pooled funds.", "page-what-is-ethereum-dao-alt": "A representation of a DAO voting on a proposal.", "page-what-is-ethereum-use-cases-title": "Discover Ethereum use cases", "page-what-is-ethereum-use-cases-subtitle": "Ethereum has led to the creation of new products and services that can improve different areas of our lives.", "page-what-is-ethereum-use-cases-subtitle-two": "We're still in the early stages but there's a lot to be excited about.", "template-usecase-dropdown-defi": "Decentralized finance (DeFi)", "template-usecase-dropdown-nft": "Non-fungible tokens (NFTs)", "template-usecase-dropdown-dao": "Decentralized autonomous organisations (DAOs)", "template-usecase-dropdown": "Ethereum use cases", "template-usecase-banner": "Uses of Ethereum are always developing and evolving. Add any info you think will make things clearer or more up to date.", "template-usecase-edit-link": "Edit page", "template-usecase-dropdown-aria": "Use case dropdown menu"}, "routed": true, "originalPath": "/nft/", "redirect": false}}}, "staticQueryHashes": ["1011117294", "1339035675", "3003422828", "446219633"]}