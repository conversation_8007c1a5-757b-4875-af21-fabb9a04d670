(self.webpackChunkethereum_org_website=self.webpackChunkethereum_org_website||[]).push([[4789],{87757:function(t,e,r){t.exports=r(35666)},90416:function(t,e,r){"use strict";var n=r(67294),o=r(43587),i=r(70396),a=r(23013),c=o.default.div.withConfig({displayName:"CalloutBanner__StyledCard",componentId:"sc-gj8rbv-0"})(["display:flex;flex-direction:row-reverse;background:linear-gradient( 49.21deg,rgba(127,127,213,0.2) 19.87%,rgba(134,168,231,0.2) 58.46%,rgba(145,234,228,0.2) 97.05% );padding:3rem;margin:1rem;margin-top:6rem;margin-bottom:10rem;border-radius:4px;@media (max-width:","){flex-direction:column;margin-bottom:1rem;margin:4rem 2rem;}@media (max-width:","){padding:2rem;}"],(function(t){return t.theme.breakpoints.l}),(function(t){return t.theme.breakpoints.s})),u=o.default.div.withConfig({displayName:"CalloutBanner__Content",componentId:"sc-gj8rbv-1"})(["padding-left:5rem;flex:1 0 50%;display:flex;flex-direction:column;justify-content:center;@media (max-width:","){margin-top:2rem;padding-left:1rem;flex-direction:column;width:100%;}@media (max-width:","){padding-left:0;}"],(function(t){return t.theme.breakpoints.l}),(function(t){return t.theme.breakpoints.s})),l=o.default.p.withConfig({displayName:"CalloutBanner__Description",componentId:"sc-gj8rbv-2"})(["font-size:1.25rem;width:90%;line-height:140%;margin-bottom:2rem;color:",";"],(function(t){return t.theme.colors.text200})),s=(0,o.default)(i.G).withConfig({displayName:"CalloutBanner__Image",componentId:"sc-gj8rbv-3"})(["align-self:center;width:100%;max-width:",";margin-top:-6rem;margin-bottom:-6rem;@media (max-width:","){margin-bottom:0rem;margin-top:-6rem;}"],(function(t){return t.maxImageWidth+"px"}),(function(t){return t.theme.breakpoints.l})),f=o.default.h2.withConfig({displayName:"CalloutBanner__H2",componentId:"sc-gj8rbv-4"})(["margin-top:0rem;"]);e.Z=function(t){var e=t.image,r=t.maxImageWidth,o=t.titleKey,i=t.descriptionKey,h=t.alt,m=t.children,p=t.className;return n.createElement(c,{className:p},n.createElement(s,{image:e,alt:h,maxImageWidth:r}),n.createElement(u,null,n.createElement(f,null,n.createElement(a.Z,{id:o})),n.createElement(l,null,n.createElement(a.Z,{id:i})),m))}},30371:function(t,e,r){"use strict";r.d(e,{Y:function(){return s}});var n=r(15861),o=r(87757),i=r.n(o),a=r(96633),c=r.n(a),u=function(){var t=(0,n.Z)(i().mark((function t(e,r){var n,o;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===r&&(r=!1),t.next=3,c().get(e);case 3:return n=t.sent,o=n.data,r&&(i=e,a=o,localStorage.setItem(i,JSON.stringify({value:a,timestamp:(new Date).getTime()}))),t.abrupt("return",o);case 7:case"end":return t.stop()}var i,a}),t)})));return function(e,r){return t.apply(this,arguments)}}(),l=function(t){return e=t,JSON.parse(localStorage.getItem(e))||null;var e},s=function(){var t=(0,n.Z)(i().mark((function t(e){var r,n;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=l(e),n=(new Date).getTime(),!(r&&n-r.timestamp<36e5)){t.next=6;break}return t.abrupt("return",r.value);case 6:return t.abrupt("return",u(e,!0));case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()},35666:function(t){var e=function(t){"use strict";var e,r=Object.prototype,n=r.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(S){u=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,i=Object.create(o.prototype),a=new C(n||[]);return i._invoke=function(t,e,r){var n=f;return function(o,i){if(n===m)throw new Error("Generator is already running");if(n===p){if("throw"===o)throw i;return O()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=k(a,r);if(c){if(c===d)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=m;var u=s(t,e,r);if("normal"===u.type){if(n=r.done?p:h,u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=p,r.method="throw",r.arg=u.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(S){return{type:"throw",arg:S}}}t.wrap=l;var f="suspendedStart",h="suspendedYield",m="executing",p="completed",d={};function g(){}function v(){}function y(){}var w={};u(w,i,(function(){return this}));var b=Object.getPrototypeOf,x=b&&b(b(I([])));x&&x!==r&&n.call(x,i)&&(w=x);var E=y.prototype=g.prototype=Object.create(w);function _(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,i,a,c){var u=s(t[o],t,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var o;this._invoke=function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}}function k(t,r){var n=t.iterator[r.method];if(n===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=e,k(t,r),"throw"===r.method))return d;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var o=s(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,d;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,d):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,d)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function I(t){if(t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}return{next:O}}function O(){return{value:e,done:!0}}return v.prototype=y,u(E,"constructor",y),u(y,"constructor",v),v.displayName=u(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},t.awrap=function(t){return{__await:t}},_(L.prototype),u(L.prototype,a,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new L(l(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(E),u(E,c,"Generator"),u(E,i,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=I,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(u&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),d}},t}(t.exports);try{regeneratorRuntime=e}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}},15861:function(t,e,r){"use strict";function n(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(l){return void r(l)}c.done?e(u):Promise.resolve(u).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var a=t.apply(e,r);function c(t){n(a,o,i,c,u,"next",t)}function u(t){n(a,o,i,c,u,"throw",t)}c(void 0)}))}}r.d(e,{Z:function(){return o}})}}]);