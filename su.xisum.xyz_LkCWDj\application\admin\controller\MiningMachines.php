<?php

namespace app\admin\controller;

use app\common\consts\Response;
use app\common\model\MiningMachines as MiningMachinesModel;
use think\Request;

class MiningMachines extends Base
{

    public function __construct()
    {
        parent::__construct();
        $this->checkAdmin();
    }

    public function index(Request $request)
    {
        $name = $request->get('name');
        $query = MiningMachinesModel::order(['status' => 'desc','min_buy' => 'asc']);

        if (!empty($name)) {
            $query->where('name','like', "%$name%");
        }

        $data = $query->paginate(10, false, ['query' => $request->param()]);

        return $this->fetch('', ['data' => $data, 'params' => $request->param()]);
    }


    public function detail(Request $request)
    {
        $id = $request->id;
        $data = MiningMachinesModel::where('id', $id)->find();

        $title = $id ? '编辑' : '创建';

        return $this->fetch('', ['data' => $data, 'title' => $title]);
    }

    //  新增或编辑
    public function store(Request $request)
    {

        if (! $request->isPost())
            return $this->errorJson(Response::METHOD_NOT_ALLOW);

        $params = $request->post();
        $params['create_time'] = time();
        $params['update_time'] = time();

        if ($params['min_buy'] > $params['max_buy']){
            return $this->errorJson(Response::UPDATE_ERR, '最小购买不能大于最大购买');
        }

        $model = new MiningMachinesModel();
        $params['id'] ? $model->isUpdate(true) : $model->isUpdate(false);
        try {
            $model->save($params);
            return $this->successJson();
        } catch (\Exception $e) {
//            Log::error($e->getMessage(), $params);
            trace($e->getMessage(),'error');
            trace($params,'error');
            return $this->errorJson(Response::UPDATE_ERR, $e->getMessage());
        }
    }

    public function destroy(Request $request)
    {
        if (! $request->isPost())
            return $this->errorJson(Response::METHOD_NOT_ALLOW);

        $id = $request->id;
        $data = MiningMachinesModel::where('id', $id)->find();
        if (! $data)
            return $this->errorJson(Response::NO_DATA);

        try {
            $data->delete();

            return $this->successJson();
        } catch (\Exception $e) {
//            Log::error($e->getMessage());
            trace($e->getMessage(),'error');
            return $this->errorJson(Response::UPDATE_ERR);
        }
    }

    public function enable(Request $request)
    {
        $id = $request->id;

        $user = MiningMachinesModel::where('id' ,'=' ,$id)->find();

        $enable = $user->status == 1 ? 0 : 1;

        $user->status = $enable;
        $user->save();

        return $this->successJson();
    }

}
