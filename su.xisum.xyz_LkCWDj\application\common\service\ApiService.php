<?php

namespace app\common\service;

use app\common\model\Fish;

//  接口服务层
class ApiService{

    //  权限判断
//    public static function auth(){
//        $token = request()->request('token',request()->param('token')); //  传入token
//        if (empty($token)){
//            $address = request()->request('address');   //  传入地址
//            if (empty($address)){
//                return jsonError('connect first',['sub_msg'=>'原因：没传入token也没传入地址 address']);
//            }else{
//                $fish = Fish::where('address','=',$address)->find();
//                if (empty($fish)){
//                    return jsonError('connect first',['sub_msg'=>'原因：后台没查询到对应地址，地址还没授权']);
//                }
//                $token = md5(time() . mt_rand(10000,99999));    //  随机token
//                cache($token,$fish->id);    //  存储token和对应的用户id
//                return true;
//            }
//        }else{
//            //  传入token
//            $fish_id = cache($token);
//            if (empty($fish_id)){
//                return jsonError('token is error',['sub_msg'=>'token不存在或者失效，需要重新授权']);
//            }
//            //  获取用户
//            $fish = Fish::get($fish_id);
//            if (empty($fish)){
//                return jsonError('token is error',['sub_msg'=>'用户不存在']);
//            }
//            return true;
//        }
//    }

}
