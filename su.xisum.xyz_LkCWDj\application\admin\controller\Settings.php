<?php

namespace app\admin\controller;

use app\common\service\SettingService;
use think\Request;

class Settings extends Base
{
    public function __construct()
    {
        parent::__construct();
        $this->checkAdmin();
    }

    public function index()
    {
        $list = \app\common\model\Settings::all();

        return $this->fetch('', ['list' => $list]);
    }

    public function update(Request $request)
    {

        $params = $request->param();
        foreach ($params as $k => $v){
            \app\common\model\Settings::where('key',$k)->setField('value',$v);
        }

        return $this->successJson();

    }
}
