/**
  * Name: Coinjet
  * Version: 1.0
  * Author: Themesflat
  * Author URI: http://www.bootstrapmb.com
*/
@import url("owl.carousel.css");
@import url("flexslider.css");
@import url("font-awesome.css");
@import url("elegant.css");
@import url("themify-icons.css");
@import url("shortcodes.css");
@import url('https://fonts.googleapis.com/css?family=Montserrat:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i');
/**
  *	Reset
  *	Repeatable Patterns
  * Boxed
  *	Top
  *	Top Style1
  *	Top Style2
  * Header Top
  * Header Top Style1
  * Header
  * Header Style1
  * Header Style2
  * Main Menu
  * Page Title
  * Main Content
  * Blog Pagination
  * Footer Top
  * Footer
  * Footer Bottom
  * Blog
  * Parallax
  * Mobile Navigation
  * Go top
  * Switcher
  * Preload
*/

/* Reset
-------------------------------------------------------------- */
html {
	overflow-y: scroll;
	-webkit-text-size-adjust: 100%;
	   -ms-text-size-adjust: 100%;
}

body {
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-webkit-text-size-adjust: 100%;
	   -ms-text-size-adjust: 100%;
}

a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, caption, canvas, center, cite, code,
dd, del, details, dfn, dialog, div, dl, dt, em, embed, fieldset, figcaption, figure, form, footer, header, hgroup, h1, h2, h3, h4, h5, h6, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav,object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, tt, table, tbody, textarea, tfoot, thead, time, tr, th, td, u, ul, var, video  { 
	font-family: inherit; 
	font-size: 100%; 
	font-weight: inherit; 
	font-style: inherit; 
	vertical-align: baseline; 
	margin: 0; 
	padding: 0; 
	border: 0; 
	outline: 0;
	background: transparent;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section { 
	display: block;
}
                          
ol, ul {
	list-style: none;
}

blockquote, q { 
	-webkit-hyphens: none;
	  -moz-hyphens: none;
	   -ms-hyphens: none;
	       hyphens: none;
	        quotes: none;
}

figure {
	margin: 0;
}

:focus {
	outline: 0;
}

table { 
	border-collapse: collapse; 
	border-spacing: 0;
}

img {
	border: 0;
	-ms-interpolation-mode: bicubic;
	vertical-align: middle;
}

legend {
	white-space: normal;
}

button,
input,
select,
textarea {
	font-size: 100%;
	margin: 0;
	max-width: 100%;
	vertical-align: baseline;
	-webkit-box-sizing: border-box;
	  -moz-box-sizing: border-box;
	       box-sizing: border-box;
}

button,
input {
	line-height: normal;
}

input,
textarea {
	background-image: -webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0)); /* Removing the inner shadow, rounded corners on iOS inputs */
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	line-height: 1;
	cursor: pointer; /* Improves usability and consistency of cursor style between image-type 'input' and others */
	-webkit-appearance: button; /* Corrects inability to style clickable 'input' types in iOS */
	border: none;
}

input[type="checkbox"],
input[type="radio"] {
	padding: 0; /* Addresses excess padding in IE8/9 */
}

input[type="search"] {
	-webkit-appearance: textfield; /* Addresses appearance set to searchfield in S5, Chrome */
}

input[type="search"]::-webkit-search-decoration { /* Corrects inner padding displayed oddly in S5, Chrome on OSX */
	-webkit-appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner { /* Corrects inner padding and border displayed oddly in FF3/4 www.sitepen.com/blog/2008/05/14/the-devils-in-the-details-fixing-dojos-toolbar-buttons/ */
	border: 0;
	padding: 0;
}

*,
*:before,
*:after {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
}	

/* Repeatable Patterns
-------------------------------------------------------------- */
*,
*:before,
*:after {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
			box-sizing: border-box;
}

body {
	font: 14px/22px "Montserrat", sans-serif;
	background-color: #fff;		
	color: #7c7c7c;
	overflow: hidden;
}

a {		
	text-decoration: none;
	color: #979797;
	font-family: 'Montserrat';
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
		-ms-transition: all 0.3s ease-in-out;
		 -o-transition: all 0.3s ease-in-out;
			transition: all 0.3s ease-in-out;
}

a:hover,
a:focus {
	color: #f1a619;
	text-decoration: none;
	outline: 0;
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
		-ms-transition: all 0.3s ease-in-out;
		 -o-transition: all 0.3s ease-in-out;
			transition: all 0.3s ease-in-out;
}

ul, ol {
	padding: 0;
}

img {
	max-width: 100%;
	height: auto;		
}

b, strong {
	font-weight: 900;
}

button {
	border: none;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {		
    display: inline-block;
    color: #fff;
    padding: 0px 29px 0 30px;
    height: 46px;
    line-height: 42px;
    font-size: 13px;
    color: #ffffff;
    background: #f1a619;
    border: 2px solid #f1a619;
    font-weight: 500;
    -webkit-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    -moz-border-radius: 2px;
    font-family: "Montserrat", sans-serif;
    position: relative;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover {
	background-color: transparent;
	color: #181818;
}

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
input[type="email"] {	
	position: relative;
    display: block;
    width: 100%;
    line-height: 24px;
    padding: 8px 15px;
    color: #222222;
    border: 1px solid #d0d0d0;
    height: 46px;
    background-color: #fff;
    border-radius: 3px;
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
	    -ms-transition: all 0.3s ease-in-out;
	     -o-transition: all 0.3s ease-in-out;
	        transition: all 0.3s ease-in-out;
}

input[type="search"] {
    width: 250px;  
    margin-bottom: 0px;
    border-radius: 0px;
    background-color: #ffffff;
}


textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus {
	border: 1px solid #f1a619;
	-webkit-box-shadow: none;
	   -moz-box-shadow: none;
			box-shadow: none;
}

textarea {
	width: 100%;
	padding: 10px 15px;    
    height: 170px;
}

input[type="checkbox"] {
	display: inline;
}

textarea:-moz-placeholder,
textarea::-moz-placeholder,
input:-moz-placeholder,
input::-moz-placeholder {		
	color: #c0c0c0;
	font-style: italic;
}

input:-ms-input-placeholder {
	color: #c0c0c0;
	font-style: italic;
}

textarea::-webkit-input-placeholder,
input::-webkit-input-placeholder {
	color: #c0c0c0;
	font-style: italic;
}

/* bootstrap resetting elements bootstrapmb.com */

textarea, 
input[type="text"],
input[type="submit"],
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"], 
.uneditable-input,
.dropdown-menu,
.navbar .nav > .active > a, 
.navbar .nav > .active > a:hover, 
.navbar .nav > .active > a:focus {
	-webkit-appearance: none;
	text-shadow: none;
	-webkit-box-shadow: none;
	   -moz-box-shadow: none;
	     -o-box-shadow: none;
	        box-shadow: none;
	color: #000;
}

blockquote {
	position: relative;
}

blockquote p {
	font-weight: 500;
	font-size: 20px;
	line-height: 27px;
	color: #181818;
}

h1, h2, h3, h4, h5, h6 {
	font-family: 'Montserrat', sans-serif;
	font-weight: 500;
	color: #191919;
}

h1 { font-size: 36px;}
h2 { font-size: 24px;}
h3 { font-size: 18px;}
h4 { font-size: 16px;}
h5 { font-size: 14px;}

.container {
	width: 1200px;
	max-width: 100%;
}

.clearfix {
	clear: both;
}

.left {
	text-align: left;
}

.center {
	text-align: center;
}

.right {
	text-align: right;
}

.one-half {
	width: 50%;
	float: left;
}

.one-three {
	width: 33.33%;
	float: left;
}

.one-four {
	width: 25%;
	float: left;
}

.one-five {
	width: 20%;
}

.bg-white {
	background-color: #fff;
}

.bg-browse {
	background-color: #f7f7f7;
}

.pdr-30 {
	padding-right: 30px;
}

.pdl-30 {
	padding-left: 30px;
}

.color-green {
	color: #1be600 !important;
}

.color-red {
	color: #de2e2e !important;
}

/* Boxed
-------------------------------------------------------------- */
.home-boxed .boxed {
    max-width: 1300px;
    margin: 0 auto;
    margin-top: 30px;
    margin-bottom: 30px;
    background-color: #fff;
    box-shadow: 0 0 16px 1px #7f7f7f;
    overflow: hidden;
}

/* Themesflat Top
-------------------------------------------------------------- */
.themesflat-top {
	background-color: #181818;
	font-size: 12px;
	padding: 5px 0 5px;
}

.flat-infomation {
	float: left;
}

.flat-infomation li,
.box-account li {
	font-family: 'Montserrat';
	display: inline-block;
	position: relative;
	font-size: 12px;
	line-height: 30px;
	padding-left: 20px;
	color: #f1a619;
}

.flat-infomation li {
	margin-right: 27px;
}

.box-account li a,
.flat-infomation li a {
	color: #fff;
}

.box-account li a:hover,
.flat-infomation li a:hover {
	color: #f1a619;
}

.flat-infomation li:before,
ul.box-account li:before {
	content: "\f095";
	position: absolute;
	top: 0px;
	left: 0px;
	font-family: 'FontAwesome';
}

.flat-infomation li.email:before {
	content: "\f0e0";
}

/* Box Account */
.box-account {
	float: right;
}

ul.box-account li {
	margin-left: 23px;
	padding-left: 19px;
}

ul.box-account li.login:before {
	content: "\f007";
}

ul.box-account li.sign-in:before {
	content: "\f090";
}

/* Header 
---------------------------------------------------------------*/
.header {
	position: relative;
	background-color: #ffffff;	
	-webkit-transition: all 0.3s ease-in-out;
       -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
         -o-transition: all 0.3s ease-in-out;
            transition: all 0.3s ease-in-out;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
       -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        -ms-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
         -o-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#header.fixed-header {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1005;
	background-color: #ffffff;
	width: 100%;
}

.logo {
	margin-top: 26px;
	float: left;
}

/*top search*/
.flat-show-search {
	position: relative;
	float: right;
}

.show-search {
    float: right;
    margin-left: 48px;
    position: relative;
}

.show-search i {
    position: relative;
    display: block;
    font-size: 20px;
    color: #181818;
    margin: 28px 0px;
    cursor: pointer;
}

.top-search {
    position: absolute;
    right: 0px;
    top: 18px;
    width: 250px;
    opacity: 0;
    visibility: hidden;
    z-index: 12;
    -webkit-transition: all 0.3s ease-in-out;
       -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
         -o-transition: all 0.3s ease-in-out;
            transition: all 0.3s ease-in-out;
}

.top-search #searchform-all {
    position: relative;
}

.top-search #searchform-all input[type='text'] {
	height: 46px;
	background-color: #fff;
	border-radius: 25px;
	margin-bottom: 0px;
}

.top-search #searchform-all #searchsubmit {
    margin-bottom: 0;
    height: 45px;
    font-size: 14px;
    color: rgba(21, 65, 110, 0.65);
    background-color: #fff;
}

.top-search #searchform-all #searchsubmit {
    /*padding: 3px;*/
    position: absolute;
    right: -20px;
    top: 6px;
    font-family: "FontAwesome";
    color: #fff;
    background: #f1a619;
    border: none;
    text-align: center;
    font-size: 13px;
    font-weight: 700;
    width: 36px;
    height: 36px;
    line-height: 36px;
    border-radius: 50%;
    padding: 0;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.top-search #searchform-all #searchsubmit:hover {
	background-color: #555555;
}

.top-search.show {
    visibility: visible;
    opacity: 1;
   right: 19px;
}

.show-search.active .fa-search::before {
    content: "\f00d";
}
/* Main Menu
---------------------------------------------------------------*/
.header-wrap {
	width: 100%;
	padding: 0 15px;
}

.nav-wrap {
	float: right;
}

#mainnav .menu > li {
	display: inline-block;
	position: relative;
}

#mainnav .menu > li > a {
	line-height: 80px;
	display: block;
	padding: 0 20px;
	color: #000002;
	font-weight: 600;
	text-transform: uppercase;
	position: relative;
}

#mainnav .menu > li.active > a,
#mainnav .menu > li > a:hover {
	color: #f1a619;
}

#mainnav .menu > li:last-child a {
	padding-right: 0;
}

#mainnav .menu > li a i.fa {
	padding-left: 6px;
}

/* Sub-menu */
#mainnav .menu > li > .sub-menu {
	opacity: 0;
	position: absolute;
	width: 220px;
	top: 120%;
	left: 0;
	visibility: hidden;
	z-index: 99;
	box-shadow: 2px 3px 8px 0px rgba(0, 0, 0, 0.3);
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

#mainnav .menu > li:last-child .sub-menu {
	left: auto;
	right: 0;
}

#mainnav .menu > li:hover > .sub-menu {
	opacity: 1;
	top: 80px;
	visibility: visible;
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

#mainnav .menu > li > .sub-menu > li {
	padding-left: 20px;
	background-color: #fff;
	border-bottom: 1px solid #f2f2f2;
}

#mainnav .menu > li > .sub-menu > li > a {
	display: block;
	font-family: 'Montserrat';
	line-height: 41px;
	color: #777777;
	position: relative;
	overflow: hidden;
}

#mainnav .menu > li > .sub-menu > li > a:before {
	content: '\f0da';
	position: absolute;
	font-family: 'fontawesome';
	top: 0;
	left: -10px;
	color: #f1a619;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

#mainnav .menu > li > .sub-menu > li.active > a:before,
#mainnav .menu > li > .sub-menu > li > a:hover:before {
	left: 0px;
	opacity: 1;
}

#mainnav .menu > li > .sub-menu > li.active > a,
#mainnav .menu > li > .sub-menu > li > a:hover {
	color: #f1a619;
	padding-left: 15px;
}


/* Mobile navigation
-------------------------------------------------------------- */
#mainnav-mobi {
   	display: block;
   	margin: 0 auto;
   	width: 100%;
   	background-color: #181818;
   	z-index: 1000;
   	position: absolute;
}

#mainnav-mobi ul {
   	display: block;
   	list-style: none;
   	margin: 0;
   	padding: 0;
}

#mainnav-mobi ul li {
   	margin:0;
   	position: relative;
   	text-align: left;
   	border-top: 1px solid #2d374a;
   	cursor: pointer;
}

#mainnav-mobi ul > li > a {
   	text-decoration: none;
   	height: 50px;
   	line-height: 50px;
   	padding: 0 15px;
   	color: #fff;
   	text-transform: uppercase;
}

#mainnav-mobi ul.sub-menu {
   	top: 100%;
   	left: 0;
   	z-index: 2000;
   	position: relative;
   	background-color: #181818;
}

#mainnav-mobi > ul > li > ul > li,
#mainnav-mobi > ul > li > ul > li > ul > li {
    border-top: 1px solid #2d374a;
    background-color: #181818;
}

#mainnav-mobi > ul > li > ul > li > ul > li a {
   	padding-left: 45px !important;
}

#mainnav-mobi ul.sub-menu > li > a {
   	display: block;
   	text-decoration: none;
   	padding: 0 30px;
   	border-top-color: rgba(255,255,255,.1);
	-webkit-transition: all 0.2s ease-out;
	   -moz-transition: all 0.2s ease-out;
	     -o-transition: all 0.2s ease-out;
	        transition: all 0.2s ease-out;
}

#mainnav-mobi > ul > li > ul > li:first-child a {
	border-top: none;
}

#mainnav-mobi ul.sub-menu > li > a:hover,
#mainnav-mobi > ul > li > ul > li.active > a {
	color: #fff;
}

.btn-menu {
	display: none;
	float: right;
	position: relative;
	background: transparent;
	cursor: pointer;
	margin: 30px 0px 30px 0px;
	width: 26px;
	height: 16px;
    -webkit-transition: all ease .238s;
       -moz-transition: all ease .238s;
            transition: all ease .238s;
}

.btn-menu:before,
.btn-menu:after, 
.btn-menu span {
	background-color: #f1a619;
    -webkit-transition: all ease .238s;
       -moz-transition: all ease .238s;
            transition: all ease .238s;
}

.btn-menu:before,
.btn-menu:after {
	content: "";
	position: absolute;
	top: 0;
	height: 2px;
	width: 100%;
	left: 0;
	top: 50%;
	-webkit-transform-origin: 50% 50%;
	    -ms-transform-origin: 50% 50%;
	        transform-origin: 50% 50%;
}

.btn-menu span {
	position: absolute;
	width: 100%;
	height: 2px;
	left: 0;
	top: 50%;
	overflow: hidden;
	text-indent: 200%;
}

.btn-menu:before {
	-webkit-transform: translate3d(0, -7px, 0);
	        transform: translate3d(0, -7px, 0);
}

.btn-menu:after {
	-webkit-transform: translate3d(0, 7px, 0);
            transform: translate3d(0, 7px, 0);
}

.btn-menu.active span {
	opacity: 0;
}

.btn-menu.active:before {
	-webkit-transform: rotate3d(0, 0, 1, 45deg);
            transform: rotate3d(0, 0, 1, 45deg);
}

.btn-menu.active:after {
	-webkit-transform: rotate3d(0, 0, 1, -45deg);
            transform: rotate3d(0, 0, 1, -45deg);
}

.btn-submenu {
   position: absolute;
   right: 0px;
   top: 0;
   font: 20px/50px 'FontAwesome';
   text-align: center;
   cursor: pointer;
   width: 50px;
   height: 50px;
}

.btn-submenu:before {
   content: "\f107";
   color: #fff;
}

.btn-submenu.active:before {
   content: "\f106";
}

.btn-menu {
   display: none; 
}

/* Page Title 
---------------------------------------------------------------*/
.page-title {
	height: 300px;
	position: relative;
	background: url('../img/bg-footer.png') center center;
}

.page-title.style1 {
	background-image: url('../images/parallax/04.jpg');
}

.page-title .title-heading {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.page-title .page-title-heading .h1-title {
	color: #fff;
	font-weight: 500;
	margin-bottom: 10px;
}

.page-title .breadcrumbs li {
	display: inline-block;
	line-height: 28px;
	font-size: 16px;
	color: #fff;
}

.page-title .breadcrumbs li.dot {
	padding: 0 8px;
}

.page-title .breadcrumbs li a {
	color: #fff;
	letter-spacing: -0.2px;
}

.page-title .breadcrumbs li a i {
	padding: 0 3px 0 7px;
}

/* Main Content 
---------------------------------------------------------------*/
.main-content {
	padding: 100px 0 102px;
	background-color: #fff;
}

.main-content.style1 {
	padding: 76px 0 104px;
}

.main-content .post-wrap.style1 {
	padding-top: 21px;
}

.main-content .post-wrap.style2 {
	margin: 0px;
}

/* Blog Pagination 
---------------------------------------------------------------*/
.blog-single-pagination {
	text-align: center;
}

.blog-pagination {
	padding-top: 30px;
	border-top: 1px solid #dfdfdf;
}

.flat-pagination li {
	display: inline-block;
	margin-right: 17px;
	font-size: 18px;
}

.flat-pagination li a {
	background-color: #e1e1e1;
    display: block;
    line-height: 50px;
    width: 50px;
    text-align: center;
    text-align: center;
    border-radius: 3px;
	color: #191919;
}

.flat-pagination li a:hover,
.flat-pagination li a.active {
	background-color: #f1a619;
}


/* Footer Background
---------------------------------------------------------------*/
#footer_background {
	background: url(../img/bg-footer.jpg) center center repeat;
}

/* Footer */
.footer {
	padding: 70px 0 69px;
}

.footer-widgets {
	width: 100%;
    display: flex;
    flex-wrap: wrap;
}

.footer-widgets .widget .widget-title {
	color: #f1a619;
	font-weight: 600;
	margin-bottom: 16px;
}

/* Widget About */
.widget-about {
	padding-right: 70px;
}

.widget-about #logo-ft {
	margin-bottom: 24px;
}

.widget-about .widget-text {
	margin-bottom: 32px;
}

.widget-about .widget-text p {
	color: #fff;
	line-height: 24px;
}

/* widget-newletter */
.widget-newletter #subscribe-content {
	position: relative;
}

.widget-newletter #subscribe-content .input-email input {
	height: 40px;
	background-color: #fff;
	border-radius: 0px;
	margin-bottom: 0;
}

.widget-newletter #subscribe-content button {
	position: absolute;
	top: 0;
	right: 0;
	height: 40px;
	line-height: 40px;
	padding: 0 12px;
	border-radius: 0px;
}

.widget-newletter #subscribe-content button:hover {
	background-color: #252525;
	color: #fff;
	border-color: #252525;
}

/* widget nav menu */
.widget_nav_menu {
	margin-top: 8px;
	padding-left: 71px;
}

.widget_nav_menu .menu-footer li {
	line-height: 32px;
}

.widget_nav_menu .menu-footer li i {
	padding-right: 5px;
}

.widget_nav_menu .menu-footer li a:not(:hover) {
	color: #c0c0c0;
}

/* Widget Contact */
.widget_contact {
	padding-left: 70px;
	margin-top: 8px;
}

.footer-widgets .widget.widget_contact .widget-title {
	margin-bottom: 20px;
}

.widget_contact .contact-list li {
	line-height: 24px;
	margin-bottom: 6px;
}

.widget_contact .contact-list li span {
	color: #fff;
}

/* Widget Themesflat Socials */
.widget_themesflat_socials {
	margin-top: 30px;
}

.widget_themesflat_socials .themesflat-shortcode-socials li {
	display: inline-block;
	margin-right: 8px;
}

.widget_themesflat_socials .themesflat-shortcode-socials li:last-child {
	margin-right: 0;
}

.widget_themesflat_socials .themesflat-shortcode-socials li a {
	display: block;
	height: 40px;
	width: 40px;
	line-height: 40px;
	text-align: center;
	border-radius: 50%;
	border: 1px solid #494949;
	color: #fff;
}

.widget_themesflat_socials .themesflat-shortcode-socials li a:hover {
	background-color: #f1a619;
	border-color: #f1a619;
}

/* Footer Bottom */
.footer-bottom .container .row:before {
	content: '';
    position: absolute;
    left: 15px;
    right: 15px;
    height: 1px;
    width: calc(100% - 30px);
    background: rgba(255, 255, 255, 0.1);
    top: 0;
}

.footer-bottom .copyright {
	padding: 25px 0;
	float: left;
}

.footer-bottom .copyright p {
	font-size: 12px;
	letter-spacing: 0.7px;
}

.footer-bottom .copyright a {
	color: #fff;
}

.footer-bottom .copyright a:hover {
	color: #f1a619;
}

.menu-ft-bottom {
	float: right;
	padding: 21px 0;
}

.menu-ft-bottom li {
	display: inline-block;
	margin-left: 23px;
	font-size: 12px;
}

.menu-ft-bottom li a:not(:hover) {
	color: #bfbfbf;
}

/* BLog
-------------------------------------------------------------- */

/* Main Post */
.post-wrap {
	padding-left: 100px;
}
.post-wrap .fullwidth {
	width: 100%;
	padding-left: 15px;
	padding-right: 15px;
}

article.main-post {
	overflow: hidden;
}

article.main-post {
	margin-bottom: 64px;
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

article.main-post .featured-post {
	overflow: hidden;
	margin-bottom: 25px;
}

article.main-post .featured-post a img {
	width: 100%;
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

article.main-post:hover .featured-post a img {
	transform: scale(1.05);
}

article.main-post .entry-content .date {
	line-height: 25px;
	padding: 0 14px;
	border-radius: 2px;
	background-color: #f1a619;
	color: #fff;
	font-size: 12px;
	display: inline-block;
	-webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

article.main-post:hover .entry-content .date {
	background-color: #252525;
}

article.main-post .entry-content h2 {
	margin-bottom: 17px;
	letter-spacing: -0.25px;
}

article.main-post .entry-content h2 a:not(:hover) {
	color: #000002;
}

article.main-post .entry-content p {
	letter-spacing: -0.14px;
}

article.main-post .meta-left {
	margin-top: 21px;
}

article.main-post .meta-left li {
	display: inline-block;
	margin-right: 17px;
	font-size: 16px;
	color: #c0c0c0;
}

article.main-post .meta-left li:last-child {
	margin-right: 0;
}

article.main-post .meta-left li i {
	color: #f1a619;
	padding-right: 5px;
}

/* Blog List Full Width */
.post-wrap.post-list {
	padding: 0 70px;
}

article.main-post.blog-list-full-width {
	/*padding: 0 70px;*/
}

article.main-post.blog-list-full-width .featured-post {
	margin-bottom: 25px;
}

article.main-post.blog-list-full-width .entry-content h2 {
    margin-bottom: 14px;
    font-size: 30px;
    letter-spacing: -0.3px;
}

article.main-post.blog-list-full-width .entry-content p {
	font-size: 18px;
	line-height: 28px;
}

article.main-post.blog-list-full-width .meta-left {
	margin-top: 28px;
}

article.main-post.blog-list-full-width .meta-left li {
    display: inline-block;
    margin-right: 21px;
    font-size: 18px;
}

/* Two Column */
article.main-post.two-column,
article.main-post.three-column {
	margin-bottom: 66px;
}

article.main-post.two-column .featured-post {
	margin-bottom: 25px;
}

article.main-post.two-column .entry-content h2 {
	margin-bottom: 15px;
    letter-spacing: -0.25px;
    line-height: 30px;
}

article.main-post.two-column .entry-content p {
	line-height: 24px;
    font-size: 16px;
}

article.main-post.two-column .meta-left {
    margin-top: 20px;
}

/* Three Column */
article.main-post.three-column .entry-content h2 {
	font-size: 18px;
	line-height: 24px;
	margin-bottom: 15px;
}

article.main-post.three-column .entry-content p {
	line-height: 24px;
}

article.main-post.three-column .meta-left {
    margin-top: 29px;
}

/* Side Bar Blog */
.sidebar {
	margin-right: -30px;
	margin-top: -4px;
}

.sidebar .widget {
	margin-bottom: 67px;
	overflow: hidden;
}

/* Widget Title */
.widget .widget-title {
	margin-bottom: 28px;
	color: #0e0e0e;
	position: relative;
	font-weight: 500;
	letter-spacing: -0.2px;
}

/* Widget Search */
.sidebar .widget.widget_search {
	margin-bottom: 65px;
}

.widget_search form .input-search {
	position: relative;
}

.widget_search form .input-search input {
	height: 44px;
	background-color: transparent;
	border-radius: 2px;
	padding-right: 60px;
	margin-bottom: 0px;
	color: #999;
}

.widget_search form .input-search input::placeholder {
	color: #999;
}

.widget_search form .input-search button {
	position: absolute;
	top: 0;
	right: 0;
	width: 60px;
	height: 44px;
	border-radius: 0;
	text-align: center;
	font-size: 18px;
	padding: 0;
	border: none;
	color: #f1a619;
	background-color: transparent;
}

.widget_search form .input-search button:hover {
	background-color: #f1a619;
	color: #fff;
}

/* Widget Recent News */
.widget.widget-recent-news .widget-title {
	margin-bottom: 24px;
}

.widget-recent-news ul.recent-news > li {
	overflow: hidden;
	margin-bottom: 29px;
}

.widget-recent-news ul.recent-news > li:last-child {
	margin-bottom: 0;
}

.widget-recent-news ul.recent-news li .post-image {
	float: left;
	margin-right: 30px;
}

.widget-recent-news ul.recent-news li .post-content {
	overflow: hidden;
	margin-top: -5px;
}

.widget-recent-news ul.recent-news li .post-content h5 {
	line-height: 22px;
	margin-bottom: 6px;
	letter-spacing: -0.5px;
}

.widget-recent-news ul.recent-news li .post-content h5 a:not(:hover) {
	color: #181818;
}

.widget-recent-news ul.recent-news li .post-content ul.meta-left li {
	display: inline-block;
	color: #c0c0c0;
	margin-right: 14px;
	font-size: 12px;
	
}

.widget-recent-news ul.recent-news li .post-content ul.meta-left li i {
	color: #f1a619;
	padding-right: 2px;
}

/* Widget Tag Cloud */
.sidebar .widget.widget_tag_cloud {
	margin-bottom: 60px;
}

.sidebar .widget.widget_tag_cloud .widget-title {
	margin-bottom: 26px;
}

.widget_tag_cloud .tag-cloud a {
	display: inline-block;
	position: relative;
	font-size: 12px;
	line-height: 30px;
	border-radius: 2px;
	padding: 0 14px;
	background-color: #f3f3f3;
	color: #7c7c7c;
	margin-right: 5px;
	margin-bottom: 8px;
}

.widget_tag_cloud .tag-cloud a:hover {
	background-color: #f1a619;
	color: #fff;
}

/* Widget Categories */
.widget_categories ul li.cat-item {
	line-height: 24px;
	margin-bottom: 12px;
}


/* Main Single 
-------------------------------------------------------------- */
article.main-single {
	padding-bottom: 35px;
	margin-bottom: 45px;
	border-bottom: 1px solid #f2f2f2;
}

article.main-post.main-single:hover .featured-post a img {
	transform: scale(1);
}

article.main-single .featured-post {
	margin-bottom: 34px;
}

article.main-single .entry-title .meta-left {
	margin-bottom: 24px;
	margin-top: 18px;
}

article.main-single .entry-title .meta-left li {
	display: inline-block;
	margin-right: 22px;
    letter-spacing: 0.65px;
}

article.main-single .entry-title .meta-left li i {
	padding-right: 6px;
}

article.main-single .entry-title h2 {
	font-size: 30px;
	margin-bottom: 15px;
	letter-spacing: -0.33px;
}

article.main-single .entry-content > p {
	color: #7c7c7c;
	line-height: 24px;
	margin-bottom: 12px;
}

article.main-single .entry-content blockquote {
	margin: 24px 0 16px;
}

article.main-single .direction {
	overflow: hidden;
	margin-top: 37px;
}

article.main-single .direction .social {
	float: left;
}

article.main-single .direction ul li {
	display: inline-block;
}

article.main-single .direction ul li {
	margin-left: 15px;
	font-size: 18px;
}

article.main-single .direction ul li a:hover {
	color: #f1a619;
}

article.main-single .direction .widget_tag_cloud {
	float: right;
}

/* Comment Respond */
.comment-respond {
	margin-top: 46px;
}

.comment-respond h2 {
	margin-bottom: 35px;
}

.comment-respond form div.comment-form-name,
.comment-respond form div.comment-form-email {
	float: left;
	width: 50%;
	margin-bottom: 40px;
}

.comment-respond form div.comment-form-name {
	padding-right: 15px;
}

.comment-respond form div.comment-form-email {
	padding-left: 15px;
}

.comment-respond form div input,
.comment-respond form div textarea {
	height: 50px;
	background-color: #fff;
	padding-left: 20px;
	color: #181818;
	border-radius: 2px;
	margin-bottom: 0;
}

.comment-respond form div input::placeholder,
.comment-respond form div textarea::placeholder {
	font-size: 16px;
}

.comment-respond form div textarea {
	height: 150px;
}

.comment-respond form div.comment-form-submit {
	margin-top: 40px;
}

.comment-respond form div.comment-form-submit button {
	height: 46px;
	line-height: 46px;
	padding: 0 25px;
	border-radius: 3px;
	font-weight: 500;
}

.comment-respond form div.comment-form-submit button:hover {
	color: #f1a619;
}

/* Comment Area */
.comment-area h3 {
	margin-bottom: 35px;
	font-size: 24px;
}

.comment-list > li.comment {
	margin-bottom: 49px;
    padding-bottom: 43px;
	border-bottom: 1px solid #f1f1f1;
}

.comment-list > li.comment:last-child {
	margin-bottom: 0;
}

.comment-list li.comment article.comment-body {
	overflow: hidden;
}

.comment-list > li.comment ol li article.comment-body {
	margin-top: 47px;
	padding-top: 50px;
	border-top: 1px solid #f1f1f1;
}

.comment-list li.comment article.comment-body .comment-image {
	float: left;
	margin-right: 30px;
}

.comment-list li.comment article.comment-body .comment-text {
	overflow: hidden;
	margin-top: -3px;
}

.comment-list li.comment article.comment-body .comment-text .comment-metadata .name {
	font-weight: 500;
	color: #181818;
	font-size: 18px;
	margin-bottom: 19px;
}

.comment-list li.comment article.comment-body .comment-text .comment-metadata .date {
	font-size: 16px;
	padding-left: 32px;
	font-weight: 400;
	color: #bfbfbf;
}


.comment-list li.comment article.comment-body .comment-text .comment-content p {
	font-size: 16px;
	line-height: 24px;
}

.comment-list li.comment article.comment-body .comment-text .reply {
	font-size: 16px;
	text-transform: uppercase;
	margin-top: 20px;
}

.comment-list li.comment article.comment-body .comment-text .reply a:not(:hover) {
	color: #181818;
}

.comment-list li.comment .children li.comment article.comment-body {
	margin-left: 128px;
}

/* GoTop
-------------------------------------------------------------- */
.go-top {
   	position: fixed !important;
   	right: 15px;
	bottom: 15px;   
   	width: 40px;
   	height: 40px;
   	z-index: 9999;
   	-webkit-border-radius: 50%;
       -moz-border-radius: 50%;
        -ms-border-radius: 50%;
         -o-border-radius: 50%;
    		border-radius: 50%;
   	cursor: pointer;   
    background-color: rgb(241, 166, 25);
    box-shadow: 1px 1px 8px 0px rgba(0, 0, 0, 0.2);
   	line-height: 40px;
   	text-align: center;   	
   	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
	    -ms-transition: all 0.3s ease-in-out;
	     -o-transition: all 0.3s ease-in-out;
	        transition: all 0.3s ease-in-out;
    
}

.go-top i {
	font-size: 14px;
	color: #fff;
	font-weight: 600;
}

.go-top:hover {
    background-color: #181818;
}

/* Switcher
-------------------------------------------------------------- */
.switcher-container {
   position: fixed;
   right: -220px;
   top: 137px;
   width: 220px;
   background-color: #000;
   z-index: 99999999;
}

.switcher-container h2 {
	color: #fff;
	font-size: 13px;
	font-weight: 700;
	letter-spacing: 0;
    text-transform: uppercase;
	height: 45px;
	line-height: 45px;
	padding-left: 45px;
    padding-right: 0;
	margin: 0;
}

.switcher-container h2 a {
   background-color: #000;
   display: block;
   position: absolute;
   left: -45px;
   top: 0;
   width: 45px;
   height: 45px;
   line-height: 45px;
   text-align: center;
   outline: 0;
   color: #fff;
   -webkit-transition: all 0.3s ease-in-out;
       -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
         -o-transition: all 0.3s ease-in-out;
            transition: all 0.3s ease-in-out;
}

.switcher-container h2 a.active {
	left: 0;
}

.switcher-container h2 a.active:after {
	position: absolute;
	right: 21px;
	top: 0;
	content: "\f105";
	font-family: "FontAwesome";
	color: #f1a619;
	font-size: 22px;
}

.switcher-container h2 a.active i {
	display: none;
}

.switcher-container h2 a:hover,
.switcher-container h2 a:focus {
   text-decoration: none;
}

.switcher-container h2 i {
   	margin-top: 10px;
   	font-size: 25px;
   	color: #f1a619;
    -webkit-animation: fa-spin 2s infinite linear;
            animation: fa-spin 2s infinite linear; 
    -webkit-transition: all 0.3s ease-in-out;
       -moz-transition: all 0.3s ease-in-out;
        -ms-transition: all 0.3s ease-in-out;
         -o-transition: all 0.3s ease-in-out;
            transition: all 0.3s ease-in-out;
}

.switcher-container h2 i:hover {
	color: #fff;
}

.switcher-container h3 {
   font-size: 12px;
   font-weight: 700;
   color: #fff;
   margin: 0;
   line-height: 22px;
   margin-bottom: 10px;
}

.switcher-container .selector-box {   
   color: #fff;
   overflow: hidden;
}

.switcher-container .layout-switcher {
   margin: 0 0 10px 0;
   overflow: hidden;
}

.switcher-container .layout-switcher a.layout {
   float: left;
   display: block;
   cursor: pointer;
   text-align: center;
   font-weight: 700;
   padding: 10px 20px;
   margin-left: 10px;
}

.switcher-container .layout-switcher a.layout:first-child {
   margin-left: 0;
}

.switcher-container .layout-switcher a.layout:hover {
   color: #fff;
   cursor: pointer;
}

.switcher-container .color-box {
   height: auto;
   overflow: hidden;
   margin-bottom: 6px;
}

.switcher-container .styleswitch {
    margin-bottom: 10px;
}

.sw-odd {
	background-color: #272727;
	padding: 21px 0 30px 20px;
	-webkit-transition: all 0.3s;
	   -moz-transition: all 0.3s;
		-ms-transition: all 0.3s;
		 -o-transition: all 0.3s;
			transition: all 0.3s;
}

.sw-even {
	background-color: #191919;
	padding: 21px 0 25px 20px;
	-webkit-transition: all 0.3s;
	   -moz-transition: all 0.3s;
		-ms-transition: all 0.3s;
		 -o-transition: all 0.3s;
			transition: all 0.3s;
}

.sw-even a {	
	font-family: "Karla", sans-serif;
    text-transform: uppercase;
	font-size: 12px;
	line-height: 40px;
	color: #fff;
	border: 1px solid #fff;
	padding: 10px 20px 10px 20px;
	margin-right: 10px;
	letter-spacing: 1.8px;
}

.sw-even a:hover {
	background-color: #32bfc0;
	border: 1px solid #32bfc0;
	color: #fff !important;
}

.sw-light {
	background-color: #fff;
	color: #000 !important;
}

.sw-odd a {	
	font-size: 16px;
	color: #fff;
	width: 100%;
	display: inline-block;
	line-height: 17px;
	width: 100%;
	position: relative;
	padding-left: 47px;
}

.sw-odd .ws-colors a:before {
	background: none;
}

.sw-odd .ws-colors a {
	position: relative;
	width: auto;
	padding: 0;	
	width: 30px;
	height: 30px;
	background-color: #333;	
	display: inline-block;
	margin-right: 5px;	
	overflow: hidden;
}

.sw-odd .ws-colors a.current:before {
	position: absolute;
    left: 8px;
    top: 6px;
	font-family: FontAwesome;
	content: "\f00c";
	color: #fff;
	z-index: 999;
	text-shadow: 0 0 2px rgba( 0, 0, 0, 1 );
}


.sw-odd .ws-colors #color1 {
	background-color: #f1a619;
}

.sw-odd .ws-colors #color2 {
	background-color: #77b2ff;
}

.sw-odd .ws-colors #color3 {
	background-color: #f2c21a;
}

.sw-odd .ws-colors #color4 {
	background-color: #e33062;
}

.sw-odd .ws-colors #color5 {
	background-color: #29c2e1;
}

.sw-odd .ws-colors #color6 {
	background-color: #9b23ea;
}

.sw-odd #color2 {
	background-color: #333;
}

.sw-odd .ws-colors a#color2:after {
	border-bottom: 21px solid transparent;
    border-top: 21px solid transparent;
    border-left: 21px solid #ff5f2e;
}

.sw-odd #color3 {
	background-color: #333;
}

.sw-odd .ws-colors a#color3:after {
	border-bottom: 21px solid transparent;
    border-top: 21px solid transparent;
    border-left: 21px solid #30a9de;
}

.sw-odd #color4 {
	background-color: #333;
}

.sw-odd .ws-colors a#color4:after {
	border-bottom: 21px solid transparent;
    border-top: 21px solid transparent;
    border-left: 21px solid #d9e1e8;
}

.sw-odd #color5 {
	background-color: #333;
}

.sw-odd .ws-colors a#color5:after {
	border-bottom: 21px solid transparent;
    border-top: 21px solid transparent;
    border-left: 21px solid #090707;
}

.sw-odd #color6 {
	background-color: #333;
}

.sw-odd .ws-colors a#color6:after {
	border-bottom: 21px solid transparent;
    border-top: 21px solid transparent;
    border-left: 21px solid #79bd9a;
}

.sw-even h3 {
	margin-bottom: 6px;
}

/* Pattern */
.sw-pattern.pattern {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	   	filter: alpha(opacity=0);
	   opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
	    -ms-transition: all 0.3s ease-in-out;
	     -o-transition: all 0.3s ease-in-out;
	        transition: all 0.3s ease-in-out;
	position: absolute;
	left: 0;
	top: 98%;
	background-color: #000000;
	width: 100%;
	z-index: -1;
	padding: 20px 0 30px 20px;
}

.sw-pattern.pattern a {	
    width: 40px;
    height: 40px;
    display: inline-block;
    margin-right: 5px;
    margin-bottom: 5px;
    position: relative;
}

.sw-pattern.pattern a.current:before {
	position: absolute;
    left: 12px;
    top: 6px;
	font-family: FontAwesome;
	content: "\f00c";
	color: #fff;
	text-shadow: 0 0 2px rgba( 0, 0, 0, 1 );
}

/*switcher image http://www.bootstrapmb.com*/
.sw-odd .sw-image {
	 padding: 0 20px 15px 0;
}

.sw-odd .sw-image a{
	padding: 0;
	margin-bottom: 5px;
}

.sw-odd .sw-image .sw-buy {
	width: 100%;	
    border: none;
    background: #6f9a37; 
    position: relative;	
    height: 35px;
    line-height: 37px;
    border-radius: 0;
}

.sw-odd .sw-image .sw-buy:before {
	content: "\f07a";
	font-family: "FontAwesome";
	color: #fff;
	position: absolute;
	left: 20%;
	top: -1px;
}

.sw-odd .sw-image .sw-buy:hover {
	background: #2e363a;
}

/* Preload http://www.bootstrapmb.com
-------------------------------------------------------------- */
#loading-overlay {
    position: fixed;
    z-index: 999999;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: block;
    background: #fff;
}

.loader {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    margin: -30px 0 0 -30px;
}

.loader:before,
.loader:after {
    position: absolute;
    display: inline-block;
    width: 60px;
    height: 60px;
    content: "";
    -webkit-animation: preloader-wave 1.6s linear infinite;
        animation: preloader-wave 1.6s linear infinite; 
    border-radius: 50%;
    background: #f1a619;
}

.loader:after {
    -webkit-animation-delay: -.8s;
       animation-delay: -.8s;
}

@-webkit-keyframes preloader-wave {
    0% {
        -webkit-transform: scale(0, 0);
            transform: scale(0, 0);
        opacity: .5;
    }
    100% {
        -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
        opacity: 0;
    }
}

@keyframes preloader-wave {
    0% {
        -webkit-transform: scale(0, 0);
            transform: scale(0, 0);
        opacity: .5;
    }
    100% {
        -webkit-transform: scale(1, 1);
            transform: scale(1, 1);
        opacity: 0;
    }
}