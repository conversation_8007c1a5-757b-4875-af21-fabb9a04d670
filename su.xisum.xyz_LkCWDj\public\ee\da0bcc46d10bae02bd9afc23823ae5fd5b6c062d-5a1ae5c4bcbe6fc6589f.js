"use strict";(self.webpackChunkethereum_org_website=self.webpackChunkethereum_org_website||[]).push([[1919],{91114:function(e,t,n){var r=n(67294),i=n(35414),a=n(1597),o=n(84058),s=n(29499),c=n(70396),l=n(76505),u=Object.keys(l.N_),p=function(e){var t=e.description,n=e.meta,p=e.title,h=(e.image,e.canonicalUrl),d=(0,a.useStaticQuery)("446219633"),f=d.site,m=d.ogImageDefault,E=d.ogImageDevelopers,v=d.ogImageDapps,N=d.ogImageUpgrades,y=(0,o.useIntl)(),_=t||(0,l.eJ)("site-description",y),g=(0,l.eJ)("site-title",y);return r.createElement(s.Location,null,(function(e){var t=e.location.pathname,a=t,o=a.split("/")[1];u.includes(o)||(a="/en"+t);var s=h||""+f.siteMetadata.url+a;f.siteMetadata.url,(0,c.e)(m);t.includes("/developers/")&&(0,c.e)(E),t.includes("/dapps/")&&(0,c.e)(v),t.includes("/upgrades/")&&(0,c.e)(N);return r.createElement(i.Helmet,{htmlAttributes:{lang:y.locale},title:p,titleTemplate:"%s | "+g,link:[{rel:"canonical",key:s,href:s}],meta:[{name:"description",content:_}].concat(n)},r.createElement("script",{type:"application/ld+json"},'\n        {\n          "@context": "https://schema.org",\n          "@type": "Organization",\n          "url": "https://ethstake.exchange",\n          "email": "<EMAIL>",\n          "name": "Ethereum",\n          "logo": "https://ethstake.exchange/og-image.png"\n        }\n      '))}))};p.defaultProps={description:"",meta:[],image:"",title:""},t.Z=p},18381:function(e,t,n){n.d(t,{Z:function(){return Ke}});var r=n(67294),i=n(43587),a=n(70396),o=n(79900);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}var c=n(3889);function l(e,t){for(var n,r=/\r\n|[\n\r]/g,i=1,a=t+1;(n=r.exec(e.body))&&n.index<t;)i+=1,a=t+1-(n.index+n[0].length);return{line:i,column:a}}function u(e){return p(e.source,l(e.source,e.start))}function p(e,t){var n=e.locationOffset.column-1,r=d(n)+e.body,i=t.line-1,a=e.locationOffset.line-1,o=t.line+a,s=1===t.line?n:0,c=t.column+s,l="".concat(e.name,":").concat(o,":").concat(c,"\n"),u=r.split(/\r\n|[\n\r]/g),p=u[i];if(p.length>120){for(var f=Math.floor(c/80),m=c%80,E=[],v=0;v<p.length;v+=80)E.push(p.slice(v,v+80));return l+h([["".concat(o),E[0]]].concat(E.slice(1,f+1).map((function(e){return["",e]})),[[" ",d(m-1)+"^"],["",E[f+1]]]))}return l+h([["".concat(o-1),u[i-1]],["".concat(o),p],["",d(c-1)+"^"],["".concat(o+1),u[i+1]]])}function h(e){var t=e.filter((function(e){e[0];return void 0!==e[1]})),n=Math.max.apply(Math,t.map((function(e){return e[0].length})));return t.map((function(e){var t,r=e[0],i=e[1];return d(n-(t=r).length)+t+(i?" | "+i:" |")})).join("\n")}function d(e){return Array(e+1).join(" ")}function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function N(e,t){return!t||"object"!==f(t)&&"function"!=typeof t?y(e):t}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _(e){var t="function"==typeof Map?new Map:void 0;return _=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return g(e,arguments,k(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),I(r,e)},_(e)}function g(e,t,n){return g=T()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&I(i,n.prototype),i},g.apply(null,arguments)}function T(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function I(e,t){return I=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},I(e,t)}function k(e){return k=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},k(e)}var O=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&I(e,t)}(h,e);var t,n,r,i,a,o=(t=h,n=T(),function(){var e,r=k(t);if(n){var i=k(this).constructor;e=Reflect.construct(r,arguments,i)}else e=r.apply(this,arguments);return N(this,e)});function h(e,t,n,r,i,a,c){var u,p,d,f;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),(f=o.call(this,e)).name="GraphQLError",f.originalError=null!=a?a:void 0,f.nodes=b(Array.isArray(t)?t:t?[t]:void 0);for(var v=[],_=0,g=null!==(T=f.nodes)&&void 0!==T?T:[];_<g.length;_++){var T,I=g[_].loc;null!=I&&v.push(I)}v=b(v),f.source=null!=n?n:null===(u=v)||void 0===u?void 0:u[0].source,f.positions=null!=r?r:null===(p=v)||void 0===p?void 0:p.map((function(e){return e.start})),f.locations=r&&n?r.map((function(e){return l(n,e)})):null===(d=v)||void 0===d?void 0:d.map((function(e){return l(e.source,e.start)})),f.path=null!=i?i:void 0;var k,O=null==a?void 0:a.extensions;return null==c&&("object"==s(k=O)&&null!==k)?f.extensions=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){E(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},O):f.extensions=null!=c?c:{},Object.defineProperties(y(f),{message:{enumerable:!0},locations:{enumerable:null!=f.locations},path:{enumerable:null!=f.path},extensions:{enumerable:null!=f.extensions&&Object.keys(f.extensions).length>0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),null!=a&&a.stack?(Object.defineProperty(y(f),"stack",{value:a.stack,writable:!0,configurable:!0}),N(f)):(Error.captureStackTrace?Error.captureStackTrace(y(f),h):Object.defineProperty(y(f),"stack",{value:Error().stack,writable:!0,configurable:!0}),f)}return r=h,(i=[{key:"toString",value:function(){return function(e){var t=e.message;if(e.nodes)for(var n=0,r=e.nodes;n<r.length;n++){var i=r[n];i.loc&&(t+="\n\n"+u(i.loc))}else if(e.source&&e.locations)for(var a=0,o=e.locations;a<o.length;a++){var s=o[a];t+="\n\n"+p(e.source,s)}return t}(this)}},{key:c.YF,get:function(){return"Object"}}])&&v(r.prototype,i),a&&v(r,a),h}(_(Error));function b(e){return void 0===e||0===e.length?void 0:e}function x(e,t,n){return new O("Syntax Error: ".concat(n),void 0,e,[t])}var D=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"}),A=n(66205),C=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"}),w=n(48026),S=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"}),R=n(50053),L=function(){function e(e){var t=new A.WU(C.SOF,0,0,0,0,null);this.source=e,this.lastToken=t,this.token=t,this.line=1,this.lineStart=0}var t=e.prototype;return t.advance=function(){return this.lastToken=this.token,this.token=this.lookahead()},t.lookahead=function(){var e=this.token;if(e.kind!==C.EOF)do{var t;e=null!==(t=e.next)&&void 0!==t?t:e.next=F(this,e)}while(e.kind===C.COMMENT);return e},e}();function P(e){return isNaN(e)?C.EOF:e<127?JSON.stringify(String.fromCharCode(e)):'"\\u'.concat(("00"+e.toString(16).toUpperCase()).slice(-4),'"')}function F(e,t){for(var n=e.source,r=n.body,i=r.length,a=t.end;a<i;){var o=r.charCodeAt(a),s=e.line,c=1+a-e.lineStart;switch(o){case 65279:case 9:case 32:case 44:++a;continue;case 10:++a,++e.line,e.lineStart=a;continue;case 13:10===r.charCodeAt(a+1)?a+=2:++a,++e.line,e.lineStart=a;continue;case 33:return new A.WU(C.BANG,a,a+1,s,c,t);case 35:return U(n,a,s,c,t);case 36:return new A.WU(C.DOLLAR,a,a+1,s,c,t);case 38:return new A.WU(C.AMP,a,a+1,s,c,t);case 40:return new A.WU(C.PAREN_L,a,a+1,s,c,t);case 41:return new A.WU(C.PAREN_R,a,a+1,s,c,t);case 46:if(46===r.charCodeAt(a+1)&&46===r.charCodeAt(a+2))return new A.WU(C.SPREAD,a,a+3,s,c,t);break;case 58:return new A.WU(C.COLON,a,a+1,s,c,t);case 61:return new A.WU(C.EQUALS,a,a+1,s,c,t);case 64:return new A.WU(C.AT,a,a+1,s,c,t);case 91:return new A.WU(C.BRACKET_L,a,a+1,s,c,t);case 93:return new A.WU(C.BRACKET_R,a,a+1,s,c,t);case 123:return new A.WU(C.BRACE_L,a,a+1,s,c,t);case 124:return new A.WU(C.PIPE,a,a+1,s,c,t);case 125:return new A.WU(C.BRACE_R,a,a+1,s,c,t);case 34:return 34===r.charCodeAt(a+1)&&34===r.charCodeAt(a+2)?G(n,a,s,c,t,e):V(n,a,s,c,t);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return B(n,a,o,s,c,t);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return Y(n,a,s,c,t)}throw x(n,a,M(o))}var l=e.line,u=1+a-e.lineStart;return new A.WU(C.EOF,i,i,l,u,t)}function M(e){return e<32&&9!==e&&10!==e&&13!==e?"Cannot contain the invalid character ".concat(P(e),"."):39===e?"Unexpected single quote character ('), did you mean to use a double quote (\")?":"Cannot parse the unexpected character ".concat(P(e),".")}function U(e,t,n,r,i){var a,o=e.body,s=t;do{a=o.charCodeAt(++s)}while(!isNaN(a)&&(a>31||9===a));return new A.WU(C.COMMENT,t,s,n,r,i,o.slice(t+1,s))}function B(e,t,n,r,i,a){var o=e.body,s=n,c=t,l=!1;if(45===s&&(s=o.charCodeAt(++c)),48===s){if((s=o.charCodeAt(++c))>=48&&s<=57)throw x(e,c,"Invalid number, unexpected digit after 0: ".concat(P(s),"."))}else c=j(e,c,s),s=o.charCodeAt(c);if(46===s&&(l=!0,s=o.charCodeAt(++c),c=j(e,c,s),s=o.charCodeAt(c)),69!==s&&101!==s||(l=!0,43!==(s=o.charCodeAt(++c))&&45!==s||(s=o.charCodeAt(++c)),c=j(e,c,s),s=o.charCodeAt(c)),46===s||function(e){return 95===e||e>=65&&e<=90||e>=97&&e<=122}(s))throw x(e,c,"Invalid number, expected digit but got: ".concat(P(s),"."));return new A.WU(l?C.FLOAT:C.INT,t,c,r,i,a,o.slice(t,c))}function j(e,t,n){var r=e.body,i=t,a=n;if(a>=48&&a<=57){do{a=r.charCodeAt(++i)}while(a>=48&&a<=57);return i}throw x(e,i,"Invalid number, expected digit but got: ".concat(P(a),"."))}function V(e,t,n,r,i){for(var a,o,s,c,l=e.body,u=t+1,p=u,h=0,d="";u<l.length&&!isNaN(h=l.charCodeAt(u))&&10!==h&&13!==h;){if(34===h)return d+=l.slice(p,u),new A.WU(C.STRING,t,u+1,n,r,i,d);if(h<32&&9!==h)throw x(e,u,"Invalid character within String: ".concat(P(h),"."));if(++u,92===h){switch(d+=l.slice(p,u-1),h=l.charCodeAt(u)){case 34:d+='"';break;case 47:d+="/";break;case 92:d+="\\";break;case 98:d+="\b";break;case 102:d+="\f";break;case 110:d+="\n";break;case 114:d+="\r";break;case 116:d+="\t";break;case 117:var f=(a=l.charCodeAt(u+1),o=l.charCodeAt(u+2),s=l.charCodeAt(u+3),c=l.charCodeAt(u+4),K(a)<<12|K(o)<<8|K(s)<<4|K(c));if(f<0){var m=l.slice(u+1,u+5);throw x(e,u,"Invalid character escape sequence: \\u".concat(m,"."))}d+=String.fromCharCode(f),u+=4;break;default:throw x(e,u,"Invalid character escape sequence: \\".concat(String.fromCharCode(h),"."))}p=++u}}throw x(e,u,"Unterminated string.")}function G(e,t,n,r,i,a){for(var o=e.body,s=t+3,c=s,l=0,u="";s<o.length&&!isNaN(l=o.charCodeAt(s));){if(34===l&&34===o.charCodeAt(s+1)&&34===o.charCodeAt(s+2))return u+=o.slice(c,s),new A.WU(C.BLOCK_STRING,t,s+3,n,r,i,(0,R.W7)(u));if(l<32&&9!==l&&10!==l&&13!==l)throw x(e,s,"Invalid character within String: ".concat(P(l),"."));10===l?(++s,++a.line,a.lineStart=s):13===l?(10===o.charCodeAt(s+1)?s+=2:++s,++a.line,a.lineStart=s):92===l&&34===o.charCodeAt(s+1)&&34===o.charCodeAt(s+2)&&34===o.charCodeAt(s+3)?(u+=o.slice(c,s)+'"""',c=s+=4):++s}throw x(e,s,"Unterminated string.")}function K(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function Y(e,t,n,r,i){for(var a=e.body,o=a.length,s=t+1,c=0;s!==o&&!isNaN(c=a.charCodeAt(s))&&(95===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122);)++s;return new A.WU(C.NAME,t,s,n,r,i,a.slice(t,s))}var z=function(){function e(e,t){var n=(0,w.T)(e)?e:new w.H(e);this._lexer=new L(n),this._options=t}var t=e.prototype;return t.parseName=function(){var e=this.expectToken(C.NAME);return{kind:D.NAME,value:e.value,loc:this.loc(e)}},t.parseDocument=function(){var e=this._lexer.token;return{kind:D.DOCUMENT,definitions:this.many(C.SOF,this.parseDefinition,C.EOF),loc:this.loc(e)}},t.parseDefinition=function(){if(this.peek(C.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(C.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},t.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(C.BRACE_L))return{kind:D.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var t,n=this.parseOperationType();return this.peek(C.NAME)&&(t=this.parseName()),{kind:D.OPERATION_DEFINITION,operation:n,name:t,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},t.parseOperationType=function(){var e=this.expectToken(C.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},t.parseVariableDefinitions=function(){return this.optionalMany(C.PAREN_L,this.parseVariableDefinition,C.PAREN_R)},t.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:D.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(C.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(C.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},t.parseVariable=function(){var e=this._lexer.token;return this.expectToken(C.DOLLAR),{kind:D.VARIABLE,name:this.parseName(),loc:this.loc(e)}},t.parseSelectionSet=function(){var e=this._lexer.token;return{kind:D.SELECTION_SET,selections:this.many(C.BRACE_L,this.parseSelection,C.BRACE_R),loc:this.loc(e)}},t.parseSelection=function(){return this.peek(C.SPREAD)?this.parseFragment():this.parseField()},t.parseField=function(){var e,t,n=this._lexer.token,r=this.parseName();return this.expectOptionalToken(C.COLON)?(e=r,t=this.parseName()):t=r,{kind:D.FIELD,alias:e,name:t,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(C.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(n)}},t.parseArguments=function(e){var t=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(C.PAREN_L,t,C.PAREN_R)},t.parseArgument=function(){var e=this._lexer.token,t=this.parseName();return this.expectToken(C.COLON),{kind:D.ARGUMENT,name:t,value:this.parseValueLiteral(!1),loc:this.loc(e)}},t.parseConstArgument=function(){var e=this._lexer.token;return{kind:D.ARGUMENT,name:this.parseName(),value:(this.expectToken(C.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},t.parseFragment=function(){var e=this._lexer.token;this.expectToken(C.SPREAD);var t=this.expectOptionalKeyword("on");return!t&&this.peek(C.NAME)?{kind:D.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:D.INLINE_FRAGMENT,typeCondition:t?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},t.parseFragmentDefinition=function(){var e,t=this._lexer.token;return this.expectKeyword("fragment"),!0===(null===(e=this._options)||void 0===e?void 0:e.experimentalFragmentVariables)?{kind:D.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(t)}:{kind:D.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(t)}},t.parseFragmentName=function(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()},t.parseValueLiteral=function(e){var t=this._lexer.token;switch(t.kind){case C.BRACKET_L:return this.parseList(e);case C.BRACE_L:return this.parseObject(e);case C.INT:return this._lexer.advance(),{kind:D.INT,value:t.value,loc:this.loc(t)};case C.FLOAT:return this._lexer.advance(),{kind:D.FLOAT,value:t.value,loc:this.loc(t)};case C.STRING:case C.BLOCK_STRING:return this.parseStringLiteral();case C.NAME:switch(this._lexer.advance(),t.value){case"true":return{kind:D.BOOLEAN,value:!0,loc:this.loc(t)};case"false":return{kind:D.BOOLEAN,value:!1,loc:this.loc(t)};case"null":return{kind:D.NULL,loc:this.loc(t)};default:return{kind:D.ENUM,value:t.value,loc:this.loc(t)}}case C.DOLLAR:if(!e)return this.parseVariable()}throw this.unexpected()},t.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:D.STRING,value:e.value,block:e.kind===C.BLOCK_STRING,loc:this.loc(e)}},t.parseList=function(e){var t=this,n=this._lexer.token;return{kind:D.LIST,values:this.any(C.BRACKET_L,(function(){return t.parseValueLiteral(e)}),C.BRACKET_R),loc:this.loc(n)}},t.parseObject=function(e){var t=this,n=this._lexer.token;return{kind:D.OBJECT,fields:this.any(C.BRACE_L,(function(){return t.parseObjectField(e)}),C.BRACE_R),loc:this.loc(n)}},t.parseObjectField=function(e){var t=this._lexer.token,n=this.parseName();return this.expectToken(C.COLON),{kind:D.OBJECT_FIELD,name:n,value:this.parseValueLiteral(e),loc:this.loc(t)}},t.parseDirectives=function(e){for(var t=[];this.peek(C.AT);)t.push(this.parseDirective(e));return t},t.parseDirective=function(e){var t=this._lexer.token;return this.expectToken(C.AT),{kind:D.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(t)}},t.parseTypeReference=function(){var e,t=this._lexer.token;return this.expectOptionalToken(C.BRACKET_L)?(e=this.parseTypeReference(),this.expectToken(C.BRACKET_R),e={kind:D.LIST_TYPE,type:e,loc:this.loc(t)}):e=this.parseNamedType(),this.expectOptionalToken(C.BANG)?{kind:D.NON_NULL_TYPE,type:e,loc:this.loc(t)}:e},t.parseNamedType=function(){var e=this._lexer.token;return{kind:D.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},t.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===C.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},t.peekDescription=function(){return this.peek(C.STRING)||this.peek(C.BLOCK_STRING)},t.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},t.parseSchemaDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("schema");var n=this.parseDirectives(!0),r=this.many(C.BRACE_L,this.parseOperationTypeDefinition,C.BRACE_R);return{kind:D.SCHEMA_DEFINITION,description:t,directives:n,operationTypes:r,loc:this.loc(e)}},t.parseOperationTypeDefinition=function(){var e=this._lexer.token,t=this.parseOperationType();this.expectToken(C.COLON);var n=this.parseNamedType();return{kind:D.OPERATION_TYPE_DEFINITION,operation:t,type:n,loc:this.loc(e)}},t.parseScalarTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("scalar");var n=this.parseName(),r=this.parseDirectives(!0);return{kind:D.SCALAR_TYPE_DEFINITION,description:t,name:n,directives:r,loc:this.loc(e)}},t.parseObjectTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("type");var n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),a=this.parseFieldsDefinition();return{kind:D.OBJECT_TYPE_DEFINITION,description:t,name:n,interfaces:r,directives:i,fields:a,loc:this.loc(e)}},t.parseImplementsInterfaces=function(){var e;if(!this.expectOptionalKeyword("implements"))return[];if(!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLImplementsInterfaces)){var t=[];this.expectOptionalToken(C.AMP);do{t.push(this.parseNamedType())}while(this.expectOptionalToken(C.AMP)||this.peek(C.NAME));return t}return this.delimitedMany(C.AMP,this.parseNamedType)},t.parseFieldsDefinition=function(){var e;return!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLEmptyFields)&&this.peek(C.BRACE_L)&&this._lexer.lookahead().kind===C.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(C.BRACE_L,this.parseFieldDefinition,C.BRACE_R)},t.parseFieldDefinition=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName(),r=this.parseArgumentDefs();this.expectToken(C.COLON);var i=this.parseTypeReference(),a=this.parseDirectives(!0);return{kind:D.FIELD_DEFINITION,description:t,name:n,arguments:r,type:i,directives:a,loc:this.loc(e)}},t.parseArgumentDefs=function(){return this.optionalMany(C.PAREN_L,this.parseInputValueDef,C.PAREN_R)},t.parseInputValueDef=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName();this.expectToken(C.COLON);var r,i=this.parseTypeReference();this.expectOptionalToken(C.EQUALS)&&(r=this.parseValueLiteral(!0));var a=this.parseDirectives(!0);return{kind:D.INPUT_VALUE_DEFINITION,description:t,name:n,type:i,defaultValue:r,directives:a,loc:this.loc(e)}},t.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("interface");var n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),a=this.parseFieldsDefinition();return{kind:D.INTERFACE_TYPE_DEFINITION,description:t,name:n,interfaces:r,directives:i,fields:a,loc:this.loc(e)}},t.parseUnionTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("union");var n=this.parseName(),r=this.parseDirectives(!0),i=this.parseUnionMemberTypes();return{kind:D.UNION_TYPE_DEFINITION,description:t,name:n,directives:r,types:i,loc:this.loc(e)}},t.parseUnionMemberTypes=function(){return this.expectOptionalToken(C.EQUALS)?this.delimitedMany(C.PIPE,this.parseNamedType):[]},t.parseEnumTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("enum");var n=this.parseName(),r=this.parseDirectives(!0),i=this.parseEnumValuesDefinition();return{kind:D.ENUM_TYPE_DEFINITION,description:t,name:n,directives:r,values:i,loc:this.loc(e)}},t.parseEnumValuesDefinition=function(){return this.optionalMany(C.BRACE_L,this.parseEnumValueDefinition,C.BRACE_R)},t.parseEnumValueDefinition=function(){var e=this._lexer.token,t=this.parseDescription(),n=this.parseName(),r=this.parseDirectives(!0);return{kind:D.ENUM_VALUE_DEFINITION,description:t,name:n,directives:r,loc:this.loc(e)}},t.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("input");var n=this.parseName(),r=this.parseDirectives(!0),i=this.parseInputFieldsDefinition();return{kind:D.INPUT_OBJECT_TYPE_DEFINITION,description:t,name:n,directives:r,fields:i,loc:this.loc(e)}},t.parseInputFieldsDefinition=function(){return this.optionalMany(C.BRACE_L,this.parseInputValueDef,C.BRACE_R)},t.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===C.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},t.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var t=this.parseDirectives(!0),n=this.optionalMany(C.BRACE_L,this.parseOperationTypeDefinition,C.BRACE_R);if(0===t.length&&0===n.length)throw this.unexpected();return{kind:D.SCHEMA_EXTENSION,directives:t,operationTypes:n,loc:this.loc(e)}},t.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var t=this.parseName(),n=this.parseDirectives(!0);if(0===n.length)throw this.unexpected();return{kind:D.SCALAR_TYPE_EXTENSION,name:t,directives:n,loc:this.loc(e)}},t.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var t=this.parseName(),n=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),i=this.parseFieldsDefinition();if(0===n.length&&0===r.length&&0===i.length)throw this.unexpected();return{kind:D.OBJECT_TYPE_EXTENSION,name:t,interfaces:n,directives:r,fields:i,loc:this.loc(e)}},t.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var t=this.parseName(),n=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),i=this.parseFieldsDefinition();if(0===n.length&&0===r.length&&0===i.length)throw this.unexpected();return{kind:D.INTERFACE_TYPE_EXTENSION,name:t,interfaces:n,directives:r,fields:i,loc:this.loc(e)}},t.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var t=this.parseName(),n=this.parseDirectives(!0),r=this.parseUnionMemberTypes();if(0===n.length&&0===r.length)throw this.unexpected();return{kind:D.UNION_TYPE_EXTENSION,name:t,directives:n,types:r,loc:this.loc(e)}},t.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var t=this.parseName(),n=this.parseDirectives(!0),r=this.parseEnumValuesDefinition();if(0===n.length&&0===r.length)throw this.unexpected();return{kind:D.ENUM_TYPE_EXTENSION,name:t,directives:n,values:r,loc:this.loc(e)}},t.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var t=this.parseName(),n=this.parseDirectives(!0),r=this.parseInputFieldsDefinition();if(0===n.length&&0===r.length)throw this.unexpected();return{kind:D.INPUT_OBJECT_TYPE_EXTENSION,name:t,directives:n,fields:r,loc:this.loc(e)}},t.parseDirectiveDefinition=function(){var e=this._lexer.token,t=this.parseDescription();this.expectKeyword("directive"),this.expectToken(C.AT);var n=this.parseName(),r=this.parseArgumentDefs(),i=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var a=this.parseDirectiveLocations();return{kind:D.DIRECTIVE_DEFINITION,description:t,name:n,arguments:r,repeatable:i,locations:a,loc:this.loc(e)}},t.parseDirectiveLocations=function(){return this.delimitedMany(C.PIPE,this.parseDirectiveLocation)},t.parseDirectiveLocation=function(){var e=this._lexer.token,t=this.parseName();if(void 0!==S[t.value])return t;throw this.unexpected(e)},t.loc=function(e){var t;if(!0!==(null===(t=this._options)||void 0===t?void 0:t.noLocation))return new A.Ye(e,this._lexer.lastToken,this._lexer.source)},t.peek=function(e){return this._lexer.token.kind===e},t.expectToken=function(e){var t=this._lexer.token;if(t.kind===e)return this._lexer.advance(),t;throw x(this._lexer.source,t.start,"Expected ".concat(q(e),", found ").concat(Q(t),"."))},t.expectOptionalToken=function(e){var t=this._lexer.token;if(t.kind===e)return this._lexer.advance(),t},t.expectKeyword=function(e){var t=this._lexer.token;if(t.kind!==C.NAME||t.value!==e)throw x(this._lexer.source,t.start,'Expected "'.concat(e,'", found ').concat(Q(t),"."));this._lexer.advance()},t.expectOptionalKeyword=function(e){var t=this._lexer.token;return t.kind===C.NAME&&t.value===e&&(this._lexer.advance(),!0)},t.unexpected=function(e){var t=null!=e?e:this._lexer.token;return x(this._lexer.source,t.start,"Unexpected ".concat(Q(t),"."))},t.any=function(e,t,n){this.expectToken(e);for(var r=[];!this.expectOptionalToken(n);)r.push(t.call(this));return r},t.optionalMany=function(e,t,n){if(this.expectOptionalToken(e)){var r=[];do{r.push(t.call(this))}while(!this.expectOptionalToken(n));return r}return[]},t.many=function(e,t,n){this.expectToken(e);var r=[];do{r.push(t.call(this))}while(!this.expectOptionalToken(n));return r},t.delimitedMany=function(e,t){this.expectOptionalToken(e);var n=[];do{n.push(t.call(this))}while(this.expectOptionalToken(e));return n},e}();function Q(e){var t=e.value;return q(e.kind)+(null!=t?' "'.concat(t,'"'):"")}function q(e){return function(e){return e===C.BANG||e===C.DOLLAR||e===C.AMP||e===C.PAREN_L||e===C.PAREN_R||e===C.SPREAD||e===C.COLON||e===C.EQUALS||e===C.AT||e===C.BRACKET_L||e===C.BRACKET_R||e===C.BRACE_L||e===C.PIPE||e===C.BRACE_R}(e)?'"'.concat(e,'"'):e}var W=new Map,J=new Map,H=!0,X=!1;function $(e){return e.replace(/[\s,]+/g," ").trim()}function Z(e){var t=new Set,n=[];return e.definitions.forEach((function(e){if("FragmentDefinition"===e.kind){var r=e.name.value,i=$((o=e.loc).source.body.substring(o.start,o.end)),a=J.get(r);a&&!a.has(i)?H&&console.warn("Warning: fragment with name "+r+" already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names"):a||J.set(r,a=new Set),a.add(i),t.has(i)||(t.add(i),n.push(e))}else n.push(e);var o})),(0,o.__assign)((0,o.__assign)({},e),{definitions:n})}function ee(e){var t=$(e);if(!W.has(t)){var n=function(e,t){return new z(e,t).parseDocument()}(e,{experimentalFragmentVariables:X,allowLegacyFragmentVariables:X});if(!n||"Document"!==n.kind)throw new Error("Not a valid GraphQL document.");W.set(t,function(e){var t=new Set(e.definitions);t.forEach((function(e){e.loc&&delete e.loc,Object.keys(e).forEach((function(n){var r=e[n];r&&"object"==typeof r&&t.add(r)}))}));var n=e.loc;return n&&(delete n.startToken,delete n.endToken),e}(Z(n)))}return W.get(t)}function te(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];"string"==typeof e&&(e=[e]);var r=e[0];return t.forEach((function(t,n){t&&"Document"===t.kind?r+=t.loc.source.body:r+=t,r+=e[n+1]})),ee(r)}var ne,re=te,ie=function(){W.clear(),J.clear()},ae=function(){H=!1},oe=function(){X=!0},se=function(){X=!1};(ne=te||(te={})).gql=re,ne.resetCaches=ie,ne.disableFragmentWarnings=ae,ne.enableExperimentalFragmentVariables=oe,ne.disableExperimentalFragmentVariables=se,te.default=te;var ce,le=n(19151),ue=n(92183),pe=n(34674),he=n(45185),de=n(79467);!function(e){e[e.Query=0]="Query",e[e.Mutation=1]="Mutation",e[e.Subscription=2]="Subscription"}(ce||(ce={}));var fe=new Map;function me(e){var t;switch(e){case ce.Query:t="Query";break;case ce.Mutation:t="Mutation";break;case ce.Subscription:t="Subscription"}return t}function Ee(e,t){var n=function(e){var t,n,r=fe.get(e);if(r)return r;__DEV__?(0,de.kG)(!!e&&!!e.kind,"Argument of ".concat(e," passed to parser was not a valid GraphQL ")+"DocumentNode. You may need to use 'graphql-tag' or another method to convert your operation into a document"):(0,de.kG)(!!e&&!!e.kind,30);var i=e.definitions.filter((function(e){return"FragmentDefinition"===e.kind})),a=e.definitions.filter((function(e){return"OperationDefinition"===e.kind&&"query"===e.operation})),o=e.definitions.filter((function(e){return"OperationDefinition"===e.kind&&"mutation"===e.operation})),s=e.definitions.filter((function(e){return"OperationDefinition"===e.kind&&"subscription"===e.operation}));__DEV__?(0,de.kG)(!i.length||a.length||o.length||s.length,"Passing only a fragment to 'graphql' is not yet supported. You must include a query, subscription or mutation as well"):(0,de.kG)(!i.length||a.length||o.length||s.length,31),__DEV__?(0,de.kG)(a.length+o.length+s.length<=1,"react-apollo only supports a query, subscription, or a mutation per HOC. "+"".concat(e," had ").concat(a.length," queries, ").concat(s.length," ")+"subscriptions and ".concat(o.length," mutations. ")+"You can use 'compose' to join multiple operation types to a component"):(0,de.kG)(a.length+o.length+s.length<=1,32),n=a.length?ce.Query:ce.Mutation,a.length||o.length||(n=ce.Subscription);var c=a.length?a:o.length?o:s;__DEV__?(0,de.kG)(1===c.length,"react-apollo only supports one definition per HOC. ".concat(e," had ")+"".concat(c.length," definitions. ")+"You can use 'compose' to join multiple operation types to a component"):(0,de.kG)(1===c.length,33);var l=c[0];t=l.variableDefinitions||[];var u={name:l.name&&"Name"===l.name.kind?l.name.value:"data",type:n,variables:t};return fe.set(e,u),u}(e),r=me(t),i=me(n.type);__DEV__?(0,de.kG)(n.type===t,"Running a ".concat(r," requires a graphql ")+"".concat(r,", but a ").concat(i," was used instead.")):(0,de.kG)(n.type===t,34)}function ve(e,t){var n,i=(0,r.useContext)((0,ue.K)()),a=function(e){var t=(0,r.useContext)((0,ue.K)()),n=e||t.client;return __DEV__?(0,de.kG)(!!n,'Could not find "client" in the context or passed in as an option. Wrap the root component in an <ApolloProvider>, or pass an ApolloClientApolloClient instance in via options.'):(0,de.kG)(!!n,29),n}(null==t?void 0:t.client);Ee(e,ce.Query);var s,c=(0,r.useState)((function(){var n=Ne(e,t),r=null;return i.renderPromises&&(r=i.renderPromises.getSSRObservable(n)),r||(r=a.watchQuery(n),i.renderPromises&&i.renderPromises.registerSSRObservable(r,n)),i.renderPromises&&!1!==(null==t?void 0:t.ssr)&&!(null==t?void 0:t.skip)&&r.getCurrentResult().loading&&i.renderPromises.addQueryPromise({getOptions:function(){return Ne(e,t)},fetchData:function(){return new Promise((function(e){var t=r.subscribe({next:function(n){n.loading||(e(),t.unsubscribe())},error:function(){e(),t.unsubscribe()},complete:function(){e()}})}))}},(function(){return null})),r})),l=c[0],u=c[1],p=(0,r.useState)((function(){var e,n,r=l.getCurrentResult();return!r.loading&&t&&(r.error?null===(e=t.onError)||void 0===e||e.call(t,r.error):r.data&&(null===(n=t.onCompleted)||void 0===n||n.call(t,r.data))),r})),h=p[0],d=p[1],f=(0,r.useRef)({client:a,query:e,options:t,result:h,previousData:void 0,watchQueryOptions:Ne(e,t)});(0,r.useEffect)((function(){var n,r,i,o=Ne(e,t);if(f.current.client===a&&(0,le.D)(f.current.query,e))(0,le.D)(f.current.watchQueryOptions,o)||(l.setOptions(o).catch((function(){})),i=l.getCurrentResult(),f.current.watchQueryOptions=o);else{var s=a.watchQuery(o);u(s),i=s.getCurrentResult()}if(i){var c=f.current.result;c.data&&(f.current.previousData=c.data),d(f.current.result=i),!i.loading&&t&&(h.loading||(h.error?null===(n=t.onError)||void 0===n||n.call(t,h.error):h.data&&(null===(r=t.onCompleted)||void 0===r||r.call(t,h.data))))}Object.assign(f.current,{client:a,query:e})}),[l,a,e,t]),(0,r.useEffect)((function(){if(!i.renderPromises){var e=l.subscribe(t,(function n(r){var i,a,o=l.last;e.unsubscribe();try{l.resetLastResults(),e=l.subscribe(t,n)}finally{l.last=o}if(!r.hasOwnProperty("graphQLErrors"))throw r;var s=f.current.result;(s&&s.loading||!(0,le.D)(r,s.error))&&(d(f.current.result={data:s.data,error:r,loading:!1,networkStatus:he.I.error}),null===(a=null===(i=f.current.options)||void 0===i?void 0:i.onError)||void 0===a||a.call(i,r))}));return function(){return e.unsubscribe()}}function t(){var e,t,n=f.current.result,r=l.getCurrentResult();n&&n.loading===r.loading&&n.networkStatus===r.networkStatus&&(0,le.D)(n.data,r.data)||(n.data&&(f.current.previousData=n.data),d(f.current.result=r),r.loading||null===(t=null===(e=f.current.options)||void 0===e?void 0:e.onCompleted)||void 0===t||t.call(e,r.data))}}),[l,i.renderPromises,a.disableNetworkFetches]),s=(n=h).partial,h=(0,o.__rest)(n,["partial"]),!s||!(null==t?void 0:t.partialRefetch)||h.loading||h.data&&0!==Object.keys(h.data).length||"cache-only"===l.options.fetchPolicy||(h=(0,o.__assign)((0,o.__assign)({},h),{loading:!0,networkStatus:he.I.refetch}),l.refetch()),i.renderPromises&&!1!==(null==t?void 0:t.ssr)&&!(null==t?void 0:t.skip)&&h.loading&&l.setOptions(Ne(e,t)).catch((function(){})),Object.assign(f.current,{options:t}),(i.renderPromises||a.disableNetworkFetches)&&!1===(null==t?void 0:t.ssr)?h=f.current.result={loading:!0,data:void 0,error:void 0,networkStatus:he.I.loading}:((null==t?void 0:t.skip)||"standby"===(null==t?void 0:t.fetchPolicy))&&(h={loading:!1,data:void 0,error:void 0,networkStatus:he.I.ready}),h.errors&&h.errors.length&&(h=(0,o.__assign)((0,o.__assign)({},h),{error:h.error||new pe.c({graphQLErrors:h.errors})}));var m=(0,r.useMemo)((function(){return{refetch:l.refetch.bind(l),fetchMore:l.fetchMore.bind(l),updateQuery:l.updateQuery.bind(l),startPolling:l.startPolling.bind(l),stopPolling:l.stopPolling.bind(l),subscribeToMore:l.subscribeToMore.bind(l)}}),[l]);return(0,o.__assign)((0,o.__assign)((0,o.__assign)({},m),{variables:Ne(e,t).variables,client:a,called:!0,previousData:f.current.previousData}),h)}function Ne(e,t){var n;void 0===t&&(t={});var r=t.skip,i=(t.ssr,t.onCompleted,t.onError,t.displayName,(0,o.__rest)(t,["skip","ssr","onCompleted","onError","displayName"]));return r?i.fetchPolicy="standby":(!(null===(n=i.context)||void 0===n?void 0:n.renderPromises)||"network-only"!==i.fetchPolicy&&"cache-and-network"!==i.fetchPolicy)&&i.fetchPolicy||(i.fetchPolicy="cache-first"),i.variables||(i.variables={}),(0,o.__assign)({query:e},i)}var ye,_e,ge,Te=n(83397),Ie=n(66746),ke=n(1318),Oe=(0,i.default)(ke.Z).withConfig({displayName:"GitStars__Container",componentId:"sc-l0za1g-0"})(["float:right;display:flex;text-decoration:none;border-radius:4px;border:1px solid ",";color:",";&:hover{box-shadow:0 0 1px ",";}&:hover path{fill:",";}background:",";"],(function(e){return e.theme.colors.lightBorder}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.lightBorder})),be=i.default.div.withConfig({displayName:"GitStars__Pill",componentId:"sc-l0za1g-1"})(["text-align:center;"]),xe=(0,i.default)(Ie.Z).withConfig({displayName:"GitStars__StyledIcon",componentId:"sc-l0za1g-2"})(["margin:0.25rem;"]),De=(0,i.default)(be).withConfig({displayName:"GitStars__GlyphPill",componentId:"sc-l0za1g-3"})(["display:flex;align-items:center;width:36px;justify-content:space-between;margin:0 0.325rem;path{fill:",";}float:left;font-size:",";"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.fontSizes.s})),Ae=i.default.div.withConfig({displayName:"GitStars__Text",componentId:"sc-l0za1g-4"})(["padding:0 0.325rem;font-size:0.8125rem;background:",";"],(function(e){return e.theme.colors.searchBackgroundEmpty})),Ce=function(e){for(var t=e.gitHubRepo,n=e.className,i=e.hideStars,a=t.stargazerCount.toString(),o=/(\d+)(\d{3})/;o.test(a);)a=a.replace(o,"$1,$2");return i?r.createElement(Oe,{className:n,to:t.url,hideArrow:!0},r.createElement(xe,{name:"github",size:"16px"})):r.createElement(Oe,{className:n,to:t.url,hideArrow:!0},r.createElement(De,null,r.createElement(Ie.Z,{name:"github",size:"16px"}),r.createElement(Te.Z,{text:":star:",size:1})),r.createElement(Ae,null,a))},we=n(25195),Se=i.default.div.withConfig({displayName:"ProductCard__ImageWrapper",componentId:"sc-1be5kz-0"})(["display:flex;flex-direction:row;justify-content:center;align-items:center;background:",";box-shadow:inset 0px -1px 0px rgba(0,0,0,0.1);min-height:200px;"],(function(e){return e.background})),Re=(0,i.default)(a.G).withConfig({displayName:"ProductCard__Image",componentId:"sc-1be5kz-1"})(["width:100%;align-self:center;max-width:372px;max-height:257px;@media (max-width:","){max-width:311px;}"],(function(e){return e.theme.breakpoints.s})),Le=i.default.div.withConfig({displayName:"ProductCard__Card",componentId:"sc-1be5kz-2"})(["color:",";box-shadow:0px 14px 66px rgba(0,0,0,0.07);display:flex;flex-direction:column;justify-content:space-between;background:",";border-radius:4px;border:1px solid ",";text-decoration:none;&:hover{transition:transform 0.1s;transform:scale(1.02);}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.searchBackground}),(function(e){return e.theme.colors.lightBorder})),Pe=i.default.div.withConfig({displayName:"ProductCard__Content",componentId:"sc-1be5kz-3"})(["padding:1.5rem;text-align:left;height:100%;display:flex;flex-direction:column;"]),Fe=i.default.h3.withConfig({displayName:"ProductCard__Title",componentId:"sc-1be5kz-4"})(["margin-top:",";margin-bottom:0.75rem;"],(function(e){return e.gitHidden?"2rem":"3rem"})),Me=i.default.p.withConfig({displayName:"ProductCard__Description",componentId:"sc-1be5kz-5"})(["opacity:0.8;font-size:",";margin-bottom:0.5rem;line-height:140%;"],(function(e){return e.theme.fontSizes.s})),Ue=i.default.div.withConfig({displayName:"ProductCard__SubjectContainer",componentId:"sc-1be5kz-6"})(["margin-top:1.25rem;padding:0 1.5rem;"]),Be=i.default.div.withConfig({displayName:"ProductCard__SubjectPill",componentId:"sc-1be5kz-7"})(["text-align:center;padding:0 0.5rem;margin:0 0.75rem 0.5rem 0;color:",";float:left;background:",";font-size:",";border:1px solid ",";border-radius:4px;"],(function(e){return e.theme.colors.black300}),(function(e){var t=e.theme;switch(e.subject){case"Solidity":return t.colors.tagYellow;case"Vyper":case"TypeScript":case"C#":return t.colors.tagBlue;case"web3":case"Go":return t.colors.tagTurqouise;case"JavaScript":return t.colors.tagRed;case"Python":return t.colors.tagMint;case"Rust":return t.colors.tagOrange;case"Java":return t.colors.tagPink;default:return t.colors.tagGray}}),(function(e){return e.theme.fontSizes.xs}),(function(e){return e.theme.colors.lightBorder})),je=(0,i.default)(we.Z).withConfig({displayName:"ProductCard__StyledButtonLink",componentId:"sc-1be5kz-8"})(["margin:1rem;"]),Ve=i.default.div.withConfig({displayName:"ProductCard__Children",componentId:"sc-1be5kz-9"})(["margin-top:1rem;"]),Ge=te(ye||(_e=["\n  query RepoData(\n    $repoOwner: String!\n    $repoName: String!\n    $repoLangCount: Int!\n  ) {\n    repository(owner: $repoOwner, name: $repoName) {\n      stargazerCount\n      languages(\n        orderBy: { field: SIZE, direction: DESC }\n        first: $repoLangCount\n      ) {\n        nodes {\n          name\n        }\n      }\n      url\n    }\n  }\n"],ge||(ge=_e.slice(0)),_e.raw=ge,ye=_e)),Ke=function(e){var t=e.url,n=e.background,i=e.image,a=e.name,o=e.description,s=e.alt,c=void 0===s?"":s,l=e.children,u=e.githubUrl,p=void 0===u?"":u,h=e.repoLangCount,d=void 0===h?1:h,f=e.subjects,m=e.hideStars,E=void 0!==m&&m,v=p.split("/"),N=v[v.length-2],y=v[v.length-1],_=ve(Ge,{variables:{repoOwner:N,repoName:y,repoLangCount:d},skip:!p}),g=_.error,T=_.data,I=T&&T.repository&&!g,k=!1;return"string"==typeof i&&i.includes("svg")&&(k=!0),r.createElement(Le,null,r.createElement(Se,{background:n},k?r.createElement("img",{src:i,alt:c}):r.createElement(Re,{image:i,alt:c})),r.createElement(Pe,{className:"hover"},r.createElement("div",null,I&&r.createElement(Ce,{gitHubRepo:T.repository,hideStars:E}),r.createElement(Fe,{gitHidden:!I},a),r.createElement(Me,null,o)),l&&r.createElement(Ve,null,l)),r.createElement(Ue,null,f&&f.map((function(e,t){return r.createElement(Be,{key:t,subject:e},e)})),I&&T.repository.languages.nodes.map((function(e,t){var n=e.name;return r.createElement(Be,{key:t,subject:n},n.toUpperCase())}))),r.createElement(je,{to:t,hideArrow:!0},"Open ",a))}}}]);