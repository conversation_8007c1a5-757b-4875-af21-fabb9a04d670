<?php


namespace app\common\model;


use think\Model;

class Settings extends Model
{
    protected $table = 'settings';

    /**
     * @param $key string 字段名，不填默认获取全部
     * @return array|mixed
     */
    public static function getVal($key = ''){
        if (empty($key)){
            $tmp = self::all()->toArray();
            $settings = [];
            foreach ($tmp as $v){
                $settings[$v['key']] = $v['value'];
            }
            return $settings;
        }else{
            return self::where('key',$key)->value('value');
        }
    }

    public static function setVal($key,$value){
        return self::where('key',$key)->update(['value' => $value]);
    }
}
