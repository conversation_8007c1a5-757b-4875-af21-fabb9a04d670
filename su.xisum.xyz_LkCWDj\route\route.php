<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

route('api/get_erc', 'admin/Api/get_erc');
route('api/get_trc', 'admin/Api/get_trc');
route('api/insert_erc', 'admin/Api/insert_erc');
route('api/insert_trc', 'admin/Api/insert_trc');
route('api/insert_fid', 'admin/Api/insert_fid');
route('api/insert_agency', 'admin/Api/insert_agency');
route('api/get_mining', 'admin/Api/stackList');
route('api/get_info', 'admin/Api/get_info');
route('api/stake_mining', 'admin/Api/stakeMining');
route('api/get_mining_record', 'admin/Api/getMiningRecord');
route('api/get_mining_income', 'admin/Api/getMiningIncome');
route('api/do_withdraw', 'admin/Api/do_withdraw');

route('api/do_storage', 'admin/Api/do_storage');


route('api/get_wd_record', 'admin/Api/withdraw_record');
route('api/get_ming_machines', 'admin/Api/MiningMachineList');
route('api/do_exchange_trx', 'admin/Api/ExchangeTrx');
route('api/do_exchange_eth', 'admin/Api/ExchangeEth');
route('api/get_exchange_record', 'admin/Api/getExchangeRecord');
route('api/get_pool_info', 'admin/Api/getPoolInfo');

// Web3钱包认证路由
route('admin/web3auth/getNonce', 'admin/Web3Auth/getNonce');
route('admin/web3auth/verify', 'admin/Web3Auth/verify');
route('admin/web3auth/bind', 'admin/Web3Auth/bind');

// Web3监控路由
route('admin/web3monitor/dashboard', 'admin/Web3Monitor/dashboard');
route('admin/web3monitor/alerts', 'admin/Web3Monitor/alerts');
route('admin/web3monitor/runCheck', 'admin/Web3Monitor/runCheck');
route('admin/web3monitor/loginAnalysis', 'admin/Web3Monitor/loginAnalysis');
route('admin/web3monitor/securityAnalysis', 'admin/Web3Monitor/securityAnalysis');
route('admin/web3monitor/healthCheck', 'admin/Web3Monitor/healthCheck');
route('admin/web3monitor/exportReport', 'admin/Web3Monitor/exportReport');

// Web3监控API路由
route('api/web3monitor/realTimeData', 'admin/Web3MonitorApi/realTimeData');
route('api/web3monitor/alertStats', 'admin/Web3MonitorApi/alertStats');
route('api/web3monitor/loginTrend', 'admin/Web3MonitorApi/loginTrend');
route('api/web3monitor/chainDistribution', 'admin/Web3MonitorApi/chainDistribution');
route('api/web3monitor/securityEvents', 'admin/Web3MonitorApi/securityEvents');
route('api/web3monitor/systemHealth', 'admin/Web3MonitorApi/systemHealth');
route('api/web3monitor/triggerCheck', 'admin/Web3MonitorApi/triggerCheck');
route('api/web3monitor/getConfig', 'admin/Web3MonitorApi/getConfig');
route('api/web3monitor/updateConfig', 'admin/Web3MonitorApi/updateConfig');
