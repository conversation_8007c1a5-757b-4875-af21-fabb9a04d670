﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta data-react-helmet="true" name="description"
    content="An introduction on what, why, and how to run an Ethereum node.">
  <meta name="theme-color" content="#1c1ce1">
      <script>
  　window.onload = function(){
  　    console.log('页面加载完2成');
 　　const t = document.querySelectorAll("img[data-main-image]");
        for (let e of t) {
            e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"));
            const t = e.parentNode.querySelectorAll("source[data-srcset]");
            for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset");
            e.complete && (e.style.opacity = 1)
        }
　　}
      
  </script>
  <meta name="generator" content="Gatsby 4.7.1">
  <style data-href="/styles.92bedc857ac51bb6cf96.css" data-identity="gatsby-global-css">
    html {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 1rem
    }

    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    main,
    menu,
    nav,
    section,
    summary {
      display: block
    }

    audio,
    canvas,
    progress,
    video {
      display: inline-block
    }

    audio:not([controls]) {
      display: none;
      height: 0
    }

    progress {
      vertical-align: baseline
    }

    [hidden],
    template {
      display: none
    }

    a {
      -webkit-text-decoration-skip: objects;
      background-color: transparent;
      text-decoration: none
    }

    a:active,
    a:hover {
      outline-width: 0
    }

    abbr[title] {
      border-bottom: none;
      text-decoration: underline;
      -webkit-text-decoration: underline dotted;
      text-decoration: underline dotted
    }

    b,
    strong {
      font-weight: inherit;
      font-weight: bolder
    }

    dfn {
      font-style: italic
    }

    h1 {
      font-size: 2em;
      margin: .67em 0
    }

    mark {
      background-color: #ff0;
      color: #000
    }

    small {
      font-size: 80%
    }

    sub,
    sup {
      font-size: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline
    }

    sub {
      bottom: -.25em
    }

    sup {
      top: -.5em
    }

    img {
      border-style: none
    }

    svg:not(:root) {
      overflow: hidden
    }

    code,
    kbd,
    pre,
    samp {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: 1em
    }

    figure {
      margin: 1em 40px
    }

    hr {
      box-sizing: content-box;
      height: 0;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font: inherit;
      margin: 0
    }

    optgroup {
      font-weight: 700
    }

    button,
    input {
      overflow: visible
    }

    button,
    select {
      text-transform: none
    }

    [type=reset],
    [type=submit],
    button,
    html [type=button] {
      -webkit-appearance: button
    }

    [type=button]::-moz-focus-inner,
    [type=reset]::-moz-focus-inner,
    [type=submit]::-moz-focus-inner,
    button::-moz-focus-inner {
      border-style: none;
      padding: 0
    }

    [type=button]:-moz-focusring,
    [type=reset]:-moz-focusring,
    [type=submit]:-moz-focusring,
    button:-moz-focusring {
      outline: 1px dotted ButtonText
    }

    fieldset {
      border: 1px solid silver;
      margin: 0 2px;
      padding: .35em .625em .75em
    }

    legend {
      box-sizing: border-box;
      color: inherit;
      display: table;
      max-width: 100%;
      padding: 0;
      white-space: normal
    }

    textarea {
      overflow: auto
    }

    [type=checkbox],
    [type=radio] {
      box-sizing: border-box;
      padding: 0
    }

    [type=number]::-webkit-inner-spin-button,
    [type=number]::-webkit-outer-spin-button {
      height: auto
    }

    [type=search] {
      -webkit-appearance: textfield;
      outline-offset: -2px
    }

    [type=search]::-webkit-search-cancel-button,
    [type=search]::-webkit-search-decoration {
      -webkit-appearance: none
    }

    ::-webkit-input-placeholder {
      color: inherit;
      opacity: .54
    }

    ::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit
    }

    html {
      box-sizing: border-box;
      font: 100%/1.6em georgia, serif;
      overflow-y: scroll
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      word-wrap: break-word;
      -ms-font-feature-settings: "kern", "liga", "clig", "calt";
      -webkit-font-feature-settings: "kern", "liga", "clig", "calt";
      font-feature-settings: "kern", "liga", "clig", "calt";
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-kerning: normal;
      font-weight: 400
    }

    img {
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      max-width: 100%;
      padding: 0
    }

    h1 {
      font-size: 2.25rem;
      font-weight: 500
    }

    h1,
    h2 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 0 0 1.45rem;
      padding: 0
    }

    h2 {
      font-size: 1.62671rem;
      font-weight: 700
    }

    h3 {
      font-size: 1.38316rem;
      font-weight: 500
    }

    h3,
    h4 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 2rem 0 1.45rem;
      padding: 0
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600
    }

    h5 {
      font-size: 1rem;
      margin: 2rem 0 1.45rem
    }

    h5,
    h6 {
      text-rendering: optimizeLegibility;
      color: inherit;
      font-weight: 500;
      line-height: 1.1;
      padding: 0
    }

    h6 {
      font-size: .85028rem
    }

    h6,
    hgroup {
      margin: 0 0 1.45rem
    }

    hgroup {
      padding: 0
    }

    ol,
    ul {
      list-style-image: none;
      list-style-position: outside;
      margin: 0 0 1.45rem 1.45rem;
      padding: 0
    }

    dd,
    dl,
    figure,
    p {
      margin: 0 0 1.45rem;
      padding: 0
    }

    pre {
      word-wrap: normal;
      background: rgba(0, 0, 0, .04);
      border-radius: 3px;
      font-size: .85rem;
      line-height: 1.42;
      margin: 0 0 1.45rem;
      overflow: auto;
      padding: 1.45rem;
      white-space: pre-wrap
    }

    table {
      border-collapse: collapse;
      font-size: 1rem;
      line-height: 1.45rem;
      width: 100%
    }

    fieldset,
    table {
      margin: 0 0 1.45rem;
      padding: 0
    }

    blockquote {
      margin: 0 1.45rem 1.45rem;
      padding: 0
    }

    form,
    iframe,
    noscript {
      margin: 0 0 1.45rem;
      padding: 0
    }

    hr {
      background: rgba(0, 0, 0, .2);
      border: none;
      height: 1px;
      margin: 4rem 0 0;
      padding: 0
    }

    address {
      margin: 0 0 1.45rem;
      padding: 0
    }

    b,
    dt,
    strong,
    th {
      font-weight: 700
    }

    li {
      margin-bottom: .725rem
    }

    ol li,
    ul li {
      padding-left: 0
    }

    li>ol,
    li>ul {
      margin-bottom: .725rem;
      margin-left: 1.45rem;
      margin-top: .725rem
    }

    blockquote :last-child,
    li :last-child,
    p :last-child {
      margin-bottom: 0
    }

    li>p {
      margin-bottom: .725rem
    }

    code {
      font-size: 1em;
      line-height: 1.45em
    }

    kbd {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: .625rem;
      line-height: 1.56rem
    }

    samp {
      font-size: .85rem;
      line-height: 1.45rem
    }

    abbr,
    abbr[title],
    acronym {
      border-bottom: 1px dotted rgba(0, 0, 0, .5);
      cursor: help
    }

    abbr[title] {
      text-decoration: none
    }

    td,
    th,
    thead {
      text-align: left
    }

    td,
    th {
      font-feature-settings: "tnum";
      -moz-font-feature-settings: "tnum";
      -ms-font-feature-settings: "tnum";
      -webkit-font-feature-settings: "tnum";
      border-bottom: 1px solid hsla(0, 13%, 72%, .12);
      padding: .725rem .96667rem calc(.725rem - 1px)
    }

    td:first-child,
    th:first-child {
      padding-left: 0
    }

    td:last-child,
    th:last-child {
      padding-right: 0
    }

    tt {
      background-color: #2b2834;
      border-radius: 2px;
      color: #968af6
    }

    code,
    tt {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      padding: .2em
    }

    code {
      background-color: rgba(0, 0, 0, .04);
      border-radius: 3px
    }

    pre code {
      background: none;
      line-height: 1.42
    }

    code:before,
    pre code:after,
    pre code:before,
    pre tt:after,
    pre tt:before,
    tt:after,
    tt:before {
      content: ""
    }

    @media only screen and (max-width:480px) {
      html {
        font-size: 100%
      }
    }

    .assets-page .gatsby-resp-image-wrapper {
      max-height: 200px !important
    }

    .assets-page .gatsby-resp-image-image {
      width: auto !important
    }
  </style>
  <link rel="icon" href="/favicon-32x32.png?v=8b512faa8d4a0b019c123a771b6622aa" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="anonymous">
  <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48x48.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256x256.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="preconnect" href="https://matomo.ethstake.exchange">
  <link rel="sitemap" type="application/xml" href="/sitemap/sitemap-index.xml">
  <style type="text/css">
    .anchor.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }

    .anchor.after {
      display: inline-block;
      padding-left: 4px;
    }

    h1 .anchor svg,
    h2 .anchor svg,
    h3 .anchor svg,
    h4 .anchor svg,
    h5 .anchor svg,
    h6 .anchor svg {
      visibility: hidden;
    }

    h1:hover .anchor svg,
    h2:hover .anchor svg,
    h3:hover .anchor svg,
    h4:hover .anchor svg,
    h5:hover .anchor svg,
    h6:hover .anchor svg,
    h1 .anchor:focus svg,
    h2 .anchor:focus svg,
    h3 .anchor:focus svg,
    h4 .anchor:focus svg,
    h5 .anchor:focus svg,
    h6 .anchor:focus svg {
      visibility: visible;
    }
  </style>
  <script>
    document.addEventListener("DOMContentLoaded", function (event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
          var clientTop = document.documentElement.clientTop || document.body.clientTop || 0
          var offset = element.getBoundingClientRect().top + scrollTop - clientTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function () {
            window.scrollTo(0, offset - 0)
          }), 0)
        }
      }
    })
  </script>
  <title data-react-helmet="true">Run a node | ethstake.exchange</title>
  <link data-react-helmet="true" rel="canonical" href="index.html">
  <script data-react-helmet="true" type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": "https://ethstake.exchange",
          "email": "<EMAIL>",
          "name": "Ethereum",
          "logo": "https://ethstake.exchange/og-image.png"
        }
      </script>
  <style>
    .gatsby-image-wrapper {
      position: relative;
      overflow: hidden
    }

    .gatsby-image-wrapper picture.object-fit-polyfill {
      position: static !important
    }

    .gatsby-image-wrapper img {
      bottom: 0;
      height: 100%;
      left: 0;
      margin: 0;
      max-width: none;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      object-fit: cover
    }

    .gatsby-image-wrapper [data-main-image] {
      opacity: 0;
      transform: translateZ(0);
      transition: opacity .25s linear;
      will-change: opacity
    }

    .gatsby-image-wrapper-constrained {
      display: inline-block;
      vertical-align: top
    }
  </style><noscript>
    <style>
      .gatsby-image-wrapper noscript [data-main-image] {
        opacity: 1 !important
      }

      .gatsby-image-wrapper [data-placeholder-image] {
        opacity: 0 !important
      }
    </style>
  </noscript>
  <script
    type="module">const e = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; e && document.body.addEventListener("load", (function (e) { if (void 0 === e.target.dataset.mainImage) return; if (void 0 === e.target.dataset.gatsbyImageSsr) return; const t = e.target; let a = null, n = t; for (; null === a && n;)void 0 !== n.parentNode.dataset.gatsbyImageWrapper && (a = n.parentNode), n = n.parentNode; const o = a.querySelector("[data-placeholder-image]"), r = new Image; r.src = t.currentSrc, r.decode().catch((() => { })).then((() => { t.style.opacity = 1, o && (o.style.opacity = 0, o.style.transition = "opacity 500ms linear") })) }), !0);</script>
  <style data-styled="" data-styled-version="5.3.3">
    body {
      background-color: #ffffff;
      color: #333333;
    }

    /*!sc*/
    a {
      color: #1c1cff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    mark {
      background: rgba(143, 187, 237, .1);
      box-shadow: inset 0 -2px 0 0 rgba(69, 142, 225, .8);
    }

    /*!sc*/
    .anchor.before {
      fill: #333333;
    }

    /*!sc*/
    hr {
      background: #ecececnull#1c1cff;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
      position: absolute;
    }

    /*!sc*/
    iframe {
      display: block;
      max-width: 560px;
      margin: 32px 0;
    }

    /*!sc*/
    h1 {
      font-size: 3rem;
      line-height: 1.4;
      margin: 2rem 0;
      font-weight: 700;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h1 {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    h2 {
      font-size: 2rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 3rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h2 {
        font-size: 1.5rem;
      }
    }

    /*!sc*/
    h3 {
      font-size: 1.5rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 2.5rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h3 {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    h4 {
      font-size: 1.25rem;
      line-height: 1.4;
      font-weight: 500;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h4 {
        font-size: 1rem;
      }
    }

    /*!sc*/
    h5 {
      font-size: 1rem;
      line-height: 1.4;
      font-weight: 450;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    h6 {
      font-size: 0.9rem;
      line-height: 1.4;
      font-weight: 400;
      text-transform: uppercase;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    data-styled.g1[id="sc-global-hcwgEG1"] {
      content: "sc-global-hcwgEG1,"
    }

    /*!sc*/
    .iylOGp {
      fill: #b2b2b2;
    }

    /*!sc*/
    .iylOGp:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g2[id="Icon__StyledIcon-sc-1o8zi5s-0"] {
      content: "iylOGp,"
    }

    /*!sc*/
    .gABYms:after {
      margin-left: 0.125em;
      margin-right: 0.3em;
      display: inline;
      content: "â†—";
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out;
      font-style: normal;
    }

    /*!sc*/
    .gABYms:hover:after {
      -webkit-transform: translate(0.15em, -0.2em);
      -ms-transform: translate(0.15em, -0.2em);
      transform: translate(0.15em, -0.2em);
    }

    /*!sc*/
    data-styled.g3[id="Link__ExternalLink-sc-e3riao-0"] {
      content: "gABYms,"
    }

    /*!sc*/
    .gCWUlE .is-glossary {
      white-space: nowrap;
    }

    /*!sc*/
    .gCWUlE.active {
      color: #1c1cff;
    }

    /*!sc*/
    .gCWUlE:hover svg {
      fill: #1c1cff;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g4[id="Link__InternalLink-sc-e3riao-1"] {
      content: "gCWUlE,"
    }

    /*!sc*/
    .jfMIWk {
      margin: 0 0.25rem 0 0.35rem;
      fill: #4949ff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    .jfMIWk:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g6[id="Link__GlossaryIcon-sc-e3riao-3"] {
      content: "jfMIWk,"
    }

    /*!sc*/
    .gvoBKJ {
      padding-top: 3rem;
      padding-bottom: 4rem;
      padding: 1rem 2rem;
    }

    /*!sc*/
    data-styled.g7[id="Footer__StyledFooter-sc-1to993d-0"] {
      content: "gvoBKJ,"
    }

    /*!sc*/
    .kFKfdz {
      font-size: 0.875rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g8[id="Footer__FooterTop-sc-1to993d-1"] {
      content: "kFKfdz,"
    }

    /*!sc*/
    .bWGwos {
      color: #666666;
    }

    /*!sc*/
    data-styled.g9[id="Footer__LastUpdated-sc-1to993d-2"] {
      content: "bWGwos,"
    }

    /*!sc*/
    .hlbLsM {
      display: grid;
      grid-template-columns: repeat(6, auto);
      grid-gap: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1300px) {
      .hlbLsM {
        grid-template-columns: repeat(3, auto);
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .hlbLsM {
        grid-template-columns: repeat(2, auto);
      }
    }

    /*!sc*/
    @media (max-width:500px) {
      .hlbLsM {
        grid-template-columns: auto;
      }
    }

    /*!sc*/
    data-styled.g10[id="Footer__LinkGrid-sc-1to993d-3"] {
      content: "hlbLsM,"
    }

    /*!sc*/
    .bbCEKr {
      font-size: 0.875rem;
      line-height: 1.6;
      margin: 1.14em 0;
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g12[id="Footer__SectionHeader-sc-1to993d-5"] {
      content: "bbCEKr,"
    }

    /*!sc*/
    .gjQPMc {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      margin: 0;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g13[id="Footer__List-sc-1to993d-6"] {
      content: "gjQPMc,"
    }

    /*!sc*/
    .eGhJJx {
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g14[id="Footer__ListItem-sc-1to993d-7"] {
      content: "eGhJJx,"
    }

    /*!sc*/
    .gIpSoz {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #666666;
    }

    /*!sc*/
    .gIpSoz svg {
      fill: #666666;
    }

    /*!sc*/
    .gIpSoz:after {
      color: #666666;
    }

    /*!sc*/
    .gIpSoz:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover:after {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g15[id="Footer__FooterLink-sc-1to993d-8"] {
      content: "gIpSoz,"
    }

    /*!sc*/
    .kdLbod {
      margin: 1rem 0;
    }

    /*!sc*/
    data-styled.g16[id="Footer__SocialIcons-sc-1to993d-9"] {
      content: "kdLbod,"
    }

    /*!sc*/
    .iedzfy {
      margin-left: 1rem;
      width: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iedzfy {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g17[id="Footer__SocialIcon-sc-1to993d-10"] {
      content: "iedzfy,"
    }

    /*!sc*/
    .drElXa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-right: 1.5rem;
    }

    /*!sc*/
    .drElXa:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g20[id="Dropdown__DropdownTitle-sc-1yd08gi-1"] {
      content: "drElXa,"
    }

    /*!sc*/
    .ldsPWM {
      margin: 0;
      position: absolute;
      margin-top: -1rem;
      list-style-type: none;
      list-style-image: none;
      top: 100%;
      width: auto;
      border-radius: 0.5em;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      opacity:0;
      display:none;
      transform:rotateX(-15deg) translateZ(0)
    }

    /*!sc*/
    data-styled.g21[id="Dropdown__DropdownList-sc-1yd08gi-2"] {
      content: "ldsPWM,"
    }

    /*!sc*/
    .Mkofa {
      white-space: nowrap;
      margin: 0;
      color: #333333;
    }

    /*!sc*/
     .Mkofa:hover >ul{
      color: #1c1cff;
      opacity:1;
      display:block;
      transform: none;
    }

    /*!sc*/
    data-styled.g23[id="Dropdown__NavListItem-sc-1yd08gi-4"] {
      content: "Mkofa,"
    }

    /*!sc*/
    .lgeotR {
      margin: 0;
      color: #333333;
    }

    /*!sc*/
    .lgeotR:hover {
      color: #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g24[id="Dropdown__DropdownItem-sc-1yd08gi-5"] {
      content: "lgeotR,"
    }

    /*!sc*/
    .cTcxIB {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: block;
      padding: 0.5rem;
      color: #333333;
    }

    /*!sc*/
    .cTcxIB svg {
      fill: #666666;
    }

    /*!sc*/
    .cTcxIB:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cTcxIB:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g25[id="Dropdown__NavLink-sc-1yd08gi-6"] {
      content: "cTcxIB,"
    }

    /*!sc*/
    .ivCgwn {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .ivCgwn>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .hLjau {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .hLjau>img {
      width: 3em !important;
      height: 3em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .RDZme {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .RDZme>img {
      width: 1em !important;
      height: 1em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .bhqZra {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .bhqZra>img {
      width: 2em !important;
      height: 2em !important;
      margin: 0 !important;
    }

    /*!sc*/
    data-styled.g27[id="Emoji__StyledEmoji-sc-ihpuqw-0"] {
      content: "ivCgwn,hLjau,RDZme,bhqZra,"
    }

    /*!sc*/
    .dUatah {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: none;
      border: none;
      color: inherit;
      display: inline-block;
      font: inherit;
      padding: initial;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g28[id="NakedButton-sc-1g43w8v-0"] {
      content: "dUatah,"
    }

    /*!sc*/
    .eoyKpR {
      margin: 0;
      position: relative;
      border-radius: 0.25em;
    }

    /*!sc*/
    data-styled.g29[id="Input__Form-sc-1utkal6-0"] {
      content: "eoyKpR,"
    }

    /*!sc*/
    .kkfPkW {
      border: 1px solid #7f7f7f;
      color: #333333;
      background: #ffffff;
      padding: 0.5rem;
      padding-right: 2rem;
      border-radius: 0.25em;
      width: 100%;
    }

    /*!sc*/
    .kkfPkW:focus {
      outline: #1c1cff auto 1px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .kkfPkW {
        padding-left: 2rem;
      }
    }

    /*!sc*/
    data-styled.g30[id="Input__StyledInput-sc-1utkal6-1"] {
      content: "kkfPkW,"
    }

    /*!sc*/
    .gFzMVg {
      position: absolute;
      right: 6px;
      top: 50%;
      margin-top: -12px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .gFzMVg {
        left: 6px;
      }
    }

    /*!sc*/
    data-styled.g31[id="Input__SearchIcon-sc-1utkal6-2"] {
      content: "gFzMVg,"
    }

    /*!sc*/
    .ggVPUc {
      border: 1px solid #7f7f7f;
      border-radius: 0.25em;
      color: #333333;
      display: none;
      margin-bottom: 0;
      padding: 0 6px;
      position: absolute;
      right: 6px;
      top: 20%;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .ggVPUc {
        display: inline-block;
      }
    }

    /*!sc*/
    data-styled.g32[id="Input__SearchSlash-sc-1utkal6-3"] {
      content: "ggVPUc,"
    }

    /*!sc*/
    .kNenpg {
      position: relative;
      display: grid;
      grid-gap: 1em;
    }

    /*!sc*/
    data-styled.g33[id="Search__Root-sc-1qm8xwy-0"] {
      content: "kNenpg,"
    }

    /*!sc*/
    .eJIgkk {
      display: none;
      max-height: 80vh;
      overflow: scroll;
      z-index: 2;
      position: absolute;
      right: 0;
      top: calc(100% + 0.5em);
      width: 80vw;
      max-width: 30em;
      box-shadow: 0 0 5px 0;
      padding: 0.5rem;
      background: #ffffff;
      border-radius: 0.25em;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eJIgkk {
        width: 100%;
      }
    }

    /*!sc*/
    .eJIgkk>*+* {
      padding-top: 1em !important;
      border-top: 2px solid black;
    }

    /*!sc*/
    .eJIgkk li {
      margin-bottom: 0.4rem;
    }

    /*!sc*/
    .eJIgkk li+li {
      padding-top: 0.7em;
      border-top: 1px solid #ececec;
    }

    /*!sc*/
    .eJIgkk ul {
      margin: 0;
      list-style: none;
    }

    /*!sc*/
    .eJIgkk mark {
      color: #1c1cff;
      box-shadow: inset 0 -2px 0 0 rgba(143, 187, 237, .5);
    }

    /*!sc*/
    .eJIgkk header {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk header h3 {
      color: #ffffff;
      background: #4c4c4c;
      padding: 0.1em 0.4em;
      border-radius: 0.25em;
    }

    /*!sc*/
    .eJIgkk h3 {
      margin: 0 0 0.5em;
    }

    /*!sc*/
    .eJIgkk h4 {
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk a {
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    data-styled.g34[id="Search__HitsWrapper-sc-1qm8xwy-1"] {
      content: "eJIgkk,"
    }

    /*!sc*/
    .kCvjty {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    /*!sc*/
    data-styled.g41[id="SharedStyledComponents__Page-sc-1cr9zfr-0"] {
      content: "kCvjty,"
    }

    /*!sc*/
    .euABDx {
      margin-bottom: 4rem;
      margin-top: 4rem;
      width: 10%;
      height: 0.25rem;
      background-color: #a4a4f3;
    }

    /*!sc*/
    data-styled.g42[id="SharedStyledComponents__Divider-sc-1cr9zfr-1"] {
      content: "euABDx,"
    }

    /*!sc*/
    .dedPKg {
      padding: 1rem 2rem;
      width: 100%;
    }

    /*!sc*/
    data-styled.g44[id="SharedStyledComponents__Content-sc-1cr9zfr-3"] {
      content: "dedPKg,"
    }

    /*!sc*/
    .jEZlpP {
      -webkit-text-decoration: none;
      text-decoration: none;
      margin-right: 2rem;
      color: #333333;
    }

    /*!sc*/
    .jEZlpP svg {
      fill: #666666;
    }

    /*!sc*/
    .jEZlpP:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .jEZlpP:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .jEZlpP.active {
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g52[id="SharedStyledComponents__NavLink-sc-1cr9zfr-11"] {
      content: "jEZlpP,"
    }

    /*!sc*/
    .dMzxTW {
      color: #1c1cff;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g53[id="SharedStyledComponents__FakeLink-sc-1cr9zfr-12"] {
      content: "dMzxTW,"
    }

    /*!sc*/
    .hCavpi {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(min(100%, 280px), 1fr));
      gap: 2rem;
    }

    /*!sc*/
    data-styled.g56[id="SharedStyledComponents__CardGrid-sc-1cr9zfr-15"] {
      content: "hCavpi,"
    }

    /*!sc*/
    .iuRocQ {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iuRocQ:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    .gVLXss {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
      margin-left: 0.5rem;
    }

    /*!sc*/
    .gVLXss:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    data-styled.g59[id="SharedStyledComponents__Button-sc-1cr9zfr-18"] {
      content: "iuRocQ,gVLXss,"
    }

    /*!sc*/
    .bphJHH {
      background-color: #1c1cff;
      color: #ffffff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .bphJHH:hover {
      background-color: rgba(28, 28, 225, 0.8);
    }

    /*!sc*/
    .bphJHH:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g60[id="SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19"] {
      content: "bphJHH,"
    }

    /*!sc*/
    .fMmGqe {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .fMmGqe:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .fMmGqe:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g61[id="SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20"] {
      content: "fMmGqe,"
    }

    /*!sc*/
    .hhdXUp {
      display: none;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hhdXUp {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    data-styled.g73[id="Mobile__Container-sc-zxc8gm-0"] {
      content: "hhdXUp,"
    }

    /*!sc*/
    .dUGGTH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g74[id="Mobile__MenuIcon-sc-zxc8gm-1"] {
      content: "dUGGTH,"
    }

    /*!sc*/
    .dLNRLx {
      margin-left: 1rem;
    }

    /*!sc*/
    data-styled.g75[id="Mobile__MenuButton-sc-zxc8gm-2"] {
      content: "dLNRLx,"
    }

    /*!sc*/
    .hvwyGc {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g76[id="Mobile__OtherIcon-sc-zxc8gm-3"] {
      content: "hvwyGc,"
    }

    /*!sc*/
    .bCHBHX {
      position: fixed;
      background: hsla(0, 0%, 69.8%, 0.9);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
    }

    /*!sc*/
    data-styled.g77[id="Mobile__MobileModal-sc-zxc8gm-4"] {
      content: "bCHBHX,"
    }

    /*!sc*/
    .AJukL {
      background: #ffffff;
      z-index: 99;
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
    }

    /*!sc*/
    data-styled.g78[id="Mobile__MenuContainer-sc-zxc8gm-5"] {
      content: "AJukL,"
    }

    /*!sc*/
    .gbspKa {
      margin: 0 0.125rem;
      width: 1.5rem;
      height: 2.5rem;
      position: relative;
      stroke-width: 2px;
      z-index: 100;
    }

    /*!sc*/
    .gbspKa>path {
      stroke: #333333;
      fill: none;
    }

    /*!sc*/
    .gbspKa:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gbspKa:hover>path {
      stroke: #1c1cff;
    }

    /*!sc*/
    data-styled.g79[id="Mobile__GlyphButton-sc-zxc8gm-6"] {
      content: "gbspKa,"
    }

    /*!sc*/
    .gBSEi {
      z-index: 101;
      padding: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g80[id="Mobile__SearchContainer-sc-zxc8gm-7"] {
      content: "gBSEi,"
    }

    /*!sc*/
    .iXlChz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .iXlChz>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g81[id="Mobile__SearchHeader-sc-zxc8gm-8"] {
      content: "iXlChz,"
    }

    /*!sc*/
    .jmriUx {
      z-index: 102;
      cursor: pointer;
    }

    /*!sc*/
    .jmriUx>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g82[id="Mobile__CloseIconContainer-sc-zxc8gm-9"] {
      content: "jmriUx,"
    }

    /*!sc*/
    .gYetwr {
      margin: 0;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 3rem 1rem 8rem;
    }

    /*!sc*/
    data-styled.g83[id="Mobile__MenuItems-sc-zxc8gm-10"] {
      content: "gYetwr,"
    }

    /*!sc*/
    .gXxMFO {
      margin: 0;
      margin-bottom: 3rem;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g84[id="Mobile__NavListItem-sc-zxc8gm-11"] {
      content: "gXxMFO,"
    }

    /*!sc*/
    .kuWShR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 0;
    }

    /*!sc*/
    data-styled.g85[id="Mobile__StyledNavLink-sc-zxc8gm-12"] {
      content: "kuWShR,"
    }

    /*!sc*/
    .erCTXJ {
      margin: 1rem 0;
      color: #333333;
    }

    /*!sc*/
    data-styled.g86[id="Mobile__SectionTitle-sc-zxc8gm-13"] {
      content: "erCTXJ,"
    }

    /*!sc*/
    .hghxUt {
      margin: 0;
    }

    /*!sc*/
    data-styled.g87[id="Mobile__SectionItems-sc-zxc8gm-14"] {
      content: "hghxUt,"
    }

    /*!sc*/
    .kdRQoZ {
      margin-bottom: 1rem;
      list-style-type: none;
      list-style-image: none;
      opacity: 0.7;
    }

    /*!sc*/
    .kdRQoZ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g88[id="Mobile__SectionItem-sc-zxc8gm-15"] {
      content: "kdRQoZ,"
    }

    /*!sc*/
    .iYttIj {
      background: #ffffff;
      border-top: 1px solid #ececec;
      padding-right: 1rem;
      padding-left: 1rem;
      margin-top: auto;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 108px;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      max-width: 450px;
      z-index: 99;
    }

    /*!sc*/
    data-styled.g89[id="Mobile__BottomMenu-sc-zxc8gm-16"] {
      content: "iYttIj,"
    }

    /*!sc*/
    .cnajxM {
      -webkit-flex: 1 1 120px;
      -ms-flex: 1 1 120px;
      flex: 1 1 120px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      color: #333333;
    }

    /*!sc*/
    .cnajxM>svg {
      fill: #333333;
    }

    /*!sc*/
    .cnajxM:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cnajxM:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g90[id="Mobile__BottomItem-sc-zxc8gm-17"] {
      content: "cnajxM,"
    }

    /*!sc*/
    .heSUpS {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
    }

    /*!sc*/
    .heSUpS>svg {
      fill: #333333;
    }

    /*!sc*/
    .heSUpS:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .heSUpS:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g91[id="Mobile__BottomLink-sc-zxc8gm-18"] {
      content: "heSUpS,"
    }

    /*!sc*/
    .hkZTkJ {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      margin-top: 0.5rem;
      text-transform: uppercase;
      text-align: center;
      opacity: 0.7;
    }

    /*!sc*/
    .hkZTkJ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g92[id="Mobile__BottomItemText-sc-zxc8gm-19"] {
      content: "hkZTkJ,"
    }

    /*!sc*/
    .jBipln {
      color: #333333;
      background: #f2f2f2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 10vw;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 280px;
      width: min(60vw, 280px);
      height: 280px;
      height: min(60vw, 280px);
      border-radius: 100%;
    }

    /*!sc*/
    data-styled.g93[id="Mobile__BlankSearchState-sc-zxc8gm-20"] {
      content: "jBipln,"
    }

    /*!sc*/
    .iGuESw {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 1000;
      width: 100%;
    }

    /*!sc*/
    data-styled.g94[id="Nav__NavContainer-sc-1aprtmp-0"] {
      content: "iGuESw,"
    }

    /*!sc*/
    .cpomzd {
      height: 4.75rem;
      padding: 1rem 2rem;
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g95[id="Nav__StyledNav-sc-1aprtmp-1"] {
      content: "cpomzd,"
    }

    /*!sc*/
    .faUCsG {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 1440px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .faUCsG {
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
    }

    /*!sc*/
    data-styled.g97[id="Nav__NavContent-sc-1aprtmp-3"] {
      content: "faUCsG,"
    }

    /*!sc*/
    .gjaVMk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gjaVMk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g98[id="Nav__InnerContent-sc-1aprtmp-4"] {
      content: "gjaVMk,"
    }

    /*!sc*/
    .jUJHKw {
      margin: 0;
      margin-left: 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g99[id="Nav__LeftItems-sc-1aprtmp-5"] {
      content: "jUJHKw,"
    }

    /*!sc*/
    .kQWBtS {
      margin: 0;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g100[id="Nav__RightItems-sc-1aprtmp-6"] {
      content: "kQWBtS,"
    }

    /*!sc*/
    .jODkFW {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-right: 0;
      margin-left: 1rem;
    }

    /*!sc*/
    .jODkFW:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g102[id="Nav__RightNavLink-sc-1aprtmp-8"] {
      content: "jODkFW,"
    }

    /*!sc*/
    .igUcis {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g103[id="Nav__HomeLogoNavLink-sc-1aprtmp-9"] {
      content: "igUcis,"
    }

    /*!sc*/
    .euWmfq {
      opacity: 0.85;
    }

    /*!sc*/
    .euWmfq:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g104[id="Nav__HomeLogo-sc-1aprtmp-10"] {
      content: "euWmfq,"
    }

    /*!sc*/
    .bDRFLa {
      padding-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g105[id="Nav__Span-sc-1aprtmp-11"] {
      content: "bDRFLa,"
    }

    /*!sc*/
    .hwxIMf {
      margin-left: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .hwxIMf:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g106[id="Nav__ThemeToggle-sc-1aprtmp-12"] {
      content: "hwxIMf,"
    }

    /*!sc*/
    .jOKVBH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g107[id="Nav__NavIcon-sc-1aprtmp-13"] {
      content: "jOKVBH,"
    }

    /*!sc*/
    .iMiHPL {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iMiHPL function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .iMiHPL function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g125[id="ButtonLink__StyledLinkButton-sc-8betkf-0"] {
      content: "iMiHPL,"
    }

    /*!sc*/
    .fXsDBz {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .fXsDBz function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .fXsDBz function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .fXsDBz function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .fXsDBz function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .fXsDBz function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .fXsDBz function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g126[id="ButtonLink__StyledScrollButton-sc-8betkf-1"] {
      content: "fXsDBz,"
    }

    /*!sc*/
    .kmLdQv {
      background-color: #1c1cff;
      color: #ffffff !important;
      border: 1px solid transparent;
    }

    /*!sc*/
    .kmLdQv:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .kmLdQv:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g127[id="ButtonLink__PrimaryLink-sc-8betkf-2"] {
      content: "kmLdQv,"
    }

    /*!sc*/
    .dzWGyc {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .dzWGyc:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .dzWGyc:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g128[id="ButtonLink__SecondaryLink-sc-8betkf-3"] {
      content: "dzWGyc,"
    }

    /*!sc*/
    .imwVIo {
      color: #ffffff !important;
      background-color: #1c1cff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .imwVIo:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .imwVIo:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g129[id="ButtonLink__PrimaryScrollLink-sc-8betkf-4"] {
      content: "imwVIo,"
    }

    /*!sc*/
    .gNxaxD {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .gNxaxD:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .gNxaxD:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g130[id="ButtonLink__SecondaryScrollLink-sc-8betkf-5"] {
      content: "gNxaxD,"
    }

    /*!sc*/
    .elpFuD {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g131[id="TranslationBanner__H3-sc-cd94ib-0"] {
      content: "elpFuD,"
    }

    /*!sc*/
    .jJbcq {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jJbcq {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g132[id="TranslationBanner__BannerContainer-sc-cd94ib-1"] {
      content: "jJbcq,"
    }

    /*!sc*/
    .jIVPcV {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jIVPcV {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g133[id="TranslationBanner__StyledBanner-sc-cd94ib-2"] {
      content: "jIVPcV,"
    }

    /*!sc*/
    .jiZNpa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .jiZNpa {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g134[id="TranslationBanner__BannerContent-sc-cd94ib-3"] {
      content: "jiZNpa,"
    }

    /*!sc*/
    .dOewRO {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g135[id="TranslationBanner__BannerClose-sc-cd94ib-4"] {
      content: "dOewRO,"
    }

    /*!sc*/
    .cEauOV {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g136[id="TranslationBanner__BannerCloseIcon-sc-cd94ib-5"] {
      content: "cEauOV,"
    }

    /*!sc*/
    .nChYp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .nChYp {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g137[id="TranslationBanner__Row-sc-cd94ib-6"] {
      content: "nChYp,"
    }

    /*!sc*/
    .gXNXMi {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gXNXMi {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g138[id="TranslationBanner__ButtonRow-sc-cd94ib-7"] {
      content: "gXNXMi,"
    }

    /*!sc*/
    .hTWLVy {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hTWLVy {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g139[id="TranslationBanner__StyledEmoji-sc-cd94ib-8"] {
      content: "hTWLVy,"
    }

    /*!sc*/
    .kUKdfA {
      margin-left: 0.5rem;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kUKdfA {
        margin-left: 0rem;
        margin-top: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g140[id="TranslationBanner__SecondaryButtonLink-sc-cd94ib-9"] {
      content: "kUKdfA,"
    }

    /*!sc*/
    .kIfJin {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g141[id="TranslationBannerLegal__H3-sc-1df4kz4-0"] {
      content: "kIfJin,"
    }

    /*!sc*/
    .eZKsbu {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eZKsbu {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g142[id="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1"] {
      content: "eZKsbu,"
    }

    /*!sc*/
    .cEcQwp {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .cEcQwp {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g143[id="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2"] {
      content: "cEcQwp,"
    }

    /*!sc*/
    .intGem {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .intGem {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g144[id="TranslationBannerLegal__BannerContent-sc-1df4kz4-3"] {
      content: "intGem,"
    }

    /*!sc*/
    .hMvMKu {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g145[id="TranslationBannerLegal__BannerClose-sc-1df4kz4-4"] {
      content: "hMvMKu,"
    }

    /*!sc*/
    .bhaYvl {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g146[id="TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5"] {
      content: "bhaYvl,"
    }

    /*!sc*/
    .cJRPhR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cJRPhR {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g147[id="TranslationBannerLegal__Row-sc-1df4kz4-6"] {
      content: "cJRPhR,"
    }

    /*!sc*/
    .kXSENe {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kXSENe {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g148[id="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7"] {
      content: "kXSENe,"
    }

    /*!sc*/
    .dRuawC {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dRuawC {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g149[id="TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8"] {
      content: "dRuawC,"
    }

    /*!sc*/
    .cRWHVB {
      background-color: #1c1cff;
    }

    /*!sc*/
    data-styled.g150[id="SkipLink__Div-sc-1ysqk2q-0"] {
      content: "cRWHVB,"
    }

    /*!sc*/
    .kOmocm {
      line-height: 2rem;
      position: absolute;
      top: -3rem;
      margin-left: 0.5rem;
      color: #ffffff;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .kOmocm:focus {
      position: static;
    }

    /*!sc*/
    data-styled.g151[id="SkipLink__Anchor-sc-1ysqk2q-1"] {
      content: "kOmocm,"
    }

    /*!sc*/
    .mXCTw {
      position: relative;
      margin: 0px auto;
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-flow: column;
      -ms-flex-flow: column;
      flex-flow: column;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .mXCTw {
        max-width: 1504px;
      }
    }

    /*!sc*/
    data-styled.g152[id="Layout__ContentContainer-sc-19910io-0"] {
      content: "mXCTw,"
    }

    /*!sc*/
    .gqazVg {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gqazVg {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g153[id="Layout__MainContainer-sc-19910io-1"] {
      content: "gqazVg,"
    }

    /*!sc*/
    .kCJhKM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }

    /*!sc*/
    data-styled.g154[id="Layout__MainContent-sc-19910io-2"] {
      content: "kCJhKM,"
    }

    /*!sc*/
    .dliKfQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: space-around;
      -webkit-justify-content: space-around;
      -ms-flex-pack: space-around;
      justify-content: space-around;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      overflow: visible;
      width: 100%;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    /*!sc*/
    data-styled.g155[id="Layout__Main-sc-19910io-3"] {
      content: "dliKfQ,"
    }

    /*!sc*/
    .jetTxG {
      border: 1px solid #e5e5e5;
      background-color: #ffffff;
      border-radius: 4px;
      padding: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    /*!sc*/
    data-styled.g174[id="FeedbackCard__Card-sc-siku0n-0"] {
      content: "jetTxG,"
    }

    /*!sc*/
    .gVbeDO {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gVbeDO {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g175[id="FeedbackCard__Content-sc-siku0n-1"] {
      content: "gVbeDO,"
    }

    /*!sc*/
    .hgzlkf {
      margin-top: 0rem;
      font-size: 1rem;
      font-weight: 400;
      margin-bottom: 0.5rem;
    }

    /*!sc*/
    data-styled.g176[id="FeedbackCard__Title-sc-siku0n-2"] {
      content: "hgzlkf,"
    }

    /*!sc*/
    @media (max-width:1024px) {
      .dFGrDI {
        margin-top: 1rem;
      }
    }

    /*!sc*/
    data-styled.g177[id="FeedbackCard__ButtonContainer-sc-siku0n-3"] {
      content: "dFGrDI,"
    }

    /*!sc*/
    .diVorY {
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      padding: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-bottom: 1rem;
    }

    /*!sc*/
    .diVorY:hover {
      background-color: #f7f7f7;
    }

    /*!sc*/
    data-styled.g193[id="ExpandableCard__Card-sc-1z0zrur-0"] {
      content: "diVorY,"
    }

    /*!sc*/
    .kQkhur {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kQkhur {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g194[id="ExpandableCard__Content-sc-1z0zrur-1"] {
      content: "kQkhur,"
    }

    /*!sc*/
    .fQKChX {
      margin-top: 0rem;
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    /*!sc*/
    data-styled.g195[id="ExpandableCard__Title-sc-1z0zrur-2"] {
      content: "fQKChX,"
    }

    /*!sc*/
    .jgdBtR {
      font-size: 0.875rem;
      font-weight: 400;
      color: #666666;
      margin-bottom: 0rem;
    }

    /*!sc*/
    data-styled.g196[id="ExpandableCard__TextPreview-sc-1z0zrur-3"] {
      content: "jgdBtR,"
    }

    /*!sc*/
    .dXxOUI {
      font-size: 1rem;
      font-weight: 400;
      color: #333333;
      margin-top: 2rem;
      border-top: 1px solid #e5e5e5;
      padding-top: 1.5rem;
    }

    /*!sc*/
    data-styled.g197[id="ExpandableCard__Text-sc-1z0zrur-4"] {
      content: "dXxOUI,"
    }

    /*!sc*/
    .cPoGis {
      margin-right: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cPoGis {
        margin-right: 0;
        margin-bottom: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g198[id="ExpandableCard__Question-sc-1z0zrur-5"] {
      content: "cPoGis,"
    }

    /*!sc*/
    .bWZmjK {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      margin: 1rem 0;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .bWZmjK img {
      margin-right: 1.5rem;
    }

    /*!sc*/
    data-styled.g199[id="ExpandableCard__Header-sc-1z0zrur-6"] {
      content: "bWZmjK,"
    }

    /*!sc*/
    .eHETdJ {
      margin-left: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .eHETdJ {
        margin-left: 0;
      }
    }

    /*!sc*/
    data-styled.g200[id="ExpandableCard__ButtonContainer-sc-1z0zrur-7"] {
      content: "eHETdJ,"
    }

    /*!sc*/
    .hnJxcR {
      white-space: nowrap;
    }

    /*!sc*/
    data-styled.g201[id="ExpandableCard__StyledFakeLink-sc-1z0zrur-8"] {
      content: "hnJxcR,"
    }

    /*!sc*/
    .hsvKXW {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-top: 2rem;
      margin-bottom: 0rem;
      padding: 0rem 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hsvKXW {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        padding: 0;
      }
    }

    /*!sc*/
    data-styled.g330[id="PageHero__HeroContainer-sc-r5r57a-0"] {
      content: "hsvKXW,"
    }

    /*!sc*/
    .eyNDuU {
      max-width: 640px;
      padding: 8rem 0 8rem 2rem;
      margin-right: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eyNDuU {
        padding: 4rem 0;
        max-width: 100%;
      }
    }

    /*!sc*/
    data-styled.g331[id="PageHero__HeroContent-sc-r5r57a-1"] {
      content: "eyNDuU,"
    }

    /*!sc*/
    .cRmbnZ {
      -webkit-flex: 1 1 50%;
      -ms-flex: 1 1 50%;
      flex: 1 1 50%;
      background-size: cover;
      background-repeat: no-repeat;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      margin-top: 3rem;
      margin-left: 3rem;
      width: 100%;
      max-width: 624px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .cRmbnZ {
        margin-top: 0;
        margin-left: 0;
        max-width: 560px;
      }
    }

    /*!sc*/
    data-styled.g332[id="PageHero__HeroImg-sc-r5r57a-2"] {
      content: "cRmbnZ,"
    }

    /*!sc*/
    .kdyHZT {
      font-weight: 700;
      font-size: 3rem;
      max-width: 100%;
      margin-bottom: 0rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kdyHZT {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g333[id="PageHero__Header-sc-r5r57a-3"] {
      content: "kdyHZT,"
    }

    /*!sc*/
    .dipPYX {
      text-transform: uppercase;
      font-size: 1rem;
      font-weight: 400;
      margin-bottom: 1rem;
      color: #4c4c4c;
    }

    /*!sc*/
    data-styled.g334[id="PageHero__Title-sc-r5r57a-4"] {
      content: "dipPYX,"
    }

    /*!sc*/
    .ivcuAF {
      font-size: 1.5rem;
      line-height: 140%;
      color: #666666;
      margin-top: 1rem;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .ivcuAF {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    data-styled.g335[id="PageHero__Subtitle-sc-r5r57a-5"] {
      content: "ivcuAF,"
    }

    /*!sc*/
    .caMbSB {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 1rem;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g336[id="PageHero__ButtonRow-sc-r5r57a-6"] {
      content: "caMbSB,"
    }

    /*!sc*/
    .iUNaqu {
      margin-right: 1rem;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .iUNaqu {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g337[id="PageHero__StyledButtonLink-sc-r5r57a-7"] {
      content: "iUNaqu,"
    }

    /*!sc*/
    .eoSjYG {
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      padding: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, rgba(79, 113, 235, 0.2) 9.8%, rgba(232, 79, 235, 0.2) 92.84%);
    }

    /*!sc*/
    .eoSjYG:hover img {
      -webkit-transform: scale(1.08);
      -ms-transform: scale(1.08);
      transform: scale(1.08);
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
    }

    /*!sc*/
    data-styled.g862[id="ExpandableInfo__Card-sc-mbbhey-0"] {
      content: "eoSjYG,"
    }

    /*!sc*/
    .hyYxUM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      gap: 3rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .hyYxUM {
        gap: 2rem;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g863[id="ExpandableInfo__Content-sc-mbbhey-1"] {
      content: "hyYxUM,"
    }

    /*!sc*/
    .fBPrBu {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      gap: 3rem;
      width: 100%;
    }

    /*!sc*/
    data-styled.g864[id="ExpandableInfo__TitleContent-sc-mbbhey-2"] {
      content: "fBPrBu,"
    }

    /*!sc*/
    .gHulik {
      margin-top: 0rem;
      margin-bottom: 0.5rem;
    }

    /*!sc*/
    data-styled.g865[id="ExpandableInfo__Title-sc-mbbhey-3"] {
      content: "gHulik,"
    }

    /*!sc*/
    .fCNOvJ {
      font-weight: 400;
      color: #666666;
      margin-bottom: 0rem;
    }

    /*!sc*/
    data-styled.g866[id="ExpandableInfo__TextPreview-sc-mbbhey-4"] {
      content: "fCNOvJ,"
    }

    /*!sc*/
    .dgEjeS {
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      margin-top: 2rem;
      border-top: 1px solid #e5e5e5;
      padding-top: 1.5rem;
    }

    /*!sc*/
    data-styled.g867[id="ExpandableInfo__Text-sc-mbbhey-5"] {
      content: "dgEjeS,"
    }

    /*!sc*/
    .dVvOxl {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g868[id="ExpandableInfo__Question-sc-mbbhey-6"] {
      content: "dVvOxl,"
    }

    /*!sc*/
    .fudfyE {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      margin: 1rem 0;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .fudfyE img {
      margin-right: 1.5rem;
    }

    /*!sc*/
    data-styled.g869[id="ExpandableInfo__Header-sc-mbbhey-7"] {
      content: "fudfyE,"
    }

    /*!sc*/
    .rfGjo {
      gap: 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .rfGjo {
        gap: 3rem;
      }
    }

    /*!sc*/
    .rfGjo * {
      -webkit-scroll-margin-top: 5.5rem;
      -moz-scroll-margin-top: 5.5rem;
      -ms-scroll-margin-top: 5.5rem;
      scroll-margin-top: 5.5rem;
    }

    /*!sc*/
    data-styled.g871[id="run-a-node__GappedPage-sc-16hodpi-0"] {
      content: "rfGjo,"
    }

    /*!sc*/
    .dVfCFp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      gap: 3rem;
      padding: 1rem 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .dVfCFp {
        gap: 2rem;
        padding: 1rem 2rem;
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .dVfCFp {
        padding: 1rem 0;
      }
    }

    /*!sc*/
    data-styled.g872[id="run-a-node__GappedContent-sc-16hodpi-1"] {
      content: "dVfCFp,"
    }

    /*!sc*/
    .hRLrUW {
      background: linear-gradient(0deg, rgba(153, 157, 244, 0.1) 0%, rgba(153, 157, 244, 0) 100%), linear-gradient(270.72deg, #FDF0FF 0.62%, rgba(236, 195, 195, 0.557292) 32.61%, rgba(207, 189, 230, 0.296875) 49.67%, rgba(196, 196, 196, 0) 72.88%);
      width: 100%;
    }

    /*!sc*/
    data-styled.g873[id="run-a-node__HeroContainer-sc-16hodpi-2"] {
      content: "hRLrUW,"
    }

    /*!sc*/
    .jpFHsj {
      padding-bottom: 2rem;
    }

    /*!sc*/
    data-styled.g874[id="run-a-node__Hero-sc-16hodpi-3"] {
      content: "jpFHsj,"
    }

    /*!sc*/
    .uqUCr {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 2rem;
      gap: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .uqUCr {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
        margin-left: 0rem;
        margin-right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g875[id="run-a-node__TwoColumnContent-sc-16hodpi-4"] {
      content: "uqUCr,"
    }

    /*!sc*/
    .hOYlSw {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      gap: 2rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .hOYlSw {
        width: 100%;
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }
    }

    /*!sc*/
    data-styled.g876[id="run-a-node__SplitContent-sc-16hodpi-5"] {
      content: "hOYlSw,"
    }

    /*!sc*/
    .jFregN {
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    /*!sc*/
    data-styled.g877[id="run-a-node__Column-sc-16hodpi-6"] {
      content: "jFregN,"
    }

    /*!sc*/
    .gAaFsL {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      gap: 1rem;
      width: -webkit-fit-content;
      width: -moz-fit-content;
      width: fit-content;
      padding-left: 2rem;
      padding-right: 2rem;
    }

    /*!sc*/
    .gAaFsL:hover svg {
      fill: #ffffff;
      -webkit-transform: scale(1.15);
      -ms-transform: scale(1.15);
      transform: scale(1.15);
      -webkit-transition: 0.1s;
      transition: 0.1s;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gAaFsL {
        width: 100%;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
      }
    }

    /*!sc*/
    data-styled.g878[id="run-a-node__ResponsiveButtonLink-sc-16hodpi-7"] {
      content: "gAaFsL,"
    }

    /*!sc*/
    .DQTwl {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #ccfcff;
      border: 1px solid #dadada;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 2rem 6rem;
      color: #333333;
      position: relative;
      isolation: isolate;
    }

    /*!sc*/
    .DQTwl img {
      margin: 0 0 0 2rem;
    }

    /*!sc*/
    .DQTwl:nth-of-type(even) {
      -webkit-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
    }

    /*!sc*/
    .DQTwl:nth-of-type(even) img {
      margin: 0 2rem 0 0;
    }

    /*!sc*/
    @media (max-width:768px) {
      .DQTwl {
        padding: 2rem;
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .DQTwl:nth-of-type(even) {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .DQTwl:nth-of-type(even) img {
        margin: 0 0 2rem;
      }

      .DQTwl img {
        margin: 0 0 2rem;
      }
    }

    /*!sc*/
    .DQTwl::after {
      content: "";
      position: absolute;
      inset: 0;
      z-index: -1;
      background: inherit;
      -webkit-filter: blur(1rem);
      filter: blur(1rem);
    }

    /*!sc*/
    .dEsqsV {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #ffe3d3;
      border: 1px solid #dadada;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 2rem 6rem;
      color: #333333;
      position: relative;
      isolation: isolate;
    }

    /*!sc*/
    .dEsqsV img {
      margin: 0 0 0 2rem;
    }

    /*!sc*/
    .dEsqsV:nth-of-type(even) {
      -webkit-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
    }

    /*!sc*/
    .dEsqsV:nth-of-type(even) img {
      margin: 0 2rem 0 0;
    }

    /*!sc*/
    @media (max-width:768px) {
      .dEsqsV {
        padding: 2rem;
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .dEsqsV:nth-of-type(even) {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .dEsqsV:nth-of-type(even) img {
        margin: 0 0 2rem;
      }

      .dEsqsV img {
        margin: 0 0 2rem;
      }
    }

    /*!sc*/
    .dEsqsV::after {
      content: "";
      position: absolute;
      inset: 0;
      z-index: -1;
      background: inherit;
      -webkit-filter: blur(1rem);
      filter: blur(1rem);
    }

    /*!sc*/
    .izhnI {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #e8e8ff;
      border: 1px solid #dadada;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 2rem 6rem;
      color: #333333;
      position: relative;
      isolation: isolate;
    }

    /*!sc*/
    .izhnI img {
      margin: 0 0 0 2rem;
    }

    /*!sc*/
    .izhnI:nth-of-type(even) {
      -webkit-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
    }

    /*!sc*/
    .izhnI:nth-of-type(even) img {
      margin: 0 2rem 0 0;
    }

    /*!sc*/
    @media (max-width:768px) {
      .izhnI {
        padding: 2rem;
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .izhnI:nth-of-type(even) {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }

      .izhnI:nth-of-type(even) img {
        margin: 0 0 2rem;
      }

      .izhnI img {
        margin: 0 0 2rem;
      }
    }

    /*!sc*/
    .izhnI::after {
      content: "";
      position: absolute;
      inset: 0;
      z-index: -1;
      background: inherit;
      -webkit-filter: blur(1rem);
      filter: blur(1rem);
    }

    /*!sc*/
    data-styled.g879[id="run-a-node__Highlight-sc-16hodpi-8"] {
      content: "DQTwl,dEsqsV,izhnI,"
    }

    /*!sc*/
    .gMuGQG {
      width: 90%;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
    }

    /*!sc*/
    @media (max-width:768px) {
      .gMuGQG {
        margin: 0;
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g881[id="run-a-node__StyledExpandableInfo-sc-16hodpi-10"] {
      content: "gMuGQG,"
    }

    /*!sc*/
    .jHReSL {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(min(100%, 340px), 1fr));
      gap: 1rem 2rem;
    }

    /*!sc*/
    .jHReSL>div {
      height: -webkit-fit-content;
      height: -moz-fit-content;
      height: fit-content;
    }

    /*!sc*/
    .jHReSL>div:hover {
      -webkit-transition: 0.1s;
      transition: 0.1s;
      -webkit-transform: scale(1.01);
      -ms-transform: scale(1.01);
      transform: scale(1.01);
    }

    /*!sc*/
    .jHReSL>div:hover img {
      -webkit-transition: 0.1s;
      transition: 0.1s;
      -webkit-transform: scale(1.1);
      -ms-transform: scale(1.1);
      transform: scale(1.1);
    }

    /*!sc*/
    data-styled.g882[id="run-a-node__InfoGrid-sc-16hodpi-11"] {
      content: "jHReSL,"
    }

    /*!sc*/
    .eBYXqY {
      line-height: 2;
      box-sizing: border-box;
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    /*!sc*/
    .eBYXqY ul {
      list-style: none;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eBYXqY {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g883[id="run-a-node__ColumnFill-sc-16hodpi-12"] {
      content: "eBYXqY,"
    }

    /*!sc*/
    .gDPznN {
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      inset: auto;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:768px) {
      .gDPznN {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g884[id="run-a-node__ColumnNarrow-sc-16hodpi-13"] {
      content: "gDPznN,"
    }

    /*!sc*/
    .kNrMST {
      -webkit-flex: 3;
      -ms-flex: 3;
      flex: 3;
    }

    /*!sc*/
    @media (max-width:768px) {
      .kNrMST {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g885[id="run-a-node__Width60-sc-16hodpi-14"] {
      content: "kNrMST,"
    }

    /*!sc*/
    .hZLFCE {
      -webkit-flex: 2;
      -ms-flex: 2;
      flex: 2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
    }

    /*!sc*/
    @media (max-width:768px) {
      .hZLFCE {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g886[id="run-a-node__Width40-sc-16hodpi-15"] {
      content: "hZLFCE,"
    }

    /*!sc*/
    .bOAabr {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g887[id="run-a-node__FlexContent-sc-16hodpi-16"] {
      content: "bOAabr,"
    }

    /*!sc*/
    .ihENAF {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      gap: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .ihENAF {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g888[id="run-a-node__Flex-sc-16hodpi-17"] {
      content: "ihENAF,"
    }

    /*!sc*/
    .bFrsG {
      margin: 3rem 0;
    }

    /*!sc*/
    data-styled.g889[id="run-a-node__MarginFlex-sc-16hodpi-18"] {
      content: "bFrsG,"
    }

    /*!sc*/
    .kZIACG {
      background: #fcfcfc;
      border: 1px solid #d1d1d1;
      box-sizing: border-box;
      border-radius: 5px;
      color: #333333;
      padding: 0 2rem;
    }

    /*!sc*/
    data-styled.g890[id="run-a-node__Container-sc-16hodpi-19"] {
      content: "kZIACG,"
    }

    /*!sc*/
    .iZDRqn {
      background: #f2f2f2;
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
      padding: 2rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .iZDRqn {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    .iZDRqn>p:last-of-type {
      margin-bottom: 2rem;
    }

    /*!sc*/
    data-styled.g891[id="run-a-node__BuildBox-sc-16hodpi-20"] {
      content: "iZDRqn,"
    }

    /*!sc*/
    .ldqQeS {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    .ldqQeS:hover {
      -webkit-transform: scale(1.02);
      -ms-transform: scale(1.02);
      transform: scale(1.02);
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
    }

    /*!sc*/
    data-styled.g892[id="run-a-node__BuildBoxSpace-sc-16hodpi-21"] {
      content: "ldqQeS,"
    }

    /*!sc*/
    .knlSZL {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      line-height: 200%;
      padding: 2rem;
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    /*!sc*/
    .knlSZL p {
      font-size: 110%;
    }

    /*!sc*/
    .knlSZL code {
      font-weight: 600;
      line-height: 125%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .knlSZL button {
        width: -webkit-fit-content;
        width: -moz-fit-content;
        width: fit-content;
        padding-left: 2rem;
        padding-right: 2rem;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .knlSZL button {
        width: 100%;
      }
    }

    /*!sc*/
    .knlSZL:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.02);
      -ms-transform: scale(1.02);
      transform: scale(1.02);
    }

    /*!sc*/
    data-styled.g893[id="run-a-node__FullyLoaded-sc-16hodpi-22"] {
      content: "knlSZL,"
    }

    /*!sc*/
    .jyUDtB {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      gap: 1rem;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g894[id="run-a-node__SvgTitle-sc-16hodpi-23"] {
      content: "jyUDtB,"
    }

    /*!sc*/
    .kiQPBL {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      gap: 1rem;
      margin-top: auto;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kiQPBL {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g895[id="run-a-node__ButtonContainer-sc-16hodpi-24"] {
      content: "kiQPBL,"
    }

    /*!sc*/
    .ixhdyo {
      background-color: #187d76;
    }

    /*!sc*/
    .ixhdyo span {
      color: #ffffff;
    }

    /*!sc*/
    .ixhdyo:hover {
      background-color: #0f5f5f;
      box-shadow: 4px 4px 0 0 rgba(#187d76, 0.47);
    }

    /*!sc*/
    data-styled.g896[id="run-a-node__DappNodeButtonLink-sc-16hodpi-25"] {
      content: "ixhdyo,"
    }

    /*!sc*/
    .cEXogT {
      background-color: #37822e;
    }

    /*!sc*/
    .cEXogT span {
      color: #ffffff;
    }

    /*!sc*/
    .cEXogT:hover {
      background-color: #2e6d2e;
      box-shadow: 4px 4px 0 0 rgba(#37822e, 0.47);
    }

    /*!sc*/
    data-styled.g897[id="run-a-node__AvadoButtonLink-sc-16hodpi-26"] {
      content: "cEXogT,"
    }

    /*!sc*/
    .dNxqsR {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g898[id="run-a-node__StyledEmoji-sc-16hodpi-27"] {
      content: "dNxqsR,"
    }

    /*!sc*/
    .ccjPxH {
      text-align: start;
      color: #1c1cff;
    }

    /*!sc*/
    .ccjPxH.active {
      color: #1c1cff;
    }

    /*!sc*/
    data-styled.g899[id="run-a-node__ScrollLink-sc-16hodpi-28"] {
      content: "ccjPxH,"
    }

    /*!sc*/
    .bjnfkk {
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
      padding: 2rem;
      border-radius: none;
      border: none;
      background: none;
    }

    /*!sc*/
    @media (max-width:768px) {
      .bjnfkk {
        padding: 2rem 0;
      }
    }

    /*!sc*/
    data-styled.g900[id="run-a-node__BuildContainer-sc-16hodpi-29"] {
      content: "bjnfkk,"
    }

    /*!sc*/
    .eyuVG {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 2rem;
      margin-top: 1rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .eyuVG:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .eyuVG:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    .eyuVG:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.05);
      -ms-transform: scale(1.05);
      transform: scale(1.05);
    }

    /*!sc*/
    data-styled.g901[id="run-a-node__ScrollButtonSecondary-sc-16hodpi-30"] {
      content: "eyuVG,"
    }

    /*!sc*/
    .bqXgMp {
      fill: #ffffff;
    }

    /*!sc*/
    data-styled.g902[id="run-a-node__DiscordIcon-sc-16hodpi-31"] {
      content: "bqXgMp,"
    }

    /*!sc*/
    .eksTnR {
      background: linear-gradient(262.78deg, rgba(152, 186, 249, 0.25) 0%, rgba(207, 177, 251, 0.25) 53.12%, rgba(151, 252, 246, 0.25) 100%);
      width: 100%;
      padding: 2rem;
      gap: 5rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eksTnR {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 3rem;
      }
    }

    /*!sc*/
    data-styled.g903[id="run-a-node__StakingCalloutContainer-sc-16hodpi-32"] {
      content: "eksTnR,"
    }

    /*!sc*/
    .kCVIBG {
      -webkit-transform: scaleX(-1) scale(1.15) translateX(2rem);
      -ms-transform: scaleX(-1) scale(1.15) translateX(2rem);
      transform: scaleX(-1) scale(1.15) translateX(2rem);
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kCVIBG {
        -webkit-transform: scaleX(-1) translateY(-3rem);
        -ms-transform: scaleX(-1) translateY(-3rem);
        transform: scaleX(-1) translateY(-3rem);
      }
    }

    /*!sc*/
    data-styled.g904[id="run-a-node__Leslie-sc-16hodpi-33"] {
      content: "kCVIBG,"
    }

    /*!sc*/
    .jJpdFO {
      width: 100%;
      max-width: 700px;
      margin: 0 2rem;
    }

    /*!sc*/
    data-styled.g905[id="run-a-node__StyledFeedbackCard-sc-16hodpi-34"] {
      content: "jJpdFO,"
    }

    /*!sc*/
    .iCfTWr {
      font-size: 150%;
      font-weight: 600;
    }

    /*!sc*/
    data-styled.g906[id="run-a-node__StrongParagraph-sc-16hodpi-35"] {
      content: "iCfTWr,"
    }

    /*!sc*/
  </style>
  <link as="script" rel="preload" href="/webpack-runtime-d600da28e471609bf3f3.js">
  <link as="script" rel="preload" href="/framework-4e285adfb333f1b50c05.js">
  <link as="script" rel="preload" href="/252f366e-2705b607be296edabcea.js">
  <link as="script" rel="preload" href="/ae51ba48-34d54094a2c04f215fb8.js">
  <link as="script" rel="preload" href="/1bfc9850-0f18e2d74feedfc6e426.js">
  <link as="script" rel="preload" href="/0c428ae2-2128ff22fce458b543bd.js">
  <link as="script" rel="preload" href="/0f1ac474-e8f788f62189f421a856.js">
  <link as="script" rel="preload" href="/app-b670b5ed3a389af0ed04.js">
  <link as="script" rel="preload" href="/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js">
  <link as="fetch" rel="preload" href="/page-data/en/run-a-node/page-data.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1011117294.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/3003422828.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/446219633.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/app-data.json" crossorigin="anonymous">
</head>

<body>
  <div id="___gatsby">
    <div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper">
      <div class="SkipLink__Div-sc-1ysqk2q-0 cRWHVB"><a href="#main-content"
          class="SkipLink__Anchor-sc-1ysqk2q-1 kOmocm"><span>Skip to main content</span></a></div>
      <div class="TranslationBanner__BannerContainer-sc-cd94ib-1 jJbcq">
        <div class="TranslationBanner__StyledBanner-sc-cd94ib-2 jIVPcV">
          <div class="TranslationBanner__BannerContent-sc-cd94ib-3 jiZNpa">
            <div class="TranslationBanner__Row-sc-cd94ib-6 nChYp">
              <h3 class="TranslationBanner__H3-sc-cd94ib-0 elpFuD"><span>Help update this page</span></h3><span
                size="1.5" ml="0.5rem" mt="0" mr="0" mb="0"
                class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBanner__StyledEmoji-sc-cd94ib-8 hTWLVy undefined"><img
                  alt="ðŸŒ" src="https://twemoji.maxcdn.com/2/svg/1f30f.svg"
                  style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
            </div>
            <p><span>Thereâ€™s a new version of this page but itâ€™s only in English right now. Help us translate the
                latest version.</span></p>
            <div class="TranslationBanner__ButtonRow-sc-cd94ib-7 gXNXMi">
              <div><a
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                  href="..\contributing\translation-program\index.html"><span>Translate page</span></a></div>
              <div><a aria-current="page"
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc TranslationBanner__SecondaryButtonLink-sc-cd94ib-9 kUKdfA active"
                  href="index.html"><span>See English</span></a></div>
            </div>
          </div>
          <div class="TranslationBanner__BannerClose-sc-cd94ib-4 dOewRO"><svg stroke="currentColor" fill="currentColor"
              stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBanner__BannerCloseIcon-sc-cd94ib-5 iylOGp cEauOV"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1 eZKsbu">
        <div class="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2 cEcQwp">
          <div class="TranslationBannerLegal__BannerContent-sc-1df4kz4-3 intGem">
            <div class="TranslationBannerLegal__Row-sc-1df4kz4-6 cJRPhR">
              <h3 class="TranslationBannerLegal__H3-sc-1df4kz4-0 kIfJin"><span>No bugs here!</span><span size="1.5"
                  ml="0.5rem" mt="0" mr="0" mb="0"
                  class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8 dRuawC undefined"><img
                    alt="ðŸ›" src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                    style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
            </div>
            <p><span>This page is not being translated. We've intentionally left this page in English for now.</span>
            </p>
            <div class="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7 kXSENe"><button
                class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Don't
                  show again</span></button></div>
          </div>
          <div class="TranslationBannerLegal__BannerClose-sc-1df4kz4-4 hMvMKu"><svg stroke="currentColor"
              fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5 iylOGp bhaYvl"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="Layout__ContentContainer-sc-19910io-0 mXCTw">
        <div class="Nav__NavContainer-sc-1aprtmp-0 iGuESw">
          <nav class="Nav__StyledNav-sc-1aprtmp-1 cpomzd">
            <div class="Nav__NavContent-sc-1aprtmp-3 faUCsG"><a
                class="Link__InternalLink-sc-e3riao-1 gCWUlE Nav__HomeLogoNavLink-sc-1aprtmp-9 igUcis active"
                href="..\index.html">
                <div data-gatsby-image-wrapper="" style="width:22px;height:35px"
                  class="gatsby-image-wrapper Nav__HomeLogo-sc-1aprtmp-10 euWmfq"><img aria-hidden="true"
                    data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                    src="data:image/png;base64,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"
                    alt="">
                  <picture>
                    <source type="image/webp"
                      sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                      decoding="async" loading="lazy"
                      src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                     >
                  </picture><noscript>
                    <picture>
                      <source type="image/webp"
                       sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                        decoding="async" loading="lazy"
                        src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                        >
                    </picture>
                  </noscript>
                  <script
                    type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                </div>
              </a>
              <div class="Nav__InnerContent-sc-1aprtmp-4 gjaVMk">
                <ul class="Nav__LeftItems-sc-1aprtmp-5 jUJHKw">
                  <li aria-label="Use Ethereum menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Use Ethereum</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a aria-current="page"
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB active"
                          href="index.html"><span>Run a node</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\staking\index.html"><span>Stake ETH</span></a></li>
                          <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Learn menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Learn</span><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\history\index.html"><span>History of Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk"
                            height="12px" width="12px" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                            </path>
                          </svg></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\web3\index.html"><span>What is Web3?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Developers&#x27; Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span
                      tabindex="0" class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Developers</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\index.html"><span>Developers' home</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="/en/developers/docs/"><span>Documentation</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                      </li>
                    </ul>
                  </li>
                  <li aria-label="Enterprise Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Enterprise</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Community Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Community</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\index.html"><span>Community hub</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\online\index.html"><span>Online communities</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\grants\index.html"><span>Grants</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                    </ul>
                  </li>
                </ul>
                <div class="Nav__RightItems-sc-1aprtmp-6 kQWBtS">
                  <div class="Search__Root-sc-1qm8xwy-0 kNenpg">
                    <form class="Input__Form-sc-1utkal6-0 eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput-sc-1utkal6-1 kkfPkW">
                      <p class="Input__SearchSlash-sc-1utkal6-3 ggVPUc">/</p><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Input__SearchIcon-sc-1utkal6-2 iylOGp gFzMVg" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper-sc-1qm8xwy-1 eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div><button aria-label="Switch to Dark Theme"
                    class="NakedButton-sc-1g43w8v-0 Nav__ThemeToggle-sc-1aprtmp-12 dUatah hwxIMf"><svg
                      stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH" height="24"
                      width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg></button><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE SharedStyledComponents__NavLink-sc-1cr9zfr-11 Nav__RightNavLink-sc-1aprtmp-8 jEZlpP jODkFW"
                    href="..\languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH"
                      height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                      </path>
                    </svg><span class="Nav__Span-sc-1aprtmp-11 bDRFLa"><span>Languages</span></span></a>
                </div>
              </div>
              <div class="Mobile__Container hhdXUp"><button aria-label="Toggle search button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg stroke="currentColor" fill="currentColor"
                    stroke-width="0" viewbox="0 0 24 24"
                    class="Icon__StyledIcon Mobile__MenuIcon Mobile__OtherIcon iylOGp dUGGTH hvwyGc" height="24"
                    width="24" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                    </path>
                  </svg></button><button aria-label="Toggle menu button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg viewbox="0 0 24 40"
                    class="Mobile__GlyphButton gbspKa">
                    <path d="M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0"></path>
                  </svg></button>
                <div class="Mobile__MobileModal bCHBHX" style="display:none;opacity:0"></div>
                <div aria-hidden="true" class="Mobile__MenuContainer AJukL"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <ul class="Mobile__MenuItems gYetwr">
                    <li aria-label="Select Use Ethereum" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Use Ethereum</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\staking\index.html"><span>Stake ETH</span></a></li>
                                                    <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Learn" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Learn</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\history\index.html"><span>History of Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE is-glossary SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                              fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                              class="Icon__StyledIcon Link__GlossaryIcon iylOGp jfMIWk" height="12px" width="12px"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                              </path>
                            </svg></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\web3\index.html"><span>What is Web3?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Developers" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Developers</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\index.html"><span>Developers' home</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\/en/developers/docs/"><span>Documentation</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                        </li>
                      </ul>
                    </li>
                    <li aria-label="Select Enterprise" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Enterprise</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Community" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Community</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\index.html"><span>Community hub</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\online\index.html"><span>Online communities</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\grants\index.html"><span>Grants</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                      </ul>
                    </li>
                  </ul>
                </div>
                <div aria-hidden="true" class="Mobile__BottomMenu iYttIj"
                  style="transform:translateX(-100%) translateZ(0)">
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Search</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Light</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><a class="Link__InternalLink gCWUlE Mobile__BottomLink8 heSUpS"
                      href="languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                        viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                        </path>
                      </svg>
                      <div class="Mobile__BottomItemText hkZTkJ"><span>Languages</span></div>
                    </a></div>
                </div>
                <div class="Mobile__MenuContainer Mobile__SearchContainer AJukL gBSEi"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <h3 class="Mobile__SearchHeader iXlChz"><span>Search</span><span
                      class="Mobile__CloseIconContainer jmriUx"><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon iylOGp" height="24" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                        </path>
                      </svg></span></h3>
                  <div class="Search__Root kNenpg">
                    <form class="Input__Form eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput kkfPkW">
                      <p class="Input__SearchSlash ggVPUc">/</p><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon Input__SearchIcon iylOGp gFzMVg"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div>
                  <div class="Mobile__BlankSearchState jBipln"><span size="3" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji hLjau undefined"><img alt="â›µ"
                        src="https://twemoji.maxcdn.com/2/svg/26f5.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Search
                      away!</span></div>
                </div>
              </div>
            </div>
          </nav>
        </div>
        <div id="main-content"></div>
        <div class="Layout__MainContainer-sc-19910io-1 gqazVg">
          <div class="Layout__MainContent-sc-19910io-2 kCJhKM">
            <main class="Layout__Main-sc-19910io-3 dliKfQ">
              <div class="SharedStyledComponents__Page-sc-1cr9zfr-0 run-a-node__GappedPage-sc-16hodpi-0 kCvjty rfGjo">
                <div class="run-a-node__HeroContainer-sc-16hodpi-2 hRLrUW">
                  <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                    <div class="PageHero__HeroContainer-sc-r5r57a-0 hsvKXW run-a-node__Hero-sc-16hodpi-3 jpFHsj">
                      <div class="PageHero__HeroContent-sc-r5r57a-1 eyNDuU">
                        <h1 class="PageHero__Title-sc-r5r57a-4 dipPYX"><span>Run a node</span></h1>
                        <h2 class="PageHero__Header-sc-r5r57a-3 kdyHZT"><span>Take full control.<br>Run your own
                            node.</span></h2>
                        <div class="PageHero__Subtitle-sc-r5r57a-5 ivcuAF"><span>Become fully sovereign while helping
                            secure the network. Become Ethereum.</span></div>
                        <div class="PageHero__ButtonRow-sc-r5r57a-6 caMbSB"><button
                            class="ButtonLink__StyledScrollButton-sc-8betkf-1 ButtonLink__PrimaryScrollLink-sc-8betkf-4 fXsDBz imwVIo PageHero__StyledButtonLink-sc-r5r57a-7 iUNaqu"><span>Learn
                              more</span></button><button
                            class="ButtonLink__StyledScrollButton-sc-8betkf-1 ButtonLink__SecondaryScrollLink-sc-8betkf-5 fXsDBz gNxaxD PageHero__StyledButtonLink-sc-r5r57a-7 iUNaqu"><span>Let's
                              dive in!</span></button></div>
                      </div>
                      <div data-gatsby-image-wrapper=""
                        class="gatsby-image-wrapper gatsby-image-wrapper-constrained PageHero__HeroImg-sc-r5r57a-2 cRmbnZ">
                        <div style="max-width:624px;display:block"><img alt="" role="presentation" aria-hidden="true"
                            src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;608&#x27; width=&#x27;624&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                            style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAATCAYAAACQjC21AAAACXBIWXMAABYlAAAWJQFJUiTwAAAFXUlEQVQ4y21UeUibdxh+v7RsUNZ1N+yCsT/GBrOlO+ioYg+kl7VVa6JpDs1tYvLlUKO5NTHmy2GMR77ExHgkxqitqcZ4tDqn3bq1sgodDEoL619t3frPWBmlM/l+i3Ywdjzw8rvg4Xl/PM8L8C9U1WqA12gAfdAHhWVMOHa2Btu39wQUFNLfKDhML+TR1VhFkR6OFDGholQILLoU/hcOix28rg4Q6UygcTlh/2flcIZVu/32wkuf7Dgv1TRX8/Ahbpn4ZcE5MbBZMuBU1YFJZf0nkctKgKfNCdEACcEcodvhhvHBMNSpdTC6PoPdRg+Bq21SSAzmH/UNLWtyHn5KK1SCQqjBxBwFmNXtIKnW/k0Y6emFO9cXQCzQwPpyCgJeH3y3MEm7/M00zdTnA7HBwtF4iYeNbe77DqPrFxOuD8KuwzsQQqAWa0HE1kCD3AwysQbsptZnpOlEDAZ6/TAdH8IAPoaN75dgZeUSSI2W4wbS98gc6nlia/f/5icCj71G5z1c1JwnqMKhrcmK+V1u6CYIsOrNwOfKAGymNjBorVh24xZcGIoAt1q5eyY+JItFQt32geA9z8QgsoUCT3u80c1I59CTiDOICJ1H12n2wsr4BO3m4kXs4mCA5jC3YKdPVOYIjVZQKXQAr5Vj8VCo5Kvp8Ws3F6dQPDmKAukJRE7Hs93hQRQnL1IJcmxzwj+Kwq7ITQFX/e5W27/eWYL5iWHwe3sgv+AcAIclB4vO9sH0yODYD6sz6O7aFXTn+uXMysLk5lBqPEum4mg4mKBmw7NUqj+NckUl+1JoPBy+mxwOTkXJ7tbRkJ/R5ezIKz7Jeg4mYwnodvd2D/fHUHxgODPS15dJjQxQ12YS6OpMgkpMxql0LE0tjy9Sl+PzVHp4mpqKJKm50RF0bTaOVqfjOfIeZFTjP5efrsiDXfD8TovJPdvrH0MD0bnN3vAUIjyDlIUIUHaHD7nb3Ij0dKGcCmo20U9dTcXQ2kKC+nZuLDs72p8hidZNvVyI5NXnnwqYrL2gVeKviwW6WyrchYiusc3ohWUqvbBGTS3eoMgLS1SrN4aMNi/SG73IZHAhm5lAhJVAfR0OymvRUi0aadYgFyCcx3nApVe9DzVs4UdyqSmjbu5CGguJtPZ+1EGOZfrH5jKDqdVseGAWeYJhykdGKZXGhZicBoovs1J2XSPqsTVRjmblH2alGOF8zn36GcabUMORvyoR1htlctO8ooH4Sanv3NS09CJVC4kMzkHUG52nhseuoOSlL7P9kWSm1RrI6pucVJfNkPG363MWUiGdXPy4lsPRFeSX7gSFWAXKOv2Wv2nlxZVv5cx5VCTWKgVSQ0yhaH3QZOxEJiKUCUaSKJn6GqWX1hFJDqOQ04R8Vl3WqJKPirn8/UEL/1lK6kQKUEgN2O85Tx0vOAZcRjUwJfUwElyADo0D95p9SGfqQbiq/XZjQ9uy3RnZcNo9WV+rflWnUp8EeBHjMLhgVYuwvH25o6BGAWK+GvhCLQgEDZiIr6KpcPMOR5UETCLdvpbmtkeGlu6Nek3HwS0BzDL2hwqR/FBJCX93M66CG0kCZDV8jEtnQoMkp5JVVQtWuweYlbXAOS8DLrsOeLmqPc4DfolgN5stnWbXKKW4xgy5ZGCnCo+BXFALe94pBkuDBlOJJWBU1QHjnACsWtWztrciVEUXbQ/M4hM1UHCIAV8cZMDRo2xafiH9wIH8sj1vv/c5nCkVYgpZE9asbqI14WrothuhqIgF0R4H1MsUIOSI/jto12MpeOXTAmBWCIFRKoD8IxXb92fLJJBfWAm5LwEJXwESXl1ulf+1l22L2sKfypuw9/82zc4AAAAASUVORK5CYII="
                          alt="">
                        <picture>
                          <source type="image/webp"
                           sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="eager"
                            src="../../static/dbf666200848e8f160f485972460b177/36d7b/ethereum-inside.png"
                          >
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                              sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                              style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="eager"
                              src="../../static/dbf666200848e8f160f485972460b177/36d7b/ethereum-inside.png"
                              >
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="what-is-a-node" class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="run-a-node__TwoColumnContent-sc-16hodpi-4 uqUCr">
                    <div class="run-a-node__Width60-sc-16hodpi-14 kNrMST">
                      <h2><span><i>What</i> does it mean to "run a node"?</span></h2>
                      <h3><span>Run software.</span></h3>
                      <p><span>Known as a 'client', this software downloads a copy of the Ethereum blockchain and
                          verifies the validity of every block, then keeps it up-to-date with new blocks and
                          transactions, and helps others download and update their own copies.</span></p>
                      <h3><span>With hardware.</span></h3>
                      <p><span>Ethereum is designed to run a node on average consumer-grade computers. You can use any
                          personal computer, but most users opt to run their node on dedicated hardware to eliminate the
                          performance impact on their machine and minimize node downtime.</span></p>
                      <h3><span>While online.</span></h3>
                      <p><span>Running an Ethereum node may sound complicated at first, but it's merely the act of
                          continuously running client software on a computer while connected to the internet. While
                          offline, your node will simply be inactive until it gets back online and catches up with the
                          latest changes.</span></p>
                    </div>
                    <div class="run-a-node__Width40-sc-16hodpi-15 hZLFCE">
                      <div data-gatsby-image-wrapper="" class="gatsby-image-wrapper gatsby-image-wrapper-constrained">
                        <div style="max-width:624px;display:block"><img alt="" role="presentation" aria-hidden="true"
                            src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;416&#x27; width=&#x27;624&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                            style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAANCAYAAACpUE5eAAAACXBIWXMAAD2EAAA9hAHVrK90AAAEDUlEQVQ4yzXUa0yadxQG8AMucR+WLFuyJV2XJu2Wrt3UrmvTWtd6m2itRauo3ATk8oIvAgL1VqiiiBcmqKCoRUEQsVrEuxtemGZutdFubku72c3GNG7JLkmdafZBBf7DJTvJ8/WX8+QkByA88ckMQAhB4oWThIRzb0PalXNvfnwhhvl+dPRbF8/HgCTtKGwu9QP6+ydoqSkC0qUjQEk7Ddmkk1BwIwYyk08AOelE2FgFkOqm/sPeCMMV+s8iyPGx8EncmVlywhmUefkDD+n0S8BOOnb82fJg1A+LI2SlkKImJ7+nyE3/EMsmRbGo1z86Sk4+FQZPERDaAEjJKQbXCoJS7RABr50Js9GRtPSL31XRziJFZtTq1Ug40qli/L5+fwb99mQFTQ50oroKGTJobqIGlQz1mRs8Ue8eh5cJRMKKbwhApHJDkeoeEVPdA6zcfoMtsf1cKeLtNzHeQXhu+p6yssNUI8rYpooMqG9g8sA/4d8fvfsosDi9vjfl2UA9Xf7Fw4aHMerqAXCNF2jFrZCYznulwfX0T3ndLBLw1MGy/NgQM78YSXVfhpRlquc8mQvN3/81ODM8Gxy2/4KWJx8Hpoe30Z2OlYX/QX1tA4Cu7wmxzr4B1V3fnjePvdhXaOeDbMwY0lgfIWWlLSAvr0eYSoWYxaagd241NOPyB51NfyFv2x8Br+k56tGv+HZnw+AWguaaZgBZoy9C0bwA8sa5dNw0gWhiU4iNWUJVxpngoCoV3cITHjOkki2Box+5f1wLzrvdwV79SMhlHA/0GyaQVd+9doUDEUw5gKGmHCAWY0CqRgrxGPPVTINm92pdLZIrBcHesmsHmttNiFuoreazyOUkrhCZHM69VZ8j4POYg/4xy344yNlRvSPlMl5XCFhhUE6Asw+MIGlyEgVGC+RaDNNkowFRONlIKq37B1P791IoquFPrc3rC95+9HB+PLQ0OoDutpvRSHcnaqlSIbel5XNBBh/k2aUEj60VgMu/TeBwdZCRSH6NySvKy2Lyq2KTOILr1IqOUrWt+1IK22E0GyZ3Npe/3vxmdnHc3uWyGfQ3e/T6HKNahXfV18c5W5rB09VOPDwMsAQGYPINMXyxeReXtiGh5M4BR+F4mkMvqqfShP6eNgsatZt2qhUY0qtlB0aNYldXjm/JCqmua4lxx6okEtAqpcQpuxV8I+ENBTIHsPBejCtxLnLEPV8U4DYLXTuIURLEsPT9Nrx49kDiG7K6C3MyvTgjb6OInvdQzMz/SlxAXRPRc3mCvCwQ0SnEW2IhlPB5AByhEQowE4jkDmi1bgKvZBhYjWNAT8WJ/XOb4Rpb4GpvhJykRCDFX47cWZ4DbQkOxQVUwGgUkLLphJJCBmSRkg/fAvwLhW79p19VhEMAAAAASUVORK5CYII="
                          alt="">
                        <picture>
                          <source type="image/webp"
                           sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                            src="../../static/9a6e158f4ffd1cb5de246a3ecd0d7f86/ce941/hackathon_transparent.png"
                            >
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                             sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                              style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                              src="../../static/9a6e158f4ffd1cb5de246a3ecd0d7f86/ce941/hackathon_transparent.png"
                              >
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="SharedStyledComponents__Content-sc-1cr9zfr-3 run-a-node__FlexContent-sc-16hodpi-16 dedPKg bOAabr">
                  <div
                    class="ExpandableInfo__Card-sc-mbbhey-0 eoSjYG run-a-node__StyledExpandableInfo-sc-16hodpi-10 gMuGQG">
                    <div class="ExpandableInfo__Content-sc-mbbhey-1 hyYxUM">
                      <div data-gatsby-image-wrapper="" class="gatsby-image-wrapper gatsby-image-wrapper-constrained">
                        <div style="max-width:300px;display:block"><img alt="" role="presentation" aria-hidden="true"
                            src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;271&#x27; width=&#x27;300&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                            style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAASCAYAAABb0P4QAAAACXBIWXMAAD2EAAA9hAHVrK90AAAFF0lEQVQ4y2VUa0yTZxQ+pc79WLItLpotkZklm8s2/bHhYnSLm07nJe7ibk7UiTqsyKYIIqhYqSCUtbXQFumdttDSFtpKgVJo0XJroZWLpVAYeOMmCBuCIor0O/vaScyykzx5kzd5nnPO+7znADwLdn4NyMu6QGXtoxRW3wQGz/DKG0uXUhy9CPuSUiFDaIar3QiJ6fKXo+OZi86wNOSdJcRdv/VH+F+Ym6dAbGynSEu9kF/uX66vHfZniizf5erdF6ISUxcnZspWik3t9Et6D0de3uVFxBBvV3QSMMXW50JSQw8ozDchO7+JUlw3+mJtH4K+9i5L5xgaMTrHKzT2O6iz9LRKmnvu6K4OYUXbI6PZMzXzh8y2RaBtApGhfaGkuAckxX4Q6joACi2DoLXdo1zSeIGvdgFHaV9c6r4/VlB1w2VueXCv9Moo1tc9QPXkJOqaR7DMPXWbTHRP5xisekhWyVXUQ5F1FArK+yH/ch+AqNgXJjN1A7/QsyFLbIviq5tpBZW3kczcX9I4jrW2iYCnYTqgxkeBgv4x1NuHAyLD9buqipuPYxKzI/L0rXGFlv5wTdUwKdhLAba4nsqRNAIz98qvUlMXCjQep6CwdS6NXz2sK/Zja81DosIyQWiHpghlYJIQ2H2YzqkcFOq8TwUad5O89M9HZ5mmFSlME1zgWSkQd1pGPXQ0B0hskhr9KDb4UKTvnMvJd6PWMEbwS2YwTXE/UK9/Qjia5lDdMkJkK90E+WYBWWk35mmvT3wUse3NNWu/h+8jT1AgJkEQ9kt0Buzefz6Cp7o2m6tpxUxBDcGWuAi6qBtPKyeJTM1jvGZCbCycIdTSbmRL6jE9uyog1HuR7G7gnbfXvBqxajtEHc6kwPYdMSG3g98giaHtLLDcQWVFb4DBqSTSJD6CIfbiWZ5vqEjSN65T96FM0kak51QTRTUDTyUl3XgsWa51tCHcnkbKhs17AQ7GskNiQcSfUbVyRC40NPw1l6NwYSq3LkBXjWMiz9twWdrbYdeMYVqWNSA2dqL+6ugsU1CLv5+U5Nd3IHgHMWz1J9sBIvfTIVvaEsYRNUNymj4x/aIdhVrfnNn9gNBYb2GGwk9kFM1gs/EJyhXeAE/dhuQQENlyD57nVGN8inJbSsZloGeVhW3+6iBA9FEuHInPDbXNlbQCg2VRp120oUjne2pyTiDp4hyD33lDJfWP5em8qCq/RQgK2uYYbCumsipS0rk2ECi8Ycveehc+2/gzwIEjLIg8kApsoYuakeOApFQd86LUiZIS/7TE4Mccpdu5ZePhxQkpqpVcuWuEdBVlxp7HWZcceCxJvvvkOV2wOmpZHcLadd8CDAwMAC2OByW2aQpP2gGJdI2Cr2pBq3cW7V0EGp1josqWWXCTI2l233dd6UYs8zxElrAB404pjjNY1cDKc1I3bN797ywHBXNVXUFzKDt+SoAPV216KfZEXoKwuN1GjtcNmdm3S2HpDJmmsPSkkEujj6toNB+M5ewh6ZSgRrCyULvzgg3uIdh3mAtRtCzY+jUNks/r4Nk2eYEgz34Sqz/9cv5u4d/kSboL4cuWw/sr1sDnm3bBui92PhcMRtwpJdCO8iHqEBNSMo1UkhzKPkuSn5BYv+WHeUGYQQw7fkZJjdx/DoJd7aNlPF9fQcF5LFiwAPbE0OGbyN/gvZUfw96YsxAdfwFikzkQk8QGWgITdh44AYteWwJLXg+H5R9EQHh4+H/26j8fvxKTT/pDjAAAAABJRU5ErkJggg=="
                          alt="">
                        <picture>
                          <source type="image/webp"
                           sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 300px) 300px, 100vw" decoding="async" loading="lazy"
                            src="../../static/4d030a46f561e5c754cabfc1a97528ff/6833d/impact_transparent.png"
                            >
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                             sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                              style="opacity:0" sizes="(min-width: 300px) 300px, 100vw" decoding="async" loading="lazy"
                              src="../../static/4d030a46f561e5c754cabfc1a97528ff/6833d/impact_transparent.png"
                              >
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                      <div class="ExpandableInfo__TitleContent-sc-mbbhey-2 fBPrBu">
                        <div class="ExpandableInfo__Question-sc-mbbhey-6 dVvOxl">
                          <div class="ExpandableInfo__Header-sc-mbbhey-7 fudfyE">
                            <h2 class="ExpandableInfo__Title-sc-mbbhey-3 gHulik"><span><i>Who</i> should run a
                                node?</span></h2>
                          </div>
                          <p class="ExpandableInfo__TextPreview-sc-mbbhey-4 fCNOvJ"><span>Everyone! Nodes are not just
                              for miners and validators. <i>Anyone</i> can run a nodeâ€”you don't even need ETH.</span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div style="height:100%">
                      <div style="display:inline-block">
                        <div class="ExpandableInfo__Text-sc-mbbhey-5 dgEjeS" style="opacity:1">
                          <p><span>You don't need to stake ETH or be a miner to run a node. In fact, it's every other
                              node on Ethereum that holds miners and validators accountable.</span></p>
                          <p><span>You may not get the financial rewards that validators and miners earn, but there are
                              many other benefits of running a node for any Ethereum user to consider, including
                              privacy, security, reduced reliance on third-party servers, censorship resistance and
                              improved health and decentralization of the network.</span></p>
                          <p><span>Having your own node means you don't need to trust information about the state of the
                              network provided by a third party.</span></p>
                          <p class="run-a-node__StrongParagraph-sc-16hodpi-35 iCfTWr"><span>Don't trust. Verify.</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2><span><i>Why</i> run a node?</span></h2>
                  <div
                    class="SharedStyledComponents__CardGrid-sc-1cr9zfr-15 run-a-node__InfoGrid-sc-16hodpi-11 hCavpi jHReSL">
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTUxLjQ1OTggNDIuMjgzMUM1MS40NTk4IDQzLjYwNCA1MS40MTU2IDQ0LjgzOTUgNTEuMjYzMiA0Ni4wNTk5QzUwLjk2MzcgNDguNDU3OSA1MC4yNDY3IDUwLjc5NzggNDguNjI3MSA1My42MTM3QzQ2Ljk4OTQgNTYuNDYxMSA0NC43MTgxIDU4LjIzODUgNDIuMDE3NiA1OS45ODE1QzM4Ljk5NyA2MS45MzExIDM1LjQzOTUgNjQgMzEuNjMxMyA2NEMyOC4yMTI5IDY0IDI0Ljk5NjUgNjIuMTQ5NiAyMi4xODkxIDYwLjU1MzlDMTYuMDAzIDU3LjAzNzkgMTEuODAyNyA1MC4xNzQzIDExLjgwMjcgNDIuMjgzMUMxMS44MDI3IDMwLjgxMDYgMTIuMjc0OCAxNS4zNzI5IDMxLjYzMTMgMTUuMzcyOUM0Ny40OTA2IDE1LjM3MjkgNTAuNjk2OSAyNi4yNjA5IDUxLjMxNzIgMzYuNTQ0NEM1MS40MzU2IDM4LjUwNTggNTEuNDU5OCA0MC40NDUzIDUxLjQ1OTggNDIuMjgzMVoiIGZpbGw9IiNGRkRENjciLz4KPHBhdGggZD0iTTUxLjQ1OTggNDIuMjgzQzUxLjQ1OTggNDAuNDQ1MyA1MS40MzU2IDM4LjUwNTggNTEuMzE3MyAzNi41NDQzQzUxLjUyMjIgMzYuNDExNCA1Mi4xMjA4IDM2LjE0NTYgNTIuODc2MSAzNi4xNDU2QzUzLjgyMDQgMzYuMTQ1NiA1NS43MDg4IDM2LjYxNzcgNTUuNzA4OCA0MC44NjY3QzU1LjcwODggNDUuMTE1NyA1Mi45NTUgNDYuMDU5OSA1MS4yNjMyIDQ2LjA1OTlDNTEuNDE1NiA0NC44Mzk0IDUxLjQ1OTggNDMuNjAzOSA1MS40NTk4IDQyLjI4M1oiIGZpbGw9IiNGRkRENjciLz4KPHBhdGggZD0iTTUyLjE2NzggMzYuMzgxN0M1Mi4zNzI3IDM2LjI0ODggNTIuODc2IDM2LjE0NTcgNTMuMTEyIDM2LjE0NTdDNTMuNDkyNiAzNi4xNDU3IDUzLjc5MDQgMzYuMjIyMyA1NC4yOTIyIDM2LjU5MkM1NC4yODc1IDM0LjMzMzEgNTMuNjYxNiAzMS44OTMxIDUzLjM0ODEgMzAuOTUyNEM1MS43NzQ0IDMwLjQ4MDMgNDguODE1OCAzMC4wMDgyIDQ5LjU3MTIgMzEuODk2NkM1MC4zMjY2IDMzLjc4NTEgNTAuMjAwNyAzOC4wMzQgNTAuMDQzMyAzOS45MjI1QzUxLjQ1OTYgMzkuNDUwNCA1MS45MzE3IDM3LjU2MiA1Mi4xNjc4IDM2LjM4MTdaIiBmaWxsPSIjRkZCMzAwIi8+CjxwYXRoIGQ9Ik00OC4xNTQ4IDM4LjUwNjFDNDguMTU0OCAzOS45MjI1IDQ3LjY4MjcgNDIuMjgzIDQyLjAxNzQgNDIuMjgzQzM5LjY1NzIgNDIuMjgzIDM2Ljc4NjEgNDIuMTI5MSAzNi4zNTI1IDQwLjM5NDZDMzUuNzYyNCAzOC4wMzQgMzYuNzU2OCAzNC42NTYyIDQxLjU0NTMgMzQuMjU3MkM0Ny4yMTA2IDMzLjc4NTEgNDguMTU0OCAzNy4wODk4IDQ4LjE1NDggMzguNTA2MVoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik00OC4xMjY4IDM5LjA0NjFDNDguMTQ4MSAzOC44NTY1IDQ4LjE1NDggMzguNjc0NiA0OC4xNTQ4IDM4LjUwNjFDNDguMTU0OCAzNy4wODk4IDQ3LjIxMDYgMzMuNzg1MSA0MS41NDUzIDM0LjI1NzJDMzcuNTQxMSAzNC41OTA5IDM2LjE4OTkgMzcuMDA3NSAzNi4xOTk4IDM5LjE3MDVDMzYuNzc2NCAzNy40ODYgMzguMjk3OCAzNS45ODM1IDQyLjAxNzcgMzUuNjczNUM0Ni41ODQxIDM1LjI5MyA0Ny45MjU0IDM3LjU2NzkgNDguMTI2OCAzOS4wNDYxWiIgZmlsbD0iIzY2NEUyNyIvPgo8Y2lyY2xlIGN4PSI0MS43MzQ2IiBjeT0iMzguMDM0MSIgcj0iMy43NzY4NiIgZmlsbD0iIzY2NEUyNyIvPgo8Y2lyY2xlIGN4PSI0MS41NDU1IiBjeT0iMzguMDM0MSIgcj0iMS40MTYzMiIgZmlsbD0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMTguMTc2MSIgY3k9IjM3Ljc5OCIgcj0iMTIuOTgzIiBmaWxsPSIjRkZERDY3Ii8+CjxwYXRoIGQ9Ik0xMS4zNjY5IDM3LjkwOTVDMTEuMzY2OSAzOS41NDk2IDExLjkxMzYgNDIuMjgzIDE4LjQ3MzggNDIuMjgzQzIxLjIwNjggNDIuMjgzIDI0LjUzMTQgNDIuMTA0OCAyNS4wMzM1IDQwLjA5NjNDMjUuNzE2OSAzNy4zNjI4IDI0LjU2NTMgMzMuNDUxNCAxOS4wMjA1IDMyLjk4OTRDMTIuNDYwMyAzMi40NDI3IDExLjM2NjkgMzYuMjY5NSAxMS4zNjY5IDM3LjkwOTVaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTEuMzk5NCAzOC41MzQ4QzExLjM3NDcgMzguMzE1MiAxMS4zNjY5IDM4LjEwNDYgMTEuMzY2OSAzNy45MDk1QzExLjM2NjkgMzYuMjY5NSAxMi40NjAzIDMyLjQ0MjcgMTkuMDIwNSAzMi45ODk0QzIzLjY1NzIgMzMuMzc1OCAyNS4yMjE4IDM2LjE3NDIgMjUuMjEwNCAzOC42Nzg4QzI0LjU0MjcgMzYuNzI4MyAyMi43ODA5IDM0Ljk4ODQgMTguNDczNCAzNC42Mjk0QzEzLjE4NTcgMzQuMTg4OCAxMS42MzI2IDM2LjgyMzEgMTEuMzk5NCAzOC41MzQ4WiIgZmlsbD0iIzY2NEUyNyIvPgo8Y2lyY2xlIHI9IjQuMzczNDYiIHRyYW5zZm9ybT0ibWF0cml4KC0xIDAgMCAxIDE4LjgwMTYgMzcuMzYzKSIgZmlsbD0iIzY2NEUyNyIvPgo8Y2lyY2xlIHI9IjEuNjQwMDUiIHRyYW5zZm9ybT0ibWF0cml4KC0xIDAgMCAxIDE5LjAyMDEgMzcuMzYyOSkiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjEwLjkwMDkiIHk9IjQ3LjAyODciIHdpZHRoPSIzLjEwNjk3IiBoZWlnaHQ9IjUuNjY1MjkiIHRyYW5zZm9ybT0icm90YXRlKDMwIDEwLjkwMDkgNDcuMDI4NykiIGZpbGw9IiNCMEJEQzYiLz4KPHBhdGggZD0iTTguNDYyMTYgNDguNzUxOEwxMy4yODYzIDUxLjUzNzFMNy40ODg1NSA2MS41NzkxTDIuNjY0MzkgNTguNzkzOUw4LjQ2MjE2IDQ4Ljc1MThaIiBmaWxsPSIjMzMzMzMzIi8+CjxwYXRoIGQ9Ik04LjIzMzU5IDYxLjk4NjVMOC42OTg1OCA2MS4xODgyQzguODU0MDYgNjAuOTIxMyA2Ljg1NzY5IDU5LjU4MTMgNS44MjEgNTguOTc3NUM0Ljc4NDMyIDU4LjM3MzYgMi43MTA5NCA1Ny4xNjU5IDIuNDc4NDUgNTcuNTY1TDIuMDEzNDUgNTguMzYzM0MxLjc4MDk1IDU4Ljc2MjUgMy41OTUxNyA1OS44MTkyIDQuODkxMDMgNjAuNTc0QzYuMTg2ODkgNjEuMzI4OSA4LjAwMTA5IDYyLjM4NTYgOC4yMzM1OSA2MS45ODY1WiIgZmlsbD0iI0IwQkRDNiIvPgo8Y2lyY2xlIGN4PSIxOC4xNzYxIiBjeT0iMzcuNzk4IiByPSIxMS43MzMiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMyIgc3Ryb2tlPSIjQjBCREM2IiBzdHJva2Utd2lkdGg9IjIuNSIvPgo8cGF0aCBkPSJNNDkuMDk5MiAwLjI2NTQ0N0M0Ny40NzcgLTEuMzU2NzggNDQuMzc4MSA0Ljk4NjUzIDMxLjE1OTEgNC45ODY1M0MyMi4xODkxIDQuOTg2NTMgMTUuMTA3NSAtMC42Nzg3NjkgMTQuMTYzMiAwLjI2NTQ0N0MxMy4yMTkgMS4yMDk2NiAxNC4xNjMyIDIuNjI1OTkgMTQuMTYzMiA5LjcwNzZDMTQuMTYzMiAxNS4zNzI5IDExLjAxNTggMTkuOTM2NiA5LjQ0MjE2IDIxLjUxMDNDOC4wMjU4MyAyMi45MjY2IDAgMjYuMjMxNCAwIDI3LjY0NzdDMCAzMC40ODAzIDQuNzIxMDggMzQuNzI5MyAzMi4xMDMzIDM0LjcyOTNDNTkuNDg1NiAzNC43MjkzIDYzLjI2MjQgMjkuNTM2MSA2My4yNjI0IDI3LjY0NzdDNjMuMjYyNCAyNi4yMzE0IDU3LjEyNSAyMy44NzA4IDUzLjgyMDMgMjEuNTEwM0M1MC45ODc2IDE5LjE0OTggNDkuMDk5MiAxMy4wMTI0IDQ5LjA5OTIgOS4yMzU1QzQ5LjA5OTIgMy4wOTgxIDUwLjA0MzQgMS4yMDk2NiA0OS4wOTkyIDAuMjY1NDQ3WiIgZmlsbD0iIzYyNzI3QSIvPgo8cGF0aCBkPSJNNTEuMjMzOSAxNy45MDhDNDcuMTUyOSAxOC41NTE0IDQwLjcxNDYgMTkuMTQ5OCAzMS42MzExIDE5LjE0OThDMjQuNjQwNiAxOS4xNDk4IDE3LjU4MjcgMTguNTg4OCAxMi4wMjUxIDE3Ljg2ODhDMTEuMTI3MSAxOS41NTI0IDEwLjEzMjkgMjAuODE5NSA5LjQ0MjA0IDIxLjUxMDRDOS4zODY3NSAyMS41NjU2IDkuMzIxMzkgMjEuNjIzOCA5LjI0NjgzIDIxLjY4NDZDMTQuNjc4NSAyMi42MjUzIDIyLjE1NzkgMjMuMzk4OCAzMS42MzExIDIzLjM5ODhDNDIuNDk5NyAyMy4zOTg4IDQ5LjcyNTggMjIuNjA2OSA1NC4xNDM0IDIxLjczNTNDNTQuMDMzIDIxLjY2MDMgNTMuOTI1MiAyMS41ODUzIDUzLjgyMDIgMjEuNTEwNEM1Mi44MjkgMjAuNjg0NCA1MS45NTM0IDE5LjM5NTkgNTEuMjMzOSAxNy45MDhaIiBmaWxsPSJibGFjayIgZmlsbC1vcGFjaXR5PSIwLjYiLz4KPHBhdGggZD0iTTM4LjM0NTYgNTEuNzI1MkgzMi4xMDMzSDI1Ljg2MUMyNC45NjkyIDUxLjcyNTIgMjQuMzEzNSA1MS43MjUyIDI0LjMxMzUgNTIuODU4M0MyNC4zMTM1IDUzLjk5MTMgMjUuOTY1OSA1Ni45MTg0IDMyLjEwMzMgNTYuOTE4NEMzNy43Njg2IDU2LjkxODQgMzkuNjU3IDUzLjk5MTMgMzkuNjU3IDUyLjg1ODNDMzkuNjU3IDUxLjcyNTIgMzkuMjM3MyA1MS43MjUyIDM4LjM0NTYgNTEuNzI1MloiIGZpbGw9IiM5MzcyMzciLz4KPHBhdGggZD0iTTM1LjQwOCA0Ny4wMDQySDMyLjEwMzJIMjguNzk4NUMyOC4zMjY0IDQ3LjAwNDIgMjcuODU0MiA0Ny4wMDQyIDI3Ljg1NDIgNDcuNDc2M0MyNy44NTQyIDQ3Ljk0ODQgMjkuNzQyNyA0OS4zNjQ3IDMyLjEwMzIgNDkuMzY0N0MzNC40NjM4IDQ5LjM2NDcgMzYuMzUyMiA0Ny45NDg0IDM2LjM1MjIgNDcuNDc2M0MzNi4zNTIyIDQ3LjAwNDIgMzUuODgwMSA0Ny4wMDQyIDM1LjQwOCA0Ny4wMDQyWiIgZmlsbD0iI0VCQTM1MiIvPgo8L3N2Zz4K"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Privacy & Security</span></h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>Stop leaking your personal
                              information to third party nodes.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>When sending transactions using public nodes, personal information can be leaked to
                                these third-party services such as your IP address and which Ethereum addresses you
                                own.</span></p>
                            <p><span>By pointing compatible wallets to your own node you can use your wallet to
                                privately and securely interact with the blockchain.</span></p>
                            <p><span>Also, if a malicious node distributes an invalid transaction, your node will simply
                                disregard it. Every transaction is verified locally on your own machine, so you don't
                                need to trust anyone.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="data:image/svg+xml;base64,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"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Censorship Resistance</span>
                            </h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>Ensure access when you need
                              it, and don't be censored.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>A 3rd-party node could choose to refuse transactions from specific IP addresses, or
                                transactions that involve specific accounts, potentially blocking you from using the
                                network when you need it. </span></p>
                            <p><span>Having your own node to submit transactions to guarantees that you can broadcast
                                your transaction to the rest of the peer-to-peer network at any time.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="data:image/svg+xml;base64,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"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Participate</span></h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>The decentralization
                              revolution starts with <strong>you</strong>.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>By running a node you become part of a global movement to decentralize control and
                                power over a world of information.</span></p>
                            <p><span>If you're a holder, bring value to your ETH by supporting the health and
                                decentralization of the network, and ensure you have a say in its future.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA0MCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgb3BhY2l0eT0iMC45Ij4KPHBhdGggZD0iTTIwLjA5MTEgNjEuNTY5N1Y0My43NTE5TDM2Ljk0MTcgMzQuMDY3NkwyMC4wOTExIDYxLjU2OTdaIiBmaWxsPSIjODU5RkQ1IiBmaWxsLW9wYWNpdHk9IjAuNSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNMjAuMDkxMSA2MS41Njk3VjQzLjc1MTlMMy4yNDA0MyAzNC4wNjc2TDIwLjA5MTEgNjEuNTY5N1oiIGZpbGw9IiM4NTlGRDUiIGZpbGwtb3BhY2l0eT0iMC41IiBzdHJva2U9ImJsYWNrIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0zNi45NDE3IDMwLjc0NDRMMjAuMDkxMSAyMy4xOTA3VjQwLjYyMjRMMzYuOTQxNyAzMC43NDQ0WiIgZmlsbD0iIzg1OUZENSIgZmlsbC1vcGFjaXR5PSIwLjUiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIwLjA5MTEgNDAuNjIxOFYyMy4xOTAxTDMuMjQwNDggMzAuNzQzOEwyMC4wOTExIDQwLjYyMThaIiBmaWxsPSIjODU5RkQ1IiBmaWxsLW9wYWNpdHk9IjAuNSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNMzYuOTQxNyAzMC43NDM4TDIwLjA5MTEgMy4yNDA0OFYyMy4xOTAxTDM2Ljk0MTcgMzAuNzQzOFoiIGZpbGw9IiM4NTlGRDUiIGZpbGwtb3BhY2l0eT0iMC41IiBzdHJva2U9ImJsYWNrIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0zLjI0MDQzIDMwLjc0MzhMMjAuMDkxMSAzLjI0MDQ4VjIzLjE5MDFMMy4yNDA0MyAzMC43NDM4WiIgZmlsbD0iIzg1OUZENSIgZmlsbC1vcGFjaXR5PSIwLjUiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPGNpcmNsZSBjeD0iMjAuMjUzMiIgY3k9IjMuMjQwNTEiIHI9IjIuNzQwNTEiIGZpbGw9IiNFOEJBQkEiIHN0cm9rZT0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMy4yNDA1MSIgY3k9IjMyLjQwNTEiIHI9IjIuNzQwNTEiIGZpbGw9IiNDRkU4QkEiIHN0cm9rZT0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMzYuNDU1NiIgY3k9IjMyLjQwNTEiIHI9IjIuNzQwNTEiIGZpbGw9IiNCQUNBRTgiIHN0cm9rZT0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMjAuMjUzMiIgY3k9IjQxLjMxNjQiIHI9IjIuNzQwNTEiIGZpbGw9IiNCQUU4RTAiIHN0cm9rZT0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMjAuMjUzMSIgY3k9IjYxLjU2OTciIHI9IjEuOTMwMzgiIGZpbGw9IiNDRkJBRTgiIHN0cm9rZT0iYmxhY2siLz4KPGNpcmNsZSBjeD0iMjAuMjUzMyIgY3k9IjIzLjQ5MzYiIHI9IjMuNTUwNjMiIGZpbGw9IiNFOEUxQkEiIHN0cm9rZT0iYmxhY2siLz4KPC9nPgo8L3N2Zz4K"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Decentralization</span></h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>Resist strengthening
                              centralized points of failure.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>Centralized cloud servers can provide a lot of computing power, but they provide a
                                target for nation-states or attackers looking to disrupt the network.</span></p>
                            <p><span>Network resilience is achieved with more nodes, in geographically diverse
                                locations, operated by more people of diverse backgrounds. As more people run their own
                                node, reliance on centralized points of failure diminishes, making the network
                                stronger.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="data:image/svg+xml;base64,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"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Voice your choice</span></h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>Don't give up control in the
                              event of a fork.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>In the event of a chain fork, where two chains emerge with two different sets of
                                rules, running your own node guarantees your ability to choose which set of rules you
                                support. It's up to you to upgrade to new rules and support proposed changes, or
                                not.</span></p>
                            <p><span>If you're staking ETH, running your own node allows you to chose your own client,
                                to minimize your risk of slashing and to react to fluctuating demands of the network
                                over time. Staking with a third party forfeits your vote on which client you think is
                                the best choice.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="ExpandableCard__Card-sc-1z0zrur-0 diVorY">
                      <div class="ExpandableCard__Content-sc-1z0zrur-1 kQkhur">
                        <div class="ExpandableCard__Question-sc-1z0zrur-5 cPoGis">
                          <div class="ExpandableCard__Header-sc-1z0zrur-6 bWZmjK"><img
                              src="/static/sovereignty-glyph-717941dbfa1cd6eef8834f9d3255de8e.svg"
                              alt="[object Object]">
                            <h3 class="ExpandableCard__Title-sc-1z0zrur-2 fQKChX"><span>Sovereignty</span></h3>
                          </div>
                          <p class="ExpandableCard__TextPreview-sc-1z0zrur-3 jgdBtR"><span>Think of running a node like
                              the next step beyond getting your own Ethereum wallet.</span></p>
                        </div>
                        <div class="ExpandableCard__ButtonContainer-sc-1z0zrur-7 eHETdJ"><span
                            class="SharedStyledComponents__FakeLink-sc-1cr9zfr-12 ExpandableCard__StyledFakeLink-sc-1z0zrur-8 dMzxTW hnJxcR"><span>More</span></span>
                        </div>
                      </div>
                      <div style="height:0px">
                        <div style="display:none">
                          <div class="ExpandableCard__Text-sc-1z0zrur-4 dXxOUI" style="opacity:0">
                            <p><span>An Ethereum wallet allows you to take full custody and control of your digital
                                assets by holding the private keys to your addresses, but those keys don't tell you the
                                current state of the blockchain, such as your wallet balance.</span></p>
                            <p><span>By default, Ethereum wallets typically reach out to a 3rd-party node, such as
                                Infura or Alchemy, when looking up your balances. Running your own node allows you to
                                have your own copy of the Ethereum blockchain.</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Divider-sc-1cr9zfr-1 euABDx"></div>
                <div id="getting-started" class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2><span>Getting started</span></h2>
                  <div
                    class="SharedStyledComponents__Content-sc-1cr9zfr-3 run-a-node__GappedContent-sc-16hodpi-1 dedPKg dVfCFp">
                    <div
                      class="SharedStyledComponents__Content-sc-1cr9zfr-3 run-a-node__Highlight-sc-16hodpi-8 run-a-node__SoftwareHighlight-sc-16hodpi-9 dedPKg DQTwl">
                      <div class="run-a-node__ColumnFill-sc-16hodpi-12 eBYXqY">
                        <p><span>In the earlier days of the network, users needed to have the ability to interface with
                            the command-line in order to operate an Ethereum node.</span></p>
                        <p>
                          <code><span size="1" mt="0" mr="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 RDZme run-a-node__StyledEmoji-sc-16hodpi-27 dNxqsR undefined"><img alt="âš ï¸" src="https://twemoji.maxcdn.com/2/svg/26a0.svg" style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>If this is your preference, and you've got the skills, feel free to check out our technical docs.</span></code>
                        </p><a class="Link__InternalLink-sc-e3riao-1 gCWUlE"
                          href="/en/developers/docs/nodes-and-clients/run-a-node/"><span>Spin up an Ethereum
                            node</span></a>
                      </div>
                      <div class="run-a-node__ColumnNarrow-sc-16hodpi-13 gDPznN"><img
                          src="../../static/terminal-ee07657c4890727d1d31f717905032a9.svg" alt="Terminal glyph"></div>
                    </div>
                    <div
                      class="SharedStyledComponents__Content-sc-1cr9zfr-3 run-a-node__Highlight-sc-16hodpi-8 run-a-node__SoftwareHighlight-sc-16hodpi-9 dedPKg dEsqsV">
                      <div class="run-a-node__ColumnFill-sc-16hodpi-12 eBYXqY">
                        <p><span>Now we have DAppNode, which is <b>free and open-source software</b> that gives users an
                            <b>app-like experience</b> while managing their node.</span></p>
                      </div>
                      <div class="run-a-node__ColumnNarrow-sc-16hodpi-13 gDPznN"><img
                          src="data:image/svg+xml;base64,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"
                          alt="DAppNode glyph"></div>
                    </div>
                    <div
                      class="SharedStyledComponents__Content-sc-1cr9zfr-3 run-a-node__Highlight-sc-16hodpi-8 run-a-node__SoftwareHighlight-sc-16hodpi-9 dedPKg izhnI">
                      <div class="run-a-node__ColumnFill-sc-16hodpi-12 eBYXqY">
                        <p><span>In just a few taps you can have your node up and running.</span></p>
                        <p><span>DAppNode makes it easy for users to run full nodes, as well as dapps and other P2P
                            networks, with no need to touch the command-line. This makes it easier for everyone to
                            participate and create a more decentralized network.</span></p>
                      </div>
                      <div class="run-a-node__ColumnNarrow-sc-16hodpi-13 gDPznN"><img
                          src="data:image/svg+xml;base64,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"
                          alt="Phone tap glyph"></div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2><span>Choose your adventure</span></h2>
                  <p><span>You'll need some hardware to get started. Although running node software is possible on a
                      personal computer, having a dedicated machine can greatly enhance the performance of your node
                      while minimizing its impact on your primary computer.</span></p>
                  <p><span>When selecting hardware, consider that the chain is continually growing, and maintenance will
                      inevitably be needed. Increasing specs can help delay the need for node maintenance.</span></p>
                  <div class="run-a-node__Flex-sc-16hodpi-17 run-a-node__MarginFlex-sc-16hodpi-18 ihENAF bFrsG">
                    <div
                      class="run-a-node__Container-sc-16hodpi-19 run-a-node__FullyLoaded-sc-16hodpi-22 kZIACG knlSZL">
                      <div>
                        <h3><span size="2" mt="0" mr="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 bhqZra run-a-node__StyledEmoji-sc-16hodpi-27 dNxqsR undefined"><img
                              alt="ðŸ›’" src="https://twemoji.maxcdn.com/2/svg/1f6d2.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Buy
                            fully loaded</span></h3>
                        <p><span>Order a plug and play option from vendors for the simplest onboarding
                            experience.</span></p>
                        <ul>
                          <li><span>No building needed.</span></li>
                          <li><span>App-like setup with a GUI.</span></li>
                          <li><code><span>No command-line required.</span></code></li>
                        </ul>
                      </div>
                      <div class="run-a-node__ButtonContainer-sc-16hodpi-24 kiQPBL"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv run-a-node__ResponsiveButtonLink-sc-16hodpi-7 run-a-node__DappNodeButtonLink-sc-16hodpi-25 gAaFsL ixhdyo"
                          href="https://shop.dappnode.io/" target="_blank" rel="noopener noreferrer"><span>Shop
                            DAppNode</span></a><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv run-a-node__ResponsiveButtonLink-sc-16hodpi-7 run-a-node__AvadoButtonLink-sc-16hodpi-26 gAaFsL cEXogT"
                          href="https://ava.do/" target="_blank" rel="noopener noreferrer"><span>Shop Avado</span></a>
                      </div>
                    </div>
                    <div
                      class="run-a-node__Container-sc-16hodpi-19 run-a-node__FullyLoaded-sc-16hodpi-22 kZIACG knlSZL">
                      <div>
                        <h3><span size="2" mt="0" mr="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 bhqZra run-a-node__StyledEmoji-sc-16hodpi-27 dNxqsR undefined"><img
                              alt="ðŸ—ï¸" src="https://twemoji.maxcdn.com/2/svg/1f3d7.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Build
                            your own</span></h3>
                        <p><span>A cheaper and more customizable option for slightly more technical users.</span></p>
                        <ul>
                          <li><span>Source your own parts.</span></li>
                          <li><span>Install DAppNode.</span></li>
                          <li><span>Or, choose your own OS and clients.</span></li>
                        </ul>
                      </div><button class="run-a-node__ScrollButtonSecondary-sc-16hodpi-30 eyuVG"><span>Start
                          building</span></button>
                    </div>
                  </div>
                </div>
                <div id="build-your-own" class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2><span>Build your own</span></h2>
                  <div
                    class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildContainer-sc-16hodpi-29 kZIACG bjnfkk">
                    <div class="run-a-node__SvgTitle-sc-16hodpi-23 jyUDtB"><img
                        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDkiIGhlaWdodD0iNDIiIHZpZXdCb3g9IjAgMCA0OSA0MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTAgMjUuNjIyMUg0OVY0MC44NjY1QzQ5IDQxLjQ2NzkgNDguNTEyNSA0MS45NTU0IDQ3LjkxMTEgNDEuOTU1NEgxLjA4ODg5QzAuNDg3NTEyIDQxLjk1NTQgMCA0MS40Njc5IDAgNDAuODY2NUwwIDI1LjYyMjFaIiBmaWxsPSIjQjBCREM2Ii8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDYuODIyMiAyNy43OTk4SDIuMTc3NzhWMzkuNzc3Nkg0Ni44MjIyVjI3Ljc5OThaTTAgMjUuNjIyMUwwIDQwLjg2NjVDMCA0MS40Njc5IDAuNDg3NTEyIDQxLjk1NTQgMS4wODg4OSA0MS45NTU0SDQ3LjkxMTFDNDguNTEyNSA0MS45NTU0IDQ5IDQxLjQ2NzkgNDkgNDAuODY2NVYyNS42MjIxSDBaIiBmaWxsPSIjNjI3MjdBIi8+CjxwYXRoIGQ9Ik03LjM4NzkgMS4zNTA2NEwwIDI1LjcyMkg0OUw0MS42MTIxIDEuMzUwNjRDNDEuNDcyOSAwLjg5MTU2MiA0MS4wNDk3IDAuNTc3NjM3IDQwLjU3IDAuNTc3NjM3SDguNDI5OTZDNy45NTAyNSAwLjU3NzYzNyA3LjUyNzA2IDAuODkxNTYzIDcuMzg3OSAxLjM1MDY0WiIgZmlsbD0iIzYyNzI3QSIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTAgMjUuNzIySDQ5TDQxLjYxMjEgMS4zNTA2NEM0MS40NzI5IDAuODkxNTYyIDQxLjA0OTcgMC41Nzc2MzcgNDAuNTcgMC41Nzc2MzdIOC40Mjk5NkM3Ljk1MDI1IDAuNTc3NjM3IDcuNTI3MDYgMC44OTE1NjMgNy4zODc5IDEuMzUwNjRMMCAyNS43MjJaTTEuNDY3OSAyNC42MzMxSDQ3LjUzMjFMNDAuNTcgMS42NjY1M0w4LjQyOTk2IDEuNjY2NTNMMS40Njc5IDI0LjYzMzFaIiBmaWxsPSIjMkMzMDMzIi8+CjxwYXRoIGQ9Ik00MS4zNzggMzMuMjQ0N0M0MS4zNzggMzQuNDQ3NCA0MC40MDMgMzUuNDIyNSAzOS4yMDAyIDM1LjQyMjVDMzcuOTk3NSAzNS40MjI1IDM3LjAyMjUgMzQuNDQ3NCAzNy4wMjI1IDMzLjI0NDdDMzcuMDIyNSAzMi4wNDE5IDM3Ljk5NzUgMzEuMDY2OSAzOS4yMDAyIDMxLjA2NjlDNDAuNDAzIDMxLjA2NjkgNDEuMzc4IDMyLjA0MTkgNDEuMzc4IDMzLjI0NDdaIiBmaWxsPSIjNjI3MjdBIi8+Cjwvc3ZnPgo="
                        alt="Hardware glyph">
                      <h3><span>Step 1 â€“ Hardware</span></h3>
                    </div>
                    <div class="run-a-node__Flex-sc-16hodpi-17 ihENAF">
                      <div class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildBox-sc-16hodpi-20 kZIACG iZDRqn">
                        <h4><span>Minimum specs</span></h4>
                        <ul>
                          <li>
                            <p><span>4 - 8Â GB RAM</span></p>
                            <p><button
                                class="NakedButton-sc-1g43w8v-0 run-a-node__ScrollLink-sc-16hodpi-28 dUatah ccjPxH"><span>See
                                  note on staking</span></button></p>
                            <p><button
                                class="NakedButton-sc-1g43w8v-0 run-a-node__ScrollLink-sc-16hodpi-28 dUatah ccjPxH"><span>See
                                  note on Raspberry Pi</span></button></p>
                          </li>
                          <li>
                            <p><span>2 TB SSD</span></p>
                            <p><small><em><span>SSD necessary for required write speeds.</span></em></small></p>
                          </li>
                        </ul>
                      </div>
                      <div class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildBox-sc-16hodpi-20 kZIACG iZDRqn">
                        <h4><span>Recommended</span></h4>
                        <ul>
                          <li><span>Intel NUC, 7th gen or higher</span>
                            <p><small><span>x86 processor</span></small></p>
                          </li>
                          <li><span>Wired internet connection</span>
                            <p><small><span>Not required, but provides easier setup and most consistent
                                  connection</span></small></p>
                          </li>
                          <li><span>Display screen and keyboard</span>
                            <p><small><span>Unless you're using DAppNode, or ssh/headless setup</span></small></p>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div
                    class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildContainer-sc-16hodpi-29 kZIACG bjnfkk">
                    <div class="run-a-node__SvgTitle-sc-16hodpi-23 jyUDtB"><img
                        src="data:image/svg+xml;base64,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"
                        alt="Software download glyph">
                      <h3><span>Step 2 â€“ Software</span></h3>
                    </div>
                    <div class="run-a-node__Flex-sc-16hodpi-17 ihENAF">
                      <div
                        class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildBox-sc-16hodpi-20 run-a-node__BuildBoxSpace-sc-16hodpi-21 kZIACG iZDRqn ldqQeS">
                        <div>
                          <h4><span>Option 1 â€“ DAppNode</span></h4>
                          <p><span>When you're ready with your hardware, the DAppNode operating system can be downloaded
                              using any computer and installed onto a fresh SSD via a USB drive.</span></p>
                        </div>
                        <div class="run-a-node__ButtonContainer-sc-16hodpi-24 kiQPBL"><a
                            class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv run-a-node__ResponsiveButtonLink-sc-16hodpi-7 run-a-node__DappNodeButtonLink-sc-16hodpi-25 gAaFsL ixhdyo"
                            href="https://docs.dappnode.io" target="_blank" rel="noopener noreferrer"><span>DAppNode
                              Setup</span></a></div>
                      </div>
                      <div
                        class="run-a-node__Container-sc-16hodpi-19 run-a-node__BuildBox-sc-16hodpi-20 run-a-node__BuildBoxSpace-sc-16hodpi-21 kZIACG iZDRqn ldqQeS">
                        <div>
                          <h4><span>Option 2 â€“ Command line</span></h4>
                          <p><span>For maximum control, experienced users may prefer using the command line
                              instead.</span></p>
                          <p><span>See our developer docs for more information on getting started with client
                              selection.</span></p>
                        </div>
                        <div class="run-a-node__ButtonContainer-sc-16hodpi-24 kiQPBL"><a
                            class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc run-a-node__ResponsiveButtonLink-sc-16hodpi-7 gAaFsL"
                            href="/en/developers/docs/nodes-and-clients/run-a-node/#spinning-up-node"><code><span>Command line setup</span></code></a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="run-a-node__SplitContent-sc-16hodpi-5 hOYlSw">
                    <div class="run-a-node__Column-sc-16hodpi-6 jFregN">
                      <h2><span>Find some helpers</span></h2>
                      <p><span>Online platforms such as Discord or Reddit are home to a large number of community
                          builders willing to help you with any questions you may encounter.</span></p>
                      <p><span>Don't go at it alone. If you have a question it's likely someone here can help you find
                          an answer.</span></p>
                      <div class="run-a-node__ButtonContainer-sc-16hodpi-24 kiQPBL"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv run-a-node__ResponsiveButtonLink-sc-16hodpi-7 gAaFsL"
                          href="https://discord.gg/c28an8dA5k" target="_blank" rel="noopener noreferrer"><svg
                            stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 640 512"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 run-a-node__DiscordIcon-sc-16hodpi-31 iylOGp bqXgMp"
                            height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                            </path>
                          </svg><span>Join the DAppNode Discord</span></a><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc run-a-node__ResponsiveButtonLink-sc-16hodpi-7 gAaFsL"
                          href="..\community\online\index.html"><span>Find online communities</span></a></div>
                    </div>
                    <div class="run-a-node__Column-sc-16hodpi-6 jFregN">
                      <div data-gatsby-image-wrapper="" class="gatsby-image-wrapper gatsby-image-wrapper-constrained">
                        <div style="max-width:624px;display:block"><img alt="" role="presentation" aria-hidden="true"
                            src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;428&#x27; width=&#x27;624&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                            style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAAEHElEQVQ4yy1UWU8bBxAeEzWqqqgSb1WT/oI+tGqVqu1bxUufIyVV8lC1Uqv2IagVLUJpSERCRJICoUm1uBhICHZMgvGu1zbGYGwC5tjYhnDalMMmvtfHetdrr4+1PcVJR/qkGY30aeabA+DYfr24BaO9Aoz2ZEF5I6lQdSUbzn+1BqdgFRARvvli792SXHvt16HrPgB53gA5Gw0+jbHBp6EVSTMNqUka/rfLr8nU98Q6IQx0psA9Jzc4LQVwGHOXXM8lyfIs/dkszYOdFhoQN4G3kJA2kwrfYxJ2RylIGikFN0kB9AzFoGcwBn29rKIRddDfyZ7u70icHRtbbdg/EMBB88OxcAGfT3F/RYMSJOPFE4h+SNCkIk6RsDNCfbz5yPBhitLDMRRwsy8I7df8CoBm0Ook0PSIpvGBLKpH4u9blNzFFUrEeESqMoZcluxLfmp6wIF96PDEwxYKur/VvxMwTIWitOlIc5l8e6Z9HODa7QBc6fTDH9cDjV2qyHfqIc47+UTE4T728w2baN5dzOOqNVvwUAVcnRI0m7YcbBv/Pbn7aBy8I/qPjihrYY+ewRcDuq+9T0mAcWf+hG4pD/3jqXvkkojkQhaH1Qlb0yeOU7456QEzLVbXbFJlYzqPjDFjWNJnQDyMnzR36GCFmDxrnF+v0nsRXHAf3XKFUgA//B6A71uDjSqLGNE6RNQ4ctUJpyg/meH2n43xNuoxJznUXNWll3BSlbjEeuT6FBWvUhJQ06FzHlHCPbmEPl5y1DcAevU5uK8TmpTWIhLWYqXfnK+paB4fT3EVg1XEaU0KZ7WhyqJOwulR/ukrX+YyG80vxML5P/XzCbN1R8RZr4juoJCxv0x9AD/djJ25Ncpv9VsKqLKXKyqTgPefcftqdaA5dFiKM88lVL8Uq3MTKdxdzOB6qMQnElnEbAmNC5niz9pErZcUcGqZZx+Oh09D29/pL6+oBLw+xJdua7O1q/+k8Wp/UlY+ZZMOplg92BZxwxOsbTuPauweV/MEJdRmRHk2LlapCI/3haw8KIj4YCttN9dbBrgAbQSn69BK2K4u1FqH8uXWYanSQuTwxztZmTQn5MAeKx/GBNm9kinPmgJl5+aWbIjmyipfptwT5Ys9Yg7vBNP3lHisb/ONMFxqsr3VNiCo7o4JtUFTBgfpFFosr3Ca9OOdYRbD3gAu+cPo3DhAj3UX/RY37r/YxQkTi3bbPjLM1hQAd1zcJkBLd1wBZxDmVqLgopbPxRjPUGzFRXBuRsmvMcrQkptILy4RpNNLzK7uEKxjldiZ2SH4tRdEnHERKZdbGbYzfSM35t8z9y0A/NIVhQu/xRTLQVlx5PYD5n2Ape3jJ+B5g6IXsLIM+nIcGGQBM8dxJPImV1uH+l2j5IbzbTtQV/A/VqX8NdBegkAAAAAASUVORK5CYII="
                          alt="">
                        <picture>
                          <source type="image/webp"
                            sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                            src="../../static/bf78b49d7e23b88a7eea934225b0cf96/53fa0/enterprise-eth.png"
                            >
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                              sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                              style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                              src="../../static/bf78b49d7e23b88a7eea934225b0cf96/53fa0/enterprise-eth.png"
                              >
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2><span>Further reading</span></h2>
                  <ul>
                    <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://github.com/ethereumbook/ethereumbook/blob/develop/03clients.asciidoc#should-i-run-a-full-node"
                        target="_blank" rel="noopener noreferrer"><span>Mastering Ethereum - Should I Run a Full
                          Node</span></a> <!-- -->-
                      <!-- --> <i><span>Andreas Antonopoulos</span></i>
                    </li>
                    <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://ethereum-on-arm-documentation.readthedocs.io/en/latest/" target="_blank"
                        rel="noopener noreferrer"><span>Ethereum on ARM - Quick Start Guide</span></a></li>
                    <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://vitalik.ca/general/2021/05/23/scaling.html" target="_blank"
                        rel="noopener noreferrer"><span>The Limits to Blockchain Scalability</span></a> <!-- -->-
                      <!-- --> <i><span>Vitalik Buterin</span></i>
                    </li>
                  </ul>
                </div>
                <div class="SharedStyledComponents__Divider-sc-1cr9zfr-1 euABDx"></div>
                <div
                  class="run-a-node__SplitContent-sc-16hodpi-5 run-a-node__StakingCalloutContainer-sc-16hodpi-32 hOYlSw eksTnR">
                  <div class="run-a-node__Column-sc-16hodpi-6 jFregN">
                    <div data-gatsby-image-wrapper=""
                      class="gatsby-image-wrapper gatsby-image-wrapper-constrained run-a-node__Leslie-sc-16hodpi-33 kCVIBG">
                      <div style="max-width:624px;display:block"><img alt="" role="presentation" aria-hidden="true"
                          src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;522&#x27; width=&#x27;624&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                          style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                        data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAARCAYAAADdRIy+AAAACXBIWXMAABYlAAAWJQFJUiTwAAAFAElEQVQ4y0WSaUzTZxzHf4DMaHRm883isiy6GN+4LZlZtrEt23DEOUWnuJnodItM0AkdLYjldBxylaOlLUc5etBSjrYgFHrQ/ulJKbS0pS2n6CbLHJuDaHAqof/f/mUuvnyuz/N5vt8HjqVURrwTfxX2xabuLJYMEClVsqzyriHI4HWxMwUqVrNpHBARTh/PBGzohJUK4ZbHMck7vO9fenGVydmG+Tz4LaUC/k6rgpWrVQBfXiqPePsQHfZ+dHkPV2PHgg6d6Uw692SpwrCe1qC0AUB0GLgEr8FTlmgrRQeDyfmV0exMRti5+X61ZPP9VBYs06tgmUEBX4o9B90W1ybF3ALQ2PJGocOHrQ4vCuweLJBpfIJB+51MfncKo8cEtxMyd3IFvcc9OhPaB4jJN+GLFzCXuwVpz4CUJXX/WdgFjI1ncdXWEalnCuWTM2sy73QoDJaM+7FGY1uJO5L+OqUKKrGmy9egQkfzzdWUK6wDyGS/gIwb/wHpYSB8Ex17tCjuk2PM7GZi7BcRBciV60OnchoxX64N1VtcT1qdk0iv6+aInLOg4/R/NnpDieJ88RXPmYxtmNcUjYyq58Bj2ZcvZF/Pedp4vu5Bndr6QDYxhefzm8gKUT/JGx4jBbYJUuwKIFs38ldOg+raTyXiwlpuT1JipwNWcuu3wZ4z8C2N/RxYJ6WP8vrjsTanFtsdAewIzJJ8nR2lw06UuAOkxB1EypqUUNAe/xyy+sz4eSbnw9i0SoCKtsheqmEXrfJZhtUASnXVo2rNB8hU14Qk7nmyxenFDv8sKaJArU4fNju8G4ZCCipyB540jXgwsVxMu9amhqI2dVRI3AsP6dX/5wfAU+pdtYNdVPiBdeGYH5X+OVI5HsSwEWfIgTVaO9lGwcNQynS9jYqEKR3wUUcjw+c//a4ABOJGaFeKQSCkDEvbNN3drlmUOLzrct8MKXcFyTyphqw3jZMcvQNrCSdZrrVhk9MXfjYZbj88jvmh+OAbX2dBXQ8RLe5XRPTaLdA/rAFIY0kzawesWKYdecq3udf5xFiIb3Fhers6dE2pw5w+I5k5SIRy9GZkmZ0kVdpavc2Np0tbiuqHbRvfTa7pgcC0N2r61nQk7ICEyN2HEvdzWC23W25akKHQYlKzApmqIayxuNe5dhfmGqyYbTCHmEMmLBl2PGZTcwksIWfNM7premGmul3dvW/C54TZhRmAiz9L6cVt8ocycSrymTxLXInwJMD+7WkFnCZ15yAWduhXL7YqlrNuEpih0IfKzaOYpzTgjr3J762uLJYuLt3DdqP27OKMe6/b54yHwyncdy+Wteji8xlnw/rn2GpIr+g7xVdI7gmb6HjhxPUT0TFZu3NYdY9ohZWtJUpHWQ27cb2wjKlc/ufJPqPdcEDW3/nK0r27S9N35h/DERoX+FoCvm+8vNG6nLDA4dS6uEtVslsfM35MrW6VQqdlDlKLixMOJia92qzTQlJxWXypWFS+thaAMY8LFoLjLwemJo6YPK7TcDSVG8Ed0G0K20k0mu2dw8YElY1Ibujpq5RoDbx+K9G8eHdh4vc//+iV6LXF1LpdZTXzlBbbcYXZ+FZYgrz/K8zfCoIjOEmldTQDykSdoLKagNq8tYMwnJAThjylZbiiy2zktQ/pOJ7ZKYc16A+KNeqiLhNBazfoY6plgqjBcSMMGu3gpwqZnw9EOgLeqH8BasAopOWhUqgAAAAASUVORK5CYII="
                        alt="">
                      <picture>
                        <source type="image/webp"
                         sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                          style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                          src="../../static/50d8cb4568b509f5b76f877c89cdee33/4c4ce/upgrade_rhino.png"
                          >
                      </picture><noscript>
                        <picture>
                          <source type="image/webp"
                           sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="lazy"
                            src="../../static/50d8cb4568b509f5b76f877c89cdee33/4c4ce/upgrade_rhino.png"
                            >
                        </picture>
                      </noscript>
                      <script
                        type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                    </div>
                  </div>
                  <div class="run-a-node__Column-sc-16hodpi-6 jFregN">
                    <h2><span>Stake your ETH</span></h2>
                    <p><span>Though not required, with a node up and running you're one step closer to staking your ETH
                        to earn rewards and help contribute to a different component of Ethereum security.</span></p>
                    <div class="run-a-node__ButtonContainer-sc-16hodpi-24 kiQPBL"><a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv run-a-node__ResponsiveButtonLink-sc-16hodpi-7 gAaFsL"
                        href="..\staking\index.html"><span>Stake ETH</span></a></div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h3 id="plan-on-staking"><span size="2" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji-sc-ihpuqw-0 bhqZra run-a-node__StyledEmoji-sc-16hodpi-27 dNxqsR undefined"><img
                        alt="ðŸ¥©" src="https://twemoji.maxcdn.com/2/svg/1f969.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Plan on
                      staking?</span></h3>
                  <p><span>To maximize the efficiency of your validator, a minimum of 16â€¯GB RAM is recommended, but
                      32â€¯GB is better, with a CPU benchmark score of 6667+ on <a href="https://cpubenchmark.net"
                        target="_blank">cpubenchmark.net</a>. It is also recommended that stakers have access to
                      unlimited high-speed internet bandwidth, though this is not an absolute requirement.</span></p>
                  <p><span>EthStaker goes into more detail in this hour long special</span> <!-- -->-
                    <!-- --> <a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://youtu.be/C2wwu1IlhDc"
                      target="_blank" rel="noopener noreferrer"><span>How to shop for Ethereum validator
                        hardware</span></a>
                  </p>
                  <h3 id="rasp-pi"><span size="2" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji-sc-ihpuqw-0 bhqZra run-a-node__StyledEmoji-sc-16hodpi-27 dNxqsR undefined"><img
                        alt="ðŸ¥§" src="https://twemoji.maxcdn.com/2/svg/1f967.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>A note on
                      Raspberry Pi (ARM processor)</span></h3>
                  <p><span>Raspberry Pis are lightweight and affordable computers, but they have limitations that may
                      impact the performance of your node. Though not currently recommended for staking, these can be an
                      excellent and inexpensive option for running a node for personal use, with as little as 4 - 8â€¯GB
                      of RAM.</span></p>
                  <ul>
                    <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://docs.dappnode.io/get-started/installation/arm-hardware/installation"
                        target="_blank" rel="noopener noreferrer"><span>DAppNode on ARM</span></a> <!-- -->-
                      <!-- --> <i><span>See these instructions if you plan on running DAppNode on a Raspberry
                          Pi</span></i>
                    </li>
                    <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://ethereum-on-arm-documentation.readthedocs.io/en/latest" target="_blank"
                        rel="noopener noreferrer"><span>Ethereum on ARM documentation</span></a> <!-- -->-
                      <!-- --> <i><span>Learn how to set up a node via the command line on a Raspberry Pi</span></i>
                    </li>
                    <li><a class="Link__InternalLink-sc-e3riao-1 gCWUlE"
                        href="/en/developers/tutorials/run-node-raspberry-pi"><span>Run a node with Raspberry
                          Pi</span></a> <!-- -->-
                      <!-- --> <i><span>Follow along here if tutorials are your preference</span></i>
                    </li>
                  </ul>
                </div>
                <div class="FeedbackCard__Card-sc-siku0n-0 jetTxG run-a-node__StyledFeedbackCard-sc-16hodpi-34 jJpdFO">
                  <div class="FeedbackCard__Content-sc-siku0n-1 gVbeDO">
                    <h3 class="FeedbackCard__Title-sc-siku0n-2 hgzlkf">Did you find this page helpful?</h3>
                    <div class="FeedbackCard__ButtonContainer-sc-siku0n-3 dFGrDI"><button
                        class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 iuRocQ fMmGqe"><span>Yes</span></button><button
                        class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 gVLXss fMmGqe"><span>No</span></button>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <footer class="Footer__StyledFooter-sc-1to993d-0 gvoBKJ">
          <div class="Footer__FooterTop-sc-1to993d-1 kFKfdz">
            <div class="Footer__LastUpdated-sc-1to993d-2 bWGwos"><span>Website last updated</span>:
              <!-- -->
              <!-- -->December 1, 2020
            </div>
            <div class="Footer__SocialIcons-sc-1to993d-9 kdLbod"><a
                href="https://github.com/ethereum/ethereum-org-website" target="_blank" rel="noopener noreferrer"
                aria-label="GitHub"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 496 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z">
                  </path>
                </svg></a><a href="https://twitter.com/ethdotorg" target="_blank" rel="noopener noreferrer"
                aria-label="Twitter"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 512 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                  </path>
                </svg></a><a href="https://youtube.com/channel/UCNOfzGXD_C9YMYmnefmPH0g" target="_blank"
                rel="noopener noreferrer" aria-label="Youtube"><svg stroke="currentColor" fill="currentColor"
                  stroke-width="0" viewbox="0 0 576 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                  </path>
                </svg></a><a href="https://discord.gg/CetY6Y4" target="_blank" rel="noopener noreferrer"
                aria-label="Discord"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 640 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                  </path>
                </svg></a></div>
          </div>
          <div class="Footer__LinkGrid-sc-1to993d-3 hlbLsM">
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Use Ethereum</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a aria-current="page"
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz active"
                    href="index.html"><span>Run a node</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\staking\index.html"><span>Stake ETH</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Learn</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\history\index.html"><span>History of Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                      fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                      width="12px" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                      </path>
                    </svg></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\web3\index.html"><span>What is Web3?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Developers</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\index.html"><span>Get started</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/developers/docs/"><span>Documentation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\local-environment\index.html"><span>Set up local environment</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Ecosystem</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\community\index.html"><span>Community hub</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\foundation\index.html"><span>Ethereum Foundation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://blog.ethstake.exchange/" target="_blank" rel="noopener noreferrer"><span>Ethereum
                      Foundation Blog</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://esp.ethereum.foundation" target="_blank" rel="noopener noreferrer"><span>Ecosystem
                      Support Program</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/community/grants"><span>Ecosystem Grant Programs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\assets\index.html"><span>Ethereum brand assets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://devcon.org/" target="_blank" rel="noopener noreferrer"><span>Devcon</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Enterprise</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Enterprise</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>About ethstake.exchange</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html"><span>About us</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html#open-jobs"><span>Jobs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\contributing\index.html"><span>Contributing</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\languages\index.html"><span>Language support</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\privacy-policy\index.html"><span>Privacy policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\terms-of-use\index.html"><span>Terms of use</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\cookie-policy\index.html"><span>Cookie policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/cdn-cgi/l/email-protection#93e3e1f6e0e0d3f6e7fbe0e7f2f8f6bdf6ebf0fbf2fdf4f6" target="_blank"
                    rel="noopener noreferrer"><span>Contact</span></a></li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <div id="gatsby-announcer"
      style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0"
      aria-live="assertive" aria-atomic="true"></div>
  </div>
  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let svgIcon = document.getElementsByClassName('Mobile__GlyphButton')[0]
    let menuBtn = document.getElementsByClassName('Mobile__MenuButton')
    let hideMenu = document.getElementsByClassName('Mobile__MobileModal')[0]
    let menuContainer = document.getElementsByClassName('Mobile__MenuContainer')[0]
    let menuUl = document.getElementsByClassName('Mobile__MenuItems')[0]
    let menuBottom = document.getElementsByClassName('Mobile__BottomMenu')[0]
    let searchPage = document.getElementsByClassName('Mobile__MenuContainer')[1]
    let closeBtn = document.getElementsByClassName('Mobile__CloseIconContainer')[0]
    let searchBtn = document.getElementsByClassName('Mobile__BottomItem')[0]
    menuBtn[1].addEventListener('click', function () {
      this.toggleAttribute('open')

      let type = this.hasAttribute('open')
      let path = svgIcon.getElementsByTagName('path')[0]
      let d = path.getAttribute('d')
      path.style='transition: all ease-in 0.25s;'
      console.log(path, d,'aaaa')
      if (type) {
        hideMenu.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuContainer.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        path.setAttribute('d','M 2 19 l 10 -14 l 0 0 l 10 14 M 2 19 l 10 7 M 12 26 l 10 -7 M 2 22 l 10 15 l 0 0 l 9 -15')
        setTimeout(()=>{
          path.setAttribute('d','M 2 13 l 0 -3 l 20 0 l 0 3 M 7 14 l 10 10 M 7 24 l 10 -10 M 2 25 l 0 3 l 20 0 l 0 -3')

        },700)

      } else {
        hideMenu.style = 'transition: all ease-in 0.2s;display: none; opacity: 0;'
        menuContainer.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        path.setAttribute('d','M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0')

      }
       // menuUl.toggleAttribute('class', 'gYetwr')
    })
    menuBtn[0].addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    searchBtn.addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    closeBtn.addEventListener('click', function () {
      console.log('111')
      searchPage.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'

    })
    console.log(menuBtn, '......')
    window.dev = undefined
    if (window.dev === true || !(navigator.doNotTrack === '1' || window.doNotTrack === '1')) {
      window._paq = window._paq || [];



      window._paq.push(['setTrackerUrl', 'https://matomo.ethstake.exchange/matomo.php']);
      window._paq.push(['setSiteId', '4']);
      window._paq.push(['enableHeartBeatTimer']);
      window.start = new Date();

      (function () {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript'; g.async = true; g.defer = true; g.src = 'https://matomo.ethstake.exchange/matomo.js'; s.parentNode.insertBefore(g, s);
      })();

      if (window.dev === true) {
        console.debug('[Matomo] Tracking initialized')
        console.debug('[Matomo] matomoUrl: https://matomo.ethstake.exchange, siteId: 4')
      }
    }
  </script><noscript><img
      src="https://matomo.ethstake.exchange/matomo.php?idsite=4&rec=1&url=https://ethstake.exchange/en/run-a-node/"
      style="border:0" alt="tracker"></noscript>
  <script
    id="gatsby-script-loader">/*<![CDATA[*/window.pagePath = "/en/run-a-node/"; window.___webpackCompilationHash = "0375e64c51e377521456";/*]]>*/</script>
  <script
    id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping = { "polyfill": ["/polyfill-b6350ac254e29f22a1e4.js"], "app": ["/app-b670b5ed3a389af0ed04.js"], "component---src-pages-404-js": ["/component---src-pages-404-js-a6aee0605f3068868f92.js"], "component---src-pages-assets-js": ["/component---src-pages-assets-js-ba78d988431646b4760b.js"], "component---src-pages-community-js": ["/component---src-pages-community-js-5ca1d5f82d581db39798.js"], "component---src-pages-conditional-dapps-js": ["/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js"], "component---src-pages-conditional-eth-js": ["/component---src-pages-conditional-eth-js-21644356026cce06349e.js"], "component---src-pages-conditional-wallets-index-js": ["/component---src-pages-conditional-wallets-index-js-6c55787f35fe5cc07ef3.js"], "component---src-pages-conditional-what-is-ethereum-js": ["/component---src-pages-conditional-what-is-ethereum-js-144e7cb6cba17bff7608.js"], "component---src-pages-contributing-translation-program-acknowledgements-js": ["/component---src-pages-contributing-translation-program-acknowledgements-js-a596bd2823410bf53c11.js"], "component---src-pages-contributing-translation-program-contributors-js": ["/component---src-pages-contributing-translation-program-contributors-js-4f2a18f6f82d2a84de27.js"], "component---src-pages-developers-index-js": ["/component---src-pages-developers-index-js-eba978370f325b125b03.js"], "component---src-pages-developers-learning-tools-js": ["/component---src-pages-developers-learning-tools-js-88bd1bcad31958f723e3.js"], "component---src-pages-developers-local-environment-js": ["/component---src-pages-developers-local-environment-js-768783f49122fff8d82f.js"], "component---src-pages-developers-tutorials-js": ["/component---src-pages-developers-tutorials-js-f82f9627cb9b2ce3318b.js"], "component---src-pages-get-eth-js": ["/component---src-pages-get-eth-js-e6c689f28770388e40be.js"], "component---src-pages-index-js": ["/component---src-pages-index-js-c7245d4f213dfe2095c4.js"], "component---src-pages-languages-js": ["/component---src-pages-languages-js-b1a3a1c01ec6bdcd87ac.js"], "component---src-pages-run-a-node-js": ["/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js"], "component---src-pages-stablecoins-js": ["/component---src-pages-stablecoins-js-4bc5b567f2c38baaaa5b.js"], "component---src-pages-stakenow-js": ["/component---src-pages-stakenow-js-40c51639947629777abf.js"], "component---src-pages-staking-deposit-contract-js": ["/component---src-pages-staking-deposit-contract-js-ac8273bd9f4711b38540.js"], "component---src-pages-staking-index-js": ["/component---src-pages-staking-index-js-3d50b1ef7b3b9814f6e2.js"], "component---src-pages-studio-js": ["/component---src-pages-studio-js-c16fabe2f5808fafce99.js"], "component---src-pages-upgrades-get-involved-bug-bounty-js": ["/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js"], "component---src-pages-upgrades-get-involved-index-js": ["/component---src-pages-upgrades-get-involved-index-js-207fa70ee7aa5720b909.js"], "component---src-pages-upgrades-index-js": ["/component---src-pages-upgrades-index-js-dd49283310be18b0a199.js"], "component---src-pages-upgrades-vision-js": ["/component---src-pages-upgrades-vision-js-2c86e72cede9c6155cbf.js"], "component---src-pages-wallets-find-wallet-js": ["/component---src-pages-wallets-find-wallet-js-97c53af70032ab1edbc4.js"], "component---src-templates-static-js": ["/component---src-templates-static-js-3cd4030c1e191ee95c2b.js"], "component---src-templates-upgrade-js": ["/component---src-templates-upgrade-js-700f5089c86b74f8484f.js"], "component---src-templates-use-cases-js": ["/component---src-templates-use-cases-js-75c0d7fec84444cc8c68.js"] };/*]]>*/</script>
  <script src="/polyfill-b6350ac254e29f22a1e4.js" nomodule=""></script>
  <script src="/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js" async=""></script>
  <script src="/app-b670b5ed3a389af0ed04.js" async=""></script>
  <script src="/0f1ac474-e8f788f62189f421a856.js" async=""></script>
  <script src="/0c428ae2-2128ff22fce458b543bd.js" async=""></script>
  <script src="/1bfc9850-0f18e2d74feedfc6e426.js" async=""></script>
  <script src="/ae51ba48-34d54094a2c04f215fb8.js" async=""></script>
  <script src="/252f366e-2705b607be296edabcea.js" async=""></script>
  <script src="/framework-4e285adfb333f1b50c05.js" async=""></script>
  <script src="/webpack-runtime-d600da28e471609bf3f3.js" async=""></script>
</body>

</html>