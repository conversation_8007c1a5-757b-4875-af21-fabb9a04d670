<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

/**
 * 清理过期nonce的定时任务
 * 
 * 使用方法：
 * php think clean:nonces
 */
class CleanExpiredNonces extends Command
{
    protected function configure()
    {
        $this->setName('clean:nonces')
            ->setDescription('清理过期的Web3钱包nonce记录');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始清理过期的nonce记录...');

        try {
            // 删除过期的nonce记录
            $deletedCount = Db::table('wallet_nonces')
                ->where('expire_time', '<', time())
                ->delete();

            $output->writeln("成功清理 {$deletedCount} 条过期记录");

            // 清理超过7天的登录日志（可选）
            $logDeletedCount = Db::table('web3_login_logs')
                ->where('create_time', '<', time() - 7 * 24 * 3600)
                ->delete();

            $output->writeln("成功清理 {$logDeletedCount} 条过期登录日志");

            return 0; // 成功

        } catch (\Exception $e) {
            $output->writeln('清理失败: ' . $e->getMessage());
            return 1; // 失败
        }
    }
}
