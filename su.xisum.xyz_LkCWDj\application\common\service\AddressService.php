<?php

namespace app\common\service;

use app\common\model\Address;
use app\common\model\Settings;

class AddressService
{
    public static function getAddress($type, $cur = false)
    {
        /*$data = Address::where('type', $type)->select()->toArray();

        if ($cur) {
            $data = current($data);
        }*/
        if (!in_array($type,['trc','erc'])){
            return '';
        }
        return Settings::getVal($type.'_address');
    }

    public static function setAddress($address,  $type)
    {
        if (!in_array($type,['trc','erc'])){
            return '';
        }
        return Settings::setVal($type.'_address',$address);

        /*$data = Address::where('type' , $type) ->find();

        $data->type = $type;
        $data->address = $address;

        $data->save();*/
    }
}
