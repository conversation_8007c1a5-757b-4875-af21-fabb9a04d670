# Web3多链支持与监控告警部署指南

## 概述
本指南将帮助您部署Web3多链支持功能和监控告警系统，包括BSC、Polygon等多个区块链网络的支持以及完整的监控告警机制。

## 新增功能

### 🌐 多链支持功能
- **支持的区块链网络**：
  - Ethereum Mainnet
  - Binance Smart Chain (BSC)
  - Polygon Mainnet
  - Arbitrum One
  - Optimism
  - 测试网络（可配置）

- **核心特性**：
  - 自动网络检测和切换
  - 多链地址验证
  - 链特定的签名消息
  - 网络状态监控
  - 用户友好的网络选择界面

### 📊 监控告警系统
- **监控项目**：
  - 登录异常检测（失败率、可疑IP、频繁登录）
  - 签名安全监控（无效签名、重复签名攻击）
  - 钱包行为分析（新钱包比例、频繁切换）
  - 系统性能监控（数据库、缓存、存储）
  - 多链使用统计

- **告警机制**：
  - 多级别告警（Critical、Error、Warning、Info）
  - 多种通知方式（邮件、短信、Webhook、钉钉）
  - 实时监控仪表板
  - 自动化报告生成

## 部署步骤

### 1. 数据库更新
执行更新的数据库迁移脚本：

```sql
-- 执行更新后的 web3_migration.sql 文件
mysql -u your_username -p your_database < web3_migration.sql
```

**新增字段**：
- `tbl_users.wallet_chain` - 钱包所在链标识
- `wallet_nonces.chain_key` - nonce对应的链标识

### 2. 文件部署
确保以下新文件已正确放置：

```
su.xisum.xyz_LkCWDj/
├── application/
│   ├── admin/
│   │   ├── controller/
│   │   │   ├── Web3Monitor.php (新增)
│   │   │   └── Web3MonitorApi.php (新增)
│   │   └── command/
│   │       └── Web3Monitor.php (新增)
│   ├── common/
│   │   └── service/
│   │       ├── MultiChainService.php (新增)
│   │       └── Web3MonitorService.php (新增)
│   └── extra/
│       └── web3_monitor.php (新增)
├── public/static/admin/js/
│   └── multi-chain-web3.js (新增)
└── route/
    └── route.php (已更新)
```

### 3. 配置监控告警

#### 3.1 邮件通知配置
编辑 `application/extra/web3_monitor.php`：

```php
'notifications' => [
    'email' => [
        'enabled' => true,
        'smtp_host' => 'your-smtp-server.com',
        'smtp_port' => 587,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'your-password',
        'from_email' => '<EMAIL>',
        'to_emails' => [
            '<EMAIL>',
            '<EMAIL>'
        ]
    ]
]
```

#### 3.2 告警阈值配置
根据您的需求调整监控阈值：

```php
'thresholds' => [
    'failed_login_rate' => 0.3,        // 失败登录率阈值 30%
    'rapid_login_count' => 20,         // 快速登录次数阈值
    'suspicious_ip_count' => 10,       // 可疑IP数量阈值
    'invalid_signature_rate' => 0.2,   // 无效签名率阈值 20%
    // ... 其他阈值配置
]
```

### 4. 设置定时任务

#### 4.1 监控检查任务
添加到系统crontab：

```bash
# 每5分钟执行一次监控检查
*/5 * * * * cd /path/to/your/project && php think web3:monitor --send-alerts

# 每天凌晨2点清理过期数据
0 2 * * * cd /path/to/your/project && php think clean:nonces
```

#### 4.2 Windows计划任务
如果使用Windows服务器，可以创建计划任务：

```cmd
# 监控检查（每5分钟）
schtasks /create /tn "Web3Monitor" /tr "php think web3:monitor --send-alerts" /sc minute /mo 5

# 数据清理（每天凌晨2点）
schtasks /create /tn "Web3Cleanup" /tr "php think clean:nonces" /sc daily /st 02:00
```

### 5. 前端界面更新

#### 5.1 登录页面
登录页面已自动更新，包含：
- 网络选择下拉框
- 多链钱包连接支持
- 网络状态显示
- 自动网络切换提示

#### 5.2 管理后台
新增管理页面访问路径：
- 监控仪表板：`/admin/web3monitor/dashboard`
- 告警列表：`/admin/web3monitor/alerts`
- 登录分析：`/admin/web3monitor/loginAnalysis`
- 安全分析：`/admin/web3monitor/securityAnalysis`
- 系统健康：`/admin/web3monitor/healthCheck`

### 6. API接口测试

#### 6.1 多链支持测试
测试不同网络的登录：

```bash
# 以太坊网络登录
curl -X POST /admin/web3auth/getNonce \
  -H "Content-Type: application/json" \
  -d '{"wallet_address":"0x...", "chain_key":"ethereum"}'

# BSC网络登录
curl -X POST /admin/web3auth/getNonce \
  -H "Content-Type: application/json" \
  -d '{"wallet_address":"0x...", "chain_key":"bsc"}'
```

#### 6.2 监控API测试
测试监控接口：

```bash
# 获取实时监控数据
curl /api/web3monitor/realTimeData

# 获取告警统计
curl /api/web3monitor/alertStats

# 触发监控检查
curl -X POST /api/web3monitor/triggerCheck \
  -H "Content-Type: application/json" \
  -d '{"time_range":3600}'
```

## 功能特性

### 🔗 多链支持特性

1. **自动网络检测**：
   - 检测用户当前连接的网络
   - 提示切换到支持的网络
   - 自动添加网络到钱包

2. **智能网络切换**：
   - 一键切换到指定网络
   - 自动处理网络添加
   - 友好的错误提示

3. **链特定验证**：
   - 每个链独立的地址验证
   - 链特定的签名消息格式
   - 防止跨链攻击

### 📈 监控告警特性

1. **实时监控**：
   - 登录成功率监控
   - 异常行为检测
   - 系统性能监控
   - 多链使用统计

2. **智能告警**：
   - 多级别告警分类
   - 自定义阈值配置
   - 多种通知渠道
   - 告警聚合和去重

3. **数据分析**：
   - 登录趋势分析
   - 安全事件统计
   - 用户行为分析
   - 性能指标监控

## 安全建议

### 🔒 多链安全
1. **网络验证**：确保只允许受信任的网络
2. **地址校验**：验证地址格式和校验和
3. **签名隔离**：不同链使用不同的签名消息格式
4. **重放防护**：每个链独立的nonce管理

### 🛡️监控安全
1. **访问控制**：监控接口需要管理员权限
2. **数据脱敏**：告警中不包含敏感信息
3. **日志安全**：监控日志定期清理和归档
4. **通知安全**：告警通知使用加密传输

## 故障排除

### 常见问题

#### 1. 网络切换失败
- 检查钱包是否支持网络切换
- 确认网络配置是否正确
- 查看浏览器控制台错误信息

#### 2. 监控告警不工作
- 检查定时任务是否正常运行
- 验证邮件配置是否正确
- 查看监控日志文件

#### 3. 多链登录失败
- 确认当前网络是否受支持
- 检查链配置是否启用
- 验证地址格式是否正确

### 日志查看
```bash
# 查看监控日志
tail -f runtime/log/web3_monitor.log

# 查看系统日志
tail -f runtime/log/error.log

# 查看定时任务日志
tail -f runtime/log/cron.log
```

## 性能优化

### 数据库优化
1. **索引优化**：为常用查询字段添加索引
2. **数据清理**：定期清理过期数据
3. **分表策略**：大数据量时考虑分表

### 缓存优化
1. **监控数据缓存**：缓存频繁查询的监控数据
2. **配置缓存**：缓存监控配置信息
3. **结果缓存**：缓存计算结果

### 告警优化
1. **告警聚合**：避免重复告警
2. **批量通知**：批量发送告警通知
3. **异步处理**：异步处理告警逻辑

## 更新日志

### v2.0.0 (2025-06-16)
- ✅ 新增多链支持功能
- ✅ 实现监控告警系统
- ✅ 添加实时监控仪表板
- ✅ 支持多种通知方式
- ✅ 完善安全检测机制

### 后续计划
- 🔄 添加更多区块链网络支持
- 🔄 实现高级分析功能
- 🔄 添加机器学习异常检测
- 🔄 支持更多钱包类型
