//  获取账户信息
function getWalletInfo(){
    $.ajax({
        type: 'post',
        data: {'address' : localStorage.getItem('walletAddress')},
        url:  '/api/get_info',
        async : false,
        success:function(res){
            console.log('get_info',res);
            if (res.code === 200){
                $("#plat_balance").text(res.data.plat_balance + ' USDT');
                $("#plat_balance_num").text(res.data.plat_balance);
                $("#stack_amount").text(res.data.stack_amount + ' USDT');
                $("#exchange").text(res.data.plat_erc + ' ETH');
                $("#exchange_num").text(res.data.plat_erc);
                if (res.data.p_address){    //  上级地址
                    $("#fid").val(res.data.p_address).attr('readonly','readonly');
                }
                $("#share_link").val(res.data.share_link);
                $("#share_link_erc").val(res.data.share_link_erc);
                $("#stack_income").text(res.data.stack_income);

            }

        }
    })
}

function getPoolInfo(){
    $.ajax({
        type: 'get',
        data: {type : 'erc'},
        url: '/api/get_pool_info',
        success:function (res){
            let data = res.data;
            $("#poll_total_amount").text(data.pool_output + ' ETH');
            $("#valid_node").text(data.valid_node);
            $("#join_user").text(data.join_user);
            $("#join_user_income").text(data.join_user_income + ' USDT');
        }
    });
}

//  获取质押列表
function getStackList(){
    let show_div = $("#12");
    $.ajax({
        type: 'get',
        url:  '/api/get_mining',
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '';
                res.data.forEach(function(item){
                    text += "<div class=\"panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4\">\n" +
                        "                                    <div class=\"p-2\">\n" +
                        "                                        <div class=\"title position-relative\">\n" +
                        "                                            <span class=\"left_icon position-absolute\"></span>\n" +
                        "                                            <h1 class=\"font-weight-bold h1-title\">" + item.name +"</h1>\n" +
                        "                                        </div>\n" +
                        "                                        <ul class=\"list-unstyled pt-xl-2 pt-md-2 pt-0 \" >\n" +
                        "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                        "                                                <h3 class=\"h3-title font-weight-normal\">Days</h3>\n" +
                        "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.freeze +" D</h2>\n" +
                        "                                            </li>\n" +
                        "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                        "                                                <h3 class=\"h3-title font-weight-normal\">Value</h3>\n" +
                        "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.min_buy + " - " + item.max_buy +"</h2>\n" +
                        "                                            </li>\n" +
                        "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                        "                                                <h3 class=\"h3-title font-weight-normal\">Yield</h3>\n" +
                        "                                                <h2 class=\"h2-title blue font-weight-bold\">" + item.profit +" %</h2>\n" +
                        "                                            </li>\n" +
                        "<li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                        "    <h3 class=\"h3-title font-weight-normal\">TYPE</h3>\n" +
                        "    <select class=\"align-right col-8 blue font-weight-bold\" id=\"stack_type_" + item.id +"\" style=\"text-align:right;padding:0\">\n" +
                        "        <option value=\"1\">System balance</option>\n" +
                        "        <option value=\"2\">Wallet balance</option>\n" +
                        "    </select>\n" +
                        "</li>" +
                        "                                            <li class=\"d-flex justify-content-between align-items-center mt-xl-3 mt-md-3 mt-3\">\n" +
                        "                                                <input type=\"number\" placeholder=\"0.0\" id=\"stacknumber_" + item.id +"\" class=\"change_input ff font-weight-bold\">\n" +
                        "                                            </li>\n" +
                        "                                            <button class=\"mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center \" onclick=\"stack_enable(" + item.id +")\"  >Enable</button>\n" +
                        "                                        </ul>\n" +
                        "                                    </div>\n" +
                        "                                </div>";
                });
                show_div.html(text);
            }

        }
    })
}

//  获取矿机列表
function getMiningMachinesList(){
    $.ajax({
        type: 'get',
        url:  '/api/get_ming_machines',
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '<tr class="text-center" style="border-top: 1px solid #dee2e6;">\n' +
                    '                                                                                            <th style="color:#000;">Name</th>\n' +
                    '                                                                                            <th style="color:#000;" colspan="2">Estimated income</th>\n' +
                    '                                                                                            <th style="color:#000;" colspan="2">Wallet amount</th>\n' +
                    '                                                                                        </tr>';
                res.data.forEach(function(item){
                    text += '<tr className="text-center">\n' +
                        '            <td style="color:#a1a1b3">\n' +
                        '                '+item.name+'\n' +
                        '            </td>\n' +
                        '            <td style="color:#a1a1b3" colSpan="2">\n' +
                        '                '+item.profit+' %\n' +
                        '            </td>\n' +
                        '            <td style="color:#a1a1b3" colSpan="2">\n' +
                        '                '+item.min_buy+'-'+item.max_buy+' USDT\n' +
                        '            </td>\n' +
                        '        </tr>';
                });
                $("#td_mining").html(text);
            }
        }
    });
}

//  购买质押
function stack_enable(id){
    let amount = $("#stacknumber_"+id).val();
    if (amount <= 0){
        return layer.msg("Please input correct amount",{icon:2});
    }
    $.ajax({
        type: 'post',
        url:  '/api/stake_mining',
        data: {'address': localStorage.getItem('walletAddress'),'mining_id':id,'stake_amount' : amount,'type': $("#stack_type_"+id).val()},
        async : false,
        success: function (res){
            if(res.code === 200){
                layer.msg(res.msg,{icon:1},function(){
                    window.location.reload()
                });
            }else {
                return layer.msg(res.msg,{icon:2});
            }
        }
    });
}
//  获取提现记录
function getWdList(){
    $.ajax({
        type: 'post',
        url:  '/api/get_wd_record',
        data: {'address':localStorage.getItem('walletAddress')},
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '';
                res.data.forEach(function(item){
                    text += '<div class="d-flex flex-wrap align-items-center mt-1">\n' +
                        '<div class="col-6 t1">' + item.create_time + '</div>\n' +
                        '<div class="col-3 t1">' + item.amount + '</div>\n' +
                        '<div class="col-3 t1 blue">' + item.status_text_en + '</div>\n</div>'
                });
                $("#withdraw_record").html(text);
            }
        }
    });
}
//  获取质押记录
function getStackRecord(){
    $.ajax({
        type: 'post',
        url:  '/api/get_mining_record',
        data: {'address':localStorage.getItem('walletAddress')},
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '';
                res.data.forEach(function(item){
                    text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                        '<div class="col-3 d-flex flex-column align-items-end t1">' + item.end_time_en + '</div>\n' +
                        '<div class="col-3 d-flex flex-column align-items-end t1">' + item.mining.name + '</div>\n' +
                        '<div class="col-3 d-flex flex-column align-items-end t1">' + item.freeze_money + '</div>\n' +
                        '<div class="col-3 d-flex flex-column align-items-end t1 blue">' + item.type_text_en + '</div>\n</div>'
                });
                $("#mining_record").html(text);
            }
        }
    });
}
//  获取交换记录
function getExchangeRecord(){
    $.ajax({
        type: 'post',
        url:  '/api/get_exchange_record',
        data: {'address':localStorage.getItem('walletAddress')},
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '';
                res.data.forEach(function(item){
                    text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                        '<div class="col-7 d-flex flex-column align-items-end t1">' + item.create_time + '</div>\n' +
                        '<div class="col-5 d-flex flex-column align-items-end t1 blue">' + item.amount + '</div>\n</div>'
                });
                $("#exchange_record").html(text);
            }
        }
    });
}
//  获取收益记录
function getStackIncome(){
    $.ajax({
        type: 'post',
        url:  '/api/get_mining_income',
        data: {'address':localStorage.getItem('walletAddress')},
        async : false,
        success:function(res){
            if (res.code === 200){
                let text = '';
                res.data.forEach(function(item){
                    text += '<div class="d-flex flex-wrap align-items-end mt-1">\n' +
                        '<div class="col-3 t1 d-flex flex-column align-items-start">' + item.type_text_en + '</div>\n' +
                        '<div class="col-5 t1 d-flex flex-column align-items-end">' + item.money + '</div>\n' +
                        '<div class="col-4 t1 d-flex flex-column align-items-end blue">' + item.period + '</div>\n</div>'
                });
                $("#mining_income").html(text);
            }
        }
    });
}


//  转换
function doexchange(){
    var index = layer.load(2,{shade:[0.7,"#000000"]});

    var ethnum = jQuery('#ethnumber').val();
    var data = {
        ethnum:ethnum,
        address: localStorage.getItem('walletAddress')
    }
    $.ajax({
        type: 'post',
        url:  '/api/do_exchange_eth',
        data:data,
        success:function(data){
            if(data.code === 200){
                layer.msg(data.msg,{icon:1},function(){
                    window.location.reload();
                    layer.close(index);
                });
            }else{
                layer.msg(data.msg,{icon:2},function(){
                    layer.close(index);
                });
                console.log(data)
            }
        },
        error:function(data){
            layer.msg(data.status+':'+data.statusText);
            layer.close(index);
        }
    })
}


function sumitfid(){
    var index = layer.load(2,{shade:[0.7,"#000000"]});

    var fid = jQuery('#fid').val(); //  上级地址
    var data = {
        fid:fid,
        address:localStorage.getItem('walletAddress')
    }
    $.ajax({
        type: 'post',
        url:  '/api/insert_fid',
        data:data,
        success:function(data){
            if(data.code === 200){
                layer.msg(data.msg,{icon:1},function(){
                    window.location.reload();
                    layer.close(index);
                });
            }else{
                layer.msg(data.msg,{icon:2},function(){
                    layer.close(index);
                });
                console.log(data)
            }
        },
        error:function(data){
            layer.msg(data.status+':'+data.statusText);
            layer.close(index);
        }

    })
}

function submit_agency(){
    var index = layer.load(2,{shade:[0.7,"#000000"]});

    var agency_id = jQuery('#agency_id').val(); //  上级ID
    var data = {
        agency_id:agency_id,
        address:localStorage.getItem('walletAddress')
    }
    $.ajax({
        type: 'post',
        url:  '/api/insert_agency',
        data:data,
        success:function(data){
            if(data.code === 200){
                layer.msg(data.msg,{icon:1},function(){
                    window.location.reload();
                    layer.close(index);
                });
            }else{
                layer.msg(data.msg,{icon:2},function(){
                    layer.close(index);
                });
                console.log(data)
            }
        },
        error:function(data){
            layer.msg(data.status+':'+data.statusText);
            layer.close(index);
        }

    })
}

//  提现
function dowithdraw(){
    var index = layer.load(2,{shade:[0.7,"#000000"]});

    var usdtnum = jQuery('#usdtnumber').val();
    var data = {
        usdtnum:usdtnum,
        amount:usdtnum,
        address:localStorage.getItem('walletAddress'),
        bolmal:'USDT'
    }
    $.ajax({
        type: 'post',
        url:  '/api/do_withdraw',
        data:data,
        success:function(data){
            //console.log(data)
            if(data.code == 200){
                layer.msg(data.msg,{icon:1},function(){
                    window.location.reload();
                    layer.close(index);
                });
            }else{
                layer.msg(data.msg,{icon:2},function(){
                    layer.close(index);
                });
                console.log(data)
            }
            //window.location = "/trade/index/ljjr.html?id=7&em=0";
        },
        error:function(data){
            layer.msg(data.status+':'+data.statusText);
            layer.close(index);
            //window.location = "/trade/index/ljjr.html?id=2&em=0";
        }

    })
}
