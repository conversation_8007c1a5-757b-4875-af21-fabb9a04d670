(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{35553:function(e,t,s){"use strict";s.d(t,{dF:function(){return k},bM:function(){return w},vz:function(){return y}});var a=s(16441),r=s(1581),n=s(48794),i=s(2593);const l=new r.Yd(n.i),o={},c=i.O$.from(0),d=i.O$.from(-1);function m(e,t,s,a){const n={fault:t,operation:s};return void 0!==a&&(n.value=a),l.throwError(e,r.Yd.errors.NUMERIC_FAULT,n)}let h="0";for(;h.length<256;)h+=h;function u(e){if("number"!==typeof e)try{e=i.O$.from(e).toNumber()}catch(t){}return"number"===typeof e&&e>=0&&e<=256&&!(e%1)?"1"+h.substring(0,e):l.throwArgumentError("invalid decimal size","decimals",e)}function f(e,t){null==t&&(t=0);const s=u(t),a=(e=i.O$.from(e)).lt(c);a&&(e=e.mul(d));let r=e.mod(s).toString();for(;r.length<s.length-1;)r="0"+r;r=r.match(/^([0-9]*[1-9]|0)(0*)/)[1];const n=e.div(s).toString();return e=1===s.length?n:n+"."+r,a&&(e="-"+e),e}function x(e,t){null==t&&(t=0);const s=u(t);"string"===typeof e&&e.match(/^-?[0-9.]+$/)||l.throwArgumentError("invalid decimal value","value",e);const a="-"===e.substring(0,1);a&&(e=e.substring(1)),"."===e&&l.throwArgumentError("missing value","value",e);const r=e.split(".");r.length>2&&l.throwArgumentError("too many decimal points","value",e);let n=r[0],o=r[1];for(n||(n="0"),o||(o="0");"0"===o[o.length-1];)o=o.substring(0,o.length-1);for(o.length>s.length-1&&m("fractional component exceeds decimals","underflow","parseFixed"),""===o&&(o="0");o.length<s.length-1;)o+="0";const c=i.O$.from(n),h=i.O$.from(o);let f=c.mul(s).add(h);return a&&(f=f.mul(d)),f}class b{constructor(e,t,s,a){e!==o&&l.throwError("cannot use FixedFormat constructor; use FixedFormat.from",r.Yd.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.signed=t,this.width=s,this.decimals=a,this.name=(t?"":"u")+"fixed"+String(s)+"x"+String(a),this._multiplier=u(a),Object.freeze(this)}static from(e){if(e instanceof b)return e;"number"===typeof e&&(e=`fixed128x${e}`);let t=!0,s=128,a=18;if("string"===typeof e)if("fixed"===e);else if("ufixed"===e)t=!1;else{const r=e.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);r||l.throwArgumentError("invalid fixed format","format",e),t="u"!==r[1],s=parseInt(r[2]),a=parseInt(r[3])}else if(e){const r=(t,s,a)=>null==e[t]?a:(typeof e[t]!==s&&l.throwArgumentError("invalid fixed format ("+t+" not "+s+")","format."+t,e[t]),e[t]);t=r("signed","boolean",t),s=r("width","number",s),a=r("decimals","number",a)}return s%8&&l.throwArgumentError("invalid fixed format width (not byte aligned)","format.width",s),a>80&&l.throwArgumentError("invalid fixed format (decimals too large)","format.decimals",a),new b(o,t,s,a)}}class g{constructor(e,t,s,a){l.checkNew(new.target,g),e!==o&&l.throwError("cannot use FixedNumber constructor; use FixedNumber.from",r.Yd.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.format=a,this._hex=t,this._value=s,this._isFixedNumber=!0,Object.freeze(this)}_checkFormat(e){this.format.name!==e.format.name&&l.throwArgumentError("incompatible format; use fixedNumber.toFormat","other",e)}addUnsafe(e){this._checkFormat(e);const t=x(this._value,this.format.decimals),s=x(e._value,e.format.decimals);return g.fromValue(t.add(s),this.format.decimals,this.format)}subUnsafe(e){this._checkFormat(e);const t=x(this._value,this.format.decimals),s=x(e._value,e.format.decimals);return g.fromValue(t.sub(s),this.format.decimals,this.format)}mulUnsafe(e){this._checkFormat(e);const t=x(this._value,this.format.decimals),s=x(e._value,e.format.decimals);return g.fromValue(t.mul(s).div(this.format._multiplier),this.format.decimals,this.format)}divUnsafe(e){this._checkFormat(e);const t=x(this._value,this.format.decimals),s=x(e._value,e.format.decimals);return g.fromValue(t.mul(this.format._multiplier).div(s),this.format.decimals,this.format)}floor(){const e=this.toString().split(".");1===e.length&&e.push("0");let t=g.from(e[0],this.format);const s=!e[1].match(/^(0*)$/);return this.isNegative()&&s&&(t=t.subUnsafe(p.toFormat(t.format))),t}ceiling(){const e=this.toString().split(".");1===e.length&&e.push("0");let t=g.from(e[0],this.format);const s=!e[1].match(/^(0*)$/);return!this.isNegative()&&s&&(t=t.addUnsafe(p.toFormat(t.format))),t}round(e){null==e&&(e=0);const t=this.toString().split(".");if(1===t.length&&t.push("0"),(e<0||e>80||e%1)&&l.throwArgumentError("invalid decimal count","decimals",e),t[1].length<=e)return this;const s=g.from("1"+h.substring(0,e),this.format),a=j.toFormat(this.format);return this.mulUnsafe(s).addUnsafe(a).floor().divUnsafe(s)}isZero(){return"0.0"===this._value||"0"===this._value}isNegative(){return"-"===this._value[0]}toString(){return this._value}toHexString(e){if(null==e)return this._hex;e%8&&l.throwArgumentError("invalid byte width","width",e);const t=i.O$.from(this._hex).fromTwos(this.format.width).toTwos(e).toHexString();return(0,a.$m)(t,e/8)}toUnsafeFloat(){return parseFloat(this.toString())}toFormat(e){return g.fromString(this._value,e)}static fromValue(e,t,s){return null!=s||null==t||(0,i.Zm)(t)||(s=t,t=null),null==t&&(t=0),null==s&&(s="fixed"),g.fromString(f(e,t),b.from(s))}static fromString(e,t){null==t&&(t="fixed");const s=b.from(t),r=x(e,s.decimals);!s.signed&&r.lt(c)&&m("unsigned value cannot be negative","overflow","value",e);let n=null;s.signed?n=r.toTwos(s.width).toHexString():(n=r.toHexString(),n=(0,a.$m)(n,s.width/8));const i=f(r,s.decimals);return new g(o,n,i,s)}static fromBytes(e,t){null==t&&(t="fixed");const s=b.from(t);if((0,a.lE)(e).length>s.width/8)throw new Error("overflow");let r=i.O$.from(e);s.signed&&(r=r.fromTwos(s.width));const n=r.toTwos((s.signed?0:1)+s.width).toHexString(),l=f(r,s.decimals);return new g(o,n,l,s)}static from(e,t){if("string"===typeof e)return g.fromString(e,t);if((0,a._t)(e))return g.fromBytes(e,t);try{return g.fromValue(e,0,t)}catch(s){if(s.code!==r.Yd.errors.INVALID_ARGUMENT)throw s}return l.throwArgumentError("invalid FixedNumber value","value",e)}static isFixedNumber(e){return!(!e||!e._isFixedNumber)}}const p=g.from(1),j=g.from("0.5"),v=new r.Yd("units/5.6.0"),N=["wei","kwei","mwei","gwei","szabo","finney","ether"];function w(e,t){if("string"===typeof t){const e=N.indexOf(t);-1!==e&&(t=3*e)}return f(e,null!=t?t:18)}function y(e,t){if("string"!==typeof e&&v.throwArgumentError("value must be a string","value",e),"string"===typeof t){const e=N.indexOf(t);-1!==e&&(t=3*e)}return x(e,null!=t?t:18)}function k(e){return w(e,18)}},45301:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return s(5075)}])},5075:function(e,t,s){"use strict";s.r(t);var a=s(34051),r=s.n(a),n=s(85893),i=s(65988),l=s.n(i),o=s(41664),c=s(8100),d=s(1797),m=s(15924),h=s(41435),u=s(33683),f=s(965),x=s(98352),b=(s(30933),s(92),s(92077)),g=s.n(b),p=s(35553),j=s(2593),v=s(67294),N=s(16536),w=s(11382),y=s(9669),k=s.n(y);function E(e,t,s,a,r,n,i){try{var l=e[n](i),o=l.value}catch(c){return void s(c)}l.done?t(o):Promise.resolve(o).then(a,r)}var _=function(){var e,t=(0,h.Z)(),s=(0,c.ZP)((0,u.KV)("/api/v1/packages"));return(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)(l(),{id:"ae94bc47e04fd095",children:"section.jsx-ae94bc47e04fd095{background-image: radial-gradient(circle at 100% -80%, rgb(254, 242, 244), rgb(253, 248, 247), rgb(255, 242, 230), rgb(229, 246, 234), rgb(223, 245, 250), rgb(227, 239, 250), rgb(231, 233, 250))}\nbutton.jsx-ae94bc47e04fd095::after{content:''}\n.swiper-wrapper.jsx-ae94bc47e04fd095{padding:0 2rem}"}),(0,n.jsxs)("section",{className:"jsx-ae94bc47e04fd095 p-8 sm:p-12 flex items-center gap-12 min-h-[calc(100vh-5rem)] lg:min-h-[calc(100vh-16rem)]",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 font-bold mb-8 md:hidden",children:m.Z.name}),(0,n.jsx)("h1",{className:"jsx-ae94bc47e04fd095 text-3xl md:text-5xl font-medium mb-8",children:m.Z.description}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 text-lg mb-8",children:"Earn continuous rewards for providing a public good to the community."}),(0,n.jsx)("img",{src:"/static/leslie-rhino.png",alt:"Leslie Rhino",className:"jsx-ae94bc47e04fd095 block mx-auto mb-12 w-64 md:hidden"}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 flex flex-wrap gap-4",children:(0,n.jsx)("button",{type:"button",onClick:function(){return t.stake()},className:"jsx-ae94bc47e04fd095 btn-primary font-bold",children:t.isAuthorized||t.isRecruiter?"Account":t.account?"Get Staking Certificate":"Connect Wallet"})})]}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 shrink-0 hidden md:block",children:(0,n.jsx)("img",{src:"/static/leslie-rhino.png",alt:"Leslie Rhino",className:"jsx-ae94bc47e04fd095 w-48 lg:w-72 xl:w-96"})})]}),(0,n.jsxs)("div",{id:"StakingOptions",className:"jsx-ae94bc47e04fd095 p-6 md:p-12 py-12 md:py-32 bg-[rgba(231,233,250,0.5)]",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 flex justify-between items-center",children:[(0,n.jsx)("h1",{className:"jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0",children:"Staking Options"}),t.isAuthorized||t.isRecruiter?(0,n.jsx)(o.default,{href:"/account",children:(0,n.jsx)("a",{style:{zoom:.8},className:"jsx-ae94bc47e04fd095 btn-primary font-bold py-2 px-4 leading-none",children:"My Account"})}):null]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 mt-8 -mx-6 md:-mx-8 px-8 relative",children:[(0,n.jsx)(x.tq,{modules:[f.W_],navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},spaceBetween:8,slidesPerView:1,breakpoints:{768:{slidesPerView:2},1280:{slidesPerView:3}},children:null===(e=s.data)||void 0===e?void 0:e.map((function(e,t){return(0,n.jsx)(x.o5,{className:"px-3 py-6",children:(0,n.jsx)(S,{package:e})},"".concat(e.id,"-").concat(t))}))}),(0,n.jsx)("button",{type:"button",className:"jsx-ae94bc47e04fd095 swiper-button-prev bg-[rgb(223,245,250)] shadow rounded transition hover:shadow-lg",children:(0,n.jsx)("img",{src:"/static/chevron-left.png",className:"jsx-ae94bc47e04fd095 h-4"})}),(0,n.jsx)("button",{type:"button",className:"jsx-ae94bc47e04fd095 swiper-button-next bg-[rgb(223,245,250)] shadow rounded transition hover:shadow-lg",children:(0,n.jsx)("img",{src:"/static/chevron-right.png",className:"jsx-ae94bc47e04fd095 h-4"})})]})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 p-6 md:p-12 py-16 md:py-32 bg-[rgb(248,250,249)]",children:[(0,n.jsx)("h1",{className:"jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium m-0",children:"The Beacon Chain"}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 px-4 py-12",children:(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grid lg:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4",children:[(0,n.jsx)("h2",{className:"jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4",children:"Total ETH staked"}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600",children:"11,012,715 ETH"})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4",children:[(0,n.jsx)("h2",{className:"jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4",children:"Total validators"}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600",children:"328,081"})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grow shink-0 bg-white rounded border-gray-800 border px-8 py-4",children:[(0,n.jsx)("h2",{className:"jsx-ae94bc47e04fd095 font-medium text-xl lg:text-2xl mb-4",children:"Highest Return"}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 font-bold text-xl lg:text-2xl text-green-600",children:"4.8%"})]})]})}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 px-4 pb-0 md:pb-12",children:(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grid lg:grid-cols-3 gap-4",children:[(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095"}),(0,n.jsx)("a",{href:"https://mainnet.beaconcha.in/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 btn-secondary font-bold text-center",children:"More Stats"}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095"})]})})]}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 bg-white",children:(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 flex gap-8",children:[(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 shrink-0 hidden md:block",children:(0,n.jsx)("img",{src:"/static/eth-round-landing.svg",alt:"Ethereum",className:"jsx-ae94bc47e04fd095 w-32"})}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 text-lg relative",children:[(0,n.jsx)("img",{src:"/static/eth-diamond-plain.svg",alt:"Ethereum",className:"jsx-ae94bc47e04fd095 md:hidden w-64 max-w-full opacity-20 absolute -top-12 right-0"}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 relative",children:[(0,n.jsx)("h1",{className:"jsx-ae94bc47e04fd095 text-3xl lg:text-4xl font-medium",children:"Validators and Ethereum"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"This launchpad will help you become a validator, so you can play an active part in Ethereum\u2019s future. Validators are key to the more secure, scalable, and sustainable Ethereum we\u2019re building together."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/vision/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the Ethereum Vision"})}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-8",children:"As a validator, you\u2019ll be responsible for securing the network and receive continuous payouts for actions that help the network reach consensus."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Today, you\u2019ll secure the Beacon Chain, the first main scaling upgrade. It\u2019s a separate chain that uses a proof-of-stake consensus mechanism. Eventually you\u2019ll help secure all of Ethereum, once mainnet (the Ethereum we use today) merges with the Beacon Chain."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/beacon-chain/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the Beacon Chain"})}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/merge/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the merge"})}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-8",children:"Validating in Ethereum is not the same as mining. The outcomes are similar: the work you do will extend and secure the chain. But the process is completely different because they use different consensus mechanisms."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/developers/docs/consensus-mechanisms/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on consensus mechanisms"})})]})]})]})}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 bg-gray-50",children:(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 text-lg",children:[(0,n.jsx)("h2",{className:"jsx-ae94bc47e04fd095 text-2xl lg:text-3xl font-medium",children:"Become a validator"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4 font-normal",children:"Becoming a validator is a big responsibility with important preparation steps. Only start the deposit process when you\u2019re ready."}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:p-8 gap-4",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 text-8xl mt-8",children:"\ud83d\udcda"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 font-medium mt-4",children:"1. Learn about your responsibilities"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"The Ethereum upgrades will only be successful if validators understand the risks and responsibilities."})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 text-8xl mt-8",children:"\ud83d\udcb0"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 font-medium mt-4",children:"2. Deposit your ETH"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Once you\u2019re comfortable, you\u2019ll go through generating your keys and depositing your ETH."})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 text-8xl mt-8",children:"\ud83d\udd70"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 font-medium mt-4",children:"3. Wait to become active"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Once set up, your validator won\u2019t become active straight away. Use this time to complete the checklist and learn more about staking."})]})]}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 flex items-center justify-center mt-8",children:(0,n.jsx)("button",{type:"button",onClick:function(){return t.stake()},className:"jsx-ae94bc47e04fd095 btn-primary font-bold md:px-16",children:t.isAuthorized||t.isRecruiter?"Account":t.account?"Get Staking Certificate":"Connect Wallet"})})]})}),(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 bg-[rgb(240,242,251)]",children:(0,n.jsx)("div",{className:"jsx-ae94bc47e04fd095 px-8 md:px-16 py-16 md:py-32 text-lg",children:(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 flex flex-col xl:flex-row gap-8",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095",children:[(0,n.jsx)("h1",{className:"jsx-ae94bc47e04fd095 text-4xl lg:text-5xl font-medium",children:"How is Ethereum scaling?"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Several upgrades are underway that will make Ethereum more scalable, secure, and sustainable. These upgrades will improve Ethereum while seamlessly continuing on the chain of today. Here\u2019s more on the different upgrades:"}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8",children:"Proof-of-stake (PoS) and the Beacon Chain"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"PoS is a more secure, decentralized, and environmentally-friendly consensus mechanism than the proof-of-work (PoW) that secures Ethereum today. It rewards validators for building the chain, but slashes their deposits if they try to attack it, incentivising healthy behaviour. This upgrade is already live in the form of the Beacon Chain."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/beacon-chain/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the Beacon Chain"})}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8",children:"The Merge"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"The merge will see the Ethereum Mainnet we use today merge with the Beacon Chain. This is when Ethereum will fully transition to proof-of-stake."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/merge/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the merge"})}),(0,n.jsx)("h3",{className:"jsx-ae94bc47e04fd095 text-xl lg:text-2xl font-bold mt-8",children:"Sharding"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Sharding will make more data available to the network by introducing 64 parallel chains. Each new chain will be able to handle at least as much data as mainnet today, probably more."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/shard-chains/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"More on the shard chains"})})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 shink-0 xl:w-[600px] flex flex-col gap-4 pt-16 xl:pt-0",children:[(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4",children:[(0,n.jsx)("h4",{className:"jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl",children:"The upgrades"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Dig deeper into Ethereum upgrades."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"How does this all happen?"})})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4",children:[(0,n.jsx)("h4",{className:"jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl",children:"Deposit contract formally verified"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"The deposit contract has been verified at a byte-code level to ensure your safety."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)("a",{href:"https://github.com/runtimeverification/deposit-contract-verification/blob/96434de/deposit-contract-verification.pdf",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-ae94bc47e04fd095 font-medium",children:"Formal verification report"})})]}),(0,n.jsxs)("div",{className:"jsx-ae94bc47e04fd095 bg-[rgb(227,229,242)] p-4",children:[(0,n.jsx)("h4",{className:"jsx-ae94bc47e04fd095 font-medium text-lg md:text-xl",children:"Validators FAQ"}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:"Learn more about the roles and responsibilities of Ethereum validators."}),(0,n.jsx)("p",{className:"jsx-ae94bc47e04fd095 mt-4",children:(0,n.jsx)(o.default,{href:"/faq",children:(0,n.jsx)("a",{className:"jsx-ae94bc47e04fd095 font-medium",children:"More on validators"})})})]})]})]})})})]})},S=function(e){var t=e.package,s=(0,h.Z)(),a=(0,v.useRef)(null),i=(0,c.ZP)(s.account?(0,u.KV)("/api/v1/account/ethereum",{where:{address:s.account}}):null),l=(0,v.useState)(!1),o=l[0],d=l[1],m=function(){var e,l=(e=r().mark((function e(){var l,c,m,h,u,f;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s.provider&&!o){e.next=3;break}return e.abrupt("return");case 3:if(s.account){e.next=5;break}return e.abrupt("return",N.ZP.warn((0,n.jsx)("span",{className:"align-middle font-normal",children:"Please connect your wallet first."})));case 5:if(s.isAuthorized||s.isRecruiter){e.next=7;break}return e.abrupt("return",N.ZP.warn((0,n.jsx)("span",{className:"align-middle font-normal",children:"Please get a Staking Certificate before continue."})));case 7:if((m=null===(l=a.current)||void 0===l?void 0:l.value)&&!isNaN(Number(m))){e.next=10;break}return e.abrupt("return",N.ZP.warn((0,n.jsx)("span",{className:"align-middle font-normal",children:"Please enter a valid amount."})));case 10:if(null===(c=s.wallet)||void 0===c?void 0:c.coldWallet){e.next=12;break}return e.abrupt("return");case 12:if(1===s.provider.network.chainId){e.next=14;break}return e.abrupt("return",N.ZP.warn((0,n.jsx)("span",{className:"align-middle font-normal",children:"Please switch to Ethereum network before continue."})));case 14:if(h=p.vz(m,6),i.data&&!j.O$.from(i.data.spotUsdtBalance).add(null!==(u=i.data.bonus)&&void 0!==u?u:0).lt(h)){e.next=18;break}return e.abrupt("return",N.ZP.warn((0,n.jsx)("span",{className:"align-middle font-normal",children:"Insufficient USDT balance."})));case 18:if(e.prev=18,!h.lt(t.minUsdt)){e.next=21;break}return e.abrupt("return",N.ZP.warn((0,n.jsxs)("span",{className:"align-middle font-normal",children:["USDT amount too low. Must be at least ",g()(Number(p.bM(t.minUsdt,6))).format("0,0")]})));case 21:if(!h.gte(t.maxUsdt)){e.next=23;break}return e.abrupt("return",N.ZP.warn((0,n.jsxs)("span",{className:"align-middle font-normal",children:["USDT amount too big. Must be less than ",g()(Number(p.bM(t.maxUsdt,6))).format("0,0")]})));case 23:return d(!0),e.next=26,k().post("/api/v1/subscription/subscribe/".concat(t.id),{address:s.account,amount:Number(m)});case 26:f=e.sent,console.log("result",f.data),N.ZP.success((0,n.jsx)("span",{className:"align-middle font-normal",children:"Staked successfully."})),a.current.value="",e.next=36;break;case 32:e.prev=32,e.t0=e.catch(18),console.error("error",e.t0),N.ZP.error((0,n.jsx)("span",{className:"align-middle font-normal",children:"Insufficient ETH / USDT"}));case 36:d(!1);case 37:case"end":return e.stop()}}),e,null,[[18,32]])})),function(){var t=this,s=arguments;return new Promise((function(a,r){var n=e.apply(t,s);function i(e){E(n,a,r,i,l,"next",e)}function l(e){E(n,a,r,i,l,"throw",e)}i(void 0)}))});return function(){return l.apply(this,arguments)}}();return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg shadow-black/10 select-none",children:[(0,n.jsxs)("div",{className:"flex items-center p-4",children:[(0,n.jsx)("img",{src:"/static/ethereum.png",alt:"Ethereum",className:"w-8"}),(0,n.jsx)("img",{src:"/static/switch-horizontal.png",className:"h-4 mx-1"}),(0,n.jsx)("img",{src:"/static/usdt.png",alt:"USDT",className:"w-8"}),(0,n.jsxs)("h4",{className:"font-bold text-xl lg:text-2xl ml-2",children:["Ethereum",(0,n.jsx)("small",{className:"font-normal",children:" / USDT"})]})]}),(0,n.jsxs)("p",{className:"px-4 font-medium text-lg lg:text-xl",children:[g()(Number(p.bM(t.minUsdt,6))).format("0,0")," - ",g()(Number(p.bM(t.maxUsdt,6))-1).format("0,0")," USDT"]}),(0,n.jsx)("div",{className:"mt-4 border-t p-4",children:(0,n.jsxs)("ul",{className:"text-base lg:text-lg font-normal",children:[(0,n.jsxs)("li",{className:"leading-loose",children:["\u2705 Staking for ",(0,n.jsxs)("span",{className:"font-bold",children:[t.days," days"]})]}),(0,n.jsxs)("li",{className:"leading-loose",children:["\u2705 Daily Return ",(0,n.jsxs)("span",{className:"font-bold",children:[t.minRate/100,"% - ",t.maxRate/100,"%"]})]}),(0,n.jsxs)("li",{className:"leading-loose",children:["\u2705 Custody Fee ",(0,n.jsxs)("span",{className:"font-bold",children:[t.hostingFee/100,"%"]})]}),(0,n.jsxs)("li",{className:"leading-loose",children:["\u2705 ",t.verifyNode]}),(0,n.jsxs)("li",{className:"leading-loose",children:["\u2705 ",t.validators]})]})}),(0,n.jsxs)("div",{className:"mt-4 border-t p-4",children:[(0,n.jsx)("input",{type:"text",ref:a,className:"focus:outline-none border rounded px-2 py-1 w-full",placeholder:"USDT Amount",onChange:function(e){var t=e.target.value;t=t.replace(/[^0-9]/g,""),e.target.value=t},disabled:o}),(0,n.jsx)("button",{type:"button",className:"font-medium px-4 py-2 bg-rose-500 transition focus:outline-none focus:ring focus:ring-rose-200 text-white w-full mt-4 rounded hover:bg-rose-600",onClick:m,children:o?(0,n.jsx)(w.Z,{size:"small"}):"Stake Now"})]})]})};_.layout=d.Z,t.default=_}},function(e){e.O(0,[411,77,936,794,797,774,888,179],(function(){return t=45301,e(e.s=t);var t}));var t=e.O();_N_E=t}]);