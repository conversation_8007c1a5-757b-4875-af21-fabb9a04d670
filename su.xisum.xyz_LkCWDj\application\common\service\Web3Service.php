<?php

namespace app\common\service;

use think\Db;
use app\common\library\Web3Signature;

/**
 * Web3服务类
 * 处理钱包地址验证、签名验证、nonce管理等功能
 */
class Web3Service
{
    // nonce有效期（30分钟）
    const NONCE_EXPIRE_TIME = 1800;

    /**
     * 验证以太坊地址格式
     * @param string $address
     * @return bool
     */
    public function isValidAddress($address)
    {
        // 以太坊地址格式：0x开头，42位十六进制字符
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }

    /**
     * 生成签名nonce
     * @param string $walletAddress
     * @return string
     */
    public function generateNonce($walletAddress)
    {
        // 清理过期的nonce
        $this->cleanExpiredNonces();

        // 生成32位随机字符串
        $nonce = bin2hex(random_bytes(16));
        $expireTime = time() + self::NONCE_EXPIRE_TIME;

        // 存储nonce
        Db::table('wallet_nonces')->insert([
            'wallet_address' => $walletAddress,
            'nonce' => $nonce,
            'expire_time' => $expireTime,
            'is_used' => 0,
            'create_time' => time()
        ]);

        return $nonce;
    }

    /**
     * 生成签名消息
     * @param string $walletAddress
     * @param string $nonce
     * @return string
     */
    public function generateSignMessage($walletAddress, $nonce)
    {
        $domain = request()->domain();
        $timestamp = time();
        
        $message = "Welcome to {$domain}!\n\n";
        $message .= "Click to sign in and accept the Terms of Service.\n\n";
        $message .= "This request will not trigger a blockchain transaction or cost any gas fees.\n\n";
        $message .= "Wallet address:\n{$walletAddress}\n\n";
        $message .= "Nonce:\n{$nonce}\n\n";
        $message .= "Issued At:\n" . date('Y-m-d H:i:s', $timestamp);

        return $message;
    }

    /**
     * 验证签名
     * @param string $walletAddress
     * @param string $signature
     * @param string $nonce
     * @return bool
     */
    public function verifySignature($walletAddress, $signature, $nonce)
    {
        try {
            // 检查nonce是否有效
            if (!$this->isValidNonce($walletAddress, $nonce)) {
                return false;
            }

            // 生成原始消息
            $message = $this->generateSignMessage($walletAddress, $nonce);

            // 使用Web3Signature工具类验证签名
            return Web3Signature::verifySignature($message, $signature, $walletAddress);

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 从签名中恢复地址
     * @param string $message
     * @param string $signature
     * @return string
     */
    private function recoverAddressFromSignature($message, $signature)
    {
        // 使用Web3Signature工具类进行签名验证
        return Web3Signature::verifySignature($message, $signature, '');
    }

    /**
     * 检查nonce是否有效
     * @param string $walletAddress
     * @param string $nonce
     * @return bool
     */
    public function isValidNonce($walletAddress, $nonce)
    {
        $record = Db::table('wallet_nonces')
            ->where('wallet_address', $walletAddress)
            ->where('nonce', $nonce)
            ->where('is_used', 0)
            ->where('expire_time', '>', time())
            ->find();

        return !empty($record);
    }

    /**
     * 标记nonce为已使用
     * @param string $walletAddress
     * @param string $nonce
     * @return bool
     */
    public function markNonceAsUsed($walletAddress, $nonce)
    {
        return Db::table('wallet_nonces')
            ->where('wallet_address', $walletAddress)
            ->where('nonce', $nonce)
            ->update(['is_used' => 1]);
    }

    /**
     * 清理过期的nonce
     */
    public function cleanExpiredNonces()
    {
        Db::table('wallet_nonces')
            ->where('expire_time', '<', time())
            ->delete();
    }

    /**
     * 获取钱包类型
     * @param string $userAgent
     * @return string
     */
    public function detectWalletType($userAgent = '')
    {
        if (empty($userAgent)) {
            $userAgent = request()->header('User-Agent');
        }

        // 检测常见钱包类型
        if (strpos($userAgent, 'MetaMask') !== false) {
            return 'MetaMask';
        }
        
        if (strpos($userAgent, 'WalletConnect') !== false) {
            return 'WalletConnect';
        }
        
        if (strpos($userAgent, 'Trust') !== false) {
            return 'TrustWallet';
        }
        
        if (strpos($userAgent, 'Coinbase') !== false) {
            return 'CoinbaseWallet';
        }

        return 'Unknown';
    }

    /**
     * 验证钱包地址的校验和
     * @param string $address
     * @return bool
     */
    public function isValidChecksum($address)
    {
        // EIP-55 校验和验证
        if (!$this->isValidAddress($address)) {
            return false;
        }

        $address = substr($address, 2); // 移除0x前缀
        $hash = hash('sha3-256', strtolower($address));

        for ($i = 0; $i < 40; $i++) {
            $char = $address[$i];
            $hashChar = $hash[$i];
            
            if (ctype_alpha($char)) {
                if ((ctype_upper($char) && hexdec($hashChar) < 8) ||
                    (ctype_lower($char) && hexdec($hashChar) >= 8)) {
                    return false;
                }
            }
        }

        return true;
    }
}
