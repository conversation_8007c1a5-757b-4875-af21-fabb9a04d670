{extend name="public:template" /}

{block name="content"}
<div class="section-header">
    <div class="section-header-breadcrumb">
        <div class="breadcrumb-item active"><a href="{:url('Mining/index')}">质押列表</a></div>
<!--        <div class="breadcrumb-item">矿机编辑</div>-->
    </div>
</div>

<div class="section-body">
    <h2 class="section-title">{$title}</h2>

    <div class="row">
        <div class="col-12">
            <div class="card">
                 <form id="data-form" autocomplete=off class="needs-validation" novalidate="">
                     <input type="hidden" name="id" value="{$data.id}">
                    <div class="card-body">
                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">名称</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="text" class="form-control" name="name" required="" value="{$data.name}">
<!--                                <div class="invalid-feedback">请填写名称</div>-->
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">日化收益/天（%）</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" step="0.001" class="form-control" name="profit" value="{$data.profit}">
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">质押天数</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" class="form-control" name="freeze" value="{$data.freeze}">
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">最小购买</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" class="form-control" name="min_buy" value="{$data.min_buy}">
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">最大购买</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" class="form-control" name="max_buy" value="{$data.max_buy}">
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">状态</label>
                            <div class="col-sm-12 col-md-7">

                                    <label class="lyear-radio radio-inline radio-primary">
                                        <input type="radio" name="status" value="1" {$data.status == 1 ? 'checked' : ''}><span>显示</span>
                                     </label>
                                      <label class="lyear-radio radio-inline radio-primary">
                                        <input type="radio" name="status" value="0" {$data.status == 0 ? 'checked' : ''}><span>隐藏</span>
                                     </label>

                            </div>
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                            <div class="col-sm-12 col-md-7">
                                <button type="button" class="btn btn-primary submit">提交</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{/block}


{block name="extend-script"}
<script type="text/javascript">

$('.submit').click(function () {
    $("#data-form").addClass('was-validated');
    if ($("#data-form")[0].checkValidity() === false)
      return ;

    $('.submit').attr('disabled', true);

    $.ajax({
        url: "{:url('Mining/store')}",
        method: 'post',
        data: $("#data-form").serialize(),
        dataType: 'json',
        success(res) {
            $('.submit').attr('disabled', false);
            if (res.success === true) {
                swal('操作成功', {buttons: false, icon: 'success'});
                setTimeout(function () { window.location.href= "{:url('Mining/index')}" }, 1500)
            }
            if (res.success === false) swal('出现错误', res.err_msg, 'error');
        }
    })
})
</script>
{/block}
