<?php

namespace app\admin\controller;

use app\common\model\User as UserModel;
use app\common\service\Web3Service;
use app\common\service\MultiChainService;
use think\Controller;
use think\Request;
use think\Response;
use think\Db;

/**
 * Web3钱包认证控制器
 */
class Web3Auth extends Controller
{
    protected $web3Service;

    public function __construct()
    {
        parent::__construct();
        $this->web3Service = new Web3Service();
    }

    /**
     * 获取签名nonce
     * @param Request $request
     * @return \think\response\Json
     */
    public function getNonce(Request $request)
    {
        if (!$request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $walletAddress = $request->post('wallet_address', '');
        $chainKey = $request->post('chain_key', 'ethereum');

        if (empty($walletAddress)) {
            return json(['code' => 400, 'msg' => '钱包地址不能为空']);
        }

        // 检查链是否支持
        if (!MultiChainService::isChainEnabled($chainKey)) {
            return json(['code' => 400, 'msg' => '不支持的区块链网络']);
        }

        // 验证钱包地址格式
        if (!$this->web3Service->isValidAddress($walletAddress, $chainKey)) {
            return json(['code' => 400, 'msg' => '钱包地址格式无效']);
        }

        try {
            // 生成nonce
            $nonce = $this->web3Service->generateNonce($walletAddress, $chainKey);

            // 生成签名消息
            $message = $this->web3Service->generateSignMessage($walletAddress, $nonce, $chainKey);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'nonce' => $nonce,
                    'message' => $message,
                    'wallet_address' => $walletAddress,
                    'chain_key' => $chainKey,
                    'chain_name' => MultiChainService::getChainDisplayName($chainKey)
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 验证签名并登录
     * @param Request $request
     * @return \think\response\Json
     */
    public function verify(Request $request)
    {
        if (!$request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $params = $request->post();
        $walletAddress = $params['wallet_address'] ?? '';
        $signature = $params['signature'] ?? '';
        $nonce = $params['nonce'] ?? '';

        if (empty($walletAddress) || empty($signature) || empty($nonce)) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }

        try {
            // 验证签名
            $isValid = $this->web3Service->verifySignature($walletAddress, $signature, $nonce);
            
            if (!$isValid) {
                $this->logLoginAttempt($walletAddress, null, $nonce, $signature, '', false);
                return json(['code' => 401, 'msg' => '签名验证失败']);
            }

            // 查找或创建用户
            $user = $this->findOrCreateUser($walletAddress);
            
            if (!$user) {
                return json(['code' => 500, 'msg' => '用户创建失败']);
            }

            // 检查用户状态
            if ($user['is_delete'] == 1) {
                return json(['code' => 403, 'msg' => '账户已被禁用']);
            }

            // 设置登录session
            session('user_id', $user['userId']);
            session('user_email', $user['email']);
            session('user_role', $user['roleId']);
            session('wallet_address', $walletAddress);

            // 记录登录日志
            $this->logLoginAttempt($walletAddress, $user['userId'], $nonce, $signature, '', true);

            // 标记nonce为已使用
            $this->web3Service->markNonceAsUsed($walletAddress, $nonce);

            return json([
                'code' => 200,
                'msg' => '登录成功',
                'data' => [
                    'user_id' => $user['userId'],
                    'email' => $user['email'],
                    'wallet_address' => $walletAddress,
                    'redirect_url' => url('admin/index/index')
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 绑定钱包到现有账户
     * @param Request $request
     * @return \think\response\Json
     */
    public function bind(Request $request)
    {
        if (!$request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        // 检查用户是否已登录
        $userId = session('user_id');
        if (!$userId) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        $params = $request->post();
        $walletAddress = $params['wallet_address'] ?? '';
        $signature = $params['signature'] ?? '';
        $nonce = $params['nonce'] ?? '';

        if (empty($walletAddress) || empty($signature) || empty($nonce)) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }

        try {
            // 验证签名
            $isValid = $this->web3Service->verifySignature($walletAddress, $signature, $nonce);
            
            if (!$isValid) {
                return json(['code' => 401, 'msg' => '签名验证失败']);
            }

            // 检查钱包是否已被其他用户绑定
            $existingUser = UserModel::where('wallet_address', $walletAddress)
                ->where('userId', '<>', $userId)
                ->find();
            
            if ($existingUser) {
                return json(['code' => 409, 'msg' => '该钱包地址已被其他账户绑定']);
            }

            // 绑定钱包到当前用户
            $result = UserModel::where('userId', $userId)->update([
                'wallet_address' => $walletAddress,
                'wallet_type' => 'MetaMask', // 默认类型，后续可扩展
                'wallet_bind_time' => time()
            ]);

            if ($result) {
                // 标记nonce为已使用
                $this->web3Service->markNonceAsUsed($walletAddress, $nonce);
                
                return json(['code' => 200, 'msg' => '钱包绑定成功']);
            } else {
                return json(['code' => 500, 'msg' => '绑定失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 查找或创建用户
     * @param string $walletAddress
     * @return array|null
     */
    private function findOrCreateUser($walletAddress)
    {
        // 先查找是否存在该钱包地址的用户
        $user = UserModel::where('wallet_address', $walletAddress)->find();
        
        if ($user) {
            return $user->toArray();
        }

        // 如果不存在，创建新用户
        $userData = [
            'email' => $walletAddress . '@web3.local', // 临时邮箱格式
            'password' => '', // Web3用户不需要密码
            'roleId' => 2, // 普通用户角色
            'parent_id' => 0,
            'is_delete' => 0,
            'is_withdraw' => 1,
            'create_time' => time(),
            'wallet_address' => $walletAddress,
            'wallet_type' => 'MetaMask',
            'wallet_bind_time' => time()
        ];

        $userId = UserModel::insertGetId($userData);
        
        if ($userId) {
            return UserModel::where('userId', $userId)->find()->toArray();
        }

        return null;
    }

    /**
     * 记录登录尝试日志
     * @param string $walletAddress
     * @param int|null $userId
     * @param string $nonce
     * @param string $signature
     * @param string $message
     * @param bool $success
     */
    private function logLoginAttempt($walletAddress, $userId, $nonce, $signature, $message, $success)
    {
        try {
            Db::table('web3_login_logs')->insert([
                'wallet_address' => $walletAddress,
                'user_id' => $userId,
                'nonce' => $nonce,
                'signature' => $signature,
                'message' => $message,
                'login_status' => $success ? 1 : 0,
                'ip_address' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'create_time' => time()
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }
}
