﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta data-react-helmet="true" name="description"
    content="Explore and download Ethereum and ethstake.exchange brand assets, illustrations and media.">
  <meta name="theme-color" content="#1c1ce1">
  <meta name="generator" content="Gatsby 4.7.1">
  <style data-href="/styles.92bedc857ac51bb6cf96.css" data-identity="gatsby-global-css">
    html {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 1rem
    }

    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    main,
    menu,
    nav,
    section,
    summary {
      display: block
    }

    audio,
    canvas,
    progress,
    video {
      display: inline-block
    }

    audio:not([controls]) {
      display: none;
      height: 0
    }

    progress {
      vertical-align: baseline
    }

    [hidden],
    template {
      display: none
    }

    a {
      -webkit-text-decoration-skip: objects;
      background-color: transparent;
      text-decoration: none
    }

    a:active,
    a:hover {
      outline-width: 0
    }

    abbr[title] {
      border-bottom: none;
      text-decoration: underline;
      -webkit-text-decoration: underline dotted;
      text-decoration: underline dotted
    }

    b,
    strong {
      font-weight: inherit;
      font-weight: bolder
    }

    dfn {
      font-style: italic
    }

    h1 {
      font-size: 2em;
      margin: .67em 0
    }

    mark {
      background-color: #ff0;
      color: #000
    }

    small {
      font-size: 80%
    }

    sub,
    sup {
      font-size: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline
    }

    sub {
      bottom: -.25em
    }

    sup {
      top: -.5em
    }

    img {
      border-style: none
    }

    svg:not(:root) {
      overflow: hidden
    }

    code,
    kbd,
    pre,
    samp {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: 1em
    }

    figure {
      margin: 1em 40px
    }

    hr {
      box-sizing: content-box;
      height: 0;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font: inherit;
      margin: 0
    }

    optgroup {
      font-weight: 700
    }

    button,
    input {
      overflow: visible
    }

    button,
    select {
      text-transform: none
    }

    [type=reset],
    [type=submit],
    button,
    html [type=button] {
      -webkit-appearance: button
    }

    [type=button]::-moz-focus-inner,
    [type=reset]::-moz-focus-inner,
    [type=submit]::-moz-focus-inner,
    button::-moz-focus-inner {
      border-style: none;
      padding: 0
    }

    [type=button]:-moz-focusring,
    [type=reset]:-moz-focusring,
    [type=submit]:-moz-focusring,
    button:-moz-focusring {
      outline: 1px dotted ButtonText
    }

    fieldset {
      border: 1px solid silver;
      margin: 0 2px;
      padding: .35em .625em .75em
    }

    legend {
      box-sizing: border-box;
      color: inherit;
      display: table;
      max-width: 100%;
      padding: 0;
      white-space: normal
    }

    textarea {
      overflow: auto
    }

    [type=checkbox],
    [type=radio] {
      box-sizing: border-box;
      padding: 0
    }

    [type=number]::-webkit-inner-spin-button,
    [type=number]::-webkit-outer-spin-button {
      height: auto
    }

    [type=search] {
      -webkit-appearance: textfield;
      outline-offset: -2px
    }

    [type=search]::-webkit-search-cancel-button,
    [type=search]::-webkit-search-decoration {
      -webkit-appearance: none
    }

    ::-webkit-input-placeholder {
      color: inherit;
      opacity: .54
    }

    ::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit
    }

    html {
      box-sizing: border-box;
      font: 100%/1.6em georgia, serif;
      overflow-y: scroll
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      word-wrap: break-word;
      -ms-font-feature-settings: "kern", "liga", "clig", "calt";
      -webkit-font-feature-settings: "kern", "liga", "clig", "calt";
      font-feature-settings: "kern", "liga", "clig", "calt";
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-kerning: normal;
      font-weight: 400
    }

    img {
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      max-width: 100%;
      padding: 0
    }

    h1 {
      font-size: 2.25rem;
      font-weight: 500
    }

    h1,
    h2 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 0 0 1.45rem;
      padding: 0
    }

    h2 {
      font-size: 1.62671rem;
      font-weight: 700
    }

    h3 {
      font-size: 1.38316rem;
      font-weight: 500
    }

    h3,
    h4 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 2rem 0 1.45rem;
      padding: 0
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600
    }

    h5 {
      font-size: 1rem;
      margin: 2rem 0 1.45rem
    }

    h5,
    h6 {
      text-rendering: optimizeLegibility;
      color: inherit;
      font-weight: 500;
      line-height: 1.1;
      padding: 0
    }

    h6 {
      font-size: .85028rem
    }

    h6,
    hgroup {
      margin: 0 0 1.45rem
    }

    hgroup {
      padding: 0
    }

    ol,
    ul {
      list-style-image: none;
      list-style-position: outside;
      margin: 0 0 1.45rem 1.45rem;
      padding: 0
    }

    dd,
    dl,
    figure,
    p {
      margin: 0 0 1.45rem;
      padding: 0
    }

    pre {
      word-wrap: normal;
      background: rgba(0, 0, 0, .04);
      border-radius: 3px;
      font-size: .85rem;
      line-height: 1.42;
      margin: 0 0 1.45rem;
      overflow: auto;
      padding: 1.45rem;
      white-space: pre-wrap
    }

    table {
      border-collapse: collapse;
      font-size: 1rem;
      line-height: 1.45rem;
      width: 100%
    }

    fieldset,
    table {
      margin: 0 0 1.45rem;
      padding: 0
    }

    blockquote {
      margin: 0 1.45rem 1.45rem;
      padding: 0
    }

    form,
    iframe,
    noscript {
      margin: 0 0 1.45rem;
      padding: 0
    }

    hr {
      background: rgba(0, 0, 0, .2);
      border: none;
      height: 1px;
      margin: 4rem 0 0;
      padding: 0
    }

    address {
      margin: 0 0 1.45rem;
      padding: 0
    }

    b,
    dt,
    strong,
    th {
      font-weight: 700
    }

    li {
      margin-bottom: .725rem
    }

    ol li,
    ul li {
      padding-left: 0
    }

    li>ol,
    li>ul {
      margin-bottom: .725rem;
      margin-left: 1.45rem;
      margin-top: .725rem
    }

    blockquote :last-child,
    li :last-child,
    p :last-child {
      margin-bottom: 0
    }

    li>p {
      margin-bottom: .725rem
    }

    code {
      font-size: 1em;
      line-height: 1.45em
    }

    kbd {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: .625rem;
      line-height: 1.56rem
    }

    samp {
      font-size: .85rem;
      line-height: 1.45rem
    }

    abbr,
    abbr[title],
    acronym {
      border-bottom: 1px dotted rgba(0, 0, 0, .5);
      cursor: help
    }

    abbr[title] {
      text-decoration: none
    }

    td,
    th,
    thead {
      text-align: left
    }

    td,
    th {
      font-feature-settings: "tnum";
      -moz-font-feature-settings: "tnum";
      -ms-font-feature-settings: "tnum";
      -webkit-font-feature-settings: "tnum";
      border-bottom: 1px solid hsla(0, 13%, 72%, .12);
      padding: .725rem .96667rem calc(.725rem - 1px)
    }

    td:first-child,
    th:first-child {
      padding-left: 0
    }

    td:last-child,
    th:last-child {
      padding-right: 0
    }

    tt {
      background-color: #2b2834;
      border-radius: 2px;
      color: #968af6
    }

    code,
    tt {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      padding: .2em
    }

    code {
      background-color: rgba(0, 0, 0, .04);
      border-radius: 3px
    }

    pre code {
      background: none;
      line-height: 1.42
    }

    code:before,
    pre code:after,
    pre code:before,
    pre tt:after,
    pre tt:before,
    tt:after,
    tt:before {
      content: ""
    }

    @media only screen and (max-width:480px) {
      html {
        font-size: 100%
      }
    }

    .assets-page .gatsby-resp-image-wrapper {
      max-height: 200px !important
    }

    .assets-page .gatsby-resp-image-image {
      width: auto !important
    }
  </style>
  <link rel="icon" href="/favicon-32x32.png?v=8b512faa8d4a0b019c123a771b6622aa" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="anonymous">
  <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48x48.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256x256.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="preconnect" href="https://matomo.ethstake.exchange">
  <link rel="sitemap" type="application/xml" href="/sitemap/sitemap-index.xml">
  <style type="text/css">
    .anchor.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }

    .anchor.after {
      display: inline-block;
      padding-left: 4px;
    }

    h1 .anchor svg,
    h2 .anchor svg,
    h3 .anchor svg,
    h4 .anchor svg,
    h5 .anchor svg,
    h6 .anchor svg {
      visibility: hidden;
    }

    h1:hover .anchor svg,
    h2:hover .anchor svg,
    h3:hover .anchor svg,
    h4:hover .anchor svg,
    h5:hover .anchor svg,
    h6:hover .anchor svg,
    h1 .anchor:focus svg,
    h2 .anchor:focus svg,
    h3 .anchor:focus svg,
    h4 .anchor:focus svg,
    h5 .anchor:focus svg,
    h6 .anchor:focus svg {
      visibility: visible;
    }
  </style>
  <script>
    document.addEventListener("DOMContentLoaded", function (event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
          var clientTop = document.documentElement.clientTop || document.body.clientTop || 0
          var offset = element.getBoundingClientRect().top + scrollTop - clientTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function () {
            window.scrollTo(0, offset - 0)
          }), 0)
        }
      }
    })
  </script>
  <title data-react-helmet="true">Ethereum brand assets | ethstake.exchange</title>
  <link data-react-helmet="true" rel="canonical" href="index.html">
  <script data-react-helmet="true" type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": "https://ethstake.exchange",
          "email": "<EMAIL>",
          "name": "Ethereum",
          "logo": "https://ethstake.exchange/og-image.png"
        }
      </script>
  <style>
    .gatsby-image-wrapper {
      position: relative;
      overflow: hidden
    }

    .gatsby-image-wrapper picture.object-fit-polyfill {
      position: static !important
    }

    .gatsby-image-wrapper img {
      bottom: 0;
      height: 100%;
      left: 0;
      margin: 0;
      max-width: none;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      object-fit: cover
    }

    .gatsby-image-wrapper [data-main-image] {
      opacity: 0;
      transform: translateZ(0);
      transition: opacity .25s linear;
      will-change: opacity
    }

    .gatsby-image-wrapper-constrained {
      display: inline-block;
      vertical-align: top
    }
  </style><noscript>
    <style>
      .gatsby-image-wrapper noscript [data-main-image] {
        opacity: 1 !important
      }

      .gatsby-image-wrapper [data-placeholder-image] {
        opacity: 0 !important
      }
    </style>
  </noscript>
  <script
    type="module">const e = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; e && document.body.addEventListener("load", (function (e) { if (void 0 === e.target.dataset.mainImage) return; if (void 0 === e.target.dataset.gatsbyImageSsr) return; const t = e.target; let a = null, n = t; for (; null === a && n;)void 0 !== n.parentNode.dataset.gatsbyImageWrapper && (a = n.parentNode), n = n.parentNode; const o = a.querySelector("[data-placeholder-image]"), r = new Image; r.src = t.currentSrc, r.decode().catch((() => { })).then((() => { t.style.opacity = 1, o && (o.style.opacity = 0, o.style.transition = "opacity 500ms linear") })) }), !0);</script>
  <style data-styled="" data-styled-version="5.3.3">
    body {
      background-color: #ffffff;
      color: #333333;
    }

    /*!sc*/
    a {
      color: #1c1cff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    mark {
      background: rgba(143, 187, 237, .1);
      box-shadow: inset 0 -2px 0 0 rgba(69, 142, 225, .8);
    }

    /*!sc*/
    .anchor.before {
      fill: #333333;
    }

    /*!sc*/
    hr {
      background: #ecececnull#1c1cff;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
      position: absolute;
    }

    /*!sc*/
    iframe {
      display: block;
      max-width: 560px;
      margin: 32px 0;
    }

    /*!sc*/
    h1 {
      font-size: 3rem;
      line-height: 1.4;
      margin: 2rem 0;
      font-weight: 700;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h1 {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    h2 {
      font-size: 2rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 3rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h2 {
        font-size: 1.5rem;
      }
    }

    /*!sc*/
    h3 {
      font-size: 1.5rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 2.5rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h3 {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    h4 {
      font-size: 1.25rem;
      line-height: 1.4;
      font-weight: 500;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h4 {
        font-size: 1rem;
      }
    }

    /*!sc*/
    h5 {
      font-size: 1rem;
      line-height: 1.4;
      font-weight: 450;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    h6 {
      font-size: 0.9rem;
      line-height: 1.4;
      font-weight: 400;
      text-transform: uppercase;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    data-styled.g1[id="sc-global-hcwgEG1"] {
      content: "sc-global-hcwgEG1,"
    }

    /*!sc*/
    .iylOGp {
      fill: #b2b2b2;
    }

    /*!sc*/
    .iylOGp:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g2[id="Icon__StyledIcon-sc-1o8zi5s-0"] {
      content: "iylOGp,"
    }

    /*!sc*/
    .gABYms:after {
      margin-left: 0.125em;
      margin-right: 0.3em;
      display: inline;
      content: "â†—";
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out;
      font-style: normal;
    }

    /*!sc*/
    .gABYms:hover:after {
      -webkit-transform: translate(0.15em, -0.2em);
      -ms-transform: translate(0.15em, -0.2em);
      transform: translate(0.15em, -0.2em);
    }

    /*!sc*/
    data-styled.g3[id="Link__ExternalLink-sc-e3riao-0"] {
      content: "gABYms,"
    }

    /*!sc*/
    .gCWUlE .is-glossary {
      white-space: nowrap;
    }

    /*!sc*/
    .gCWUlE.active {
      color: #1c1cff;
    }

    /*!sc*/
    .gCWUlE:hover svg {
      fill: #1c1cff;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g4[id="Link__InternalLink-sc-e3riao-1"] {
      content: "gCWUlE,"
    }

    /*!sc*/
    .jfMIWk {
      margin: 0 0.25rem 0 0.35rem;
      fill: #4949ff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    .jfMIWk:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g6[id="Link__GlossaryIcon-sc-e3riao-3"] {
      content: "jfMIWk,"
    }

    /*!sc*/
    .gvoBKJ {
      padding-top: 3rem;
      padding-bottom: 4rem;
      padding: 1rem 2rem;
    }

    /*!sc*/
    data-styled.g7[id="Footer__StyledFooter-sc-1to993d-0"] {
      content: "gvoBKJ,"
    }

    /*!sc*/
    .kFKfdz {
      font-size: 0.875rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g8[id="Footer__FooterTop-sc-1to993d-1"] {
      content: "kFKfdz,"
    }

    /*!sc*/
    .bWGwos {
      color: #666666;
    }

    /*!sc*/
    data-styled.g9[id="Footer__LastUpdated-sc-1to993d-2"] {
      content: "bWGwos,"
    }

    /*!sc*/
    .hlbLsM {
      display: grid;
      grid-template-columns: repeat(6, auto);
      grid-gap: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1300px) {
      .hlbLsM {
        grid-template-columns: repeat(3, auto);
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .hlbLsM {
        grid-template-columns: repeat(2, auto);
      }
    }

    /*!sc*/
    @media (max-width:500px) {
      .hlbLsM {
        grid-template-columns: auto;
      }
    }

    /*!sc*/
    data-styled.g10[id="Footer__LinkGrid-sc-1to993d-3"] {
      content: "hlbLsM,"
    }

    /*!sc*/
    .bbCEKr {
      font-size: 0.875rem;
      line-height: 1.6;
      margin: 1.14em 0;
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g12[id="Footer__SectionHeader-sc-1to993d-5"] {
      content: "bbCEKr,"
    }

    /*!sc*/
    .gjQPMc {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      margin: 0;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g13[id="Footer__List-sc-1to993d-6"] {
      content: "gjQPMc,"
    }

    /*!sc*/
    .eGhJJx {
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g14[id="Footer__ListItem-sc-1to993d-7"] {
      content: "eGhJJx,"
    }

    /*!sc*/
    .gIpSoz {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #666666;
    }

    /*!sc*/
    .gIpSoz svg {
      fill: #666666;
    }

    /*!sc*/
    .gIpSoz:after {
      color: #666666;
    }

    /*!sc*/
    .gIpSoz:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover:after {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g15[id="Footer__FooterLink-sc-1to993d-8"] {
      content: "gIpSoz,"
    }

    /*!sc*/
    .kdLbod {
      margin: 1rem 0;
    }

    /*!sc*/
    data-styled.g16[id="Footer__SocialIcons-sc-1to993d-9"] {
      content: "kdLbod,"
    }

    /*!sc*/
    .iedzfy {
      margin-left: 1rem;
      width: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iedzfy {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g17[id="Footer__SocialIcon-sc-1to993d-10"] {
      content: "iedzfy,"
    }

    /*!sc*/
    .drElXa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-right: 1.5rem;
    }

    /*!sc*/
    .drElXa:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g20[id="Dropdown__DropdownTitle-sc-1yd08gi-1"] {
      content: "drElXa,"
    }

    /*!sc*/
    .ldsPWM {
      margin: 0;
      position: absolute;
      margin-top: -1rem;
      list-style-type: none;
      list-style-image: none;
      top: 100%;
      width: auto;
      border-radius: 0.5em;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;

      opacity:0;
      display:none;
      transform:rotateX(-15deg) translateZ(0)

    }

    /*!sc*/
    data-styled.g21[id="Dropdown__DropdownList-sc-1yd08gi-2"] {
      content: "ldsPWM,"
    }

    /*!sc*/
    .Mkofa {
      white-space: nowrap;
      margin: 0;
      color: #333333;
    }

    /*!sc*/
     .Mkofa:hover >ul{
      color: #1c1cff;
      opacity:1;
      display:block;
      transform: none;
    }

    /*!sc*/
    data-styled.g23[id="Dropdown__NavListItem-sc-1yd08gi-4"] {
      content: "Mkofa,"
    }

    /*!sc*/
    .lgeotR {
      margin: 0;
      color: #333333;
    }

    /*!sc*/
    .lgeotR:hover {
      color: #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g24[id="Dropdown__DropdownItem-sc-1yd08gi-5"] {
      content: "lgeotR,"
    }

    /*!sc*/
    .cTcxIB {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: block;
      padding: 0.5rem;
      color: #333333;
    }

    /*!sc*/
    .cTcxIB svg {
      fill: #666666;
    }

    /*!sc*/
    .cTcxIB:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cTcxIB:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g25[id="Dropdown__NavLink-sc-1yd08gi-6"] {
      content: "cTcxIB,"
    }

    /*!sc*/
    .ivCgwn {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .ivCgwn>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .hLjau {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .hLjau>img {
      width: 3em !important;
      height: 3em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .coxJXZ {
      display: inline-block;
      margin-right: 0.5em;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .coxJXZ>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    data-styled.g27[id="Emoji__StyledEmoji-sc-ihpuqw-0"] {
      content: "ivCgwn,hLjau,coxJXZ,"
    }

    /*!sc*/
    .dUatah {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: none;
      border: none;
      color: inherit;
      display: inline-block;
      font: inherit;
      padding: initial;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g28[id="NakedButton-sc-1g43w8v-0"] {
      content: "dUatah,"
    }

    /*!sc*/
    .eoyKpR {
      margin: 0;
      position: relative;
      border-radius: 0.25em;
    }

    /*!sc*/
    data-styled.g29[id="Input__Form-sc-1utkal6-0"] {
      content: "eoyKpR,"
    }

    /*!sc*/
    .kkfPkW {
      border: 1px solid #7f7f7f;
      color: #333333;
      background: #ffffff;
      padding: 0.5rem;
      padding-right: 2rem;
      border-radius: 0.25em;
      width: 100%;
    }

    /*!sc*/
    .kkfPkW:focus {
      outline: #1c1cff auto 1px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .kkfPkW {
        padding-left: 2rem;
      }
    }

    /*!sc*/
    data-styled.g30[id="Input__StyledInput-sc-1utkal6-1"] {
      content: "kkfPkW,"
    }

    /*!sc*/
    .gFzMVg {
      position: absolute;
      right: 6px;
      top: 50%;
      margin-top: -12px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .gFzMVg {
        left: 6px;
      }
    }

    /*!sc*/
    data-styled.g31[id="Input__SearchIcon-sc-1utkal6-2"] {
      content: "gFzMVg,"
    }

    /*!sc*/
    .ggVPUc {
      border: 1px solid #7f7f7f;
      border-radius: 0.25em;
      color: #333333;
      display: none;
      margin-bottom: 0;
      padding: 0 6px;
      position: absolute;
      right: 6px;
      top: 20%;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .ggVPUc {
        display: inline-block;
      }
    }

    /*!sc*/
    data-styled.g32[id="Input__SearchSlash-sc-1utkal6-3"] {
      content: "ggVPUc,"
    }

    /*!sc*/
    .kNenpg {
      position: relative;
      display: grid;
      grid-gap: 1em;
    }

    /*!sc*/
    data-styled.g33[id="Search__Root-sc-1qm8xwy-0"] {
      content: "kNenpg,"
    }

    /*!sc*/
    .eJIgkk {
      display: none;
      max-height: 80vh;
      overflow: scroll;
      z-index: 2;
      position: absolute;
      right: 0;
      top: calc(100% + 0.5em);
      width: 80vw;
      max-width: 30em;
      box-shadow: 0 0 5px 0;
      padding: 0.5rem;
      background: #ffffff;
      border-radius: 0.25em;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eJIgkk {
        width: 100%;
      }
    }

    /*!sc*/
    .eJIgkk>*+* {
      padding-top: 1em !important;
      border-top: 2px solid black;
    }

    /*!sc*/
    .eJIgkk li {
      margin-bottom: 0.4rem;
    }

    /*!sc*/
    .eJIgkk li+li {
      padding-top: 0.7em;
      border-top: 1px solid #ececec;
    }

    /*!sc*/
    .eJIgkk ul {
      margin: 0;
      list-style: none;
    }

    /*!sc*/
    .eJIgkk mark {
      color: #1c1cff;
      box-shadow: inset 0 -2px 0 0 rgba(143, 187, 237, .5);
    }

    /*!sc*/
    .eJIgkk header {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk header h3 {
      color: #ffffff;
      background: #4c4c4c;
      padding: 0.1em 0.4em;
      border-radius: 0.25em;
    }

    /*!sc*/
    .eJIgkk h3 {
      margin: 0 0 0.5em;
    }

    /*!sc*/
    .eJIgkk h4 {
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk a {
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    data-styled.g34[id="Search__HitsWrapper-sc-1qm8xwy-1"] {
      content: "eJIgkk,"
    }

    /*!sc*/
    .kCvjty {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    /*!sc*/
    data-styled.g41[id="SharedStyledComponents__Page-sc-1cr9zfr-0"] {
      content: "kCvjty,"
    }

    /*!sc*/
    .dedPKg {
      padding: 1rem 2rem;
      width: 100%;
    }

    /*!sc*/
    data-styled.g44[id="SharedStyledComponents__Content-sc-1cr9zfr-3"] {
      content: "dedPKg,"
    }

    /*!sc*/
    .jEZlpP {
      -webkit-text-decoration: none;
      text-decoration: none;
      margin-right: 2rem;
      color: #333333;
    }

    /*!sc*/
    .jEZlpP svg {
      fill: #666666;
    }

    /*!sc*/
    .jEZlpP:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .jEZlpP:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .jEZlpP.active {
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g52[id="SharedStyledComponents__NavLink-sc-1cr9zfr-11"] {
      content: "jEZlpP,"
    }

    /*!sc*/
    .iuRocQ {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iuRocQ:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    data-styled.g59[id="SharedStyledComponents__Button-sc-1cr9zfr-18"] {
      content: "iuRocQ,"
    }

    /*!sc*/
    .bphJHH {
      background-color: #1c1cff;
      color: #ffffff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .bphJHH:hover {
      background-color: rgba(28, 28, 225, 0.8);
    }

    /*!sc*/
    .bphJHH:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g60[id="SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19"] {
      content: "bphJHH,"
    }

    /*!sc*/
    .hhdXUp {
      display: none;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hhdXUp {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    data-styled.g73[id="Mobile__Container-sc-zxc8gm-0"] {
      content: "hhdXUp,"
    }

    /*!sc*/
    .dUGGTH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g74[id="Mobile__MenuIcon-sc-zxc8gm-1"] {
      content: "dUGGTH,"
    }

    /*!sc*/
    .dLNRLx {
      margin-left: 1rem;
    }

    /*!sc*/
    data-styled.g75[id="Mobile__MenuButton-sc-zxc8gm-2"] {
      content: "dLNRLx,"
    }

    /*!sc*/
    .hvwyGc {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g76[id="Mobile__OtherIcon-sc-zxc8gm-3"] {
      content: "hvwyGc,"
    }

    /*!sc*/
    .bCHBHX {
      position: fixed;
      background: hsla(0, 0%, 69.8%, 0.9);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
    }

    /*!sc*/
    data-styled.g77[id="Mobile__MobileModal-sc-zxc8gm-4"] {
      content: "bCHBHX,"
    }

    /*!sc*/
    .AJukL {
      background: #ffffff;
      z-index: 99;
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
    }

    /*!sc*/
    data-styled.g78[id="Mobile__MenuContainer-sc-zxc8gm-5"] {
      content: "AJukL,"
    }

    /*!sc*/
    .gbspKa {
      margin: 0 0.125rem;
      width: 1.5rem;
      height: 2.5rem;
      position: relative;
      stroke-width: 2px;
      z-index: 100;
    }

    /*!sc*/
    .gbspKa>path {
      stroke: #333333;
      fill: none;
    }

    /*!sc*/
    .gbspKa:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gbspKa:hover>path {
      stroke: #1c1cff;
    }

    /*!sc*/
    data-styled.g79[id="Mobile__GlyphButton-sc-zxc8gm-6"] {
      content: "gbspKa,"
    }

    /*!sc*/
    .gBSEi {
      z-index: 101;
      padding: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g80[id="Mobile__SearchContainer-sc-zxc8gm-7"] {
      content: "gBSEi,"
    }

    /*!sc*/
    .iXlChz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .iXlChz>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g81[id="Mobile__SearchHeader-sc-zxc8gm-8"] {
      content: "iXlChz,"
    }

    /*!sc*/
    .jmriUx {
      z-index: 102;
      cursor: pointer;
    }

    /*!sc*/
    .jmriUx>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g82[id="Mobile__CloseIconContainer-sc-zxc8gm-9"] {
      content: "jmriUx,"
    }

    /*!sc*/
    .gYetwr {
      margin: 0;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 3rem 1rem 8rem;
    }

    /*!sc*/
    data-styled.g83[id="Mobile__MenuItems-sc-zxc8gm-10"] {
      content: "gYetwr,"
    }

    /*!sc*/
    .gXxMFO {
      margin: 0;
      margin-bottom: 3rem;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g84[id="Mobile__NavListItem-sc-zxc8gm-11"] {
      content: "gXxMFO,"
    }

    /*!sc*/
    .kuWShR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 0;
    }

    /*!sc*/
    data-styled.g85[id="Mobile__StyledNavLink-sc-zxc8gm-12"] {
      content: "kuWShR,"
    }

    /*!sc*/
    .erCTXJ {
      margin: 1rem 0;
      color: #333333;
    }

    /*!sc*/
    data-styled.g86[id="Mobile__SectionTitle-sc-zxc8gm-13"] {
      content: "erCTXJ,"
    }

    /*!sc*/
    .hghxUt {
      margin: 0;
    }

    /*!sc*/
    data-styled.g87[id="Mobile__SectionItems-sc-zxc8gm-14"] {
      content: "hghxUt,"
    }

    /*!sc*/
    .kdRQoZ {
      margin-bottom: 1rem;
      list-style-type: none;
      list-style-image: none;
      opacity: 0.7;
    }

    /*!sc*/
    .kdRQoZ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g88[id="Mobile__SectionItem-sc-zxc8gm-15"] {
      content: "kdRQoZ,"
    }

    /*!sc*/
    .iYttIj {
      background: #ffffff;
      border-top: 1px solid #ececec;
      padding-right: 1rem;
      padding-left: 1rem;
      margin-top: auto;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 108px;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      max-width: 450px;
      z-index: 99;
    }

    /*!sc*/
    data-styled.g89[id="Mobile__BottomMenu-sc-zxc8gm-16"] {
      content: "iYttIj,"
    }

    /*!sc*/
    .cnajxM {
      -webkit-flex: 1 1 120px;
      -ms-flex: 1 1 120px;
      flex: 1 1 120px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      color: #333333;
    }

    /*!sc*/
    .cnajxM>svg {
      fill: #333333;
    }

    /*!sc*/
    .cnajxM:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cnajxM:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g90[id="Mobile__BottomItem-sc-zxc8gm-17"] {
      content: "cnajxM,"
    }

    /*!sc*/
    .heSUpS {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
    }

    /*!sc*/
    .heSUpS>svg {
      fill: #333333;
    }

    /*!sc*/
    .heSUpS:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .heSUpS:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g91[id="Mobile__BottomLink-sc-zxc8gm-18"] {
      content: "heSUpS,"
    }

    /*!sc*/
    .hkZTkJ {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      margin-top: 0.5rem;
      text-transform: uppercase;
      text-align: center;
      opacity: 0.7;
    }

    /*!sc*/
    .hkZTkJ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g92[id="Mobile__BottomItemText-sc-zxc8gm-19"] {
      content: "hkZTkJ,"
    }

    /*!sc*/
    .jBipln {
      color: #333333;
      background: #f2f2f2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 10vw;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 280px;
      width: min(60vw, 280px);
      height: 280px;
      height: min(60vw, 280px);
      border-radius: 100%;
    }

    /*!sc*/
    data-styled.g93[id="Mobile__BlankSearchState-sc-zxc8gm-20"] {
      content: "jBipln,"
    }

    /*!sc*/
    .iGuESw {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 1000;
      width: 100%;
    }

    /*!sc*/
    data-styled.g94[id="Nav__NavContainer-sc-1aprtmp-0"] {
      content: "iGuESw,"
    }

    /*!sc*/
    .cpomzd {
      height: 4.75rem;
      padding: 1rem 2rem;
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g95[id="Nav__StyledNav-sc-1aprtmp-1"] {
      content: "cpomzd,"
    }

    /*!sc*/
    .faUCsG {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 1440px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .faUCsG {
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
    }

    /*!sc*/
    data-styled.g97[id="Nav__NavContent-sc-1aprtmp-3"] {
      content: "faUCsG,"
    }

    /*!sc*/
    .gjaVMk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gjaVMk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g98[id="Nav__InnerContent-sc-1aprtmp-4"] {
      content: "gjaVMk,"
    }

    /*!sc*/
    .jUJHKw {
      margin: 0;
      margin-left: 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g99[id="Nav__LeftItems-sc-1aprtmp-5"] {
      content: "jUJHKw,"
    }

    /*!sc*/
    .kQWBtS {
      margin: 0;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g100[id="Nav__RightItems-sc-1aprtmp-6"] {
      content: "kQWBtS,"
    }

    /*!sc*/
    .jODkFW {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-right: 0;
      margin-left: 1rem;
    }

    /*!sc*/
    .jODkFW:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g102[id="Nav__RightNavLink-sc-1aprtmp-8"] {
      content: "jODkFW,"
    }

    /*!sc*/
    .igUcis {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g103[id="Nav__HomeLogoNavLink-sc-1aprtmp-9"] {
      content: "igUcis,"
    }

    /*!sc*/
    .euWmfq {
      opacity: 0.85;
    }

    /*!sc*/
    .euWmfq:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g104[id="Nav__HomeLogo-sc-1aprtmp-10"] {
      content: "euWmfq,"
    }

    /*!sc*/
    .bDRFLa {
      padding-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g105[id="Nav__Span-sc-1aprtmp-11"] {
      content: "bDRFLa,"
    }

    /*!sc*/
    .hwxIMf {
      margin-left: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .hwxIMf:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g106[id="Nav__ThemeToggle-sc-1aprtmp-12"] {
      content: "hwxIMf,"
    }

    /*!sc*/
    .jOKVBH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g107[id="Nav__NavIcon-sc-1aprtmp-13"] {
      content: "jOKVBH,"
    }

    /*!sc*/
    .iMiHPL {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iMiHPL function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .iMiHPL function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g125[id="ButtonLink__StyledLinkButton-sc-8betkf-0"] {
      content: "iMiHPL,"
    }

    /*!sc*/
    .kmLdQv {
      background-color: #1c1cff;
      color: #ffffff !important;
      border: 1px solid transparent;
    }

    /*!sc*/
    .kmLdQv:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .kmLdQv:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g127[id="ButtonLink__PrimaryLink-sc-8betkf-2"] {
      content: "kmLdQv,"
    }

    /*!sc*/
    .dzWGyc {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .dzWGyc:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .dzWGyc:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g128[id="ButtonLink__SecondaryLink-sc-8betkf-3"] {
      content: "dzWGyc,"
    }

    /*!sc*/
    .elpFuD {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g131[id="TranslationBanner__H3-sc-cd94ib-0"] {
      content: "elpFuD,"
    }

    /*!sc*/
    .jJbcq {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jJbcq {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g132[id="TranslationBanner__BannerContainer-sc-cd94ib-1"] {
      content: "jJbcq,"
    }

    /*!sc*/
    .jIVPcV {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jIVPcV {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g133[id="TranslationBanner__StyledBanner-sc-cd94ib-2"] {
      content: "jIVPcV,"
    }

    /*!sc*/
    .jiZNpa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .jiZNpa {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g134[id="TranslationBanner__BannerContent-sc-cd94ib-3"] {
      content: "jiZNpa,"
    }

    /*!sc*/
    .dOewRO {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g135[id="TranslationBanner__BannerClose-sc-cd94ib-4"] {
      content: "dOewRO,"
    }

    /*!sc*/
    .cEauOV {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g136[id="TranslationBanner__BannerCloseIcon-sc-cd94ib-5"] {
      content: "cEauOV,"
    }

    /*!sc*/
    .nChYp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .nChYp {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g137[id="TranslationBanner__Row-sc-cd94ib-6"] {
      content: "nChYp,"
    }

    /*!sc*/
    .gXNXMi {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gXNXMi {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g138[id="TranslationBanner__ButtonRow-sc-cd94ib-7"] {
      content: "gXNXMi,"
    }

    /*!sc*/
    .hTWLVy {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hTWLVy {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g139[id="TranslationBanner__StyledEmoji-sc-cd94ib-8"] {
      content: "hTWLVy,"
    }

    /*!sc*/
    .kUKdfA {
      margin-left: 0.5rem;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kUKdfA {
        margin-left: 0rem;
        margin-top: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g140[id="TranslationBanner__SecondaryButtonLink-sc-cd94ib-9"] {
      content: "kUKdfA,"
    }

    /*!sc*/
    .kIfJin {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g141[id="TranslationBannerLegal__H3-sc-1df4kz4-0"] {
      content: "kIfJin,"
    }

    /*!sc*/
    .eZKsbu {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eZKsbu {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g142[id="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1"] {
      content: "eZKsbu,"
    }

    /*!sc*/
    .cEcQwp {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .cEcQwp {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g143[id="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2"] {
      content: "cEcQwp,"
    }

    /*!sc*/
    .intGem {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .intGem {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g144[id="TranslationBannerLegal__BannerContent-sc-1df4kz4-3"] {
      content: "intGem,"
    }

    /*!sc*/
    .hMvMKu {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g145[id="TranslationBannerLegal__BannerClose-sc-1df4kz4-4"] {
      content: "hMvMKu,"
    }

    /*!sc*/
    .bhaYvl {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g146[id="TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5"] {
      content: "bhaYvl,"
    }

    /*!sc*/
    .cJRPhR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cJRPhR {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g147[id="TranslationBannerLegal__Row-sc-1df4kz4-6"] {
      content: "cJRPhR,"
    }

    /*!sc*/
    .kXSENe {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kXSENe {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g148[id="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7"] {
      content: "kXSENe,"
    }

    /*!sc*/
    .dRuawC {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dRuawC {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g149[id="TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8"] {
      content: "dRuawC,"
    }

    /*!sc*/
    .cRWHVB {
      background-color: #1c1cff;
    }

    /*!sc*/
    data-styled.g150[id="SkipLink__Div-sc-1ysqk2q-0"] {
      content: "cRWHVB,"
    }

    /*!sc*/
    .kOmocm {
      line-height: 2rem;
      position: absolute;
      top: -3rem;
      margin-left: 0.5rem;
      color: #ffffff;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .kOmocm:focus {
      position: static;
    }

    /*!sc*/
    data-styled.g151[id="SkipLink__Anchor-sc-1ysqk2q-1"] {
      content: "kOmocm,"
    }

    /*!sc*/
    .mXCTw {
      position: relative;
      margin: 0px auto;
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-flow: column;
      -ms-flex-flow: column;
      flex-flow: column;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .mXCTw {
        max-width: 1504px;
      }
    }

    /*!sc*/
    data-styled.g152[id="Layout__ContentContainer-sc-19910io-0"] {
      content: "mXCTw,"
    }

    /*!sc*/
    .gqazVg {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gqazVg {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g153[id="Layout__MainContainer-sc-19910io-1"] {
      content: "gqazVg,"
    }

    /*!sc*/
    .kCJhKM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }

    /*!sc*/
    data-styled.g154[id="Layout__MainContent-sc-19910io-2"] {
      content: "kCJhKM,"
    }

    /*!sc*/
    .dliKfQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: space-around;
      -webkit-justify-content: space-around;
      -ms-flex-pack: space-around;
      justify-content: space-around;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      overflow: visible;
      width: 100%;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    /*!sc*/
    data-styled.g155[id="Layout__Main-sc-19910io-3"] {
      content: "dliKfQ,"
    }

    /*!sc*/
    .jdkhYU {
      -webkit-flex: 1 1 45%;
      -ms-flex: 1 1 45%;
      flex: 1 1 45%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin: 1rem;
      opacity: 1;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .jdkhYU {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    .gbAtZo {
      -webkit-flex: 1 1 45%;
      -ms-flex: 1 1 45%;
      flex: 1 1 45%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin: 1rem;
      opacity: 0;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gbAtZo {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g1024[id="AssetDownload__Container-sc-1etxw81-0"] {
      content: "jdkhYU,gbAtZo,"
    }

    /*!sc*/
    .ktdNpG {
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 100%;
    }

    /*!sc*/
    data-styled.g1025[id="AssetDownload__Image-sc-1etxw81-1"] {
      content: "ktdNpG,"
    }

    /*!sc*/
    .ikiGpt {
      border: 1px solid #e5e5e5;
      width: 100%;
      padding: 2rem;
      text-align: center;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }

    /*!sc*/
    data-styled.g1026[id="AssetDownload__ImageContainer-sc-1etxw81-2"] {
      content: "ikiGpt,"
    }

    /*!sc*/
    .gTZdoK {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      font-size: 1rem;
      color: #4c4c4c;
      margin-right: 0.5rem;
    }

    /*!sc*/
    data-styled.g1027[id="AssetDownload__ArtistSubtitle-sc-1etxw81-3"] {
      content: "gTZdoK,"
    }

    /*!sc*/
    .cQJlwD {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: start;
      -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
      justify-content: flex-start;
      margin-bottom: 1rem;
      width: 100%;
      background: #ffffff;
      border-left: 1px solid #e5e5e5;
      border-bottom: 1px solid #e5e5e5;
      border-right: 1px solid #e5e5e5;
      border-radius: 0px 0px 4px 4px;
      padding: 0.5rem 1rem;
    }

    /*!sc*/
    data-styled.g1028[id="AssetDownload__Caption-sc-1etxw81-4"] {
      content: "cQJlwD,"
    }

    /*!sc*/
    .jmLfmz {
      margin-top: 1rem;
    }

    /*!sc*/
    data-styled.g1029[id="AssetDownload__ButtonContainer-sc-1etxw81-5"] {
      content: "jmLfmz,"
    }

    /*!sc*/
    .drhuTm {
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 100%;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .drhuTm {
        width: 60%;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .drhuTm {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g1030[id="assets__Image-sc-4yoqxh-0"] {
      content: "drhuTm,"
    }

    /*!sc*/
    .EZoBf {
      width: 100%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 2rem 0rem;
    }

    /*!sc*/
    data-styled.g1031[id="assets__HeroContainer-sc-4yoqxh-1"] {
      content: "EZoBf,"
    }

    /*!sc*/
    .kbiKQJ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin: 0 -1rem 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kbiKQJ {
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
      }
    }

    /*!sc*/
    data-styled.g1032[id="assets__Row-sc-4yoqxh-2"] {
      content: "kbiKQJ,"
    }

    /*!sc*/
    .hAckNE {
      margin: 4.5rem 0 1.5rem;
      position: inherit !important;
    }

    /*!sc*/
    .hAckNE:before {
      content: "";
      display: block;
      height: 120px;
      margin-top: -120px;
      visibility: hidden;
    }

    /*!sc*/
    data-styled.g1033[id="assets__H2-sc-4yoqxh-3"] {
      content: "hAckNE,"
    }

    /*!sc*/
    .jiuwPB {
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g1034[id="assets__H3-sc-4yoqxh-4"] {
      content: "jiuwPB,"
    }

    /*!sc*/
    .bsOmTv {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 6rem;
      text-align: center;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .bsOmTv {
        margin: 2rem;
      }
    }

    /*!sc*/
    data-styled.g1035[id="assets__Header-sc-4yoqxh-5"] {
      content: "bsOmTv,"
    }

    /*!sc*/
  </style>
  <link as="script" rel="preload" href="/webpack-runtime-d600da28e471609bf3f3.js">
  <link as="script" rel="preload" href="/framework-4e285adfb333f1b50c05.js">
  <link as="script" rel="preload" href="/252f366e-2705b607be296edabcea.js">
  <link as="script" rel="preload" href="/ae51ba48-34d54094a2c04f215fb8.js">
  <link as="script" rel="preload" href="/1bfc9850-0f18e2d74feedfc6e426.js">
  <link as="script" rel="preload" href="/0c428ae2-2128ff22fce458b543bd.js">
  <link as="script" rel="preload" href="/0f1ac474-e8f788f62189f421a856.js">
  <link as="script" rel="preload" href="/app-b670b5ed3a389af0ed04.js">
  <link as="script" rel="preload" href="/component---src-pages-assets-js-ba78d988431646b4760b.js">
  <link as="fetch" rel="preload" href="/page-data/en/assets/page-data.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1011117294.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/3003422828.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/446219633.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/app-data.json" crossorigin="anonymous">
</head>

<body>
  <div id="___gatsby">
    <div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper">
      <div class="SkipLink__Div-sc-1ysqk2q-0 cRWHVB"><a href="#main-content"
          class="SkipLink__Anchor-sc-1ysqk2q-1 kOmocm"><span>Skip to main content</span></a></div>
      <div class="TranslationBanner__BannerContainer-sc-cd94ib-1 jJbcq">
        <div class="TranslationBanner__StyledBanner-sc-cd94ib-2 jIVPcV">
          <div class="TranslationBanner__BannerContent-sc-cd94ib-3 jiZNpa">
            <div class="TranslationBanner__Row-sc-cd94ib-6 nChYp">
              <h3 class="TranslationBanner__H3-sc-cd94ib-0 elpFuD"><span>Help update this page</span></h3><span
                size="1.5" ml="0.5rem" mt="0" mr="0" mb="0"
                class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBanner__StyledEmoji-sc-cd94ib-8 hTWLVy undefined"><img
                  alt="ðŸŒ" src="https://twemoji.maxcdn.com/2/svg/1f30f.svg"
                  style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
            </div>
            <p><span>Thereâ€™s a new version of this page but itâ€™s only in English right now. Help us translate the
                latest version.</span></p>
            <div class="TranslationBanner__ButtonRow-sc-cd94ib-7 gXNXMi">
              <div><a
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                  href="..\contributing\translation-program\index.html"><span>Translate page</span></a></div>
              <div><a aria-current="page"
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc TranslationBanner__SecondaryButtonLink-sc-cd94ib-9 kUKdfA active"
                  href="index.html"><span>See English</span></a></div>
            </div>
          </div>
          <div class="TranslationBanner__BannerClose-sc-cd94ib-4 dOewRO"><svg stroke="currentColor" fill="currentColor"
              stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBanner__BannerCloseIcon-sc-cd94ib-5 iylOGp cEauOV"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1 eZKsbu">
        <div class="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2 cEcQwp">
          <div class="TranslationBannerLegal__BannerContent-sc-1df4kz4-3 intGem">
            <div class="TranslationBannerLegal__Row-sc-1df4kz4-6 cJRPhR">
              <h3 class="TranslationBannerLegal__H3-sc-1df4kz4-0 kIfJin"><span>No bugs here!</span><span size="1.5"
                  ml="0.5rem" mt="0" mr="0" mb="0"
                  class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8 dRuawC undefined"><img
                    alt="ðŸ›" src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                    style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
            </div>
            <p><span>This page is not being translated. We've intentionally left this page in English for now.</span>
            </p>
            <div class="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7 kXSENe"><button
                class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Don't
                  show again</span></button></div>
          </div>
          <div class="TranslationBannerLegal__BannerClose-sc-1df4kz4-4 hMvMKu"><svg stroke="currentColor"
              fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5 iylOGp bhaYvl"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="Layout__ContentContainer-sc-19910io-0 mXCTw">
        <div class="Nav__NavContainer-sc-1aprtmp-0 iGuESw">
          <nav class="Nav__StyledNav-sc-1aprtmp-1 cpomzd">
            <div class="Nav__NavContent-sc-1aprtmp-3 faUCsG"><a
                class="Link__InternalLink-sc-e3riao-1 gCWUlE Nav__HomeLogoNavLink-sc-1aprtmp-9 igUcis active"
                href="..\index.html">
                <div data-gatsby-image-wrapper="" style="width:22px;height:35px"
                  class="gatsby-image-wrapper Nav__HomeLogo-sc-1aprtmp-10 euWmfq"><img aria-hidden="true"
                    data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                    src="data:image/png;base64,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"
                    alt="">
                  <picture>
                    <source type="image/webp"
                      sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                      decoding="async" loading="lazy"
                      src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                      >
                  </picture><noscript>
                    <picture>
                      <source type="image/webp"
                       sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                        decoding="async" loading="lazy"
                        src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                        >
                    </picture>
                  </noscript>
                  <script
                    type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                </div>
              </a>
              <div class="Nav__InnerContent-sc-1aprtmp-4 gjaVMk">
                <ul class="Nav__LeftItems-sc-1aprtmp-5 jUJHKw">
                  <li aria-label="Use Ethereum menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Use Ethereum</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\staking\index.html"><span>Stake ETH</span></a></li>
                          <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Learn menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Learn</span><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\history\index.html"><span>History of Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk"
                            height="12px" width="12px" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                            </path>
                          </svg></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\web3\index.html"><span>What is Web3?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Developers&#x27; Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span
                      tabindex="0" class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Developers</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\index.html"><span>Developers' home</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="/en/developers/docs/"><span>Documentation</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                      </li>
                    </ul>
                  </li>
                  <li aria-label="Enterprise Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Enterprise</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Community Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Community</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\index.html"><span>Community hub</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\online\index.html"><span>Online communities</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\grants\index.html"><span>Grants</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                    </ul>
                  </li>
                </ul>
                <div class="Nav__RightItems-sc-1aprtmp-6 kQWBtS">
                  <div class="Search__Root-sc-1qm8xwy-0 kNenpg">
                    <form class="Input__Form-sc-1utkal6-0 eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput-sc-1utkal6-1 kkfPkW">
                      <p class="Input__SearchSlash-sc-1utkal6-3 ggVPUc">/</p><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Input__SearchIcon-sc-1utkal6-2 iylOGp gFzMVg" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper-sc-1qm8xwy-1 eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div><button aria-label="Switch to Dark Theme"
                    class="NakedButton-sc-1g43w8v-0 Nav__ThemeToggle-sc-1aprtmp-12 dUatah hwxIMf"><svg
                      stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH" height="24"
                      width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg></button><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE SharedStyledComponents__NavLink-sc-1cr9zfr-11 Nav__RightNavLink-sc-1aprtmp-8 jEZlpP jODkFW"
                    href="..\languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH"
                      height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                      </path>
                    </svg><span class="Nav__Span-sc-1aprtmp-11 bDRFLa"><span>Languages</span></span></a>
                </div>
              </div>






              <div class="Mobile__Container hhdXUp"><button aria-label="Toggle search button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg stroke="currentColor" fill="currentColor"
                    stroke-width="0" viewbox="0 0 24 24"
                    class="Icon__StyledIcon Mobile__MenuIcon Mobile__OtherIcon iylOGp dUGGTH hvwyGc" height="24"
                    width="24" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                    </path>
                  </svg></button><button aria-label="Toggle menu button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg viewbox="0 0 24 40"
                    class="Mobile__GlyphButton gbspKa">
                    <path d="M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0"></path>
                  </svg></button>
                <div class="Mobile__MobileModal bCHBHX" style="display:none;opacity:0"></div>
                <div aria-hidden="true" class="Mobile__MenuContainer AJukL"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <ul class="Mobile__MenuItems gYetwr">
                    <li aria-label="Select Use Ethereum" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Use Ethereum</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\staking\index.html"><span>Stake ETH</span></a></li>
                             <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Learn" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Learn</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\history\index.html"><span>History of Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE is-glossary SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                              fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                              class="Icon__StyledIcon Link__GlossaryIcon iylOGp jfMIWk" height="12px" width="12px"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                              </path>
                            </svg></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\web3\index.html"><span>What is Web3?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Developers" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Developers</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\index.html"><span>Developers' home</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\/en/developers/docs/"><span>Documentation</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                        </li>
                      </ul>
                    </li>
                    <li aria-label="Select Enterprise" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Enterprise</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Community" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Community</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\index.html"><span>Community hub</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\online\index.html"><span>Online communities</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\grants\index.html"><span>Grants</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                      </ul>
                    </li>
                  </ul>
                </div>
                <div aria-hidden="true" class="Mobile__BottomMenu iYttIj"
                  style="transform:translateX(-100%) translateZ(0)">
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Search</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Light</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><a class="Link__InternalLink gCWUlE Mobile__BottomLink8 heSUpS"
                      href="languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                        viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                        </path>
                      </svg>
                      <div class="Mobile__BottomItemText hkZTkJ"><span>Languages</span></div>
                    </a></div>
                </div>
                <div class="Mobile__MenuContainer Mobile__SearchContainer AJukL gBSEi"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <h3 class="Mobile__SearchHeader iXlChz"><span>Search</span><span
                      class="Mobile__CloseIconContainer jmriUx"><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon iylOGp" height="24" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                        </path>
                      </svg></span></h3>
                  <div class="Search__Root kNenpg">
                    <form class="Input__Form eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput kkfPkW">
                      <p class="Input__SearchSlash ggVPUc">/</p><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon Input__SearchIcon iylOGp gFzMVg"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div>
                  <div class="Mobile__BlankSearchState jBipln"><span size="3" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji hLjau undefined"><img alt="â›µ"
                        src="https://twemoji.maxcdn.com/2/svg/26f5.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Search
                      away!</span></div>
                </div>
              </div>


            </div>
          </nav>
        </div>
        <div id="main-content"></div>
        <div class="Layout__MainContainer-sc-19910io-1 gqazVg">
          <div class="Layout__MainContent-sc-19910io-2 kCJhKM">
            <main class="Layout__Main-sc-19910io-3 dliKfQ">
              <div class="SharedStyledComponents__Page-sc-1cr9zfr-0 kCvjty">
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="assets__HeroContainer-sc-4yoqxh-1 EZoBf">
                    <header class="assets__Header-sc-4yoqxh-5 bsOmTv">
                      <div data-gatsby-image-wrapper="" style="width:80px;height:130px"
                        class="gatsby-image-wrapper assets__Image-sc-4yoqxh-0 drhuTm"><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAhCAYAAADZPosTAAAACXBIWXMAAAsTAAALEwEAmpwYAAACp0lEQVRIx6WWW4iMYRjHZx2S2BxrS6Eo7rSNCyHZUhsuHFNkXbhS2Fk7hmUddtnacOWGC0tSlCsSV6TU7s7OzsFhl7UkJeUwuCCFcuH/1O+tt69vxsy3b/16d7/55v8+53disfJrHPsqUR94FnlNFF3i0liFatjXiG4xLDbzbHxUsbmiVXSKgrgvpgfeqWo1iRSCafFKHKk2lu7kuGgXzeK0GBR5LF1SrehUsU8cEglPMCteiCvVWrdWHBVJ0RKwMCNeiq3/S5ATm4dlJnYwRNCsfCoeiJmVJMgS0UZ2wwRzWDkqjpWKpTthKYlwYsYBBLOeoONxuQ6qFfspkyR7iuScxKoconkstgRdLWXdeqxzsUtg3SlxS7wRT7A064lagrYFEzTfi1szoucQ6hfPxBfxWbzl/yyCdshDMcufKE2eZefFPV7OIZhDzES/iqJ4J54TihFxwgnWE6OL9GqO09PQT3cUESx6wra/J5YFuit2wSuFjCdkDJQQDPJJ/BU3THCFuC6GcLPXExsoY6GF4KP4IX4ittjP9g5cHkawr4ygCX0Tf/BqnV8xy8VCHswQx7326oOC55pZ9pukpDyDdooN9sc0inejmMSHNp56CIMrjw+49l1cFnN419y8Ka75Pb2AtFsNLvNO3STuitfil3gkGvhsCr3sarIu2CkNuGsTeTeH2JrMKGvxDtpOMY9QMo1ho8wKfJc4zPhqw8Ja7504FTFK65lYR9hwcFbW0X5J9nYsW03xD9EdaVy9jQehMzFshCUZEGcRchM7z+iKV3q3bAlcAZ3UpBO0229PJXd0jXdJ7SWeCUbYoDcDe6Jc8ovIuBPMMKp6uXci3c2NWNmByyOuG6L8HLE1gVLqwtXusfwMcV+aLc6IO8Q2sqAfI3N9ZSVx+wf7hd4AcxLNJAAAAABJRU5ErkJggg=="
                          alt="">
                        <picture>
                          <source type="image/webp"
                           sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="80px"
                            decoding="async" loading="lazy"
                            src="../../static/6b935ac0e6194247347855dc3d328e83/6ed5f/eth-diamond-black.png"
                            >
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                              sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="80px" decoding="async" loading="lazy"
                              src="../../static/6b935ac0e6194247347855dc3d328e83/6ed5f/eth-diamond-black.png"
                              >
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                      <h1><span>ethstake.exchange assets</span></h1><a class="Link__InternalLink-sc-e3riao-1 gCWUlE"
                        href="index.html#illustrations"><span>Illustrations</span></a><a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE" href="index.html#historical"><span>Historical
                          artwork</span></a><a class="Link__InternalLink-sc-e3riao-1 gCWUlE"
                        href="index.html#brand"><span>Ethereum "brand" assets</span></a>
                    </header>
                  </div>
                  <h2 id="illustrations" class="assets__H2-sc-4yoqxh-3 hAckNE"><span>Illustrations</span></h2>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ethstake.exchange hero</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:42.083333333333336%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAACXBIWXMAACE4AAAhOAFFljFgAAACJUlEQVQozw2Q30/acBTF+8fucW9m2cOSZVlmsizzwTEzzbYMdUbFregEYpwsRREcplSUoRQKRX6U0lILfttS0Hv2fTjJfTjn3s89Qr4R4rrh4/eND6nkIhrV8eTpFZKHJjp9H5dVBln1oXCP2vNRuHAw/7GBuRcVxMQ2LlsM+/88ZJpTSNoUAmYhTB7smAGOtQl2T2yIYgejUYieEUDjRvuOazyFO3lEpT7G6zcqnr+sYmGlgQPlHvZogocgAKYhhLjMULxhOFcZdNNHqeLCNDyw4AG1pgd6JFRrIU75USKCyon2UiakYxt/zwbQewz5uodkiZMqDMJeKUCx6kG68pAuj5FTbGiai9uBD8sNEQQzrCc8HB158Dlh3/LR5TKtMZSyg3TRxm6RIX4RYjPHF/4Uu9ja6UK5dqHWbdSbY3TaDDflIXTVxJBNIBU8XOQ59Zih1XJRq7nI5hyIiT5Sp0MkMwZWxRaWxR6EuWdl+vy1Sa8+6LS4adBa2qZ0ZkDpRIsUSaeCfE8n5w5luORSm3LygM4vLSpLKv3YqNDyd4MWYgbFMxalpD4JW6kh4vtDLEbaeL/Uw3b6DlsHDgr8lSrvt3TKO24yZG8DxLNtZDNd3GoOWoUGttfqmOe5d9E+Ng9GEP/cQ/iVZfRlx6HIqkGrMZu0szr5lkW6OiU5yahSDejamVHZmpF0xUhue3RYHFE0MaTIxoDeLnVo5VufPvE5eezRf0Q7LGm2T/uSAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/28214bb68eb5445dcb063a72535bc90c/f51a3/hero.png"
                               >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/28214bb68eb5445dcb063a72535bc90c/f51a3/hero.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://liamcobb.com/"
                            target="_blank" rel="noopener noreferrer">Liam Cobb</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/28214bb68eb5445dcb063a72535bc90c/f51a3/hero.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Doge using dapps</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:71.5625%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAADxUlEQVQ4y4WSz28idRTAH9DERL2ZNTHxYvbgzWjUP8BkY2Ji1Bg1uptsjDFq3NNmk71o48Fko7urxjWu3bVuW2hpC8wMFIYCM8OP0qFIF2jl11B+SFugUH4MMDAtUOZJe9GL+r289w7v8z4v7wvwj2e/rwPfIgmu2cWngoTnWXFzDaosCyr4WuP8yXmeu+sA9y9O1blzAYhHm5DYap7FdKINQrwFO8n237DLb/CQ42PqWmYX9sLCy7XMflaguA/TpPPNYnBrqRwvaBG7gDjUGG9y/w/0rnah4I+q9mO7kODzTza3t3tt3oNykMNGwI+5jdSn9WQGmglBc+9Lz38DiakazN7fg006pQqRQXDc9T0jHxzI/fQfisAEj0vhBLZ3sp80UlkohoWJ5x6/9u9Ap7kF5HwN9DMHsOWtT2xSaWCJ4sxAkrFSlk50lsNBJt/DUq79PaICcrM/IW/heHWEGx/R8PuPpCowRavI7zhgpjgAt0cCiqgBtyypXZYeWLTSW4zhEDtCCkdjQig1OClVjjHqPYgCJFW3Jkcg0NGJfcqqQcWhmrn4K8xdJcYD1mB20jG+rF2E8NIAkEJgbO2PWX1bdCxLGHXsjJT24VgEFRz0Rm7TPrqXZTN7q/FIkQtCyWIFPKJBd3n6Me1V4gnEMui+GgOdREvtIFtgMzbP+8xdDJiOxo3SaM0qK4lIGwu5YxRiXcVlEEd+g4z+OfFK0mS9IszbyLKB5KuUJVmy0LUs5fwBoy4A3Z2ySvtzGZYfVN9l9J1hwChvBixdOUINkDN1FZpojRhKwnWT1GeXZJnXZzoNC4FFyoYd3o1yyIPDiAcrLOtHzAN47G3o1FEzNqyuGJqKWVv5/CHRa4bGpj6bpPAeGYPeI1wnOxmTSbkeNsewT2pPKtTKsOL2nLR470B6yGOW9jGnh4LpO7vq08QwW32ftXXQSbXQOiPiqq6JHnsH15jeAc/JLWauJjoW8nTM6B91VojRHmnHXXJVkYRkv1fax7gzwp4Bv51MqC697tecFtRC/cUx4JKNbF4n9bXbdkPtbXgFJ/IEd7NGWTC1tIopI4PHe3nMOQPDqt2Bw2IOh/USpt1R/xnwm8kEvPeq9+xfWZcbEPL3gV4RwbRwCHZ6BOxvEUjf0z1dmDNeC03ThqjOlW5nc8qfrnVMWtaVaioXaWQLt8P09gsj5Rjgxhdx+OA1H7xzwQu0SVRveI40dkLUWPR1tXP1RM0vxtSidgq6Rh0cmin47MLKo8yD4PMb8/6LER370qlIv1wAwRc7k/oLU7nwtpVa/FsAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/5dea0acbc8484c42006d7bbed32fa019/366e5/doge-computer.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/5dea0acbc8484c42006d7bbed32fa019/366e5/doge-computer.png"
                                 >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/5dea0acbc8484c42006d7bbed32fa019/366e5/doge-computer.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Building blocks</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:71.51041666666667%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAAD2UlEQVQ4y22Sb0xbVRjGn9sSYmY0mQnJ/IJ+cFuixsXpF52ZibqYmCxBE/1ijOC+jCBmgy1zUKCjwEYLlFEoXVcoA9ryv6V/KPJXA0zImK4sY9PJCm1vb+9tKW0pUBnjeC4mmjjf5OTee3Lu733e5zzAf8pQ7kNPncB01/DoqA6+Z5EFX5AvBZ4rmw/sV86xqLH68HYFC7Kwhc+LQlDMrjD1HAsn4ZCruIOnqq0yAKtmFf1XIzDXhQ6PZXBp2pvBN81O/rvaWyyMyxsMjv8GQgiyzgcz5Te9qPEEGL2fhWqW/X+gjQKd2ihs9QKKC7w4XRDYX1LJfnLfQvbOGKtDsDQIJzUWfr30tu+VKg8L5RQrqZ72Pw3UVYibz+JAWieGXTuSvjmC0nLf0ZzSQLZNyb/RUC+Y8dZIulUn9DVYBFL9Czt9CG1MTvHPqBxfYf4BffjaMM7nLODjIyM4uK8bPzn/TPuhdwNd1yLF3ZrwKVBxV0fZTzuKhAyHUVA0aXlyupxLXXJxRL0QaNAFw7gmxP8Fqku8eP/QEH2T49FdIjFpw9Ar2WP2jjjpb1kbq6p9dPAw8aTbjeESgyFC8qu4nTwFt3tGxe1cnuSJao79Wn2XAz541Y0vT8xgXfjbH/YhkTy4TdDewJ8Y7tkg1rZYf0td8OVj4BnkzKCmlm3PpbB8Jb97VsXv5iuF3bwrwZRswC/76oIXUF1cwqmTsxRVgjHrVrrLso7ORuGboa4kcfckRWBjX+sa1OeW0HrBD833y+/WyP1rhZcCJO9K6HFReYCoildmDcd/f16X9wdwZ4aIPkkeeoi05/oqyr69/xJVFhfVOUyJJB2Z7dJF5Ner2RdHWhM0UpEChyZCblwObtfL/U9oXsmgJrLdrgoeMSnpyL9OEXTrV8F7CVpqOXHMxUl7itg7EylHZ4LQFaKqBWrBF7bOODymbbj0McdIS5y4tNEt2oTYddHCYUMcdE+KqaFtmJrCR+lPtonBFKHfhN4umbCliMu8vuI0Jz7rN0b3DXbE4TLGpWZVCDrZcqbbEAtNtCeJoznqEkMuVqsigL3EO02J+Wn3Y0KjQoZ7kovUN631RqywqcKXMTawCaOaA1XMTA2k4NRFpXYa+t56IYvC/Poy34HuWh69akEiNsOtSSIezh63bt2ztcfOZr0z+Yy7KwmqlloQxOjAppSOy4iNWXr74lNcyUWCZtlyRlsli8GmVWZIH4OtcRXIlHRg3JbakyxCzmV7QH1Mo8qk5uYwMzf+BPM/Eshy76Gl3A9F7iIaL3rxYJRAV7IiKmPc12OYMm/io9fd+AtF9mOcLxTuOAAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/810eb64d89629231aa4d8c7fe5f20ee5/52fd2/developers-eth-blocks.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/810eb64d89629231aa4d8c7fe5f20ee5/52fd2/developers-eth-blocks.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/810eb64d89629231aa4d8c7fe5f20ee5/52fd2/developers-eth-blocks.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Enterprise Ethereum</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:68.48958333333333%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAAEHElEQVQ4yy1UWU8bBxAeEzWqqqgSb1WT/oI+tGqVqu1bxUufIyVV8lC1Uqv2IagVLUJpSERCRJICoUm1uBhICHZMgvGu1zbGYGwC5tjYhnDalMMmvtfHetdrr4+1PcVJR/qkGY30aeabA+DYfr24BaO9Aoz2ZEF5I6lQdSUbzn+1BqdgFRARvvli792SXHvt16HrPgB53gA5Gw0+jbHBp6EVSTMNqUka/rfLr8nU98Q6IQx0psA9Jzc4LQVwGHOXXM8lyfIs/dkszYOdFhoQN4G3kJA2kwrfYxJ2RylIGikFN0kB9AzFoGcwBn29rKIRddDfyZ7u70icHRtbbdg/EMBB88OxcAGfT3F/RYMSJOPFE4h+SNCkIk6RsDNCfbz5yPBhitLDMRRwsy8I7df8CoBm0Ook0PSIpvGBLKpH4u9blNzFFUrEeESqMoZcluxLfmp6wIF96PDEwxYKur/VvxMwTIWitOlIc5l8e6Z9HODa7QBc6fTDH9cDjV2qyHfqIc47+UTE4T728w2baN5dzOOqNVvwUAVcnRI0m7YcbBv/Pbn7aBy8I/qPjihrYY+ewRcDuq+9T0mAcWf+hG4pD/3jqXvkkojkQhaH1Qlb0yeOU7456QEzLVbXbFJlYzqPjDFjWNJnQDyMnzR36GCFmDxrnF+v0nsRXHAf3XKFUgA//B6A71uDjSqLGNE6RNQ4ctUJpyg/meH2n43xNuoxJznUXNWll3BSlbjEeuT6FBWvUhJQ06FzHlHCPbmEPl5y1DcAevU5uK8TmpTWIhLWYqXfnK+paB4fT3EVg1XEaU0KZ7WhyqJOwulR/ukrX+YyG80vxML5P/XzCbN1R8RZr4juoJCxv0x9AD/djJ25Ncpv9VsKqLKXKyqTgPefcftqdaA5dFiKM88lVL8Uq3MTKdxdzOB6qMQnElnEbAmNC5niz9pErZcUcGqZZx+Oh09D29/pL6+oBLw+xJdua7O1q/+k8Wp/UlY+ZZMOplg92BZxwxOsbTuPauweV/MEJdRmRHk2LlapCI/3haw8KIj4YCttN9dbBrgAbQSn69BK2K4u1FqH8uXWYanSQuTwxztZmTQn5MAeKx/GBNm9kinPmgJl5+aWbIjmyipfptwT5Ys9Yg7vBNP3lHisb/ONMFxqsr3VNiCo7o4JtUFTBgfpFFosr3Ca9OOdYRbD3gAu+cPo3DhAj3UX/RY37r/YxQkTi3bbPjLM1hQAd1zcJkBLd1wBZxDmVqLgopbPxRjPUGzFRXBuRsmvMcrQkptILy4RpNNLzK7uEKxjldiZ2SH4tRdEnHERKZdbGbYzfSM35t8z9y0A/NIVhQu/xRTLQVlx5PYD5n2Ape3jJ+B5g6IXsLIM+nIcGGQBM8dxJPImV1uH+l2j5IbzbTtQV/A/VqX8NdBegkAAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/bf78b49d7e23b88a7eea934225b0cf96/dd036/enterprise-eth.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/bf78b49d7e23b88a7eea934225b0cf96/dd036/enterprise-eth.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/bf78b49d7e23b88a7eea934225b0cf96/dd036/enterprise-eth.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Infrastructure</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:109.27083333333331%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAWCAYAAADAQbwGAAAACXBIWXMAAD2EAAA9hAHVrK90AAAFXklEQVQ4y8WVe0zTVxTHD4gadIv7R5NpotuyREeMMSZuM9lE3aAv2kKLtIA/nrblVV7aB4+WlpdAeehAQEFh8h4VoQXGUBBFBfExFZ1QZFE2Mw0+QNzUPu5ZW8yM2/5cspt8c/K7v9/95HvOPff+AP6vsWbNali+fDmsXPk+rFq1yjmHiODh8cm/LxgnxBmzVMmglseAWhHrok2NX6BJkb6RMs4tMy3eLTk6wi1CKFggpoJdQwN2QkSgACKDhBDo5wtA91fC11wZ8CI14O2b7hqV1eCaUlzvdkXvD9/kREOxJhJ0qnDITaGgQB0FGtVeEAaFA5cTDEJhJGzyigVVdCAoREHQk8sFaOk+D5fGJx32XTw2xgFfXAGy3Bo42UwtK8mO8SnUSvwKNOLgPPVuUUFGXHyWWqakdoWqeNygcEFA2LZ1W8KWRokjQS4SuDSp2QC1+j5o6TntrMfaDeKNgTEHvBILO+nhYcntMZIklIj3oFi0F3eL5EhRESgNZWBhIg8zo3mo3M3HhBDfRpmQA/BRBWRKGPOFdYyW7pGvFHmtc5V9lzHPeB3DVEYM1XaT8Oxem6Sw37pff9bKDc23qMJoZtMhf/OZfVzrYL6vbVDHxy4NRzFRRcHdGsrVCZxD1Nx+8AQP9V0lnbOPSd3d+7bikZu27N4hknNyiBSd+5EcH7tFqMRiEhEpIf05PqQ2nkbqk2jWxmRvPChl3YVP1QvXMaUAxA6cRWwbn55BXe8Nc+3kFGkx/UIaR38mh7pGsNwwTCoNF3F/Qx8yxPnI2UVhVwYLjytYxJjOtp1QMjBPRH+4nhbt7smOmAfO2GwNE8+eY/XoPUvNxD1sHpsizZP3sebibSw1DOHBjiEsqXMAdcij5oGtciYa0tg2QyoLC8T0hx94itw/o4fM13CWkKbbs8+xdMhkOeoAjk9h3fU7eGRwFEuNw1hmBxYfO4V0kQMY8hawI5WJOgljej09xn0bJ/K1QzvwzswcFvfftJRfG8P6yyasv2LCqt6rjpSx0jCCZfqzyI3OR9+QUOxSM/8GZD7ezJEu8eKJXjtEbPzpwSM8OnzdbHj6hNRN3CNlF0cx13iW7OsaxLzvz2OLyYQRqUdwp38IdqoZb4ApTCyMYj3Zyo97hyWQANgcDpG0mp7OYVHrAFZduoEHLozaEmt6SEJND9pF4ivbUF5Uh7zANJSFU9ih5qBeznAAiQOoi/L5wz8g9GOBMBxgxmqFabOlfNpqm7n1cPpXbVPXi4N6A7YZO4jeaMBWhzra8Vh1FWYo92CuSontGX5OoH2XidGRchT7JYcbsIHPDwKwmF/C1NyzhfbUF1/+7dp7O7TZ/t9W6bpPVOdgbWmWuak8y/pdZaa11a4ybby1QhNj7dJwSfMemq0thWXVy2m2gmiO1c9XsDFgZzDAi1cv4ZHZ7KylU+c0MFrms6XTvpOyYB88HOeNdYle6GjghmQ69mZxcLDAz/5Mw+a9dHs/MrA6kT3httZn6YpNPIBnVgs8sljsvUhg7Eq7y0wPzQ7ucxvQ8c+kh7MvVcTRzxyWel84mkAftsNHGmWMa0Ml/N+PK5kzegVzslXBulGX5C3v0AjhdH6Ii/Mcf5EQC5s16VBbEAZAyeBYbiQ0prIXxQ44XP/w13mfj4vdmlNpH2qDPn8v0NNjoTOrAQoOKMRQlaN8+5KtT2PBfnWgfeFq6NCyIXvdPy/iRe+uAIOWBaViT1AJt9hn3AH7pc53S5atePvj4TIBvBqQAt5Uwsl9fpBGeUF26HZID/oSMqmtoLbHIvF2aM9gulREb3epS6LBEak3lIh2wCkNGy7k+v73/54/AUaEKKCt+fV1AAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="../../static/a44134e541c72364beb121234ab5864e/5791f/infrastructure_transparent.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/a44134e541c72364beb121234ab5864e/5791f/infrastructure_transparent.png"
                                >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/a44134e541c72364beb121234ab5864e/5791f/infrastructure_transparent.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Finance</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:102.13541666666666%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAD2EAAA9hAHVrK90AAAFHElEQVQ4y51Ua0yTVxh+vvJvatRsycyyC9kWHXFKRWCAqCiUQgttv68XCrXlfhcEpFIus9ACcuuFshYQ2YQFRA04BTEO5WYVvOF06lymJotzy1yy7MeWTEHOTkui2R+T7SRvzsn5vvO8z/u+z/sCr1iF7CcvTJ8Q9uL8v5aqeDcI3U9QY8evIeNHgnJFCQwJeujT6v87YLlrAMVPCLIJgWJ6Bl23CKwxKpi0VajObkBJSs2rASZbWGz190VFUhAGjSL0lYTAVJbGaypV4VNDsqhxt/b0ARvwJuWtTzZioDKGOWUS406XGt8dSoRky9qXYF83SDw/oCY5FONNMt4QBZyrD8XZUTNv8mwOSizmSUNOMSnW2vX7UrphKmj3OWmSgLhLcb5Jyky1yOC2sli3joIe1kfB46mvXAi3jfOCH6kQvn6oRr3S2GNBoaM5Pr2m4S+BqIiUZTqIOc8R1VbagSOV7PuPB7Iw5iFTEc30lwvgKtgOCIM/QPfeSBCaq28PJngAY+92a1rr7j3EHpd9e56t+TdxWgUJjSh5pk00kwOFHb/gLduy251SgdsmN+VL+Ss0kX5ozdvGVCYFAn6+a/CoT8d7ciwF5xql2698phybrI2OkticHxW02OfjDaUkNC19IVydS8Jy0uZzs+uIMdM5iFXwGWuUdl6wcocJOUMJnUABtxm42aFivu9OxIPDmlU05EMzDuVRmAhSaxqaVTlWEp/S8GxHZj4Jz0klgnT9czbDQTJ0FiIKrnr3fg+7ZaKZnaUpC/Sk7lhVDA+TzTJmvEmKM3Xx7820yo9dsHEZ9zvVWA5/H06S3KHQGAmb6lyQplqfczo74ZQFv+4UJoU5yrUYt6jWXHMqR07XirPI/QqQp00MZlvlPGq4aJdHzDoUPdMtnGAoS41KPhfkiJT8Wc1uW9yl0C7KlXsWi5QxCxZRNKkNlNqcAg4j5fLllx3yrmGTuJyKhfFK5k5XIu9hzy7caE8Iu31QPTVsigs7ZZRCGx6yospfedURpiGO+Nh5qyJysUOgfF4fkETyPxYpSwJEMCduW0llUz/rkFvID5ThH/UMaJjMdafSI5kgerZ/VSMSvI1AXu5aEXb7CzcY+cqnBzYnkZZgzVMTX00MfFlbwWtJeDSsxESzLOiEMdZI971kIg/kehEooAKXlmwVBe5w29gBqq2dV3vFyF+tRhlfmle9SUHMASqyfxM3p9m5adkXRbG4d1C77Eqbwn7JLj9+uU0R4tEwNR4N2gc/9Wl5P/frKKg8hdIfmXOpBvsM0RumXfHYvzoVBn/ZwH6+nOSuj/VrYeO8mr1o5ypvtKuGh4yxewgZ895lijcutZ6n7TxrYawQN1yq0os27hS1oakWVug2JcGwNcbXEBKnfAcc+vWiNbc6E3oom/OjdXHWzqIdbxRyAXAWRiwVpbcs2ts+tIeZaQvruWJo1fMuWNmuiRZZz7SFM47UiTPdLkncWIO07pt21RztXfeUhbW4CiP85lxKXHcqmPnRTDRlbV1iSD96Eoxhs5jZqwzy0u/dF8W/ZOciqZR0Z2olHwKbmZM1Igl1HkNzF3D3c+3yYXMcHvfreGQyn74ZAHXwcuLc7EjAWKMEo3XxDCGj+LIsChQQV9sUXme0mhihADQV3vubXTrPyOM9G80Cua2HJmr9v+dh+EZf0HygWkfnYEoojlcJeTOtnM+0RcZMUTtaKQQtAHOuUcLQMHkPerX4eyQbvw+mw5AY/ALnH3RPUuUVwZl0AAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/0453c88b09ddaa2c7e7552840c650ad2/3f5ec/finance_transparent.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/0453c88b09ddaa2c7e7552840c650ad2/3f5ec/finance_transparent.png"
                                 >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/0453c88b09ddaa2c7e7552840c650ad2/3f5ec/finance_transparent.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Impact</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:90.20833333333334%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAASCAYAAABb0P4QAAAACXBIWXMAAD2EAAA9hAHVrK90AAAFF0lEQVQ4y2VUa0yTZxQ+pc79WLItLpotkZklm8s2/bHhYnSLm07nJe7ibk7UiTqsyKYIIqhYqSCUtbXQFumdttDSFtpKgVJo0XJroZWLpVAYeOMmCBuCIor0O/vaScyykzx5kzd5nnPO+7znADwLdn4NyMu6QGXtoxRW3wQGz/DKG0uXUhy9CPuSUiFDaIar3QiJ6fKXo+OZi86wNOSdJcRdv/VH+F+Ym6dAbGynSEu9kF/uX66vHfZniizf5erdF6ISUxcnZspWik3t9Et6D0de3uVFxBBvV3QSMMXW50JSQw8ozDchO7+JUlw3+mJtH4K+9i5L5xgaMTrHKzT2O6iz9LRKmnvu6K4OYUXbI6PZMzXzh8y2RaBtApGhfaGkuAckxX4Q6joACi2DoLXdo1zSeIGvdgFHaV9c6r4/VlB1w2VueXCv9Moo1tc9QPXkJOqaR7DMPXWbTHRP5xisekhWyVXUQ5F1FArK+yH/ch+AqNgXJjN1A7/QsyFLbIviq5tpBZW3kczcX9I4jrW2iYCnYTqgxkeBgv4x1NuHAyLD9buqipuPYxKzI/L0rXGFlv5wTdUwKdhLAba4nsqRNAIz98qvUlMXCjQep6CwdS6NXz2sK/Zja81DosIyQWiHpghlYJIQ2H2YzqkcFOq8TwUad5O89M9HZ5mmFSlME1zgWSkQd1pGPXQ0B0hskhr9KDb4UKTvnMvJd6PWMEbwS2YwTXE/UK9/Qjia5lDdMkJkK90E+WYBWWk35mmvT3wUse3NNWu/h+8jT1AgJkEQ9kt0Buzefz6Cp7o2m6tpxUxBDcGWuAi6qBtPKyeJTM1jvGZCbCycIdTSbmRL6jE9uyog1HuR7G7gnbfXvBqxajtEHc6kwPYdMSG3g98giaHtLLDcQWVFb4DBqSTSJD6CIfbiWZ5vqEjSN65T96FM0kak51QTRTUDTyUl3XgsWa51tCHcnkbKhs17AQ7GskNiQcSfUbVyRC40NPw1l6NwYSq3LkBXjWMiz9twWdrbYdeMYVqWNSA2dqL+6ugsU1CLv5+U5Nd3IHgHMWz1J9sBIvfTIVvaEsYRNUNymj4x/aIdhVrfnNn9gNBYb2GGwk9kFM1gs/EJyhXeAE/dhuQQENlyD57nVGN8inJbSsZloGeVhW3+6iBA9FEuHInPDbXNlbQCg2VRp120oUjne2pyTiDp4hyD33lDJfWP5em8qCq/RQgK2uYYbCumsipS0rk2ECi8Ycveehc+2/gzwIEjLIg8kApsoYuakeOApFQd86LUiZIS/7TE4Mccpdu5ZePhxQkpqpVcuWuEdBVlxp7HWZcceCxJvvvkOV2wOmpZHcLadd8CDAwMAC2OByW2aQpP2gGJdI2Cr2pBq3cW7V0EGp1josqWWXCTI2l233dd6UYs8zxElrAB404pjjNY1cDKc1I3bN797ywHBXNVXUFzKDt+SoAPV216KfZEXoKwuN1GjtcNmdm3S2HpDJmmsPSkkEujj6toNB+M5ewh6ZSgRrCyULvzgg3uIdh3mAtRtCzY+jUNks/r4Nk2eYEgz34Sqz/9cv5u4d/kSboL4cuWw/sr1sDnm3bBui92PhcMRtwpJdCO8iHqEBNSMo1UkhzKPkuSn5BYv+WHeUGYQQw7fkZJjdx/DoJd7aNlPF9fQcF5LFiwAPbE0OGbyN/gvZUfw96YsxAdfwFikzkQk8QGWgITdh44AYteWwJLXg+H5R9EQHh4+H/26j8fvxKTT/pDjAAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/4d030a46f561e5c754cabfc1a97528ff/843b6/impact_transparent.png"
                               >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/4d030a46f561e5c754cabfc1a97528ff/843b6/impact_transparent.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/4d030a46f561e5c754cabfc1a97528ff/843b6/impact_transparent.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Future</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:78.4375%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAYAAAAWGF8bAAAACXBIWXMAAD2EAAA9hAHVrK90AAADMElEQVQ4y42SWUiUURTHzzczEVFvvQT1UkJvLVBJmA+5pRLjhkKSFISijdqM4jKOWy4jqeOSWGqLkkkmRWpYiCvmMqipo+OMC6PO5tpopmZ+3zd2OzNaPYxSF/4c7nfO+d3/ufcD2GcpGhnoqqJhsIEBVRsD0wMs6EfNYFSbwYDRoNrRnkuvZLCYxUIWtAqGqxthedjM0yvNPGzmGccwqlku7lEsZ27CDBbNju8D1P0GqliYUdCAwD8OLLK6whwCUSz8H3AHekSroIOwSYAgISoOHSbjPhOVg66LcF86O2Y+gbIcRO0JxNM5s+PbeOK23fQQzaKIdpglMwrGGnUWjbAEHRKEEnR2yeIOgZx9gGZqbmIbx9g+hRDT1CBtxriFMBpBViFsC+MWAhmEnf8XkDM3bgXaaYcZenrQ4nDX3Yh5V39d4rgXd0fe36Hl4lHH8B7bUJ91SrYLD1Aa1IzRqGYn8Tra8Vsn1sjxQU7vPsred6hsJbA6S4CQ7ygC9CY5RBjip1Ot1Q+2LGv6GxcVhtHNGMwdJuvEWkOIkzXuub4tbVqTSwsb1PraD1hZXj+q16/JFb2LpLFqhjS/1pHOZgP7aULpssCuwtjMF6qpswl7ftrCcseGodCogZAXTyGjr5ubWvPu4ILCBHnvW1xb2w0r8vp1MilnNl597Kk4m3DXHvgnwU+axg2S5cHN7GxbYJlpDmrRXVhVBaTKOyDocQE4PpHBtQBvQbhIPPWy6C0pKazUhkRF9d2OFIQGpCWDozge3BPElGdyoi0wX6OGZ6Z5CKssP57W25lw63lJUWBxXkFgbtaAT2zEqldU8IK/NGWEHxt370as8JJP/gORhyTe3iMxATxTEm0fpQfdMShBdaWjsO6NhC9NzeVn3S/xlklLfYtk5b4PZQ1++TllbnFC8JJJ+e7ieNXV0NDLzuER4CoS2QKbEDa/A4ToD3Xgk5MJAY/yIbihGpK+TgONuQyTBq6EhsO5Cw4HQjqawCUqGtwkSZRHerrtyP1k5zdAICeytoZ7PTOF51+cx7tTW8mJGWqhLLlUTR/lLIigzjg4QTfunYQiylWSAh6ZWTa8Xyt/bB7pYwd+AAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/754d2f72ce2296fb59d9d974aeda16be/e2b9b/future_transparent.png"
                               >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/754d2f72ce2296fb59d9d974aeda16be/e2b9b/future_transparent.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/754d2f72ce2296fb59d9d974aeda16be/e2b9b/future_transparent.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Hackathon</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:66.61458333333333%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAANCAYAAACpUE5eAAAACXBIWXMAAD2EAAA9hAHVrK90AAAEDUlEQVQ4yzXUa0yadxQG8AMucR+WLFuyJV2XJu2Wrt3UrmvTWtd6m2itRauo3ATk8oIvAgL1VqiiiBcmqKCoRUEQsVrEuxtemGZutdFubku72c3GNG7JLkmdafZBBf7DJTvJ8/WX8+QkByA88ckMQAhB4oWThIRzb0PalXNvfnwhhvl+dPRbF8/HgCTtKGwu9QP6+ydoqSkC0qUjQEk7Ddmkk1BwIwYyk08AOelE2FgFkOqm/sPeCMMV+s8iyPGx8EncmVlywhmUefkDD+n0S8BOOnb82fJg1A+LI2SlkKImJ7+nyE3/EMsmRbGo1z86Sk4+FQZPERDaAEjJKQbXCoJS7RABr50Js9GRtPSL31XRziJFZtTq1Ug40qli/L5+fwb99mQFTQ50oroKGTJobqIGlQz1mRs8Ue8eh5cJRMKKbwhApHJDkeoeEVPdA6zcfoMtsf1cKeLtNzHeQXhu+p6yssNUI8rYpooMqG9g8sA/4d8fvfsosDi9vjfl2UA9Xf7Fw4aHMerqAXCNF2jFrZCYznulwfX0T3ndLBLw1MGy/NgQM78YSXVfhpRlquc8mQvN3/81ODM8Gxy2/4KWJx8Hpoe30Z2OlYX/QX1tA4Cu7wmxzr4B1V3fnjePvdhXaOeDbMwY0lgfIWWlLSAvr0eYSoWYxaagd241NOPyB51NfyFv2x8Br+k56tGv+HZnw+AWguaaZgBZoy9C0bwA8sa5dNw0gWhiU4iNWUJVxpngoCoV3cITHjOkki2Box+5f1wLzrvdwV79SMhlHA/0GyaQVd+9doUDEUw5gKGmHCAWY0CqRgrxGPPVTINm92pdLZIrBcHesmsHmttNiFuoreazyOUkrhCZHM69VZ8j4POYg/4xy344yNlRvSPlMl5XCFhhUE6Asw+MIGlyEgVGC+RaDNNkowFRONlIKq37B1P791IoquFPrc3rC95+9HB+PLQ0OoDutpvRSHcnaqlSIbel5XNBBh/k2aUEj60VgMu/TeBwdZCRSH6NySvKy2Lyq2KTOILr1IqOUrWt+1IK22E0GyZ3Npe/3vxmdnHc3uWyGfQ3e/T6HKNahXfV18c5W5rB09VOPDwMsAQGYPINMXyxeReXtiGh5M4BR+F4mkMvqqfShP6eNgsatZt2qhUY0qtlB0aNYldXjm/JCqmua4lxx6okEtAqpcQpuxV8I+ENBTIHsPBejCtxLnLEPV8U4DYLXTuIURLEsPT9Nrx49kDiG7K6C3MyvTgjb6OInvdQzMz/SlxAXRPRc3mCvCwQ0SnEW2IhlPB5AByhEQowE4jkDmi1bgKvZBhYjWNAT8WJ/XOb4Rpb4GpvhJykRCDFX47cWZ4DbQkOxQVUwGgUkLLphJJCBmSRkg/fAvwLhW79p19VhEMAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/9a6e158f4ffd1cb5de246a3ecd0d7f86/d7270/hackathon_transparent.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/9a6e158f4ffd1cb5de246a3ecd0d7f86/d7270/hackathon_transparent.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/9a6e158f4ffd1cb5de246a3ecd0d7f86/d7270/hackathon_transparent.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Robot wallet</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:132.55208333333331%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,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"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                                 >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 gbAtZo">
                      <h4>Robot wallet</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:132.55208333333331%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,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"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="../../static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                            href="https://cargocollective.com/willtempest" target="_blank"
                            rel="noopener noreferrer">William Tempest</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/0feeac3f7182fbfa52ee3aa146840325/7ad5a/wallet.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Ethereum bazaar</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:84.02061855670104%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAARCAYAAADdRIy+AAAACXBIWXMAABYlAAAWJQFJUiTwAAAFC0lEQVQ4y22Sa0yTdxTGT8uyZNmyZXExc1myxc1tbl+WzcxkC+J1glowMLaAVhEpihRowYIE5NYSQaUXuQltLS2XQilocdxE2iJQilzEoSAIWMe4tm/Liogtr5y9dNm3Pf+cD+fL73/Ocx4ASidPVUB6xjQEH6iA4IAq+pfvsOB92AfBeWZ61C9ttO+38sGHUQy+kqsQ1j5K891WRvt0SxIcjLkDIkTwbxiDi4g0gK/AI75gDLicAQj2q6Ad3ieFrz84+9Fm2LPJL6cDQvbXwo4vBN67DuWfPSK+9N6ZwafwzbtZsPXjJL8d3qIm35y2+sO6gZ+5FBip8igj8zEEBWnowXtVwNgt/cmXqSGCNI/mfrVMf+a/uywsMLwOQ+qfILNreiLKZtnk/WNhZEBgFaaX/YmRD5YwyDiPfvUTiccHVwGkUilw49vhdKILDkE/cKPv9bJqJjDhvhs59TN3eGzTMKfuGfKmXr6M7LfjyZYJ6Zmw1uE0sQWVXehiD9lcx/sIDLo75/ApaN3smTCWY4ZWQz1tY+Qc/rPulOa/kN/pwnTNTFeheP6BqMWB1/pda5eGVpDdNFZTJLE+kdU5sbLD9Tr5oWM9dpjA8O6Z5SMK/SfAiYuDG3IZTSCIpTzoflOQUdtapjNj5tDfeK5tOoUVqtYml5ixdPalO/shgdGmER9ejEHFFw5hS8trsrFllTw/5MCIDkuteMNDsVhMT0tLAx6Px7il09lMHR0o03evSfpeoKRrRsEMlhsjS82YNO50Z04uYLhpMoSxJ/f3aKEB2+wu8uakcy1l1IZxvZaalEUK6L3LG37YufOtrCz+REnJdVTIy8nrFU9JYyOiSbekFF5+ZDyWZ0Fv2ZIrtoNATtNIVHbKQ2N64yRKlggy12IlU8cJZJtm+in36JCYmAgZGRmbo6PZU9fyxaitbl2/prOQApMTcwzzikqZ0xgmXsC9VTZXRJMDf1ONsVTFDv3FW1YMabaTEU12ktdLYLx5pocC0iCOw9lYd3vSheRHuVey0uuq76Kged7NebyIUQNTCm0ZaQwX2tGPAp6qs6NP3niERunWZ2rsGK61krG3bWRUO4Hn6ud6PCmkpgPKw8sxMXGX/xg1+DTUjaGgcWmNO2xF9r0puebGK0OCagkP3iJcgZUEMsrHWRqVW59cQuBR0SJ55raVvDBK4OmWhR5PZJgnTnzOYrEmNiJzv88Yf6f5KQqUi8uJ7fMY0zyh1SjWOzN0dvRvta2ENNmR1TPCqlWu6zn5DjzInyNPq21kotGOnIbZf1dm+PtvCT12bG+BRALq6upvO7vuBV7NHt1eqnAmXBQNRiiky7dzKhfxvGEBOWYCj+senNWq8C5PYccA0Zw7rMa6xqaAsf8BA44ehdDQUBAK8+harRa6Td1wRfAErquWIfemE86nNr8tLbLtksmXk8UV9r54sfmEWuXqvVBqxcP5C+gvX1iP67Mit3O6ywM8wmAAk8mkV6krobZWQ+swmrxyRFP0IvWKl7DTRUtO0YO00AqKchcAyuEkfEiX5D8/UFziEAuVxGBOteNFegOBXN3zxx6gEhdBhVYow3lP5VWOQAL0AN9nBPJ6h0FYMg6FpTa6rIb0AuqMqfALFBTNQqnCDRt9o3h1W4V0NbqgcC4V3qDe/0lKgTdKTn0mgRUoEMxC9qUBUChXYft3+6GoeI4uLV/3Qj5Cq/gVqGVroNZQRzUi/AMa3QM4mxbVkwAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/e7a074a56d991c4f9e65857bafa0f053/4e848/what-is-ethereum.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/e7a074a56d991c4f9e65857bafa0f053/4e848/what-is-ethereum.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://viktorhachmang.nl/"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/e7a074a56d991c4f9e65857bafa0f053/4e848/what-is-ethereum.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Ether (ETH)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:73.07291666666667%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAPCAYAAADkmO9VAAAACXBIWXMAABcSAAAXEgFnn9JSAAAEqElEQVQ4yx1UeUzTBxj9gDnNlm3Z4o6YLEsW42LMsiExS8ycY9PNTJPN6TzmXGQynA7nGCBe9WKIYEuBrhxllpbeUGhpS/tr6UHBHj/arq0CvcQCKgWkhSIYKKPfYP+95L289+XLy4ODpy7D+Xo+FNVw4XcaC/6g1YL8nzBotS0paEdATzRlXNW/LtYzmLrgiADOJQERU/JzToNGZ0xV6bVpHLUo9cH8QxjFSYBcWgNc4Mrh3G0pnChlwokqHtCbJCmthgqYtj+ARef4uqDGqR4mvOlGkXrFDHAJIWvvITBbSFAaCOB0CqEv6oPQ7BBAZFlwMI8CR2r3wambjDXHbjBefXHDbjBq2W/gPMKcZ/ToZO8DfGTx0e9Ku8FjcW6SMLmcLz7OfIHoNGe0dLTv5eoE6StB5BAJYFoGfPdgWrXaBteb2nmVEpV4hTRYjL6x8OOts12Dqjjhwwmjb3xK6TsadgR+YZTScfvWbdmk0zVgc7qQLZPqFN0eIMhQGhTWSVNPVIjgQCF9+yUGd66GzQw2CWj9klbeI4eFLEwEnixMtXmX4hofxrsGdTYhcYNdXoP1f1YtyOXqxWalGuu5glhOQVnm8YKbAO+9XwofbC57Pq9ccr9BaUCOmL141ytHS1fTSJdUuTQn68MpniM5YwzisNg+X00pW2LTWElnixErqW14iyWOWbs1WMbgYsYnh/fDR5//BFs+PbqnqFKImoFQgrA70EWKZrrVkp1Oert9tM6M0b9tyZjRjyNcS7y9WmRWNCoXm6uakpTzt/FsSYO/lyQCbKEcd36dK4I9Jymw40jelVKRFh1jsQW91YhmfU04/sSxeswUuPLUGsbpFk9iqtmNMfXAgEtGNtraXWiS3Fksvi7Ai5VtWp1SrhBKDZhdWJ8D3MAE/EZVygWuEJoCDxPBES9Gwh39Imvrqsc9oY2ztqFQ3DmEU90hnDUGL+mbyWpjmxdtmvvzHK4dKUxpvsNGasRqK56nahpB9jgBJXLSLvYMomN8ZsHtd6LbymlfinNglNUL06r+byL6exgx+ppOM0iQCUiGsrkPO2RBpDMMY3syv3yrbzhqz79Kx8xdP5b8X9QSuTMgujeM9slnCx1dBLZyr1MUvEvAVV1NQ/+/q4aEtqyHROCdHmICGllWDo8/gK1SP95iaIcq/2r4UNbZi4dzCmOvvf72BgDIeLlY5okIgxE0js6gyUOOevXlBwzCfDB1UNOQnALzsQog5ARkqyufv8XuucNkeWfq2IHE2Zut0wfyL+eW1QoVdVKDW24ZTIctu39Yc5Zrdt9e/mGbfxgFeu2wQni1olzIXLNy/TMyvHrSfP8lfIrPVfGJd0tEesENqq6TUqyIHMuj8TIOH/9K5fL/zFFY/CyJ7jP4rogJ+wppG3OovJMFNexd1WL+jtysrWtHut3rsT/2fdIXrUoGYvxkMEZHf2xvr3JgcwlVv7/oWsuvBZTmdNccAkvUqazlEb3FNO5G2HaIBjuzr8EpKh/OsUVAF3CAdiVreVUwbenexJvoi65P+mObMBDbkOyPrsUErrpQZgJGnRmKqXI4c5H2Sg1H9+2ZgvK1LR0e+A/osfgRl2vKNwAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/ddb9a22d53fdaaae70c0a0d94577f2aa/66201/eth.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/ddb9a22d53fdaaae70c0a0d94577f2aa/66201/eth.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://viktorhachmang.nl/"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/ddb9a22d53fdaaae70c0a0d94577f2aa/66201/eth.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Mainnet</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:74.37499999999999%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAPCAYAAADkmO9VAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEbElEQVQ4y12Te0zTVxTHv6XDxClb5iQqSFqIGdEi00QIIpXhgE0lzsnKBCoghaL1AWVgsUPxUZGok1GmTnkorxZawPJqgSJIKQzRVjfMUNxLpzPKY6IuRRDOfjWLW/ZNPn/ck3O/935PcvBjowJj1/KgyRKyAIJFEwXaDpSXbIJOJV3Tok4Utd/MQmGuBJWHYjztPW37OBjfDQy0boX9bPhKzPrJlA9r4xHm4kHC7SY+K+FNF9BQNPo0OyOuFq4/1Cn2nGc1xI/fao+hzq83C/TFuyL7m6PHB5vju82HfReY8oNzjBdEOx6Y1qJ03ce42bKa1ZHdD2zwJtBTLriAQ782bvv9LiGZKraMGeprLQ97I+nZD9G2Rp2Kauo0NNIvnLpvEpC+ruZ6lzrx2YAhgtpPhYkpQez4YswbVjkBj/tica8jOcl8XvCoq+Hoo7HvBXRRLaEL50voji6Y7mj5dO545v2iE5kPB6sDyVrsR0WFpVSvSadfL60nY2Xmo061ZPiGYfd2izkWsDGmg4YtVnNBMKmUkpcW5bLnHZlutup0/8+NqYvS2qSuFbokjr8uibvKmLqwvE3mmaKVBYnMB1xo1BQ6VVu4i0zlAvrNFH+dGC8YDsW4f6eKHm5XcKgp2flmWXQwtyF2mXNcuC+uyWdCnwgUCYCqrc6ojwdTc8J4DnfOkW1Rj49n75toObi4rWPvXJvpbMifrSdEXNwqjFhUczDUlpEgofzEjYln02JA5xZAX5PGfqp9h23OdZ/RoAxzNJ4Od+zJdZ3xIHYO2+L/GQpDdxSp+HFWPCS2LiNA35DuQeYCMRdExKpLClJc+fQT6s5LqL7UoX9v4rLAqVWdABoOQbc+FV1NKeg1pMLSLsWpINHM37NSQCJZxrgo29RZJWyolftQzRcBOWTPPLRXAZL1zpuQKHoqvpQ21+duoqsVwl+ai9LU3Zo4XadadrSvdk+qVbcnzaTak9urFV9pVu8suX3szMANednkN1lCbZfEWTo0TOxrJ8MZw7hjuJuwd96LlDN54+KeOY3bZq9TyyPimxQhl435AqNBKRxsUsZYdCdjrK15kf0aZVJ/gTzo58EDxcVj+wvqR48qoYnDK/Ue28jCk1V8jPnzZ9lC124aCQ9767KIA9X+DdDerYT1jyyUMjEmGOxxzKc7URUo3Hjhg6hwkh32s0n3y0magysSnxlX8zLQk804Tzs5gZyc2H/Nneva5+7hqLnoy6pJ/4jNOKCbylh9zMu59gUbJRZtTQTt3udPyYf5z314LhNrgvwoIAD0YTAUogA81WqZRhcOyJWDqfkL3xhyno8RZ2YFsRnN6b4wyELQIluBujQ+7ok2s0gowVBkrPtoVOwiZvzsaa7H29NuHNh5rUxPL4gZyHMpiOfzDyvwX73r4ADyWvKKSa8ls4i3ZDYF+GCa5+VAi3mw81rk7g7icvFk+XLkBwbi29WrUblyJf6vad7SVxDP22FyMc9heun7mPBehik3N7zk/PvDvwHdNSsbyy9xRgAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/a2122b00761e964ee0084399f5e2da3c/2b6f0/oldship.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/a2122b00761e964ee0084399f5e2da3c/2b6f0/oldship.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://viktorhachmang.nl"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/a2122b00761e964ee0084399f5e2da3c/2b6f0/oldship.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>The Merge</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:75.36458333333333%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAPCAYAAADkmO9VAAAACXBIWXMAABYlAAAWJQFJUiTwAAAETklEQVQ4y2WUe0ybVRTATwtB4yNm+oca/9C5xGiyLVMTp4uRyCJq8JkYwuLMfGQG4yBsY5ZH5d0hr9Ku9N0P+gRaXi2Flr77fV/7FfocUB5jEhjdGE55yMaQwriWRqPTm5x7zs0955dzb845gBCCPdmOb8PO7m7SNly7Azo1P7UlFoO9ZdFLj3QasH0hgwoO3+iHgl9G6coxf2ofSdAH3G5Ad7eSfgEnCfA3MCmrt6GddKSMNLEBAdC1LuvZ7sAw2+DFe/UOW1blsGUvAKpWroB8LABahx0yE+c2j4v+NZpP3iVB3TwZ4JmnoX75Kl3mde3BoIfAP1RTREQV9vk0l/1+bTQc1U1ERnXRMNk5FuxuD/rK9Dh+LB0tgZJwgehKmJ4EbiW2mffyoA7doWELU9BF4tltAc+sLOK7Kw1TSxI/MSsLeaKSAOEXB4g5LORdwwLkgjTk3ZCFKSQfIa/qCTxDNRmGhth4AroZB/3cVErzKAVdPk8+FqLiohF8UeAw++Sks0XpI7QKnFBjdjcmGbQ4JU67OZG5RufFVW0u2yWhz6XHAh6kpzyfykf9AAq3nd5JukFLuDKEARJJBwyfYS6bSmAZsGvadU3mftPbQWdNlkNzCXNgrbrQYP1xox0/qvQP5ygod6fCNsQU28154kRsn8PxEoi8zlQeaQOhx17BcZhmBHaTr8VirGzU9ULwTNeLNt4PMVKVs+hqO4ccku/jDvmpZafwtFme8dUL49mF+7ndnUOJGDvHZZ4TuC250DKopzeb9MAzG47W9LQbOcYebolS2p5bxTg+Yh6VUn0UspfmvKNmOqYG+dHr3ZXfCoyiDuQfwEuLeOwTTKW0v6lXy73Y19kvNBkOQJZbC3m6NnqhXg0XtaoMplwiYEj4VJGYc08ucCJf38ymVSodwYo8O12c+W2rSDlpEka2dV3UVn4za7VAJu0qVmGNtR2KV1iGjn/qsJmwpJxXSkAolz9czOe9f0bAKudXDMx2sCKotcGJWhg4EhZTSMyyInnxMBI32qIF3LLcvopv6sNlXx4qaK0F5OTS7itsSD9MS+jUhKTV8a2gawqVaVheJCnpLdDWU6eMvMA5EUP7uaZuGOmbIl9A1cnnsuuYsYOc2o1neezBEK81LQnaTbRcbPlXuPX7GgyRBOjcRNr8zTiQMzfetU5f+5G4vX5ALAtBYzUBA+u39pl+XmhYW9k58oyA3fNI80/oierSuf015ba8Rs7j92W4+1cv+9c2acH1OPhXNg5Zl1azmMHxxxBMwKrkD1q6bvBBxdzNzFmEnnqNVfXyx8zStxJvo8GkFT6qroH/wVBCq2YWoNA/DaXB6UeZvuiTn3DVKZBzAgyRxaQPlxp76DsL/sDrHDa8UcwA+dMHk338QUXlf4bDv/9TkRgE3TgNsvNpMPUbwPV7UEoE4ezIOLAvT9MndhA8X1ZCe7OynH7swnl4lXEBvBo9/An3NdC4oOkhKQAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/5d3af9eb308978e7a078bf51022d8a5c/1e715/merge.png"
                             >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/5d3af9eb308978e7a078bf51022d8a5c/1e715/merge.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://viktorhachmang.nl"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/5d3af9eb308978e7a078bf51022d8a5c/1e715/merge.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Beacon Chain</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:64.375%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAANCAYAAACpUE5eAAAACXBIWXMAABYlAAAWJQFJUiTwAAADsUlEQVQ4y2WTbUybVRTHb2GCzixxmcEP0xln/KAfNDNOY0JUkkWnAjEOJ04/bENm7VjLwA7ytLRbx0ZoaStQrAq0a0E2KLQblHaV0gKl5W28datrSymlqQhsyItYytrnHvuSLFP/ycm55578f/fc3FyE/iMvAMqNhhdjBNEs9CqRKwIo9Q0Ur0VDynhm8+pRZnre/+Khct47iY68cxzlvHsCfZ3LRBtRU5v9btwc08uHnk358K3DlNi6w+VCjtj+04lDHo2HOvD82+jVvQfjOaZYU1pgQN3aFdRr3tgpudBTLW3S7xHXahDrcxG6E+1vhqK3yc2ivLD7ibgnK/39BEyvtqKXdr2JXkvLQMcO01LPZPCSggAUw+17KVHwY9f185ny89PQpLDnR+uUTlsgtc3mTD6SwUZffcBJK/ji+9RTn1aibz8TJ4DZzwkQkVdBKf6Si4pyWfsE167s8gE8aV3Z2nsb4PVurVel4EyBQjJmnQJIH/orvM+6GnzqUNQrLG4/yjsl23MhT4aKj4oSwAp6K1JyJ5NkLBtq4PaX1bFunl4CSBsOkR+N4hChuza73Mx1wxVe7wOzf1E4AZA9EIb9rvXlg8YWV6SGpqYq2Rak4tuS48DxUQdl5vdJND5n3v3braW/DR13vZZQ8JNhAK5ja9NkavWBnO2JKIk+0N+0OyYBKk3bkY8X1takK4tBsFp81n89iMcdSJ6fX0Berz/bY/8T+nt82zqnr8kKWO38Y+WesdkDjWUzpIxphLYa/VZ0wl/1y0vCucB9+3D3EnQpp9fF/LpnaqsaE8DRPt+OySE/mh6dP91WPQv15/oetNRblkfWNz1dKkdYWjoGcp4b/8C0YimhAuvi6pzO4p4d6fMHFRc98PNZfUhMq3mlmlqVAN74xbVD2+pGOpWTJufPgIRpDDeUm0CrcZFK0RQWFJqhlnUHV9LNIOUOYkOnh+xotoNa6YTGchf8WKILnT8h2n85T5gAsuidyQT9Biot6Mi8VDoI5cU6fLHQiAW8QbJabMU8hgYqotNxqBrgc41YIpogBSUDZBW7J1JFDMMlhjqQk5G182RmfgJYQG2hnKE2ofzjP73IJQa2Oex+OEvtwg1qOzkOgDUWPwiIcSxVOGEkso3lWjtmF+nIy3zzFo+wAafEbFqwPfJTGIzriPaNDJ0rq3+8kK6fKxcPApOlC7dP+clbQOL+1TW42juDOz0BMNxfxBLTAElwTaSkfWKD+V0vFDH0dazSEVTGGkv6BwtMQarUAXBGAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/33fc191428d18ac5239516b5d37036a5/c0959/core.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/33fc191428d18ac5239516b5d37036a5/c0959/core.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://viktorhachmang.nl/"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/33fc191428d18ac5239516b5d37036a5/c0959/core.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>Sharding</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:61.04166666666667%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAMCAYAAABiDJ37AAAACXBIWXMAAAsTAAALEwEAmpwYAAADnUlEQVQoz3WRW0wbdBjFv8JmjInZMDwwCT6MRSFDoGwFCivlUta1cqncKi2UYspWFFgva+kFGNALXYG2FDaGlF5HuZRSESurljROGUPm0CFbHIjMzGAEgzFbdMHub9jDFk08yUnOw5dfzskH8B+d1Tmga9IPLY7r0GS7hnEGrKFrd0UYRd0pKKEz4Wjcm4AQenb/te80/K+wWCzITVOgdC+AxL6AEY5sguPLcVhfZQEHCxD/KkACofAFaSsPs7FswTzcNMNPqzrwewb/DSISiUAvKwMKhQoncFh4T9GHaXR8B0LLSph5dph+Z6nkYANu/8tU0rFwQW1F3F7DuZkO8PUyMAjNA9oNARczBmSRJIC0dCKkpqYCPg0PebRKDAcPTycNuIZAPeobW1wZQj+udd+2D/IUrNrWZiqrUeIwq3Lu3754/NuPa2Fc2rmv/i1VyNr96+Dhlj5vuQeJjowAvka3fy+XS/rLRrzDaHGU+ffnN0yeDot3S9zpQvkNXYgt0ey6LzfJogFeNBSqoZ6igS20AG553XPYno8nJiSSszKTxV2mcqX75sbMVQPalIVvX+rVGpRX5pCoyxXMF3Shk2eUv6NlgIkDgNMWSBsb6bK0GQmJZ2IwDwJPbQ0R6ZxwTjecVScf+IFaKfu+1R74tde/isavqtEvDfvWe4xGc7stgPgXnH/lCzQomy3/xj8tyliIC60eKZGNq1gDfo+senOKRsJCj2c5tH/mHiitgR7xBRfKrZCgpkEf0kx+9fjymBDdYYOrc/QLrX7iFlIMBRCz/QNE4mpXbriEtvkagvVKgfSTuhw9shdyglO4V1Jg0vcQ8+HsI2jVe8ekSjc6xTqPlAOBJ/qJ5V2TQ4iu1bxkrDd8Vi9W2HYYZ87fxZdyb6XQBUZnRqxj6m3iE0ORdocd//6DviTcKp/DOARq5acYo24OxCJLZ1Vl23p6TtVcU4v7T6XGixTtXPRRUfg9VQH5BINchXuHcLK4+BiBmI3HRk+TqdNW7BHUn8VeVGUYdtoSEkdVczdff/aU3JjUI4TXjuYQo94g8dl9izKuDUnOCoIWSlTwYubhkor22ShhTXMuX9SdfrrDHGfWO3FFkVG/lcbStpqTW7aVlDyrammDDuUptKcuTi86UJVZmVSGy43nM7qHRGwzOlctCvbiwx63FZPItfb5ZIHRlcfT2is43qVsm3c+nxYRtn04PPaRPKnlj3djcD9HHIp88A+ZxalUXd98OwAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/a0f00bb81aaf3743c3d42c8270451781/097da/newrings.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/a0f00bb81aaf3743c3d42c8270451781/097da/newrings.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://viktorhachmang.nl"
                            target="_blank" rel="noopener noreferrer">Viktor Hachmang</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/a0f00bb81aaf3743c3d42c8270451781/097da/newrings.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>DeFi</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:83.80208333333331%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAARCAYAAADdRIy+AAAACXBIWXMAAC4jAAAuIwF4pT92AAAFJ0lEQVQ4yx2UiU+TBxjGv+8rGucyt2VuQxcVUdlEsBStnIKglNJCoVgopRYY0AKlXFJOyzUBETqCWCp0ICoU1ODBNaxcpQiKICrihbfLjNs8smRLtljed+3+gV9+7/M8eYmmYxpaa4uOtnB/njZuHLZpqm1ciohLBMLYDH2kGm9Fd5vpTqwPdpvWghMrFF2CJX9c651v+/cX1BakZBpd7ezQ1z1wUcQR4aSh8wSha2wge7vPk1OTJnLCNEpLSEhbYgF+4u7NMW7a6IaOTKF5i3sErl5jB+7BYuQUtLw1dr+Y+Wsah3/cnz8awPRCtrcFGMCBuVF9FzF3c5p8eG+ONA4bqKlxI00qy7Qaro5iiacYDC7SmQIzxzcWbF12ACu9ATMH7/zd1//mtwfn8Pfp86fvdirlqOKFLw5rqvDlY9MocW1ijLrU30Od1rfRLs1ct5ktqvvI9OZ1WFed4ZWKp8J0XsliW/UAshMzYUdCNroJ48wh7LQPcbFtKFOWLh7RqqH2WCMcOnYcpy43PCXGRgbJngtd1NkOPa2zw0C7Mdb76bTpoazskP5PRbEGE4t0kJ2vQyk3GaKSlRAWLcetrjzczklcDOAq0M+ZDv7ePiAIFKBBl/COsORGXrVYXjx/liaOiLfmtzKenzQQ4MzBPV4R5i1MEfi4SyG3MB66DNXY2NwCsQIVBHGVkJerBUF0HHhv2QrBO0Nx6GTlM2JqcpycHBulhpq6qc66lhWvrj0PMGZ2PKzlFyGLzjE7M6KQ7h4D5dViMI3mQX11IpSryqG0eQjqBnqwrqMJZJWVIFIUgio3FQnjyGXyzvg01ZHSTHs0Nm97o8GQqZE0vL8g70Q1rwg2MILBxTUUtWoJvLyxHySpEvCTKkFU3QpSxVE0FBeBUsAHriwJ96alATE40EdOTl6hqsTFtHevX69rbNIfEjBFKOfkww57BuyhC2D9Zn9wYuyEzT5h4CspRFGMHCSCeqhSDWGSLAGEvCD4atkXEMTxBOLu7VlyZn6WyslTUeZ/3q+sMfxczFYoMSK3BvgZSoiRVACDFQl0Xw7GJqbB+IAGm7RySK3ogMr6y5irbwV+QQ7aELbo7+0GxIunC8T9OzcpRYqcZimEUqsKeGvsN5ujdqWiLrkBbOn7gCkoQac9oSCXRYO6rgRL6msgpV4HQmkF1gxeBHZ8MhCEHWYkxVsM52at46YIgiAtwI/7Ne2V9rYOGOKbCj+ws8F5XRi48YrQbbcEHF3YwI0KxSypDCKFB4AvTsPvO47gdrbEYrgOvAJZQNyevU5ax50VK7MaEtqSigiOaxB6BiaCi7cItm0QwK78Rty2Nwv2cfgw0F6JbfpyCIiTwS5+OLDCE8CfnQ7Llzog3ZGJxMzUBHnVNEq1VutsrMDag5roiYQ2LPLPgeXrWUBfy0O/o/3ACE6DstwMON3Ti1r9SVCqc0Bck4degkhw3iaApdR6ZHJi/j+ZnLs1QztzxWQFUtqbs1lVjV2Yn1S1aO8hBG9uBsR1PwefA+0Yzt0PycJSUKQUwuGeE9B85RwmHq2E8Nxi+HyFI2a3ngTiycI98tnjB7R0eYb15GUnNM2ZDjs90T1FDt/5hYHDdn/wyVAjU6EBz91JsDswBCuk0XD4pywoHenD429fQMvTx+ARJMaqS71oNaRZvs2SkFUelAX4pamtrz5gAxNXfbYGvv7WEzZ6BIOTpQwGOxm+CYqHsjgpVpWUQZmpD9vfPMdTvz6B1kcLoB4ZwZxTZ+A/SbrurcJtOaQAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/9c53e2b9d16ad28a02d9292f0fb6b943/96b77/defi.png"
                               >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/9c53e2b9d16ad28a02d9292f0fb6b943/96b77/defi.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://www.patrickatkins.co.uk/"
                            target="_blank" rel="noopener noreferrer">Patrick Atkins</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/9c53e2b9d16ad28a02d9292f0fb6b943/96b77/defi.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>DAO</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:99.94791666666666%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAE9klEQVQ4y4WUCVSUVRTH/yOynDRFFA5qR0wQGEgFZXecBEW2wIZFdFgdIXA4DZODDBoqGXVkNQQ0zRAQEYqtLMVwASFRyExPrijWKUJFWWIXmdv7RnMpOr1z7rnfvd/9ft999777gH8sIlKLSjXKac3+4QEtTjPR6Hz8WJvp8SoVi1GpnsWOuVQvBPwtDMYBNPbdbzfY0tk/hz2PuzWiYvCXY8dc6pd329X60egoRlprMUw0gX6rn3Tn+2ybbX3kfp1Il0a6xlewDNv/D9gltMADCwMMFO3H0Omj6sBRosl0+fDCmnutrvm9g8sLunssHxDp0Nl0LoP/BnLO+07GuGg7CycsJ6Mu+2NeKfOdGyH9nX0jIWWPSfhtR5vgi+u1K4t7Bw1kD7qQd6Ea1LwdZAUMpi4aY7tvGaNcD9jJ2Uzulh/AlWHVq5/3DYkyB0ic23HP92Z9litdKZ9EZ2KAnwnTDp2BaUktKGvBv4GqN81BzlagJfM0+5ebuwwveX2++kdt5+e33j7tmTNA3heIzJhPJ5Dzcx9+Wg/kHEeMIhgq1qiXgEcd+c/shnWiyprVK2IaRA4YGR6dd629RfRNT08AizPhur60+eHSnLI9Xlwd0cbge4zRmbIQQ2nWz4EbzI0w7ylw62JrWaLDAtfNCiV+6CfbA3fu7R5urfOhwV4z1/ME/+yCtDUZuwu/6yX0svhNEgEeJehh5EVg6FRD1E7WQoO2BiD0xZLZ+lCW16Dg7I8OH1VW/1p0/HhU3cXTa92aOzz5fmIni+j4gJgjh18ZipsykbyAboUB2j60egKs+L0HVOYBCjMBBepx9dG55szXZOdG4+ClFn5+9YmIuz29NnEFpV96JGcd8E8v4LLQulEp30h7Fwup2B2dX68DpbKybfqlG06nWkCJPNAWFpfL18nLTDCTFR6bblNaN5WrWXVVpV1xSanbO9l5wtmBEoE+awwsBDqFSo+aT6RCWUFyGHYp/cbt8J3BgHe64dDUgaziXby84jTQZoMpsprzGd4VTTYsU96Gow2We6tPHNtVdeSK9EKrUF2ivGV6R/ZvtI7cGuv2nkB72nrBRAS9vQhKT8MnGYqu9sGO1dGWbSO0sMQ7fOu2y+bxKXZ6EfHBphI5nWw8RzdvXCVH+fukL00s4robW1Sau7K8MVySkY3I7HyeR4sKCRxQebsLK0pO8VyqmuBY3jRrbcXJqsDIAEfunFnFxtuahkQUzg2JGjAKiiIj/5A6y2i5CLdIW6hIyhFGx0q3pKciKSOFZ+i3Fkk+0xmwtQsWEhmPHxIF4+Do193kCaUmygybTu7KOgPNDUQzAvIOp3lFrBGzzMY1/1E/ty/f243coXEwVf50lo2g6xP2pMNchvbr42AvVcB0VRi+uvlQPQWNZUpn2jttNj/lkKckN79REveuAec/lCuXNmQGVBeYYXIEszOD5vIYDYbimOeT4qBIgt3G7ViWmMQjUk8z79S+iJJLaa5iGK7WtfQTiy1Km8wia3+K+SzSPkjmay9b/Bq0BNqAZKkREKCAZeP950CDVZHQdfaB1HECnJnNzvv4eLHTDqmvjX9EuAjWUQrYxCfzA9L33JoTnz4/PDIIzn8SXGcCoS4mUCXPHPtOjLXTgDvTHkx2yFdBFrwcnvEf8BySc2DkHz7BUrRGYBy6fgq5AAu9lkHyBiD3MH6J8ReY0ptj4GIx1AAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/d17b5ecb3655c50d6540e590a93d65e7/87c97/dao-2.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/d17b5ecb3655c50d6540e590a93d65e7/87c97/dao-2.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                        <div class="AssetDownload__Caption-sc-1etxw81-4 cQJlwD">
                          <div class="AssetDownload__ArtistSubtitle-sc-1etxw81-3 gTZdoK"><span size="1.5" mr="0.5em"
                              mt="0" mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 coxJXZ undefined"><img
                                alt="ðŸŽ¨" src="https://twemoji.maxcdn.com/2/svg/1f3a8.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Artist:</span>
                          </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://www.patrickatkins.co.uk/"
                            target="_blank" rel="noopener noreferrer">Patrick Atkins</a>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/d17b5ecb3655c50d6540e590a93d65e7/87c97/dao-2.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <h2 id="historical" class="assets__H2-sc-4yoqxh-3 hAckNE"><span>Historical artwork</span></h2>
                  <h2 id="brand" class="assets__H2-sc-4yoqxh-3 hAckNE"><span>Ethereum "brand" assets</span></h2>
                  <h3 class="assets__H3-sc-4yoqxh-4 jiuwPB"><span>Transparent background</span></h3>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (glyph)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:166.71875%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAhCAYAAADZPosTAAAACXBIWXMAAAsTAAALEwEAmpwYAAACHElEQVRIx52WTShEURTHB0M+SkRJKQkLKcVCsiALKV8rxVJkNqwkxcJC2Shlq0mKjWxsJFIoEStZKMIOiYUFCTPe+F+dU6fj3Tdv3qtf78599/7v/9yPcycU8n4y6N0HxlVd4CcdXIFPUEJ1aUGEwvSeAAkiGtQlOygDryT2Q+9m4dz3ww5WSCQG4lQ+StUdi7WQgCOIUd1QkNBPhLuECvseFPhZIB4xIsSkQznAQjKXPFIReFSOHItwndcC8UiLyokjQtYut21h8wj1FlcJJeqIVe9zC51H2PVwp12y4DXIdgu1XzR0LGK20Ge0y1xwq0b2I8jT8w4qpctZH6Emc7nOYtU0Ao/o+BSUC8ROO4zgDv34dFnhZEJc/hIn6C97HIrGMdXYsQjpY3kJeuU8jqlUFfc4JT9CzLznpJDZLp1ULqHJdXObUBnHsA9qqW8DGDWFHHAG1kAmfewBN0o4Lub4BYwIU8bhHSjnihpqaJLCgGg4L0SYVVBI39vABdV367sjIjptgQqqNxnlmC6qdqrLB8ui/b9Uxmd5UzT6AFMu530QPIl25yBLZx3ONqXgWYVoOjSBKrCn5tVMR6Mt0YbFpc4dvkX5XZx13sjTqm/Ilhej1OFb7EcW5m1zkMp9nCe2TVycV17xN5oCX/czz0WrR5YeThaqTXTOJdSNIP8c5HMqnD6A4qCC7LKWUpsR7Eo1VNtWmgRLfpz9AoL6OBxwFJr+AAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/a62391514b71539906d6bd8ec820c7d8/d1ef9/eth-diamond-glyph.png"
                               >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/a62391514b71539906d6bd8ec820c7d8/d1ef9/eth-diamond-glyph.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/a62391514b71539906d6bd8ec820c7d8/d1ef9/eth-diamond-glyph.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:162.86458333333334%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAhCAYAAADZPosTAAAACXBIWXMAAAsTAAALEwEAmpwYAAACp0lEQVRIx6WWW4iMYRjHZx2S2BxrS6Eo7rSNCyHZUhsuHFNkXbhS2Fk7hmUddtnacOWGC0tSlCsSV6TU7s7OzsFhl7UkJeUwuCCFcuH/1O+tt69vxsy3b/16d7/55v8+53disfJrHPsqUR94FnlNFF3i0liFatjXiG4xLDbzbHxUsbmiVXSKgrgvpgfeqWo1iRSCafFKHKk2lu7kuGgXzeK0GBR5LF1SrehUsU8cEglPMCteiCvVWrdWHBVJ0RKwMCNeiq3/S5ATm4dlJnYwRNCsfCoeiJmVJMgS0UZ2wwRzWDkqjpWKpTthKYlwYsYBBLOeoONxuQ6qFfspkyR7iuScxKoconkstgRdLWXdeqxzsUtg3SlxS7wRT7A064lagrYFEzTfi1szoucQ6hfPxBfxWbzl/yyCdshDMcufKE2eZefFPV7OIZhDzES/iqJ4J54TihFxwgnWE6OL9GqO09PQT3cUESx6wra/J5YFuit2wSuFjCdkDJQQDPJJ/BU3THCFuC6GcLPXExsoY6GF4KP4IX4ittjP9g5cHkawr4ygCX0Tf/BqnV8xy8VCHswQx7326oOC55pZ9pukpDyDdooN9sc0inejmMSHNp56CIMrjw+49l1cFnN419y8Ka75Pb2AtFsNLvNO3STuitfil3gkGvhsCr3sarIu2CkNuGsTeTeH2JrMKGvxDtpOMY9QMo1ho8wKfJc4zPhqw8Ja7504FTFK65lYR9hwcFbW0X5J9nYsW03xD9EdaVy9jQehMzFshCUZEGcRchM7z+iKV3q3bAlcAZ3UpBO0229PJXd0jXdJ7SWeCUbYoDcDe6Jc8ovIuBPMMKp6uXci3c2NWNmByyOuG6L8HLE1gVLqwtXusfwMcV+aLc6IO8Q2sqAfI3N9ZSVx+wf7hd4AcxLNJAAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/6b935ac0e6194247347855dc3d328e83/13c43/eth-diamond-black.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/6b935ac0e6194247347855dc3d328e83/13c43/eth-diamond-black.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/6b935ac0e6194247347855dc3d328e83/13c43/eth-diamond-black.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (color)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:166.71875%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,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"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/c48a5f760c34dfadcf05a208dab137cc/8aa8f/eth-diamond-rainbow.webp 750w,/static/c48a5f760c34dfadcf05a208dab137cc/4b83e/eth-diamond-rainbow.webp 1080w,/static/c48a5f760c34dfadcf05a208dab137cc/9d18a/eth-diamond-rainbow.webp 1366w,/static/c48a5f760c34dfadcf05a208dab137cc/3a0ba/eth-diamond-rainbow.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/c48a5f760c34dfadcf05a208dab137cc/d1ef9/eth-diamond-rainbow.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/c48a5f760c34dfadcf05a208dab137cc/d1ef9/eth-diamond-rainbow.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/c48a5f760c34dfadcf05a208dab137cc/d1ef9/eth-diamond-rainbow.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:162.86458333333334%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAhCAYAAADZPosTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEzklEQVRIx42WW0xcVRSGNxx8UB800RCT6kOjMT7rg/EWW2mpFsW21EItlJnD5QyUFjvcMiWUWynMMMxhphSECqEXjAitnGmDNr7UxGsalZjYmPjkk9d6iT5YZmYf/73P2mcOCMokK2sPYX/zr73WXmsztsYnHLdYJJFkrR3TWjA0yY51X6hraHp9++HmcXYoOKbphsl0I8Y29BEgx6yciXPX2PTsR/ldA2/fqq6PfxzqOs/eXLjO9IDJ/AEBNTcGEwph2vD4uyw6cmWqd3DeBsT2G7HqypohBq/BJHSD6pKa8APDC1sGT122AUwT8PuquuF7BQzqcqTK9aCReNI9O4/S6xIYmUvrdWYKIKF0VHdAmh6IMQlfC+rAZKh5BGzE2gYw1RtByHUmx2YulMKe0B2Qpq+lMpxwleWQ3wT7lYAZAoqQUwT80K8S4/pYNlQ3zHgyl9ZnBQyWckKe56RQwBTUyIYuoLG1EiGVFsDsSNzKAMgB5J6QBShDwB9h+QTNEUAZetiTBJhQ+LkEJpJpCplLhYEVZ6hUjrsqDTpLyqxMBOBBFSp5KEzynsicqkMJ9sKh7GlSJ0NXqgT4AdjvBMqQt4dOX7H7ohc5bgpXUBG6SBD5T1SmlUIFvOBVFx25rM6P4/7ykrJufrB6kKOwpa2C1svE4AapAi4UEKwzuGocnh8/+RZHM8Bmk7/qG7ALnm/mhUWtfNe+Tl6uRwTUhmUI+DPsPqkQC6FwCSB7YNhKt/fM2PVHR7mvdohX1kSxybQrqgYBa7G3vdAMcJO9fWcLLy7pkD8kEiTg8G8Yh08xZo4ttvab79htnedSgSMjtq9mSMBsyqIMr0KPSAiAnLwEi+9Fu9t5WcVJeQTGkZGtrOnY1E+1DQkbapYBUjXmZlIqdIBSoQB6TIDTsOWdL4fskv0919j+g/0F2LhEIBmCWxIOkHuALgiWgaVoLcDWU1saHmEPPlwmE4ONQUB+o9vACWyTQjdUAqbEmr5/A18MdWzh6td56HlWB4C70O4Z/ngPrtg0qZNlAWDGozAtlBHoL1jHieglVry3QyQ3CPMLdZtRMjdh8yOT7+e3HT/LfLWxJwH91KNwWamiEGefLWjctKe0W0T2GP7vK9hn4k6rOnxR3F/4P6A4VFjUxk5EL4rKN5DlH5RC2BJs2ysHelljy5m7AZlQHQj+fnX1NILGCCrshviRptAUKy3vu6PcHx7d8VJbCNXAntsRFOE1wm56jmaf32kOeXK6RZwGcRsgX9KNUeCF2NjiZpQW213aJTZsxeYvSJUDC8SmnGtn5opxQI3V0qh9PUptSzSHNMFv9Q1d6kWNTqrSwsa/Cfot7E7q2jkqZKdrZ2dJkPphytOxve0r5c/W7DPegeUA5QxO0oBK0jq5iO9qBIiOk0I5pRVQwgyzU8HU5PMb/xoDVm52SFm/eIYU9w4pnNsHCuKa9xURWTEGrDzZxePW3lXADJ3bn7CHCJSrrzfsCabqMpdm9DgN+uUqJ2QB9MkSMcy87BtnnYeT5+Ug53Nn/+ztAN7wvG1mKqujsofqBNL/632TTZDMvBafeI8lzlx9vCc8J7rzdwf84bvQVN0S0f/vsaRCDyecbKv3YXvvTPuho6N7MFtY3WuntRVDadXnH2Uco2Jdi6xtAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/a183661dd70e0e5c70689a0ec95ef0ba/13c43/eth-diamond-purple.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/a183661dd70e0e5c70689a0ec95ef0ba/13c43/eth-diamond-purple.png"
                                >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/a183661dd70e0e5c70689a0ec95ef0ba/13c43/eth-diamond-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (color filled)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:151.56462585034015%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,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"
                              alt="">
                            <picture>
                              <source type="image/webp"
                               sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                src="../../static/4f10d2777b2d14759feb01c65b2765f7/b7d3e/eth-glyph-colored.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                 sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/4f10d2777b2d14759feb01c65b2765f7/b7d3e/eth-glyph-colored.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/4f10d2777b2d14759feb01c65b2765f7/b7d3e/eth-glyph-colored.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (color filled, SVG)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt"><img
                            src="data:image/svg+xml;base64,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"
                            alt="ETH diamond (color filled, SVG)"></div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo portrait (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:68.64583333333334%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAAsTAAALEwEAmpwYAAABQklEQVQ4y6WRsUrDYBDHv6SiiSWiEEXaWB1aB0WHQmxNAwZEhy7i6OATODvEQfAFfAPRQfAtfAEHd/ExJGv9XbmUKEFqevDL/+5y3/87EmP+Dlu1pxR7lUMMbuAZarMYWaqHcAvvcKm9WlWzFtzBC3zAG7R/zfzL8Bhe1ewLPmE4y7f04Qzu4QGuoVllw2IkcAGPcDWV2WAwGBNF0ZiSkL/8NNX1YlA0E1zXLfs5jam2KzOUutPpmNFoZOr1+sTAtm0ryzLT7XYnc/nZPP9horqa53EcmzAMjed5VpqmJgiCycGiUXEZeTja9JVd6kXtzaHzmjtJkoguUFtqYOs76bngSHFK0oYd2AMxPNBNI+gz04Z98nV0KLNogC5DQC7zCdqXlWVgi2IF1sib4KseQQ82YUkvOZea2RackG+jDb1g4xsxkGL31C7AygAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="../../static/9e43837989f5a675de707424188ea962/2a657/ethereum-logo-portrait-black.png"
                                >
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="../../static/9e43837989f5a675de707424188ea962/2a657/ethereum-logo-portrait-black.png"
                                  >
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/9e43837989f5a675de707424188ea962/2a657/ethereum-logo-portrait-black.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo landscape (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:25.05208333333333%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAFCAYAAABFA8wzAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAwklEQVQY023QSwrCMBCA4RQJERGtVmlVhKIuRNRFRbAvEE/gCdy5c+1JPIdrr+Jt/Ic2EKGBj5kkk0mIUtW4ol/nKkkSlWWZyvNcomdzS+bO/t+6jBhvPLTWKgiClu/7stlBj8K4PmAbt9yLnOYaRho+8cUHpizLY5qmO4o2FMxwIp8QL7IGuWCNMyJMscABe2m4xQt3mdBsyaEIXbQxwEiaEEOivDzESvakCcbkQ/tCGTfMjanmRVF4TX/VRL7C/d8fHsA+LPGb/J8AAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/8ea7775026f258b32e5027fe2408c49f/5c52c/ethereum-logo-landscape-black.webp 750w,/static/8ea7775026f258b32e5027fe2408c49f/3c828/ethereum-logo-landscape-black.webp 1080w,/static/8ea7775026f258b32e5027fe2408c49f/1438f/ethereum-logo-landscape-black.webp 1366w,/static/8ea7775026f258b32e5027fe2408c49f/f89fb/ethereum-logo-landscape-black.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/8ea7775026f258b32e5027fe2408c49f/57723/ethereum-logo-landscape-black.png"
                                data-srcset="/static/8ea7775026f258b32e5027fe2408c49f/4f95d/ethereum-logo-landscape-black.png 750w,/static/8ea7775026f258b32e5027fe2408c49f/0af37/ethereum-logo-landscape-black.png 1080w,/static/8ea7775026f258b32e5027fe2408c49f/527c2/ethereum-logo-landscape-black.png 1366w,/static/8ea7775026f258b32e5027fe2408c49f/57723/ethereum-logo-landscape-black.png 1920w"
                                alt="ETH logo landscape (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/8ea7775026f258b32e5027fe2408c49f/5c52c/ethereum-logo-landscape-black.webp,/static/8ea7775026f258b32e5027fe2408c49f/3c828/ethereum-logo-landscape-black.webp,/static/8ea7775026f258b32e5027fe2408c49f/1438f/ethereum-logo-landscape-black.webp,/static/8ea7775026f258b32e5027fe2408c49f/f89fb/ethereum-logo-landscape-black.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/8ea7775026f258b32e5027fe2408c49f/57723/ethereum-logo-landscape-black.png"
                                  srcset="/static/8ea7775026f258b32e5027fe2408c49f/4f95d/ethereum-logo-landscape-black.png,/static/8ea7775026f258b32e5027fe2408c49f/0af37/ethereum-logo-landscape-black.png,/static/8ea7775026f258b32e5027fe2408c49f/527c2/ethereum-logo-landscape-black.png,/static/8ea7775026f258b32e5027fe2408c49f/57723/ethereum-logo-landscape-black.png"
                                  alt="ETH logo landscape (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/8ea7775026f258b32e5027fe2408c49f/57723/ethereum-logo-landscape-black.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH wordmark (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:17.395833333333332%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAADCAYAAACTWi8uAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAh0lEQVQI1zXPywrCMBCF4SBS6UKJC7WtlCJe6EYppQgmm4BLKRQ3vv+b9D8wLj7mZCbk4mKMO6yxxQ1nXEMIzvoH8gL/WiKnv8SKrFqavaPxJXSocNeB9FQveJGTbR5wYv2xCwtV1HjaPOnAkfBApteYDY7MerztpfqJx6TL6Tf4aZ9mZI92Bt5vMPxJKatDAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/********************************/71f5b/ethereum-wordmark-black.webp 750w,/static/********************************/aaa50/ethereum-wordmark-black.webp 1080w,/static/********************************/81928/ethereum-wordmark-black.webp 1366w,/static/********************************/9874c/ethereum-wordmark-black.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/********************************/8d619/ethereum-wordmark-black.png"
                                data-srcset="/static/********************************/c8883/ethereum-wordmark-black.png 750w,/static/********************************/3a3d5/ethereum-wordmark-black.png 1080w,/static/********************************/a3b03/ethereum-wordmark-black.png 1366w,/static/********************************/8d619/ethereum-wordmark-black.png 1920w"
                                alt="ETH wordmark (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/********************************/71f5b/ethereum-wordmark-black.webp,/static/********************************/aaa50/ethereum-wordmark-black.webp,/static/********************************/81928/ethereum-wordmark-black.webp,/static/********************************/9874c/ethereum-wordmark-black.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/********************************/8d619/ethereum-wordmark-black.png"
                                  srcset="/static/********************************/c8883/ethereum-wordmark-black.png,/static/********************************/3a3d5/ethereum-wordmark-black.png,/static/********************************/a3b03/ethereum-wordmark-black.png,/static/********************************/8d619/ethereum-wordmark-black.png"
                                  alt="ETH wordmark (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/********************************/8d619/ethereum-wordmark-black.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo portrait (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:68.64583333333334%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAAsTAAALEwEAmpwYAAACcUlEQVQ4y2NgwAK6Jm4E48qGxcxltQsYqpqWxOeXzgosLJ/DkFc6izkxvZ8BhIkC9e0rGa4+/MLQNWkT44KVRxkWrT7O09S5+nVazqTrQGlGC5sshqSMfoaQqGaGiLg2wgbWtS4H0x0TNjADDWXon76tu71//f/U7En/kzP7S2OTu4Gu62MOi25hCI9tJc6FbX3rmUCGtfaus2vpXvMf6M3fIZFN/2OTu74npPXqAA1kiE/tYSTKheV1CxmyCqYx5RbPYCiqnJsF9N7X6MTO/y6eJf9dvUo/BEc0hgeE1jP4BtUwhQK9TRB0T94EciFj77StoHDUAwbBxJikrk0evhXrIuPbe1OyJqoAvQ90YS9jStYEwgaCvNo7dSvY4ClzdjMADZ4FDM8DyZkTjgNxB8i7QFczpuZMYgAajtsQGO4EJheQ64A04+wlBxmOnH/K2ti56m167uTrG3dfYXByKwZFCmNazmSQKzEN65y0EagZZuBGsIEgGsSvbV3OBEyLDBX1i4yLKuZqFZTNZsgpmsEESjZJuNJh18RNKC5ExpkFUxnq21aADc0vm8WQUzyDCaSnqWs13DfIDgBhbIKiMMUgGhhBDHVAQ28//8lQ3bQUI3g6obkK5kuQCznAXp20UQQoAMLanZM2cUEUbWQB8tmgmjlAkQWk2YHqGaFiTBADNwHFNnGC1IBMdgNyVIBYC4h1gQZpAzWYASVBLrUCYgsgHySvB9QoAaS9wGonbpQB0gKdIHriJjMg2wHoKAuQV0EKFIBYEGi4GJCWBrt0Ipi2B2JzoBp5IM0HDY5AIJYHGigHpF2BWA3IlgIaBjJYFgDTXnm1ggMEfwAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/69bcd/ethereum-logo-portrait-purple.webp 750w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/91452/ethereum-logo-portrait-purple.webp 1080w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/76700/ethereum-logo-portrait-purple.webp 1366w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/f39ba/ethereum-logo-portrait-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/2a657/ethereum-logo-portrait-purple.png"
                                data-srcset="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/0c144/ethereum-logo-portrait-purple.png 750w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/9fc57/ethereum-logo-portrait-purple.png 1080w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/894a1/ethereum-logo-portrait-purple.png 1366w,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/2a657/ethereum-logo-portrait-purple.png 1920w"
                                alt="ETH logo portrait (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/69bcd/ethereum-logo-portrait-purple.webp,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/91452/ethereum-logo-portrait-purple.webp,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/76700/ethereum-logo-portrait-purple.webp,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/f39ba/ethereum-logo-portrait-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/2a657/ethereum-logo-portrait-purple.png"
                                  srcset="/static/062ab5cbe18d16c46cbd3fa57b0e3f35/0c144/ethereum-logo-portrait-purple.png,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/9fc57/ethereum-logo-portrait-purple.png,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/894a1/ethereum-logo-portrait-purple.png,/static/062ab5cbe18d16c46cbd3fa57b0e3f35/2a657/ethereum-logo-portrait-purple.png"
                                  alt="ETH logo portrait (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/062ab5cbe18d16c46cbd3fa57b0e3f35/2a657/ethereum-logo-portrait-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo landscape (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:25.05208333333333%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAFCAYAAABFA8wzAAAACXBIWXMAAAsTAAALEwEAmpwYAAABGUlEQVQY02NgAILyuoUt0fFtQrGJHQwBwTUMjZ2rGDombmDomLCBoX0iCG9kaJ+wEczuALLhGCoPomFiDBNn74wsrZn/Pyaxoy4ytpUht3g6c2beZJAkNxDzAQ2T6oAYyAg1kBmqmRFsIBxvZAWqZWcorZ63Kyy6+b9PQNW5iroFXJ2TNpm39W/QAWrQAmIZIDYCYjEgdu6YABZTAGrWANKOQCwBxFJAi5SAtAkQ6zG09q6LzSqYeiUsqqlUQSGQAWiYCtBGcaBtPEDMAVTEC8RCQKwB1CgOlOMCssWBWBkqrgcUEwXSwh0gF56+85GzqnHxgsTUbqWk9F6GqLhWRqArGcHenIgIN5jXOiYihyWI3gD2OgRvZAAAAEWz26cbGo4AAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/da3e74023b0d48abaf82cda76e36939a/5c52c/ethereum-logo-landscape-purple.webp 750w,/static/da3e74023b0d48abaf82cda76e36939a/5b564/ethereum-logo-landscape-purple.webp 1080w,/static/da3e74023b0d48abaf82cda76e36939a/1438f/ethereum-logo-landscape-purple.webp 1366w,/static/da3e74023b0d48abaf82cda76e36939a/f89fb/ethereum-logo-landscape-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/da3e74023b0d48abaf82cda76e36939a/57723/ethereum-logo-landscape-purple.png"
                                data-srcset="/static/da3e74023b0d48abaf82cda76e36939a/4f95d/ethereum-logo-landscape-purple.png 750w,/static/da3e74023b0d48abaf82cda76e36939a/24545/ethereum-logo-landscape-purple.png 1080w,/static/da3e74023b0d48abaf82cda76e36939a/527c2/ethereum-logo-landscape-purple.png 1366w,/static/da3e74023b0d48abaf82cda76e36939a/57723/ethereum-logo-landscape-purple.png 1920w"
                                alt="ETH logo landscape (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/da3e74023b0d48abaf82cda76e36939a/5c52c/ethereum-logo-landscape-purple.webp,/static/da3e74023b0d48abaf82cda76e36939a/5b564/ethereum-logo-landscape-purple.webp,/static/da3e74023b0d48abaf82cda76e36939a/1438f/ethereum-logo-landscape-purple.webp,/static/da3e74023b0d48abaf82cda76e36939a/f89fb/ethereum-logo-landscape-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/da3e74023b0d48abaf82cda76e36939a/57723/ethereum-logo-landscape-purple.png"
                                  srcset="/static/da3e74023b0d48abaf82cda76e36939a/4f95d/ethereum-logo-landscape-purple.png,/static/da3e74023b0d48abaf82cda76e36939a/24545/ethereum-logo-landscape-purple.png,/static/da3e74023b0d48abaf82cda76e36939a/527c2/ethereum-logo-landscape-purple.png,/static/da3e74023b0d48abaf82cda76e36939a/57723/ethereum-logo-landscape-purple.png"
                                  alt="ETH logo landscape (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/da3e74023b0d48abaf82cda76e36939a/57723/ethereum-logo-landscape-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH wordmark (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:17.395833333333332%"></div><img
                              aria-hidden="true" data-placeholder-image=""
                              style="opacity:1;transition:opacity 500ms linear" decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAADCAYAAACTWi8uAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAnElEQVQI1zWPuwoCMRBFg4hioWixxlVExAc2iogI2y1YiiA2flJSJn8y84eekVgc7szczCMuJq1gGLNO0D26gV1M4lCr+5C189eQpCYeQBf6IYup1Wr8qaPxTXKGORzLQNMtNNCWhius4WELQ9LZb3HWJYfcit/awCfBiek98JiefAQLrrng38ulFYzhFWx50hXxx97FLPxSzDt8AUSIcvDLwRkfAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/07d7e123fecfe63aa415a92a6f96bb0d/71f5b/ethereum-wordmark-purple.webp 750w,/static/07d7e123fecfe63aa415a92a6f96bb0d/aaa50/ethereum-wordmark-purple.webp 1080w,/static/07d7e123fecfe63aa415a92a6f96bb0d/81928/ethereum-wordmark-purple.webp 1366w,/static/07d7e123fecfe63aa415a92a6f96bb0d/9874c/ethereum-wordmark-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/07d7e123fecfe63aa415a92a6f96bb0d/8d619/ethereum-wordmark-purple.png"
                                data-srcset="/static/07d7e123fecfe63aa415a92a6f96bb0d/c8883/ethereum-wordmark-purple.png 750w,/static/07d7e123fecfe63aa415a92a6f96bb0d/3a3d5/ethereum-wordmark-purple.png 1080w,/static/07d7e123fecfe63aa415a92a6f96bb0d/a3b03/ethereum-wordmark-purple.png 1366w,/static/07d7e123fecfe63aa415a92a6f96bb0d/8d619/ethereum-wordmark-purple.png 1920w"
                                alt="ETH wordmark (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/07d7e123fecfe63aa415a92a6f96bb0d/71f5b/ethereum-wordmark-purple.webp,/static/07d7e123fecfe63aa415a92a6f96bb0d/aaa50/ethereum-wordmark-purple.webp,/static/07d7e123fecfe63aa415a92a6f96bb0d/81928/ethereum-wordmark-purple.webp,/static/07d7e123fecfe63aa415a92a6f96bb0d/9874c/ethereum-wordmark-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/07d7e123fecfe63aa415a92a6f96bb0d/8d619/ethereum-wordmark-purple.png"
                                  srcset="/static/07d7e123fecfe63aa415a92a6f96bb0d/c8883/ethereum-wordmark-purple.png,/static/07d7e123fecfe63aa415a92a6f96bb0d/3a3d5/ethereum-wordmark-purple.png,/static/07d7e123fecfe63aa415a92a6f96bb0d/a3b03/ethereum-wordmark-purple.png,/static/07d7e123fecfe63aa415a92a6f96bb0d/8d619/ethereum-wordmark-purple.png"
                                  alt="ETH wordmark (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/07d7e123fecfe63aa415a92a6f96bb0d/8d619/ethereum-wordmark-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <h3 class="assets__H3-sc-4yoqxh-4 jiuwPB"><span>Solid background</span></h3>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (white)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:100%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/jpeg;base64,/9j/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wgARCAAUABQDASIAAhEBAxEB/8QAGQABAAMBAQAAAAAAAAAAAAAAAAcICQEK/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEAMQAAAB3boNYLGM9GjgRsElA//EABsQAAICAwEAAAAAAAAAAAAAAAUGAAcCAwQQ/9oACAEBAAEFAipLmDjqxtjeaeJa61sZlStatxytHwApg1von//EABQRAQAAAAAAAAAAAAAAAAAAACD/2gAIAQMBAT8BH//EABQRAQAAAAAAAAAAAAAAAAAAACD/2gAIAQIBAT8BH//EACYQAAICAQMDAwUAAAAAAAAAAAIEAQMFBhMUEBIiETFRACEkQVL/2gAIAQEABj8CbyThdtClRWl/Rz7BUHzZccjXXH7Mo+tTaPzzA8hm+Mrp2ZLxEJToN7CBPyqP5asT5EHM9Z8BjpeqD2XQhW2HLLMOYcjsrAxIzVPth+qmC3TUG9W4xEioYG4QA04HUl+WfxbimakdNctNXHoLbNleQ1BlH1wsoubmYoowCFDDbJ2yDeRUW3SnpmWsSpC9+dfh/IWe5GYVDRTRXPpG2osAzx148KytuKPvZPT/xAAbEAACAwEBAQAAAAAAAAAAAAABIQARMRBBgf/aAAgBAQABPyFcXThT/FKeWY9dBzZ9YHl3cdCKwbG8Eb3cnJISAWDginl7ItHDCK6CRc9ExQ0J5//aAAwDAQACAAMAAAAQMMgA/8QAFBEBAAAAAAAAAAAAAAAAAAAAIP/aAAgBAwEBPxAf/8QAFBEBAAAAAAAAAAAAAAAAAAAAIP/aAAgBAgEBPxAf/8QAGxABAAIDAQEAAAAAAAAAAAAAAREhABCBMUH/2gAIAQEAAT8Qj0OWRp83LvQJAEwDTInEIRh1NSCsEXlL5qthlCPg0Kgj4kBAyhB15KocKwAgIIiCI0iNIlIiJSJgYRU8bFZrTp1H/9k="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/bfc04ac72981166c740b189463e1f74c/b184f/eth-diamond-black-white.webp 750w,/static/bfc04ac72981166c740b189463e1f74c/941f9/eth-diamond-black-white.webp 1080w,/static/bfc04ac72981166c740b189463e1f74c/81b85/eth-diamond-black-white.webp 1366w,/static/bfc04ac72981166c740b189463e1f74c/448ee/eth-diamond-black-white.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/bfc04ac72981166c740b189463e1f74c/40129/eth-diamond-black-white.jpg"
                                data-srcset="/static/bfc04ac72981166c740b189463e1f74c/1d86d/eth-diamond-black-white.jpg 750w,/static/bfc04ac72981166c740b189463e1f74c/7f16d/eth-diamond-black-white.jpg 1080w,/static/bfc04ac72981166c740b189463e1f74c/1c5c4/eth-diamond-black-white.jpg 1366w,/static/bfc04ac72981166c740b189463e1f74c/40129/eth-diamond-black-white.jpg 1920w"
                                alt="ETH diamond (white)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/bfc04ac72981166c740b189463e1f74c/b184f/eth-diamond-black-white.webp,/static/bfc04ac72981166c740b189463e1f74c/941f9/eth-diamond-black-white.webp,/static/bfc04ac72981166c740b189463e1f74c/81b85/eth-diamond-black-white.webp,/static/bfc04ac72981166c740b189463e1f74c/448ee/eth-diamond-black-white.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/bfc04ac72981166c740b189463e1f74c/40129/eth-diamond-black-white.jpg"
                                  srcset="/static/bfc04ac72981166c740b189463e1f74c/1d86d/eth-diamond-black-white.jpg,/static/bfc04ac72981166c740b189463e1f74c/7f16d/eth-diamond-black-white.jpg,/static/bfc04ac72981166c740b189463e1f74c/1c5c4/eth-diamond-black-white.jpg,/static/bfc04ac72981166c740b189463e1f74c/40129/eth-diamond-black-white.jpg"
                                  alt="ETH diamond (white)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/bfc04ac72981166c740b189463e1f74c/40129/eth-diamond-black-white.jpg"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:100%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACUElEQVQ4y2N48/4DPvThIx5ZBqyir96++/rj57YdOxcuXvLj9x8gl1jNr9+9f/fx09MXL5uaWxydnG7fvffxy1egILGaP379tm7DxqrqGk0traqamu+/fmO1nAGLzi9fL1+9BrS2rr7BxNRUR1f3wOHDX3/8wtSPxea3Hz7OnD0HqBOITM3MVNXUIqOj32ILOQa0cPr07fuBQ4era2pbWtsaGpuAmg0MDYH6585fAAy5l2/eYtcMCqdPnx8+edrV3dMM1NrekZuXr6OjY2BkpKunZ+fgcPPOXbSQQ9H8+dv3jZu21NTW5eUX+Pr5GZuYioqKSsvIaGtrK6uoVNfWoUUbA1znp6/fTpw+ExsX7+HpaWxiAnStkbExULOgoKCIiIiMjIyklNTmrdu+/USEHANyIJ88fQYYNmrqakCdFpaWEM1iYmJ8fHysrKzOzs77Dx0G2oGuGRTIHz8BPXz1xs0ly5e7uLlpaGoam4A0MzMzq2tozJ437/Cx49du3nr/6TPc2wzIMfTi9Ztly1fs3rsPaERLezvQfhFh4ZLS0guXLvdNmJhXUPjs5SugMiyagUIfPn958PgJMHlMnjoNmE72HTi4c/eenbt3e/v4GBoZAa39/O0HlgBDxPPXb6fPngMmTGCYr167rqSsHBjJSsrKG7dsxRfPyAl77YaNtXX1FZVV2jo6yirKdQ2NyIGMTzPE81OnTa+trdM3MABGONCryOFEKFd9+Xr91m2gy41NTQ8dPYbVWpyFAVD/15+/1m/YOGXa9O8kFQbIRuAvpADHwZ8srUtjXQAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/4b5288012dc4b32ae7ff21fccac98de1/b184f/eth-diamond-black-gray.webp 750w,/static/4b5288012dc4b32ae7ff21fccac98de1/941f9/eth-diamond-black-gray.webp 1080w,/static/4b5288012dc4b32ae7ff21fccac98de1/81b85/eth-diamond-black-gray.webp 1366w,/static/4b5288012dc4b32ae7ff21fccac98de1/448ee/eth-diamond-black-gray.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/4b5288012dc4b32ae7ff21fccac98de1/e1ebd/eth-diamond-black-gray.png"
                                data-srcset="/static/4b5288012dc4b32ae7ff21fccac98de1/863eb/eth-diamond-black-gray.png 750w,/static/4b5288012dc4b32ae7ff21fccac98de1/d0859/eth-diamond-black-gray.png 1080w,/static/4b5288012dc4b32ae7ff21fccac98de1/3bdcc/eth-diamond-black-gray.png 1366w,/static/4b5288012dc4b32ae7ff21fccac98de1/e1ebd/eth-diamond-black-gray.png 1920w"
                                alt="ETH diamond (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/4b5288012dc4b32ae7ff21fccac98de1/b184f/eth-diamond-black-gray.webp,/static/4b5288012dc4b32ae7ff21fccac98de1/941f9/eth-diamond-black-gray.webp,/static/4b5288012dc4b32ae7ff21fccac98de1/81b85/eth-diamond-black-gray.webp,/static/4b5288012dc4b32ae7ff21fccac98de1/448ee/eth-diamond-black-gray.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/4b5288012dc4b32ae7ff21fccac98de1/e1ebd/eth-diamond-black-gray.png"
                                  srcset="/static/4b5288012dc4b32ae7ff21fccac98de1/863eb/eth-diamond-black-gray.png,/static/4b5288012dc4b32ae7ff21fccac98de1/d0859/eth-diamond-black-gray.png,/static/4b5288012dc4b32ae7ff21fccac98de1/3bdcc/eth-diamond-black-gray.png,/static/4b5288012dc4b32ae7ff21fccac98de1/e1ebd/eth-diamond-black-gray.png"
                                  alt="ETH diamond (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/4b5288012dc4b32ae7ff21fccac98de1/e1ebd/eth-diamond-black-gray.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:100%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACMUlEQVQ4y2MQldEnGzFgFRWXM+QTVXfxTrVyjOEX0xCTNSBWM1CpkKSOvLp1Q8fyxPQeKUUzEWldURkDYjXziWpEJ9f1TduenjvVyTNdUEILq+UMmDr5xTQNLPx6pmzp6N+QkjUxKaNfXc8Vq36sftYrKJ8yYcbO9r71yZkTUrMnB4ZXAQUJ2AwKJxF1N780oM7OiRs7J25KzZ6UkNabljPF1CZcQEITzXIGtHBSULdu7FzRN31b9+TNDW0rY5O7kzP7gZbHJLbLKFsIS+ki62dAC6e4tMZJs3bXtS7LL5udmN7n7lPuF1wbGd8GDDlXnyw0nzPAdQqIa5rYBDd2rM4unAbUlpAGQu4+Zc4exS5eJd6B1dEJ3VoGnkKS2nD9CM3CUjrAUPUPrQA6EhzIUM0uniVu3uXuPhWWdimK6vbIcQ53Nkizio6Dnqm3vql/dGJrRh7Q/l6gZk+/KgfXPBVtd1UdF3l1G6C3sfhZRFpPQt44ObstOKpEUcPW1iUJ4mddo2BZZSs710RP/3xxOSNRaT3sASYgrqWkZdcxYUNV0wILhzCgVUB36hh5R8Q1AQ2SU7VC9jC2eBZVt3KMAAb4hBk7krJaPPzyUrOnZORO0zXxxRfPyMkzMrF60qxdPVO2AWMImEJwJW8smoFBIqlgXF4/B5hOUnMmhUbXS8gZi0jrEZmrDIF5WM/MG+h5YPJU1XYWRPUqgcIAqJRXRN0nJM/BLRmY7EgoDFBNwScLAGK2/ZiRQh8EAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/655aaefb744ae2f9f818095a436d38b5/b184f/eth-diamond-purple-purple.webp 750w,/static/655aaefb744ae2f9f818095a436d38b5/941f9/eth-diamond-purple-purple.webp 1080w,/static/655aaefb744ae2f9f818095a436d38b5/81b85/eth-diamond-purple-purple.webp 1366w,/static/655aaefb744ae2f9f818095a436d38b5/448ee/eth-diamond-purple-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/655aaefb744ae2f9f818095a436d38b5/e1ebd/eth-diamond-purple-purple.png"
                                data-srcset="/static/655aaefb744ae2f9f818095a436d38b5/863eb/eth-diamond-purple-purple.png 750w,/static/655aaefb744ae2f9f818095a436d38b5/d0859/eth-diamond-purple-purple.png 1080w,/static/655aaefb744ae2f9f818095a436d38b5/3bdcc/eth-diamond-purple-purple.png 1366w,/static/655aaefb744ae2f9f818095a436d38b5/e1ebd/eth-diamond-purple-purple.png 1920w"
                                alt="ETH diamond (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/655aaefb744ae2f9f818095a436d38b5/b184f/eth-diamond-purple-purple.webp,/static/655aaefb744ae2f9f818095a436d38b5/941f9/eth-diamond-purple-purple.webp,/static/655aaefb744ae2f9f818095a436d38b5/81b85/eth-diamond-purple-purple.webp,/static/655aaefb744ae2f9f818095a436d38b5/448ee/eth-diamond-purple-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/655aaefb744ae2f9f818095a436d38b5/e1ebd/eth-diamond-purple-purple.png"
                                  srcset="/static/655aaefb744ae2f9f818095a436d38b5/863eb/eth-diamond-purple-purple.png,/static/655aaefb744ae2f9f818095a436d38b5/d0859/eth-diamond-purple-purple.png,/static/655aaefb744ae2f9f818095a436d38b5/3bdcc/eth-diamond-purple-purple.png,/static/655aaefb744ae2f9f818095a436d38b5/e1ebd/eth-diamond-purple-purple.png"
                                  alt="ETH diamond (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/655aaefb744ae2f9f818095a436d38b5/e1ebd/eth-diamond-purple-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH diamond (white)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:100%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/jpeg;base64,/9j/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wgARCAAUABQDASIAAhEBAxEB/8QAGQABAAIDAAAAAAAAAAAAAAAAAAYJAQcI/8QAFwEAAwEAAAAAAAAAAAAAAAAAAgMEBf/aAAwDAQACEAMQAAABvd5A3BWpuTXUsMOlBRgzsLL/xAAcEAACAQUBAAAAAAAAAAAAAAAFBgcAAQIDBBD/2gAIAQEAAQUCKEeYRwR3Im8ww1I4S5xcQ0TDa8+BVwSBzr//xAAeEQABBAEFAAAAAAAAAAAAAAABAgMRMRAyUWGBkf/aAAgBAwEBPwFxaw8gCtp1TfnExg2O8f/EAB8RAAICAgIDAQAAAAAAAAAAAAECERIEIQMxAAUTEP/aAAgBAgEBPwHBxsTk9fmPzMfrBg0J+VIZCG6FjprFbA1G9nxWYJyKGIVq2AJhoaRI6MHYnr8//8QAJxAAAgIBAwIFBQAAAAAAAAAAAgMBBAUREhMQIQAUIjEyI0FRUnT/2gAIAQEABj8CtZG2W1FVRML9jn2BQfljTkVhH3Io8ZnAZh0c9hxZDD9+whxAVnFB/OGj68T6iDzOvxjo1XNkEjVbFtk40hl+xYHBMmseg3QRBcpVeWuwhgiS4WiAnVYrOtyL6divk9MH5mtXqVEccjbzF24kTS18/RViKqXWHEcjYu10bynpkmYytCDylybloveZLbALUHaNlZMbuFMelcmyY+U9P//EABoQAAMBAQEBAAAAAAAAAAAAAAERIQAxEMH/2gAIAQEAAT8hidt8EX4ZkZzHX83ipWqIDCRnDgNuI4pwBac2AdpnJFth8NwG9mb0MQAmOF1qI8v/2gAMAwEAAgADAAAAEDjIQP/EABgRAQEBAQEAAAAAAAAAAAAAAAERITEQ/9oACAEDAQE/EIQ5jhqCjrxIpq7PALoKYUKULHpYWdm+f//EABgRAQEBAQEAAAAAAAAAAAAAAAERIRBB/9oACAECAQE/ECyMXTIBm7FCQBwIxMolRk8QNaR5/8QAHRABAAEEAwEAAAAAAAAAAAAAAREAECExQVGBkf/aAAgBAQABPxCOkyWDICxA9OpAyqKGn6+koCWwjSUTxoqr4M1IbBvStuFBR8qAQffJZjzXXVIBQIiIkiOxHCPIyJhIpPMWO2Zkh2WnW//Z"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/6f05d59dc633140e4b547cb92f22e781/b184f/eth-diamond-purple-white.webp 750w,/static/6f05d59dc633140e4b547cb92f22e781/941f9/eth-diamond-purple-white.webp 1080w,/static/6f05d59dc633140e4b547cb92f22e781/81b85/eth-diamond-purple-white.webp 1366w,/static/6f05d59dc633140e4b547cb92f22e781/448ee/eth-diamond-purple-white.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/6f05d59dc633140e4b547cb92f22e781/40129/eth-diamond-purple-white.jpg"
                                data-srcset="/static/6f05d59dc633140e4b547cb92f22e781/1d86d/eth-diamond-purple-white.jpg 750w,/static/6f05d59dc633140e4b547cb92f22e781/7f16d/eth-diamond-purple-white.jpg 1080w,/static/6f05d59dc633140e4b547cb92f22e781/1c5c4/eth-diamond-purple-white.jpg 1366w,/static/6f05d59dc633140e4b547cb92f22e781/40129/eth-diamond-purple-white.jpg 1920w"
                                alt="ETH diamond (white)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/6f05d59dc633140e4b547cb92f22e781/b184f/eth-diamond-purple-white.webp,/static/6f05d59dc633140e4b547cb92f22e781/941f9/eth-diamond-purple-white.webp,/static/6f05d59dc633140e4b547cb92f22e781/81b85/eth-diamond-purple-white.webp,/static/6f05d59dc633140e4b547cb92f22e781/448ee/eth-diamond-purple-white.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/6f05d59dc633140e4b547cb92f22e781/40129/eth-diamond-purple-white.jpg"
                                  srcset="/static/6f05d59dc633140e4b547cb92f22e781/1d86d/eth-diamond-purple-white.jpg,/static/6f05d59dc633140e4b547cb92f22e781/7f16d/eth-diamond-purple-white.jpg,/static/6f05d59dc633140e4b547cb92f22e781/1c5c4/eth-diamond-purple-white.jpg,/static/6f05d59dc633140e4b547cb92f22e781/40129/eth-diamond-purple-white.jpg"
                                  alt="ETH diamond (white)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/6f05d59dc633140e4b547cb92f22e781/40129/eth-diamond-purple-white.jpg"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo portrait (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA10lEQVQoz5WRWQ6CQBBEOY/cRm5iOIr6YfRARkMUZXFhUVD2Zdj8taIJiSaNmPRHZaZfV/UMF2c5VVGaoToaOPIuzRJWJDnr4DnKMytK83I9GWZe1RRPwmXzmM0XI1Es6uYPGK15WR0NcygIA56XtjLFkztj2/FkCucgTqB7ObcOrKp3qrZcrWEbJmkvGIHReg9CPBg0q5uUFW2RMLL5USzvFUU/wFPRdAj1pc+WrR9P0mb7tfkHDE/Dsr0wxBTr6lzcm+v5+LC3cO7ej9hIiymoNipOqNhPxvxYt2tKEocAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/c3bcc8c47890ffd2a2c329972c73d0fd/f1c30/ethereum-logo-portrait-black-gray.webp 750w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/54311/ethereum-logo-portrait-black-gray.webp 1080w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/8d6ee/ethereum-logo-portrait-black-gray.webp 1366w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/702da/ethereum-logo-portrait-black-gray.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/c3bcc8c47890ffd2a2c329972c73d0fd/e018d/ethereum-logo-portrait-black-gray.png"
                                data-srcset="/static/c3bcc8c47890ffd2a2c329972c73d0fd/0c057/ethereum-logo-portrait-black-gray.png 750w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/900d9/ethereum-logo-portrait-black-gray.png 1080w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/35942/ethereum-logo-portrait-black-gray.png 1366w,/static/c3bcc8c47890ffd2a2c329972c73d0fd/e018d/ethereum-logo-portrait-black-gray.png 1920w"
                                alt="ETH logo portrait (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/c3bcc8c47890ffd2a2c329972c73d0fd/f1c30/ethereum-logo-portrait-black-gray.webp,/static/c3bcc8c47890ffd2a2c329972c73d0fd/54311/ethereum-logo-portrait-black-gray.webp,/static/c3bcc8c47890ffd2a2c329972c73d0fd/8d6ee/ethereum-logo-portrait-black-gray.webp,/static/c3bcc8c47890ffd2a2c329972c73d0fd/702da/ethereum-logo-portrait-black-gray.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/c3bcc8c47890ffd2a2c329972c73d0fd/e018d/ethereum-logo-portrait-black-gray.png"
                                  srcset="/static/c3bcc8c47890ffd2a2c329972c73d0fd/0c057/ethereum-logo-portrait-black-gray.png,/static/c3bcc8c47890ffd2a2c329972c73d0fd/900d9/ethereum-logo-portrait-black-gray.png,/static/c3bcc8c47890ffd2a2c329972c73d0fd/35942/ethereum-logo-portrait-black-gray.png,/static/c3bcc8c47890ffd2a2c329972c73d0fd/e018d/ethereum-logo-portrait-black-gray.png"
                                  alt="ETH logo portrait (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/c3bcc8c47890ffd2a2c329972c73d0fd/e018d/ethereum-logo-portrait-black-gray.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo landscape (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAArElEQVQoz62S7wqCMBTFfczqWcKHivoc9Q6hlsrmzIHm3JwZ9alDgxxSQSs4XO497Izf/ninRjrLG82VaNzDQrV2Hv2H7Tw7xssqihPVnWup4KA2rYb/lPHH4bIW/fW2WK4m0xnNjyRjuyAkjCWEYkTNC57SDP2LMNh0f1lvtnPfx6L0kYGCaI8KnPAQw2cFt08xYBskkEvdoTeQNvNb7EFSuVzYH57qp0/yle6fJV87t1trhQAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/2b08b06119b8fe097637352bc8d75610/f1c30/ethereum-logo-landscape-black-gray.webp 750w,/static/2b08b06119b8fe097637352bc8d75610/54311/ethereum-logo-landscape-black-gray.webp 1080w,/static/2b08b06119b8fe097637352bc8d75610/8d6ee/ethereum-logo-landscape-black-gray.webp 1366w,/static/2b08b06119b8fe097637352bc8d75610/702da/ethereum-logo-landscape-black-gray.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/2b08b06119b8fe097637352bc8d75610/e018d/ethereum-logo-landscape-black-gray.png"
                                data-srcset="/static/2b08b06119b8fe097637352bc8d75610/0c057/ethereum-logo-landscape-black-gray.png 750w,/static/2b08b06119b8fe097637352bc8d75610/900d9/ethereum-logo-landscape-black-gray.png 1080w,/static/2b08b06119b8fe097637352bc8d75610/35942/ethereum-logo-landscape-black-gray.png 1366w,/static/2b08b06119b8fe097637352bc8d75610/e018d/ethereum-logo-landscape-black-gray.png 1920w"
                                alt="ETH logo landscape (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/2b08b06119b8fe097637352bc8d75610/f1c30/ethereum-logo-landscape-black-gray.webp,/static/2b08b06119b8fe097637352bc8d75610/54311/ethereum-logo-landscape-black-gray.webp,/static/2b08b06119b8fe097637352bc8d75610/8d6ee/ethereum-logo-landscape-black-gray.webp,/static/2b08b06119b8fe097637352bc8d75610/702da/ethereum-logo-landscape-black-gray.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/2b08b06119b8fe097637352bc8d75610/e018d/ethereum-logo-landscape-black-gray.png"
                                  srcset="/static/2b08b06119b8fe097637352bc8d75610/0c057/ethereum-logo-landscape-black-gray.png,/static/2b08b06119b8fe097637352bc8d75610/900d9/ethereum-logo-landscape-black-gray.png,/static/2b08b06119b8fe097637352bc8d75610/35942/ethereum-logo-landscape-black-gray.png,/static/2b08b06119b8fe097637352bc8d75610/e018d/ethereum-logo-landscape-black-gray.png"
                                  alt="ETH logo landscape (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/2b08b06119b8fe097637352bc8d75610/e018d/ethereum-logo-landscape-black-gray.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH wordmark (gray)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAjklEQVQoz8WRCwqDMBBEvf/hpKJSo/GTmE9pYqL2Aq4KoqLWFkF4LJOEWYaJo239N86VZmUsMBd6PA43X8y2/ZimBQFzoqobmPB0ZFaVIYynBUE4ZUJSxikXpGTyrUohM0J3zbD+pbT78JIsx3nh+UEYoSjBIJ4o7kUQrpJvx15xKvaip6mh84Xd988/0QE1h2LPHHVLnwAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/4c539a6d5efc17c084d7c42f30803612/f1c30/ethereum-wordmark-black-gray.webp 750w,/static/4c539a6d5efc17c084d7c42f30803612/54311/ethereum-wordmark-black-gray.webp 1080w,/static/4c539a6d5efc17c084d7c42f30803612/8d6ee/ethereum-wordmark-black-gray.webp 1366w,/static/4c539a6d5efc17c084d7c42f30803612/702da/ethereum-wordmark-black-gray.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/4c539a6d5efc17c084d7c42f30803612/e018d/ethereum-wordmark-black-gray.png"
                                data-srcset="/static/4c539a6d5efc17c084d7c42f30803612/0c057/ethereum-wordmark-black-gray.png 750w,/static/4c539a6d5efc17c084d7c42f30803612/900d9/ethereum-wordmark-black-gray.png 1080w,/static/4c539a6d5efc17c084d7c42f30803612/35942/ethereum-wordmark-black-gray.png 1366w,/static/4c539a6d5efc17c084d7c42f30803612/e018d/ethereum-wordmark-black-gray.png 1920w"
                                alt="ETH wordmark (gray)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/4c539a6d5efc17c084d7c42f30803612/f1c30/ethereum-wordmark-black-gray.webp,/static/4c539a6d5efc17c084d7c42f30803612/54311/ethereum-wordmark-black-gray.webp,/static/4c539a6d5efc17c084d7c42f30803612/8d6ee/ethereum-wordmark-black-gray.webp,/static/4c539a6d5efc17c084d7c42f30803612/702da/ethereum-wordmark-black-gray.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/4c539a6d5efc17c084d7c42f30803612/e018d/ethereum-wordmark-black-gray.png"
                                  srcset="/static/4c539a6d5efc17c084d7c42f30803612/0c057/ethereum-wordmark-black-gray.png,/static/4c539a6d5efc17c084d7c42f30803612/900d9/ethereum-wordmark-black-gray.png,/static/4c539a6d5efc17c084d7c42f30803612/35942/ethereum-wordmark-black-gray.png,/static/4c539a6d5efc17c084d7c42f30803612/e018d/ethereum-wordmark-black-gray.png"
                                  alt="ETH wordmark (gray)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/4c539a6d5efc17c084d7c42f30803612/e018d/ethereum-wordmark-black-gray.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo portrait (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA1UlEQVQoz2MQldHHhcRkDYAIjwIGPDpFpPWAiGTNQJ1CktqKGnayKhbCUjok2ywspZua2+0ZkC8gronL8QxYrRWU0NY2ck/NnujpVyarYgk0iASbgV6VUjQJj6txcEslJ8CAlhta+KnruQA9T6yzhSR1xGUNJRSMgU4VFNcSlNSBhDnWkGdAdqq4nJG6nrOKtoOqjhOQBCJlLXtVHUcFdWslLTtNQzdRVCcwoAaVobyaNdBaCXkjOVVLGWVzKUVTEEPFAsiQVjIj4GygayHOgzDQEJpiAA9IWp+TvWgbAAAAAElFTkSuQmCC"
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/25eba23f2f461f4f8bbb65131f68cff6/f1c30/ethereum-logo-portrait-purple-purple.webp 750w,/static/25eba23f2f461f4f8bbb65131f68cff6/54311/ethereum-logo-portrait-purple-purple.webp 1080w,/static/25eba23f2f461f4f8bbb65131f68cff6/8d6ee/ethereum-logo-portrait-purple-purple.webp 1366w,/static/25eba23f2f461f4f8bbb65131f68cff6/702da/ethereum-logo-portrait-purple-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/25eba23f2f461f4f8bbb65131f68cff6/e018d/ethereum-logo-portrait-purple-purple.png"
                                data-srcset="/static/25eba23f2f461f4f8bbb65131f68cff6/0c057/ethereum-logo-portrait-purple-purple.png 750w,/static/25eba23f2f461f4f8bbb65131f68cff6/900d9/ethereum-logo-portrait-purple-purple.png 1080w,/static/25eba23f2f461f4f8bbb65131f68cff6/35942/ethereum-logo-portrait-purple-purple.png 1366w,/static/25eba23f2f461f4f8bbb65131f68cff6/e018d/ethereum-logo-portrait-purple-purple.png 1920w"
                                alt="ETH logo portrait (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/25eba23f2f461f4f8bbb65131f68cff6/f1c30/ethereum-logo-portrait-purple-purple.webp,/static/25eba23f2f461f4f8bbb65131f68cff6/54311/ethereum-logo-portrait-purple-purple.webp,/static/25eba23f2f461f4f8bbb65131f68cff6/8d6ee/ethereum-logo-portrait-purple-purple.webp,/static/25eba23f2f461f4f8bbb65131f68cff6/702da/ethereum-logo-portrait-purple-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/25eba23f2f461f4f8bbb65131f68cff6/e018d/ethereum-logo-portrait-purple-purple.png"
                                  srcset="/static/25eba23f2f461f4f8bbb65131f68cff6/0c057/ethereum-logo-portrait-purple-purple.png,/static/25eba23f2f461f4f8bbb65131f68cff6/900d9/ethereum-logo-portrait-purple-purple.png,/static/25eba23f2f461f4f8bbb65131f68cff6/35942/ethereum-logo-portrait-purple-purple.png,/static/25eba23f2f461f4f8bbb65131f68cff6/e018d/ethereum-logo-portrait-purple-purple.png"
                                  alt="ETH logo portrait (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/25eba23f2f461f4f8bbb65131f68cff6/e018d/ethereum-logo-portrait-purple-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo landscape (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAmElEQVQoz2MQldEnGzFgCBmQqVlEWk9YSpcczSLSulKKpqo6jkKS2kBTIIJAs4BsZIRFs5isAb+Yhq1LXGh0vaS8sZyqpbKWnayKuZyalYyyubyaFdBcOVUQG7tmAXEtY6sgV69MaSUzebAeIFLSspdVsVDUsFXStANqBkoBVWJ1th5QAmibsJQOmlMJOBvZCEqiSn8oaAYAblJW1fdoDcAAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/caa67b97758c20ad6c1ca9bbf91be2f7/f1c30/ethereum-logo-landscape-purple-purple.webp 750w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/54311/ethereum-logo-landscape-purple-purple.webp 1080w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/8d6ee/ethereum-logo-landscape-purple-purple.webp 1366w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/702da/ethereum-logo-landscape-purple-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/caa67b97758c20ad6c1ca9bbf91be2f7/e018d/ethereum-logo-landscape-purple-purple.png"
                                data-srcset="/static/caa67b97758c20ad6c1ca9bbf91be2f7/0c057/ethereum-logo-landscape-purple-purple.png 750w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/900d9/ethereum-logo-landscape-purple-purple.png 1080w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/35942/ethereum-logo-landscape-purple-purple.png 1366w,/static/caa67b97758c20ad6c1ca9bbf91be2f7/e018d/ethereum-logo-landscape-purple-purple.png 1920w"
                                alt="ETH logo landscape (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/caa67b97758c20ad6c1ca9bbf91be2f7/f1c30/ethereum-logo-landscape-purple-purple.webp,/static/caa67b97758c20ad6c1ca9bbf91be2f7/54311/ethereum-logo-landscape-purple-purple.webp,/static/caa67b97758c20ad6c1ca9bbf91be2f7/8d6ee/ethereum-logo-landscape-purple-purple.webp,/static/caa67b97758c20ad6c1ca9bbf91be2f7/702da/ethereum-logo-landscape-purple-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/caa67b97758c20ad6c1ca9bbf91be2f7/e018d/ethereum-logo-landscape-purple-purple.png"
                                  srcset="/static/caa67b97758c20ad6c1ca9bbf91be2f7/0c057/ethereum-logo-landscape-purple-purple.png,/static/caa67b97758c20ad6c1ca9bbf91be2f7/900d9/ethereum-logo-landscape-purple-purple.png,/static/caa67b97758c20ad6c1ca9bbf91be2f7/35942/ethereum-logo-landscape-purple-purple.png,/static/caa67b97758c20ad6c1ca9bbf91be2f7/e018d/ethereum-logo-landscape-purple-purple.png"
                                  alt="ETH logo landscape (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/caa67b97758c20ad6c1ca9bbf91be2f7/e018d/ethereum-logo-landscape-purple-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH wordmark (purple)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAeklEQVQoz2MQldEnGzFQV7MBGIHYYrIobDAXr2ZhKV0RaT04AxkBRfBpBpoto2wur2alqGErrWQqrWQGREARCXljIENO1RKnZqDZEvJGmgauCurW8mrW6nrOKtr2Spq2QIayFoihpueE5nLszoY4kjRnUxpgAxnPJCAAmOdVDF0ccMgAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/f1d81366c70a7a8118777a2595e80904/f1c30/ethereum-wordmark-purple-purple.webp 750w,/static/f1d81366c70a7a8118777a2595e80904/54311/ethereum-wordmark-purple-purple.webp 1080w,/static/f1d81366c70a7a8118777a2595e80904/8d6ee/ethereum-wordmark-purple-purple.webp 1366w,/static/f1d81366c70a7a8118777a2595e80904/702da/ethereum-wordmark-purple-purple.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/f1d81366c70a7a8118777a2595e80904/e018d/ethereum-wordmark-purple-purple.png"
                                data-srcset="/static/f1d81366c70a7a8118777a2595e80904/0c057/ethereum-wordmark-purple-purple.png 750w,/static/f1d81366c70a7a8118777a2595e80904/900d9/ethereum-wordmark-purple-purple.png 1080w,/static/f1d81366c70a7a8118777a2595e80904/35942/ethereum-wordmark-purple-purple.png 1366w,/static/f1d81366c70a7a8118777a2595e80904/e018d/ethereum-wordmark-purple-purple.png 1920w"
                                alt="ETH wordmark (purple)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/f1d81366c70a7a8118777a2595e80904/f1c30/ethereum-wordmark-purple-purple.webp,/static/f1d81366c70a7a8118777a2595e80904/54311/ethereum-wordmark-purple-purple.webp,/static/f1d81366c70a7a8118777a2595e80904/8d6ee/ethereum-wordmark-purple-purple.webp,/static/f1d81366c70a7a8118777a2595e80904/702da/ethereum-wordmark-purple-purple.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/f1d81366c70a7a8118777a2595e80904/e018d/ethereum-wordmark-purple-purple.png"
                                  srcset="/static/f1d81366c70a7a8118777a2595e80904/0c057/ethereum-wordmark-purple-purple.png,/static/f1d81366c70a7a8118777a2595e80904/900d9/ethereum-wordmark-purple-purple.png,/static/f1d81366c70a7a8118777a2595e80904/35942/ethereum-wordmark-purple-purple.png,/static/f1d81366c70a7a8118777a2595e80904/e018d/ethereum-wordmark-purple-purple.png"
                                  alt="ETH wordmark (purple)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/f1d81366c70a7a8118777a2595e80904/e018d/ethereum-wordmark-purple-purple.png"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                  <div class="assets__Row-sc-4yoqxh-2 kbiKQJ">
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo portrait (white)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAArUlEQVQoz7WRvw4BQRDG93kVovAIorlSPATPIGqJ5qIRCpWKtW7/mt2dc+ZIrhDLifiKyVd8v5nZHValdb3rTYBVP4ilZlJdb/b5akemTMxnr8i6eh8H2bTTHRl7adq1geucVG6YTXr98ZHLL+AmulhuZ/M8RX6AY8QQsO2HEQM+Wgu0sw+RPPgAECIimcfjkzBieeCSQsbCSeiisEo7LrRSjow4m6f1/3DnlroBI9+GojTEnnwAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/0e6a20f95728214252090016c64d328f/f1c30/ethereum-logo-portrait-purple-white.webp 750w,/static/0e6a20f95728214252090016c64d328f/54311/ethereum-logo-portrait-purple-white.webp 1080w,/static/0e6a20f95728214252090016c64d328f/8d6ee/ethereum-logo-portrait-purple-white.webp 1366w,/static/0e6a20f95728214252090016c64d328f/702da/ethereum-logo-portrait-purple-white.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/0e6a20f95728214252090016c64d328f/8cf01/ethereum-logo-portrait-purple-white.jpg"
                                data-srcset="/static/0e6a20f95728214252090016c64d328f/245e5/ethereum-logo-portrait-purple-white.jpg 750w,/static/0e6a20f95728214252090016c64d328f/f1a6c/ethereum-logo-portrait-purple-white.jpg 1080w,/static/0e6a20f95728214252090016c64d328f/6a52a/ethereum-logo-portrait-purple-white.jpg 1366w,/static/0e6a20f95728214252090016c64d328f/8cf01/ethereum-logo-portrait-purple-white.jpg 1920w"
                                alt="ETH logo portrait (white)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/0e6a20f95728214252090016c64d328f/f1c30/ethereum-logo-portrait-purple-white.webp,/static/0e6a20f95728214252090016c64d328f/54311/ethereum-logo-portrait-purple-white.webp,/static/0e6a20f95728214252090016c64d328f/8d6ee/ethereum-logo-portrait-purple-white.webp,/static/0e6a20f95728214252090016c64d328f/702da/ethereum-logo-portrait-purple-white.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/0e6a20f95728214252090016c64d328f/8cf01/ethereum-logo-portrait-purple-white.jpg"
                                  srcset="/static/0e6a20f95728214252090016c64d328f/245e5/ethereum-logo-portrait-purple-white.jpg,/static/0e6a20f95728214252090016c64d328f/f1a6c/ethereum-logo-portrait-purple-white.jpg,/static/0e6a20f95728214252090016c64d328f/6a52a/ethereum-logo-portrait-purple-white.jpg,/static/0e6a20f95728214252090016c64d328f/8cf01/ethereum-logo-portrait-purple-white.jpg"
                                  alt="ETH logo portrait (white)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/0e6a20f95728214252090016c64d328f/8cf01/ethereum-logo-portrait-purple-white.jpg"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH logo landscape (white)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAg0lEQVQoz2P4TwFgoKbmf//+0ddmiIVPnr3dufsckPH3778fP38DSaDwPzCACAIRFs0Q0YNHrpRWzv7w8evnL9+B5NdvP96++/z5y4/3H758+vTt46dvX7/9RPYWirOBGq7ffAxk/PnzF2gj0GIg4x+Y/PX7z1+IS2gS2nDvDUQ8kwQAToeMDZPcB/AAAAAASUVORK5CYII="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/ef47a764bc22e35800d00eb4c7ee2781/f1c30/ethereum-logo-landscape-purple-white.webp 750w,/static/ef47a764bc22e35800d00eb4c7ee2781/54311/ethereum-logo-landscape-purple-white.webp 1080w,/static/ef47a764bc22e35800d00eb4c7ee2781/8d6ee/ethereum-logo-landscape-purple-white.webp 1366w,/static/ef47a764bc22e35800d00eb4c7ee2781/702da/ethereum-logo-landscape-purple-white.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/ef47a764bc22e35800d00eb4c7ee2781/8cf01/ethereum-logo-landscape-purple-white.jpg"
                                data-srcset="/static/ef47a764bc22e35800d00eb4c7ee2781/245e5/ethereum-logo-landscape-purple-white.jpg 750w,/static/ef47a764bc22e35800d00eb4c7ee2781/f1a6c/ethereum-logo-landscape-purple-white.jpg 1080w,/static/ef47a764bc22e35800d00eb4c7ee2781/6a52a/ethereum-logo-landscape-purple-white.jpg 1366w,/static/ef47a764bc22e35800d00eb4c7ee2781/8cf01/ethereum-logo-landscape-purple-white.jpg 1920w"
                                alt="ETH logo landscape (white)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/ef47a764bc22e35800d00eb4c7ee2781/f1c30/ethereum-logo-landscape-purple-white.webp,/static/ef47a764bc22e35800d00eb4c7ee2781/54311/ethereum-logo-landscape-purple-white.webp,/static/ef47a764bc22e35800d00eb4c7ee2781/8d6ee/ethereum-logo-landscape-purple-white.webp,/static/ef47a764bc22e35800d00eb4c7ee2781/702da/ethereum-logo-landscape-purple-white.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/ef47a764bc22e35800d00eb4c7ee2781/8cf01/ethereum-logo-landscape-purple-white.jpg"
                                  srcset="/static/ef47a764bc22e35800d00eb4c7ee2781/245e5/ethereum-logo-landscape-purple-white.jpg,/static/ef47a764bc22e35800d00eb4c7ee2781/f1a6c/ethereum-logo-landscape-purple-white.jpg,/static/ef47a764bc22e35800d00eb4c7ee2781/6a52a/ethereum-logo-landscape-purple-white.jpg,/static/ef47a764bc22e35800d00eb4c7ee2781/8cf01/ethereum-logo-landscape-purple-white.jpg"
                                  alt="ETH logo landscape (white)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/ef47a764bc22e35800d00eb4c7ee2781/8cf01/ethereum-logo-landscape-purple-white.jpg"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                    <div class="AssetDownload__Container-sc-1etxw81-0 jdkhYU">
                      <h4>ETH wordmark (white)</h4>
                      <div>
                        <div class="AssetDownload__ImageContainer-sc-1etxw81-2 ikiGpt">
                          <div data-gatsby-image-wrapper=""
                            class="gatsby-image-wrapper AssetDownload__Image-sc-1etxw81-1 ktdNpG">
                            <div aria-hidden="true" style="padding-top:56.25%"></div><img aria-hidden="true"
                              data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                              decoding="async"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAALCAIAAADwazoUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAWElEQVQoz+WRywqAIBRE/f9PLVHvVUvvy6RtEES7GmZxNgcGxo0XcZ+RVY1ZHsuiunpMUGMqIeaYKmD1AXPZAbfFo93IZtYa6ZnemUhYZMLsBLrMcT+86gDv149LUTgMgAAAAABJRU5ErkJggg=="
                              alt="">
                            <picture>
                              <source type="image/webp"
                                data-srcset="/static/707cc3ebf60db7cfdba4dad417954a26/f1c30/ethereum-wordmark-purple-white.webp 750w,/static/707cc3ebf60db7cfdba4dad417954a26/54311/ethereum-wordmark-purple-white.webp 1080w,/static/707cc3ebf60db7cfdba4dad417954a26/8d6ee/ethereum-wordmark-purple-white.webp 1366w,/static/707cc3ebf60db7cfdba4dad417954a26/702da/ethereum-wordmark-purple-white.webp 1920w"
                                sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="100vw" decoding="async" loading="lazy"
                                data-src="/static/707cc3ebf60db7cfdba4dad417954a26/8cf01/ethereum-wordmark-purple-white.jpg"
                                data-srcset="/static/707cc3ebf60db7cfdba4dad417954a26/245e5/ethereum-wordmark-purple-white.jpg 750w,/static/707cc3ebf60db7cfdba4dad417954a26/f1a6c/ethereum-wordmark-purple-white.jpg 1080w,/static/707cc3ebf60db7cfdba4dad417954a26/6a52a/ethereum-wordmark-purple-white.jpg 1366w,/static/707cc3ebf60db7cfdba4dad417954a26/8cf01/ethereum-wordmark-purple-white.jpg 1920w"
                                alt="ETH wordmark (white)">
                            </picture><noscript>
                              <picture>
                                <source type="image/webp"
                                  srcset="/static/707cc3ebf60db7cfdba4dad417954a26/f1c30/ethereum-wordmark-purple-white.webp,/static/707cc3ebf60db7cfdba4dad417954a26/54311/ethereum-wordmark-purple-white.webp,/static/707cc3ebf60db7cfdba4dad417954a26/8d6ee/ethereum-wordmark-purple-white.webp,/static/707cc3ebf60db7cfdba4dad417954a26/702da/ethereum-wordmark-purple-white.webp"
                                  sizes="100vw"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                  sizes="100vw" decoding="async" loading="lazy"
                                  src="/static/707cc3ebf60db7cfdba4dad417954a26/8cf01/ethereum-wordmark-purple-white.jpg"
                                  srcset="/static/707cc3ebf60db7cfdba4dad417954a26/245e5/ethereum-wordmark-purple-white.jpg,/static/707cc3ebf60db7cfdba4dad417954a26/f1a6c/ethereum-wordmark-purple-white.jpg,/static/707cc3ebf60db7cfdba4dad417954a26/6a52a/ethereum-wordmark-purple-white.jpg,/static/707cc3ebf60db7cfdba4dad417954a26/8cf01/ethereum-wordmark-purple-white.jpg"
                                  alt="ETH wordmark (white)">
                              </picture>
                            </noscript>
                            <script
                              type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                          </div>
                        </div>
                      </div>
                      <div class="AssetDownload__ButtonContainer-sc-1etxw81-5 jmLfmz"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                          href="https://ethstake.exchange/static/707cc3ebf60db7cfdba4dad417954a26/8cf01/ethereum-wordmark-purple-white.jpg"
                          target="_blank" rel="noopener noreferrer"><span>Download</span></a></div>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <footer class="Footer__StyledFooter-sc-1to993d-0 gvoBKJ">
          <div class="Footer__FooterTop-sc-1to993d-1 kFKfdz">
            <div class="Footer__LastUpdated-sc-1to993d-2 bWGwos"><span>Website last updated</span>:
              <!-- -->
              <!-- -->December 1, 2020
            </div>
            <div class="Footer__SocialIcons-sc-1to993d-9 kdLbod"><a
                href="https://github.com/ethereum/ethereum-org-website" target="_blank" rel="noopener noreferrer"
                aria-label="GitHub"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 496 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z">
                  </path>
                </svg></a><a href="https://twitter.com/ethdotorg" target="_blank" rel="noopener noreferrer"
                aria-label="Twitter"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 512 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                  </path>
                </svg></a><a href="https://youtube.com/channel/UCNOfzGXD_C9YMYmnefmPH0g" target="_blank"
                rel="noopener noreferrer" aria-label="Youtube"><svg stroke="currentColor" fill="currentColor"
                  stroke-width="0" viewbox="0 0 576 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                  </path>
                </svg></a><a href="https://discord.gg/CetY6Y4" target="_blank" rel="noopener noreferrer"
                aria-label="Discord"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 640 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                  </path>
                </svg></a></div>
          </div>
          <div class="Footer__LinkGrid-sc-1to993d-3 hlbLsM">
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Use Ethereum</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\staking\index.html"><span>Stake ETH</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Learn</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\history\index.html"><span>History of Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                      fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                      width="12px" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                      </path>
                    </svg></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\web3\index.html"><span>What is Web3?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Developers</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\index.html"><span>Get started</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/developers/docs/"><span>Documentation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\local-environment\index.html"><span>Set up local environment</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Ecosystem</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\community\index.html"><span>Community hub</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\foundation\index.html"><span>Ethereum Foundation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://blog.ethstake.exchange/" target="_blank" rel="noopener noreferrer"><span>Ethereum
                      Foundation Blog</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://esp.ethereum.foundation" target="_blank" rel="noopener noreferrer"><span>Ecosystem
                      Support Program</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/community/grants"><span>Ecosystem Grant Programs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a aria-current="page"
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz active"
                    href="index.html"><span>Ethereum brand assets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://devcon.org/" target="_blank" rel="noopener noreferrer"><span>Devcon</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Enterprise</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Enterprise</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>About ethstake.exchange</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html"><span>About us</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html#open-jobs"><span>Jobs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\contributing\index.html"><span>Contributing</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\languages\index.html"><span>Language support</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\privacy-policy\index.html"><span>Privacy policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\terms-of-use\index.html"><span>Terms of use</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\cookie-policy\index.html"><span>Cookie policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/cdn-cgi/l/email-protection#48383a2d3b3b082d3c203b3c29232d662d302b2029262f2d" target="_blank"
                    rel="noopener noreferrer"><span>Contact</span></a></li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <div id="gatsby-announcer"
      style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0"
      aria-live="assertive" aria-atomic="true"></div>
  </div>
  <script data-cfasync="false" src="../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let svgIcon = document.getElementsByClassName('Mobile__GlyphButton')[0]
    let menuBtn = document.getElementsByClassName('Mobile__MenuButton')
    let hideMenu = document.getElementsByClassName('Mobile__MobileModal')[0]
    let menuContainer = document.getElementsByClassName('Mobile__MenuContainer')[0]
    let menuUl = document.getElementsByClassName('Mobile__MenuItems')[0]
    let menuBottom = document.getElementsByClassName('Mobile__BottomMenu')[0]
    let searchPage = document.getElementsByClassName('Mobile__MenuContainer')[1]
    let closeBtn = document.getElementsByClassName('Mobile__CloseIconContainer')[0]
    let searchBtn = document.getElementsByClassName('Mobile__BottomItem')[0]
    menuBtn[1].addEventListener('click', function () {
      this.toggleAttribute('open')

      let type = this.hasAttribute('open')
      let path = svgIcon.getElementsByTagName('path')[0]
      let d = path.getAttribute('d')
      path.style='transition: all ease-in 0.25s;'
      console.log(path, d,'aaaa')
      if (type) {
        hideMenu.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuContainer.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        path.setAttribute('d','M 2 19 l 10 -14 l 0 0 l 10 14 M 2 19 l 10 7 M 12 26 l 10 -7 M 2 22 l 10 15 l 0 0 l 9 -15')
        setTimeout(()=>{
          path.setAttribute('d','M 2 13 l 0 -3 l 20 0 l 0 3 M 7 14 l 10 10 M 7 24 l 10 -10 M 2 25 l 0 3 l 20 0 l 0 -3')

        },700)

      } else {
        hideMenu.style = 'transition: all ease-in 0.2s;display: none; opacity: 0;'
        menuContainer.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        path.setAttribute('d','M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0')

      }
       // menuUl.toggleAttribute('class', 'gYetwr')
    })
    menuBtn[0].addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    searchBtn.addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    closeBtn.addEventListener('click', function () {
      console.log('111')
      searchPage.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'

    })
    console.log(menuBtn, '......')
    window.dev = undefined
    if (window.dev === true || !(navigator.doNotTrack === '1' || window.doNotTrack === '1')) {
      window._paq = window._paq || [];



      window._paq.push(['setTrackerUrl', 'https://matomo.ethstake.exchange/matomo.php']);
      window._paq.push(['setSiteId', '4']);
      window._paq.push(['enableHeartBeatTimer']);
      window.start = new Date();

      (function () {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript'; g.async = true; g.defer = true; g.src = 'https://matomo.ethstake.exchange/matomo.js'; s.parentNode.insertBefore(g, s);
      })();

      if (window.dev === true) {
        console.debug('[Matomo] Tracking initialized')
        console.debug('[Matomo] matomoUrl: https://matomo.ethstake.exchange, siteId: 4')
      }
    }
  </script><noscript><img
      src="https://matomo.ethstake.exchange/matomo.php?idsite=4&rec=1&url=https://ethstake.exchange/en/assets/"
      style="border:0" alt="tracker"></noscript>
  <script
    id="gatsby-script-loader">/*<![CDATA[*/window.pagePath = "/en/assets/"; window.___webpackCompilationHash = "0375e64c51e377521456";/*]]>*/</script>
  <script
    id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping = { "polyfill": ["/polyfill-b6350ac254e29f22a1e4.js"], "app": ["/app-b670b5ed3a389af0ed04.js"], "component---src-pages-404-js": ["/component---src-pages-404-js-a6aee0605f3068868f92.js"], "component---src-pages-assets-js": ["/component---src-pages-assets-js-ba78d988431646b4760b.js"], "component---src-pages-community-js": ["/component---src-pages-community-js-5ca1d5f82d581db39798.js"], "component---src-pages-conditional-dapps-js": ["/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js"], "component---src-pages-conditional-eth-js": ["/component---src-pages-conditional-eth-js-21644356026cce06349e.js"], "component---src-pages-conditional-wallets-index-js": ["/component---src-pages-conditional-wallets-index-js-6c55787f35fe5cc07ef3.js"], "component---src-pages-conditional-what-is-ethereum-js": ["/component---src-pages-conditional-what-is-ethereum-js-144e7cb6cba17bff7608.js"], "component---src-pages-contributing-translation-program-acknowledgements-js": ["/component---src-pages-contributing-translation-program-acknowledgements-js-a596bd2823410bf53c11.js"], "component---src-pages-contributing-translation-program-contributors-js": ["/component---src-pages-contributing-translation-program-contributors-js-4f2a18f6f82d2a84de27.js"], "component---src-pages-developers-index-js": ["/component---src-pages-developers-index-js-eba978370f325b125b03.js"], "component---src-pages-developers-learning-tools-js": ["/component---src-pages-developers-learning-tools-js-88bd1bcad31958f723e3.js"], "component---src-pages-developers-local-environment-js": ["/component---src-pages-developers-local-environment-js-768783f49122fff8d82f.js"], "component---src-pages-developers-tutorials-js": ["/component---src-pages-developers-tutorials-js-f82f9627cb9b2ce3318b.js"], "component---src-pages-get-eth-js": ["/component---src-pages-get-eth-js-e6c689f28770388e40be.js"], "component---src-pages-index-js": ["/component---src-pages-index-js-c7245d4f213dfe2095c4.js"], "component---src-pages-languages-js": ["/component---src-pages-languages-js-b1a3a1c01ec6bdcd87ac.js"], "component---src-pages-run-a-node-js": ["/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js"], "component---src-pages-stablecoins-js": ["/component---src-pages-stablecoins-js-4bc5b567f2c38baaaa5b.js"], "component---src-pages-stakenow-js": ["/component---src-pages-stakenow-js-40c51639947629777abf.js"], "component---src-pages-staking-deposit-contract-js": ["/component---src-pages-staking-deposit-contract-js-ac8273bd9f4711b38540.js"], "component---src-pages-staking-index-js": ["/component---src-pages-staking-index-js-3d50b1ef7b3b9814f6e2.js"], "component---src-pages-studio-js": ["/component---src-pages-studio-js-c16fabe2f5808fafce99.js"], "component---src-pages-upgrades-get-involved-bug-bounty-js": ["/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js"], "component---src-pages-upgrades-get-involved-index-js": ["/component---src-pages-upgrades-get-involved-index-js-207fa70ee7aa5720b909.js"], "component---src-pages-upgrades-index-js": ["/component---src-pages-upgrades-index-js-dd49283310be18b0a199.js"], "component---src-pages-upgrades-vision-js": ["/component---src-pages-upgrades-vision-js-2c86e72cede9c6155cbf.js"], "component---src-pages-wallets-find-wallet-js": ["/component---src-pages-wallets-find-wallet-js-97c53af70032ab1edbc4.js"], "component---src-templates-static-js": ["/component---src-templates-static-js-3cd4030c1e191ee95c2b.js"], "component---src-templates-upgrade-js": ["/component---src-templates-upgrade-js-700f5089c86b74f8484f.js"], "component---src-templates-use-cases-js": ["/component---src-templates-use-cases-js-75c0d7fec84444cc8c68.js"] };/*]]>*/</script>
  <script src="../../polyfill-b6350ac254e29f22a1e4.js" nomodule=""></script>
  <script src="../../component---src-pages-assets-js-ba78d988431646b4760b.js" async=""></script>
  <script src="../../app-b670b5ed3a389af0ed04.js" async=""></script>
  <script src="../../0f1ac474-e8f788f62189f421a856.js" async=""></script>
  <script src="../../0c428ae2-2128ff22fce458b543bd.js" async=""></script>
  <script src="../../1bfc9850-0f18e2d74feedfc6e426.js" async=""></script>
  <script src="../../ae51ba48-34d54094a2c04f215fb8.js" async=""></script>
  <script src="../../252f366e-2705b607be296edabcea.js" async=""></script>
  <script src="../../framework-4e285adfb333f1b50c05.js" async=""></script>
  <script src="/webpack-runtime-d600da28e471609bf3f3.js" async=""></script>
</body>

</html>