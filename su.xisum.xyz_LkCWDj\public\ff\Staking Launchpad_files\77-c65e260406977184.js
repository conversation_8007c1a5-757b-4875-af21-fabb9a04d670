(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[77],{92077:function(e,r,t){var n,i;n=function(){var e,r,t="2.0.6",n={},i={},o={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},a={currentLocale:o.currentLocale,zeroFormat:o.zeroFormat,nullFormat:o.nullFormat,defaultFormat:o.defaultFormat,scalePercentBy100:o.scalePercentBy100};function l(e,r){this._input=e,this._value=r}return(e=function(t){var i,o,u,s;if(e.isNumeral(t))i=t.value();else if(0===t||"undefined"===typeof t)i=0;else if(null===t||r.isNaN(t))i=null;else if("string"===typeof t)if(a.zeroFormat&&t===a.zeroFormat)i=0;else if(a.nullFormat&&t===a.nullFormat||!t.replace(/[^0-9]+/g,"").length)i=null;else{for(o in n)if((s="function"===typeof n[o].regexps.unformat?n[o].regexps.unformat():n[o].regexps.unformat)&&t.match(s)){u=n[o].unformat;break}i=(u=u||e._.stringToNumber)(t)}else i=Number(t)||null;return new l(t,i)}).version=t,e.isNumeral=function(e){return e instanceof l},e._=r={numberToFormat:function(r,t,n){var o,a,l,u,s,c,f,m=i[e.options.currentLocale],h=!1,d=!1,b=0,p="",g=1e12,v=1e9,_=1e6,y=1e3,F="",x=!1;if(r=r||0,a=Math.abs(r),e._.includes(t,"(")?(h=!0,t=t.replace(/[\(|\)]/g,"")):(e._.includes(t,"+")||e._.includes(t,"-"))&&(s=e._.includes(t,"+")?t.indexOf("+"):r<0?t.indexOf("-"):-1,t=t.replace(/[\+|\-]/g,"")),e._.includes(t,"a")&&(o=!!(o=t.match(/a(k|m|b|t)?/))&&o[1],e._.includes(t," a")&&(p=" "),t=t.replace(new RegExp(p+"a[kmbt]?"),""),a>=g&&!o||"t"===o?(p+=m.abbreviations.trillion,r/=g):a<g&&a>=v&&!o||"b"===o?(p+=m.abbreviations.billion,r/=v):a<v&&a>=_&&!o||"m"===o?(p+=m.abbreviations.million,r/=_):(a<_&&a>=y&&!o||"k"===o)&&(p+=m.abbreviations.thousand,r/=y)),e._.includes(t,"[.]")&&(d=!0,t=t.replace("[.]",".")),l=r.toString().split(".")[0],u=t.split(".")[1],c=t.indexOf(","),b=(t.split(".")[0].split(",")[0].match(/0/g)||[]).length,u?(e._.includes(u,"[")?(u=(u=u.replace("]","")).split("["),F=e._.toFixed(r,u[0].length+u[1].length,n,u[1].length)):F=e._.toFixed(r,u.length,n),l=F.split(".")[0],F=e._.includes(F,".")?m.delimiters.decimal+F.split(".")[1]:"",d&&0===Number(F.slice(1))&&(F="")):l=e._.toFixed(r,0,n),p&&!o&&Number(l)>=1e3&&p!==m.abbreviations.trillion)switch(l=String(Number(l)/1e3),p){case m.abbreviations.thousand:p=m.abbreviations.million;break;case m.abbreviations.million:p=m.abbreviations.billion;break;case m.abbreviations.billion:p=m.abbreviations.trillion}if(e._.includes(l,"-")&&(l=l.slice(1),x=!0),l.length<b)for(var w=b-l.length;w>0;w--)l="0"+l;return c>-1&&(l=l.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+m.delimiters.thousands)),0===t.indexOf(".")&&(l=""),f=l+F+(p||""),h?f=(h&&x?"(":"")+f+(h&&x?")":""):s>=0?f=0===s?(x?"-":"+")+f:f+(x?"-":"+"):x&&(f="-"+f),f},stringToNumber:function(e){var r,t,n,o=i[a.currentLocale],l=e,u={thousand:3,million:6,billion:9,trillion:12};if(a.zeroFormat&&e===a.zeroFormat)t=0;else if(a.nullFormat&&e===a.nullFormat||!e.replace(/[^0-9]+/g,"").length)t=null;else{for(r in t=1,"."!==o.delimiters.decimal&&(e=e.replace(/\./g,"").replace(o.delimiters.decimal,".")),u)if(n=new RegExp("[^a-zA-Z]"+o.abbreviations[r]+"(?:\\)|(\\"+o.currency.symbol+")?(?:\\))?)?$"),l.match(n)){t*=Math.pow(10,u[r]);break}t*=(e.split("-").length+Math.min(e.split("(").length-1,e.split(")").length-1))%2?1:-1,e=e.replace(/[^0-9\.]+/g,""),t*=Number(e)}return t},isNaN:function(e){return"number"===typeof e&&isNaN(e)},includes:function(e,r){return-1!==e.indexOf(r)},insert:function(e,r,t){return e.slice(0,t)+r+e.slice(t)},reduce:function(e,r){if(null===this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!==typeof r)throw new TypeError(r+" is not a function");var t,n=Object(e),i=n.length>>>0,o=0;if(3===arguments.length)t=arguments[2];else{for(;o<i&&!(o in n);)o++;if(o>=i)throw new TypeError("Reduce of empty array with no initial value");t=n[o++]}for(;o<i;o++)o in n&&(t=r(t,n[o],o,n));return t},multiplier:function(e){var r=e.toString().split(".");return r.length<2?1:Math.pow(10,r[1].length)},correctionFactor:function(){return Array.prototype.slice.call(arguments).reduce((function(e,t){var n=r.multiplier(t);return e>n?e:n}),1)},toFixed:function(e,r,t,n){var i,o,a,l,u=e.toString().split("."),s=r-(n||0);return i=2===u.length?Math.min(Math.max(u[1].length,s),r):s,a=Math.pow(10,i),l=(t(e+"e+"+i)/a).toFixed(i),n>r-i&&(o=new RegExp("\\.?0{1,"+(n-(r-i))+"}$"),l=l.replace(o,"")),l}},e.options=a,e.formats=n,e.locales=i,e.locale=function(e){return e&&(a.currentLocale=e.toLowerCase()),a.currentLocale},e.localeData=function(e){if(!e)return i[a.currentLocale];if(e=e.toLowerCase(),!i[e])throw new Error("Unknown locale : "+e);return i[e]},e.reset=function(){for(var e in o)a[e]=o[e]},e.zeroFormat=function(e){a.zeroFormat="string"===typeof e?e:null},e.nullFormat=function(e){a.nullFormat="string"===typeof e?e:null},e.defaultFormat=function(e){a.defaultFormat="string"===typeof e?e:"0.0"},e.register=function(e,r,t){if(r=r.toLowerCase(),this[e+"s"][r])throw new TypeError(r+" "+e+" already registered.");return this[e+"s"][r]=t,t},e.validate=function(r,t){var n,i,o,a,l,u,s,c;if("string"!==typeof r&&(r+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",r)),(r=r.trim()).match(/^\d+$/))return!0;if(""===r)return!1;try{s=e.localeData(t)}catch(f){s=e.localeData(e.locale())}return o=s.currency.symbol,l=s.abbreviations,n=s.delimiters.decimal,i="."===s.delimiters.thousands?"\\.":s.delimiters.thousands,(null===(c=r.match(/^[^\d]+/))||(r=r.substr(1),c[0]===o))&&(null===(c=r.match(/[^\d]+$/))||(r=r.slice(0,-1),c[0]===l.thousand||c[0]===l.million||c[0]===l.billion||c[0]===l.trillion))&&(u=new RegExp(i+"{2}"),!r.match(/[^\d.,]/g)&&!((a=r.split(n)).length>2)&&(a.length<2?!!a[0].match(/^\d+.*\d$/)&&!a[0].match(u):1===a[0].length?!!a[0].match(/^\d+$/)&&!a[0].match(u)&&!!a[1].match(/^\d+$/):!!a[0].match(/^\d+.*\d$/)&&!a[0].match(u)&&!!a[1].match(/^\d+$/)))},e.fn=l.prototype={clone:function(){return e(this)},format:function(r,t){var i,o,l,u=this._value,s=r||a.defaultFormat;if(t=t||Math.round,0===u&&null!==a.zeroFormat)o=a.zeroFormat;else if(null===u&&null!==a.nullFormat)o=a.nullFormat;else{for(i in n)if(s.match(n[i].regexps.format)){l=n[i].format;break}o=(l=l||e._.numberToFormat)(u,s,t)}return o},value:function(){return this._value},input:function(){return this._input},set:function(e){return this._value=Number(e),this},add:function(e){var t=r.correctionFactor.call(null,this._value,e);function n(e,r,n,i){return e+Math.round(t*r)}return this._value=r.reduce([this._value,e],n,0)/t,this},subtract:function(e){var t=r.correctionFactor.call(null,this._value,e);function n(e,r,n,i){return e-Math.round(t*r)}return this._value=r.reduce([e],n,Math.round(this._value*t))/t,this},multiply:function(e){function t(e,t,n,i){var o=r.correctionFactor(e,t);return Math.round(e*o)*Math.round(t*o)/Math.round(o*o)}return this._value=r.reduce([this._value,e],t,1),this},divide:function(e){function t(e,t,n,i){var o=r.correctionFactor(e,t);return Math.round(e*o)/Math.round(t*o)}return this._value=r.reduce([this._value,e],t),this},difference:function(r){return Math.abs(e(this._value).subtract(r).value())}},e.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var r=e%10;return 1===~~(e%100/10)?"th":1===r?"st":2===r?"nd":3===r?"rd":"th"},currency:{symbol:"$"}}),e.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(r,t,n){var i,o=e._.includes(t," BPS")?" ":"";return r*=1e4,t=t.replace(/\s?BPS/,""),i=e._.numberToFormat(r,t,n),e._.includes(i,")")?((i=i.split("")).splice(-1,0,o+"BPS"),i=i.join("")):i=i+o+"BPS",i},unformat:function(r){return+(1e-4*e._.stringToNumber(r)).toFixed(15)}}),function(){var r={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},t={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},n=r.suffixes.concat(t.suffixes.filter((function(e){return r.suffixes.indexOf(e)<0}))).join("|");n="("+n.replace("B","B(?!PS)")+")",e.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(n)},format:function(n,i,o){var a,l,u,s=e._.includes(i,"ib")?t:r,c=e._.includes(i," b")||e._.includes(i," ib")?" ":"";for(i=i.replace(/\s?i?b/,""),a=0;a<=s.suffixes.length;a++)if(l=Math.pow(s.base,a),u=Math.pow(s.base,a+1),null===n||0===n||n>=l&&n<u){c+=s.suffixes[a],l>0&&(n/=l);break}return e._.numberToFormat(n,i,o)+c},unformat:function(n){var i,o,a=e._.stringToNumber(n);if(a){for(i=r.suffixes.length-1;i>=0;i--){if(e._.includes(n,r.suffixes[i])){o=Math.pow(r.base,i);break}if(e._.includes(n,t.suffixes[i])){o=Math.pow(t.base,i);break}}a*=o||1}return a}})}(),e.register("format","currency",{regexps:{format:/(\$)/},format:function(r,t,n){var i,o,a=e.locales[e.options.currentLocale],l={before:t.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:t.match(/([\+|\-|\)|\s|\$]*)$/)[0]};for(t=t.replace(/\s?\$\s?/,""),i=e._.numberToFormat(r,t,n),r>=0?(l.before=l.before.replace(/[\-\(]/,""),l.after=l.after.replace(/[\-\)]/,"")):r<0&&!e._.includes(l.before,"-")&&!e._.includes(l.before,"(")&&(l.before="-"+l.before),o=0;o<l.before.length;o++)switch(l.before[o]){case"$":i=e._.insert(i,a.currency.symbol,o);break;case" ":i=e._.insert(i," ",o+a.currency.symbol.length-1)}for(o=l.after.length-1;o>=0;o--)switch(l.after[o]){case"$":i=o===l.after.length-1?i+a.currency.symbol:e._.insert(i,a.currency.symbol,-(l.after.length-(1+o)));break;case" ":i=o===l.after.length-1?i+" ":e._.insert(i," ",-(l.after.length-(1+o)+a.currency.symbol.length-1))}return i}}),e.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(r,t,n){var i=("number"!==typeof r||e._.isNaN(r)?"0e+0":r.toExponential()).split("e");return t=t.replace(/e[\+|\-]{1}0/,""),e._.numberToFormat(Number(i[0]),t,n)+"e"+i[1]},unformat:function(r){var t=e._.includes(r,"e+")?r.split("e+"):r.split("e-"),n=Number(t[0]),i=Number(t[1]);function o(r,t,n,i){var o=e._.correctionFactor(r,t);return r*o*(t*o)/(o*o)}return i=e._.includes(r,"e-")?i*=-1:i,e._.reduce([n,Math.pow(10,i)],o,1)}}),e.register("format","ordinal",{regexps:{format:/(o)/},format:function(r,t,n){var i=e.locales[e.options.currentLocale],o=e._.includes(t," o")?" ":"";return t=t.replace(/\s?o/,""),o+=i.ordinal(r),e._.numberToFormat(r,t,n)+o}}),e.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(r,t,n){var i,o=e._.includes(t," %")?" ":"";return e.options.scalePercentBy100&&(r*=100),t=t.replace(/\s?\%/,""),i=e._.numberToFormat(r,t,n),e._.includes(i,")")?((i=i.split("")).splice(-1,0,o+"%"),i=i.join("")):i=i+o+"%",i},unformat:function(r){var t=e._.stringToNumber(r);return e.options.scalePercentBy100?.01*t:t}}),e.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(e,r,t){var n=Math.floor(e/60/60),i=Math.floor((e-60*n*60)/60),o=Math.round(e-60*n*60-60*i);return n+":"+(i<10?"0"+i:i)+":"+(o<10?"0"+o:o)},unformat:function(e){var r=e.split(":"),t=0;return 3===r.length?(t+=60*Number(r[0])*60,t+=60*Number(r[1]),t+=Number(r[2])):2===r.length&&(t+=60*Number(r[0]),t+=Number(r[1])),Number(t)}}),e},void 0===(i="function"===typeof n?n.call(r,t,r,e):n)||(e.exports=i)}}]);