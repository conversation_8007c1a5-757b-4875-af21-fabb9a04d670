(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[794],{56848:function(e,t,i){var n={"./en.json":59748,"./zh-mo.json":97124,"./zh.json":83309};function a(e){var t=o(e);return i(t)}function o(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}a.keys=function(){return Object.keys(n)},a.resolve=o,e.exports=a,a.id=56848},23794:function(e,t,i){"use strict";i.d(t,{I:function(){return u}});var n=i(34051),a=i.n(n),o=i(85893),s=i(69483),r=i.n(s),c=i(68109),l=i(67294);function h(e,t,i,n,a,o,s){try{var r=e[o](s),c=r.value}catch(l){return void i(l)}r.done?t(c):Promise.resolve(c).then(n,a)}var d=(0,l.createContext)({setLocale:function(){},locales:[]}),u=function(e){var t,n=e.locales,s=void 0===n?[]:n,u=e.storageKey,m=void 0===u?"locale":u,p=e.children,g=(0,l.useState)(s[0]),f=g[0],y=g[1],w=function(e){e&&s.includes(e)&&(r().setItem(m,e),y(e))},v=(0,l.useCallback)((t=a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r().getItem(m);case 2:(t=e.sent)&&s.includes(t)?w(t):w(void 0);case 4:case"end":return e.stop()}}),e)})),function(){var e=this,i=arguments;return new Promise((function(n,a){var o=t.apply(e,i);function s(e){h(o,n,a,s,r,"next",e)}function r(e){h(o,n,a,s,r,"throw",e)}s(void 0)}))}),[]);(0,l.useEffect)((function(){v()}),[]);var b={locale:f,setLocale:w,locales:s};return(0,o.jsx)(d.Provider,{value:b,children:(0,o.jsx)(c.t_,{messages:i(56848)("./".concat(f,".json")),locale:f,children:p})})};t.Z=function(){return(0,l.useContext)(d)}},59748:function(e){"use strict";e.exports=JSON.parse('{"main":"Main","admin":"Admin","manager":"Manager","agent":"Agent","pending":"Pending","processing":"Processing","success":"Success","failed":"Failed","zh":"\u7b80\u4f53\u4e2d\u6587","en":"English","zh-mo":"\u7e41\u9ad4\u4e2d\u6587","Successfully exchanged {amount} {coin} for {usdt} USDT":"Successfully exchanged {amount} {coin} for {usdt} USDT","Share your referral link with your friends and earn extra reward for each reward they receive":"Share your referral link with your friends and earn extra reward for each reward they receive.","faq1":"How to join?","faq1a":"To start yield farming, you\'ll need to pay a small {coin} fees for smart contract authorization. This is just a one time step. Mining can only start after authorization is given.","faq2":"How to earn reward?","faq2a":"The cryptocurrency mined every day generates {coin} revenue and gives us a certain percentage of revenue in accordance with contract standards.","faq3":"What is the return of investment (ROI)?","faq3a":"After successfully joining, the system will start to calculate the amount of USDT you hold through the smart contract. The reward will be distributed every 6 hours.","faq3a1":"The expected daily production income:","faq4":"Is there a reward for inviting friends?","faq4aOLD":"Yes, you can invite your friends to join the mining pool through your referral link. You will get a 30% {coin} reward everytime your friends receive their reward.","faq4a":"Yes, you can invite your friends to join the mining pool through your referral link.\\nIf you invite 3-6 people, you can get a 5% reward of the invitee\'s income on the day.\\nIf you invite 7-12 people, you can get a 10% reward of the invitee\'s income on the day.\\nIf you invite more than 12 people, you can get 15% of the invitee\'s income on the day.","whitepaper":{"1":{"1":{"title":"Project Concept Background","p1":"Before the birth of Bitcoin, financial aggregation transactions around the world were realized through a centralized approach. However, with the development of internet technology, trading centres have more and more control over data, and the equality of trading individuals and trading centres in many aspects, such as the equality of trading, the transparency of information, and the reliability of historical information, is becoming more and more unequal. At the same time, data monopolies and data closures between the various centralized trading platforms have made it impossible for centralized institutions to do the same. DeFi Mining was born out of this situation and seeks to solve many of the problems of the current generation of financial services through new technology.","p2":"As a \u201cnew generation of value transfer internet\u201d, blockchain finance is not only a traditional internet information transfer, but also a future value transfer network. The value flow of the new world will be the pioneer, builder, domainer and successor of the future. With Bitcoin and Ether breaking through the new highs in the past two years, the entire cryptocurrency market sentiment has also reached a high point, but from a microscopic perspective, the best performing pass-throughs in the DeFi space are only the leading projects in each segmentation track, and many investors who bought cryptocurrencies at high levels still have no hope of returning their capital. However, against this backdrop, some of those involved in liquidity mining have been able to earn high returns while preserving their capital, thanks to DeFi Mining, which uses an innovative structured risk-regulation mechanism and liquidity provisioning mechanism to attract additional investment from major chambers of commerce to provide pledged mining capacity for DeFi Mining crypto assets. Goal is to build a large ecology, i.e. to build a strong community and consensus before launching a product, i.e. a blockchain financial project, to enable users who already hold coins to sink in."},"2":{"title":"Mining has spawned a new industry chain","p1":"Secondly, mining as an infrastructure in the cryptocurrency world, it does not just exist in isolation. Cryptocurrency mining has evolved from CPU and GPU mining to ASIC mining, from individual mining to pool mining, and from no service at the beginning to perfect supporting services now. We can see through the whole evolution process of the mining industry that the cryptocurrency mining industry has spawned a new industrial chain around the development of the whole mining industry, from production equipment such as mining machines, mining farms and pools to logistics, accessories, finance and other supporting services have all emerged in response to the development of the mining industry and the rise in coin prices, thus enabling the entire mining industry to develop better."},"3":{"title":"The mining industry has huge room for development","p1":"The, as the cryptocurrency coin price continues to rise, its wealth effect will become more and more obvious, thus attracting more global users to actively participate. At the same time, it also drives the booming development of the entire cryptocurrency mining industry upstream and downstream. From the hosting of mining farms to the research and development of mining machines, from the technical optimization of mining pools to upgrading of logistics services, from the diversification of financial services to the introduction of new accessories, from the professional introduction of talents to the active layout of capital, all of them reflect the certainty and expansiveness of the future development of the entire mining industry."},"4":{"title":"Mining can effectively combat bull and bear market","p1":"The entire digital currency market experiences bulls and bears, peaks and troughs, and some industry participants, when experiencing industry bulls and bears, will choose between risk appetite and risk avoidance. And while mining sacrifices some liquidity relative to the high-risk, highly liquid digital currencies, its returns are also more long-term and stable."},"title":"Project Background Overview"},"2":{"1":{"title":"High investment requirement","p1":"At present, the mainstream mining form is still dominated by physical mining machines, which forces many users who want to mine to spend a certain amount of capital costs to buy a mining machine, after which they also need to build their own or find a suitable site for hosting, while the risk of the digital currency itself and the fluctuation of the coin price is relatively large. With so many uncertainties, users also need to pay a lot of capital and time costs in advance, thus raising the overall barrier to entry for the industry and discouraging many users."},"2":{"title":"Extensive knowledge required","p1":"Mining also has high professional requirements for miners, which involves two aspects: the professionalism of the hosting mine on the one hand, and the professionalism of the maintenance of the mining machines on the other. This is because a professional mining site has certain standards, whether it is the distance between machines or the ventilation and humidity of the site, which not only prolongs the life of the mining machines, but also allows them to run more stably. Maintenance specialisation requires professional maintenance staff who can deal with any problems that may arise with the mining machine, whether it is poisoning or hardware failure, thus guaranteeing that the mining machine is online 24/7."},"3":{"title":"Unregulated industry","p1":"As the current policy for mining and digital currency regulation is at a relatively early stage, legislation is lagging behind, and the overall development of the industry is in a state of barbaric growth, with a lack of unified standards, pre-sales and after-sales services, and varying levels of hosting, the industry is full of chaos, schemes and unspoken rules that give new and old miners headaches and even complaints."},"4":{"title":"Lackluster service","p1":"Mining is an industry with a large initial investment in fixed assets, and the financial services to match this are not perfect, resulting in poor liquidity of the fixed assets invested by miners. When miners need a certain amount of cash flow, they are stretched to the limit and have no way to liquidate their fixed assets in time to meet their financial needs, except for pledging and selling digital currencies. In addition to financial services, other services related to mining are also relatively backward, such as the construction and transfer of mining machine farms, the warranty and maintenance of mining machine parts, the reporting and publishing of mining information, etc., are all in a relatively rudimentary state."},"title":"Traditional mining problems"},"3":{"1":{"title":"Introduction to DeFi Mining","p1":"DeFi Mining is a global technology-leading {coin} arithmetic mining cloud service platform, and an international website dedicated to providing global users with safe, convenient, reliable and innovative digital asset derivatives. DeFi Mining allows users to seamlessly switch between tokens on the chain in a fully decentralised and non-custodial manner. DeFi Mining is committed to providing global users with intelligent, secure, stable, fast and convenient digital asset investment and trading services, bringing intelligent new changes to global digital finance."},"2":{"title":"Product key elements","item1":"Product: DeFi Mining","item2":"Positioning: {coin} mining financial tool","point1":{"title":"Liquidity Mining","p1":"DeFi Mining supports single coin mining, pledged LP mining and transaction mining, and is completely decentralised. It can freely deposit tokens for exchange and can be freely withdrawn. It does not trade with other trading users, it trades with a pool of tokens and has an automatic market making model to calculate the trading price.","p2":"One of the features of DeFi Mining is that it pools everyone\'s liquidity together and then makes a market based on an algorithm. The AMM model is supposed to be one of the biggest innovations of the current bull market, the cryptocurrency market. AMM fundamentally changes the way users trade cryptocurrencies, unlike traditional order book trading models, where both sides of the trade interact with an on-chain pool of liquid assets. The liquidity pool allows users to seamlessly switch between tokens on the chain in a fully decentralised, and uncustodied manner. The liquidity provider, in turn, earns passive income through transaction fees, which are based on the percentage of their contribution to the asset pool. What\'s more, your assets remain in your personally controlled wallet and not into the trading platform, so they remain 100% safe."},"point2":{"title":"Trading and exchange type services","p1":"DeFi Mining wallets have built-in digital asset trading functionality, from wallets with access to centralised exchange platforms, such as BitPie, to wallets with access to decentralised trading platforms, such as Imtoken, to automated trading platforms with access to the Bancor mechanism, such as Tokenpocket. Some wallets offer a \u201cflash exchange\u201d function, where different currencies are exchanged at a certain \u201cexchange rate\u201d, often using a centralised exchange model for the backend of the currency exchange. This not only reduces the number of steps users have to take to transfer coins to other coins, reducing the probability of making mistakes, but also enhances user stickiness and provides a good development direction and capital deposit for the subsequent transformation of the wallet project. However, the built-in exchange greatly increases the complexity of the system and introduces greater risk for wallet products that require high security themselves, and the security of user funds will be challenged to some extent."},"point3":{"title":"News and information services","p1":"DeFi Mining has a wealth of built-in digital asset market information such as news feeds, market snapshots, project profiles, K-line charts, large capital flow monitoring, code activity and more. Blockchain project information and quotes are what pass holders need to keep up with the market, and are also a great entry point for gathering user traffic. A well-integrated information and quotes service in a wallet product will not only create enough stickiness for existing users, but also bring in more incremental users. However, information services require a certain amount of investment in human and financial resources, which can significantly increase the operating costs of the product. Currently, most wallets do not have a well-developed news and information service, and mainly focus on providing quotes."},"point4":{"title":"Money Management Services","p1":"DeFi Mining has a built-in wealth management module, which includes long-term fixed income, short-term flexible balance, digital asset P2P_financing and lending, and mortgages. At present, some of these finance modules are connected to third party services and do not participate in finance services themselves; some are finance products developed to provide cheap funds for the development of their own platforms, with the platform\'s earnings paying interest to users; some are digital assets raised by the platform and then invested in primary or secondary market transactions to obtain excess returns and pay interest to users; and some provide P2P digital asset lending and borrowing transactions. Others offer P2P digital asset lending and trading services, providing a matchmaking service between those who demand and those who provide assets. For long-term coin holders, digital asset management services hit the nail on the head and can be held to gain income, with annual yields ranging from 4% to 20% for various types of wallets. However, the blockchain industry is developing rapidly and the digital asset market is volatile, so financial products with poor liquidity will face greater risks. The digital asset finance market is currently immature and no industry benchmark leading companies have emerged. In addition digital asset P2P lending type, if it is difficult to control the risk of the lender, it is easy to cause default, please pay attention to the risk of investors."},"point5":{"title":"POS mining services","p1":"For blockchain projects that support the POS consensus algorithm, some wallets offer a lock-in to join the POS mining service, and the mining proceeds will be sent to users on a regular basis. Usually the master node for POS mining is provided by the wallet project side. Digital assets that meet certain capital requirements can participate in POS mining, with fixed lock time mining and also a mining model that supports redemption at any time. The wallet project side will take a proportional share of the mining proceeds, and both the wallet project side and the user can have a more stable additional income. The coins that currently support POS mining are: DASH, Lite Bitcoin, zCoin, Qtum and Hcash."},"point6":{"title":"Asset aggregation services","p1":"Usually users will hold different coins in multiple exchanges and wallets, which is not very convenient for centralized management and revenue enquiries. Such wallets can provide users with fund aggregation services, aggregating users\' pass holdings in multiple wallets and exchanges through API interfaces, and platforms with poor support for API interface services can also use manual maintenance for initial input. Currently, the aggregation service can support exchanges, wallets, ICO funds and fiat funds.","p2":"Currently there are many different types of exchanges and wallets, each of which supports different types of digital assets, so users\' funds are usually scattered across different platforms, which is not conducive to centralised management and queries. This type of wallet requires API interface development with many exchanges and wallets, and there are certain development and maintenance costs."},"point7":{"title":"IDO One-Stop Platform","p1":"DeFi Mining will establish a one-stop platform for IDO project evaluation, promotion and access, DeFi Mining adopts a community governance model, DeFi Mining\'s voting weight is the only measure, holding DeFi Mining is recorded as one vote, pledging DeFi Mining is recorded as two votes, the DeFi Mining re-purchased or pledged after the voting starts is not counted in the voting results. Provide traffic and support for project developers, data analysis and quality resources for customers. ido platform governance coins will also take liquidity mining mined out, involving specific economic models such as ido project mortgage, hitting new, buyback, etc."},"point8":{"title":"Other application products","p1":"The DeFi Mining team will continue to provide the latest products according to market development and demand. We strive to be the DeFi full traffic portal and provide the most complete DeFi products to serve our users."}},"3":{"1":{"title":"Lower the industry threshold and increase asset liquidity","p1":"DeFi Mining has lowered the entry barrier to the mining industry by giving fixed assets such as mining machines, mining farms and arithmetic power some liquidity in a platform-specific way, allowing more users who are interested in mining to participate and enjoy the benefits of mining without having to invest in expensive fixed assets. At the same time, it has also increased the liquidity of mining farms and miners\' assets, enabling them to develop more rapidly."},"2":{"title":"Scale and specialised expenditure costs","p1":"DeFi Mining will continue to build and purchase its own infrastructure and mining equipment, as well as cooperate with other ecological partners, in order to generate a certain scale effect, thus reducing the platform\'s procurement, construction and operating costs. At the same time, due to the large scale of the company, it will also have a relative advantage in obtaining the corresponding power resources and compliance qualification."},"3":{"title":"Cross-currency mining services to enhance the freedom of mining","p1":"As the consensus algorithms of different coins are different, the required mining machines are also different, so this is a limitation to the freedom of miners. DeFi Mining can help miners to provide mining services for different coins under different conditions according to their individual needs, thus enhancing their freedom of mining."},"4":{"title":"Empowering financial attributes to provide financial services to users","p1":"DeFi Mining will continue to expand its financial services and products to provide a full range of financial services and support to ecological members including miners, mining farms, mining pools and mining machine manufacturers to help them survive and develop better DeFi Mining and its partners will obtain the appropriate business licenses in accordance with local policies and regulations."},"title":"Project solutions"},"4":{"1":"K-Chart: Once you enter the DeFi Mining platform, you can see the K-Chart by entering the contract address, which gives you a comprehensive and thorough view of the real changes in the {coin} market. From the K-chart, investors can see both the trend of the bullion market and the daily fluctuation of the market conditions.","2":"Transparent trading: The model is simple, mainly aggregating transactions, and does not host the user\'s assets, eliminating the possibility of the exchange monitoring and stealing.","3":"Trading on the chain: The biggest difference with other {coin} mining platforms is that DeFi Mining does all this through smart contracts, placing asset escrow, aggregated transactions and asset clearing on the blockchain.","4":"Highly trustworthy: Smart contracts are used to achieve a decentralized and trustless transaction mechanism, which solves the risk of internal operation, business ethics, asset theft and other risks that seriously affect the safety of users\' assets due to human factors on centralized platforms.","5":"Security and efficiency: Users\' assets can be transferred freely without any approval, and there is no need to worry about hackers stealing or losing coins, so there is sufficient security.","title":"Advantages of DeFi Mining"},"5":{"1":{"title":"Calculating power service","p1":"DeFi Mining is based on {coin} mining machines, mining farms and the arithmetic power behind them. Arithmetic services are the core and most fundamental business of DeFi Mining, and users can enjoy a wide range of arithmetic services through the platform. In the world of cryptocurrencies, apart from buying and selling cryptocurrencies through exchanges, the only way to mine cryptocurrencies is to have arithmetic power, so the value of arithmetic power is particularly important here. Once a platform user has arithmetic power, they can turn it into tangible revenue through arithmetic power. DeFi Mining users who use the platform to purchase arithmetic for mining services will see their daily earnings increase as the price of the cryptocurrency rises, and vice versa.","p2":"Therefore, DeFi Mining itself has the ability and demand to continuously extend its cooperation with external arithmetic power, and as the arithmetic power purchased by DeFi Mining and accessed by external parties continues to increase, the larger the scale, the more users will continue to increase."},"2":{"title":"Streaming Service","p1":"DeFi Mining\'s miners can transfer their fixed assets, such as mining machines and mining farms, through the platform under certain conditions within the platform and on the secondary market, thus increasing the liquidity of miners\' assets and allowing more idle arithmetic power to come into play, so that users who want to mine can participate in the process. As DeFi Mining grows, the platform will gradually increase the amount of arithmetic purchased by the platform and the amount of external arithmetic. Users can purchase physical {coin} miners from DeFi Mining or transfer their own miners to our mine for management"},"3":{"title":"Financial Services","p1":"As one of the core services provided by DeFi Mining, we will provide cryptocurrency_users with financial services such as collateral lending, hedging, coin-flash, asset management, leveraged instalment, OTC, option contracts, etc. Cryptocurrency will be used as a payment tool for DeFi service fees. DeFi Mining will continue to improve its financial services and derivative products according to market conditions and the needs of cryptocurrency users."},"4":{"title":"Information Services","p1":"The mining industry has experienced many years of development, but the information in the industry is still relatively closed and asymmetric. From local electricity prices to the sale and purchase of mining machines, from industry blacklists to industry resource information, the mining community lacks an authoritative and systematic information release and sharing platform. In the future, DeFi Mining will gradually integrate the industry\'s leading organizations into the entire ecosystem, so that miners can take the easy way out and get access to reliable resources and information from the platform. The information service scope mainly includes network information release, mining machine sales, mining machine reviews, etc."},"title":"Value services"},"6":{"1":"Transparent transactions: The model is simple and mainly deals with aggregated transactions, and does not host users\' assets, thus eliminating the possibility of the exchange monitoring and stealing.","2":"Transaction on the chain: DeFi Mining all of this is achieved through smart contracts, the asset custody, aggregation transactions, asset liquidation are placed on the blockchain.","3":"High trust: Smart contracts are used to achieve a decentralised and trustless transaction mechanism, which solves the risk of internal operation, business ethics, asset theft and other risks that seriously affect the safety of users\' assets due to human factors.","4":"Security and efficiency: Users\' assets can be transferred freely without any approval from anyone, and there is no need to worry about hackers stealing or losing coins, so there is sufficient security.","title":"Core of DeFi Mining"},"7":{"1":{"title":"Engineering-level development capabilities","p1":"DeFi Mining community members have unique insights and experiences in blockchain technology. They have many years of development experience, which gives DeFi Mining\'s technology development a huge advantage and guarantee. DeFi Mining already has a decentralised application in internal testing, the creation of which comes from the contributions of community volunteers and the assistance of the founding team."},"2":{"title":"Composite core business logic","p1":"DeFi Mining has core business segments covering all directions, with developments in information sharing, information settlement systems, multi-functional wallets, traceable elements, smart_contracts and other areas, which will eventually converge and link into an overall cryptocurrency trading ecosystem."},"3":{"title":"The industry\'s first-tier cooperation institutions","p1":"Excellent partners are the foundation of success. DeFi Mining has top-tier partners around the world, who will cooperate with DeFi Mining in all aspects and help DeFi Mining at all levels to promote its rapid development."},"4":{"title":"Simultaneous launch of the global market","p1":"DeFi Mining has a rich community base and user base, and has been commercially deployed globally. At the same time, strong alliances allow DeFi Mining to launch simultaneously in various markets around the world, seizing opportunities and expanding markets in time!","p2":"In the future, DeFi Mining will establish multiple user communities and venues in Spain, Canada, USA, Singapore, Northern Europe, Eastern Europe, Japan, China, Korea, Australia, Malaysia, Cambodia, South America and other countries and regions to accelerate the project\'s development."},"5":{"title":"Cutting-edge industry trends","p1":"DeFi Mining enables the existing cryptocurrency trading market to gain disruptive changes and establish an advanced and unique payment ecology. It is hoped that a series of open and fair rules and standards will promote rapid changes in the entire market, optimise the current industrial structure, rationalise the allocation of public welfare resources, and ultimately realise the decentralised ecosystem established by the DeFi Mining protocol based on blockchain technology."},"6":{"title":"Global commercial application","p1":"In order to realize DeFi Mining\'s ambitious ideals and mission, DeFi Mining will make use of its advantages to land commercial applications globally, actively cooperate with various traditional industries to help the development of the blockchain crypto industry, improve its influence and establish an ecosystem led by DeFi Mining."},"title":"Cornerstone of DeFi Mining"},"title":"Overview of DeFi Mining"},"4":{"1":{"1":{"title":"Data layer","p1":"Based on the high redundancy storage mechanism of the blockchain, blockchain storage has a certain impact on the scalability and performance of the blockchain. The DeFi Mining framework is designed with a multi-layered node system, and different storage strategies (distributed bookkeeping) are selected according to different node applications."},"2":{"title":"Network layer","p1":"The P2P Protocol supports data transmission and signaling exchange among nodes in the blockchain network and is an important communication guarantee for data distribution or consensus mechanism. The DeFi Mining system is designed to support multiple P2P protocols, communication mechanisms and serialization mechanism configurations, so that flexible protocols can be used according to different scenarios. In terms of communication security, HTTPS, TLS, WSS (SecureWebsockets) and other protocols are flexibly supported, and OAuth authentication integration can be extended to support the external service interface of the platform application."},"3":{"title":"Consensus layer","p1":"DeFi Mining (delegatedByzantine Fault Tolerance) consensus algorithm, expanding the underlying network consensus algorithm, with high performance, high consistency characteristics, suitable for financial payments, digital transaction data frequently generated, and a high degree of real-time bookkeeping requirements of weak central upper layer applications."},"4":{"title":"Incentive Layer","p1":"DeFi Mining not only has an airdrop for genesis consensus rewards, but also a liquidity mining pool for long-term network value maintenance. Because of DeFi Mining\'s unique consensus mechanism, anyone can join at any time to earn rewards."},"5":{"title":"Contract Layer","p1":"For each smart contract submission, deployment, use and cancellation of a complete and controllable process management, and integrated rights management mechanism for comprehensive security management of the mechanisms of smart contract operation."},"6":{"title":"Application layer","p1":"The application layer will provide a common transaction protocol, support multi-language integration and functional expansion, with support for Java, JavaScript, Python and other languages, and has been fully applicable to the underlying network expansion."},"title":"DeFi Mining System Architecture","p1":"The DeFi Mining system has six layers, namely the data layer, network layer, consensus layer, incentive layer, contract layer and application layer."},"2":{"title":"Arithmetic time-sharing system","p1":"DeFi Mining\'s arithmetic services are provided by the platform\'s own machines and external organizations that generate arithmetic 24/7. Users can freely choose when they want to use arithmetic, the amount of arithmetic and the corresponding mining coins through the platform, which improves the efficiency of arithmetic usage and breaks the inconvenience of mining caused by the limitation of firmware facilities in the past."},"3":{"title":"Trusted Proof of Stake Consensus (TDPOS)","p1":"The DeFi Mining platform uses Trusted Proof of Stake Consensus (TDPOS), a secure and trusted consensus protocol with high robustness and flexibility. Compared to traditional consensus mechanisms, TDPOS ensures a reliable source of distributed data through a hierarchical trusted consensus and hierarchical auditing strategy, safeguarding the fair rights and data privacy of participating nodes, while the transaction performance of over 1 million TPS is fully capable of handling a high concurrency sharing ecology.","p2":"We define the hierarchical Trusted Consensus (PT) of TDPOS as a function of Proof of Identity (PIA), Proof of Compliance (PCO), and Proof of Credit (PCR): PT=F\'{f(PIA),f(PCO),f(PCR)\'} and the three hierarchical credible proofs containing documents or transaction acts are described as follows.","p3":"Proof of equity (PIA): the higher the number of nodes locking coins, the higher the score. Proof of Compliance (PCO): is to verify the transaction behavior with the corresponding organizational policy and legal code requirements. Proof of Credit (PCR): is a dynamic credit verification that identifies the transaction behaviour on the role chain.","p4":"Combined with the decentralized features of the blockchain system, we incentivize the release of transaction behaviors with high credit consensus through the layered credit management and TOKEN mechanism of TDPOS with layered trusted consensus, which can greatly reduce the cost of information screening for the roles in the ecology, while improving transaction efficiency and guaranteeing transaction security."},"4":{"1":{"title":"P2P communication","p1":"P2P is the core foundation of blockchain, with features such as decentralization, scalability, robustness, privacy and high performance. The block is linked to DeFi Mining\'s loT devices and users, and is deeply optimised in terms of session maintenance, address determination, communication mechanism, storage scheme, transaction payment, etc. By specifying the physical configuration and size of the user and consensus nodes, and adopting a sharding mechanism and high-speed network connection, the communication, computation and storage burden of the consensus nodes is reduced and the transaction performance of the blockchain is improved, thus achieving the maximum performance of blocking for loT devices and providing the foundation for the registration, digitization, authentication and security of IoT devices in the future."},"2":{"title":"DeFi Mining encryption algorithm","p1":"The encryption and decryption of information is the key link of blockchain, mainly the algorithm of hash function and asymmetric encryption.","p2":"(1) The hash function part, currently there are mainly SHA, MD5 and other algorithms, and also includes the use of algorithms in series and in parallel. Since commercial applications generally focus more on performance issues, the DeFi Mining base algorithm is mainly the SHA256 algorithm.","p3":"(2) Asymmetric encryption part, there are mainly asymmetric encryption algorithms including RSA, DSA, elliptic curve algorithms, etc. Blockchain generally uses elliptic curve algorithms, including ECDSA and SCHNORR, considering that the verification speed of Schnorr signature is faster than ECDSA signature, and the size of this signature can be smaller, and also natively supports multiple signatures. This is in line with the small size of the Internet of Things, so DeFi Mining has developed its own SDSchnorr algorithm based on Schnorr.","p4":"At the same time, DeFi Mining has abstracted the underlying cryptographic algorithm library and interchangeable channels for the algorithms to meet the algorithmic and security needs of different loT applications. The names of the wallet and the address are interchangeable in the underlying block."},"3":{"title":"DeFi Mining Smart Contract","p1":"Each Internet-accessible lot device is given an \u201cidentity\u201d on the blockchain and has a globally unique identifier that can be used to identify a credit identity. Each smart contract has a unique public address, just like a normal wallet. The difference is that the smart contract\'s private key is discarded when the contract is created, so no one can send the digital assets inside the smart contract after it is created, except for the consensus mechanism. For the lot\'s monitoring event-based up-chaining needs, i.e. the loT device holder sets the smart contract, data storage path and data accounting amount in advance, it can be broadcasted across the network as well as stored in digital records. In the event of a relevant abnormal event that is incorporated into the blockchain\'s supervision, DeFi Mining\'s smart contracts require mandatory, strong real-time and fully automated triggering. The data for triggering conditions is also blockchain-protected data, which is accurate, safe and secure, and cannot be tampered with."},"4":{"title":"Cross-chain communication protocol","p1":"DeFi Mining supports cross-chain asset exchange protocols, which are extensions to existing asset exchange protocols, allowing multiple participants to exchange assets on different blockchains, and guaranteeing that all steps in the entire transaction process are all steps of the transaction are guaranteed to succeed or fail. At the same time, the cumbersome process of exchange via an exchange is eliminated. The flexibility of payment methods will stimulate a certain degree of diversity among users."},"5":{"title":"5G cloud storage","p1":"DeFi Mining 5g storage provides a special storage mechanism, which can not only prevent the storage of duplicate data, but also ensure the same security, break the \u201ccommon sense\u201d of the industry, and achieve both fish and bear. When using this mechanism, in addition to the user permission table, a global metadata table should be maintained to record the corresponding relationship between plaintext hash and ciphertext hash. When writing data, you should first query whether there is the same hash data, and if there is no such item, save it again.","point1":"Hash (data) \u2192 hdata to calculate plaintext hash","point2":"If checkdup (hdata) = true goto 11, if the same data already exists, go to step 11","point3":"The symmetric key is randomly generated by randomsym () + STK as the file storage key","point4":"Enc (STK, data) \u2013 encdata encrypt files with storage key","point5":"Hash (encdata) \u2013 henc to calculate ciphertext hash","point6":"The symmetric key is generated from the plaintext of the data by genkey (data) \u2192 sData","point7":"After adding salt, hash value is calculated. The reason for adding salt is that plaintext hash is a public value. If 5g storage blockchain is not added salt to enable partial storage, people who do not have plaintext data can also obtain the key. In order to ensure consistency, the salt value can be a fixed value.","point8":"Enc (hdata, STK) - encstk\'encrypts the storage with the symmetric key generated by the data plaintext","point9":"Store the key. This is a very \u201cweird\u201d step. It uses plaintext as the key and the key as plaintext to encrypt. When most people look at this algorithm, they think it is reversed. In fact, it is specially designed in this way, and this step is the core step of de duplication after encryption.","point10":"Putipfs (encdata) stores encrypted data into IPFS","point11":"Putmeta (hdata, henv, encstk) stores \'encrypted ciphertext hash and plaintext","point12":"The key is recorded in the global metadata table and in the plaintext hash key","point13":"Goto 14","point14":"Get meta (hdata) henv, encstk\'get ciphertext from global metadata table","point15":"Storage key after hash and plaintext encryption","point16":"Genkey (data) \u2192 sData uses the same algorithm to generate symmetric key from data plaintext","point17":"The symmetric key is used by Dec (sData, encstk \') - STK to decrypt the storage key","point18":"Enc (spub, STK) \u2013 encstk uses the user\'s encryption public key to encrypt and store the key","point19":"Putpri (hdata, encstk) stores the encrypted storage key of the encrypted public key to the user","point20":"The user permission list is recorded under the clear text hash item","point21":"When getting data, take out the corresponding ciphertext hash from the plaintext hash in the global metadata table, take out the ciphertext hash from the IPFs through the ciphertext hash, take out the encrypted storage key from the permission list, decrypt the encrypted storage key with the user\'s encryption private key to obtain the storage key, and decrypt the encrypted data with the storage key to obtain the data plaintext.","point22":"It is obtained by getmeta (hdata) \u2192 henc from the global metadata table through plaintext hash","point23":"The ciphertext hash getpri (hdata) \u2192 encstk obtains the encrypted public key from the permission list through plaintext hash","point24":"Get IPFs (henc) \u2192 encdata extract ciphertext from IPFs with ciphertext hash","point25":"Dec (sprv, encstk) STK encrypts the user\'s public key with the user\'s private key","point26":"The encrypted storage key is decrypted to obtain the storage key","point27":"Dec (STK, encdata) - data decryption to obtain file data","point28":"The optimized DSN scheme not only effectively guarantees the security of data, but also realizes encryption and de duplication. In order to achieve better de duplication effect, the data can be divided into blocks according to a fixed length, and each block is de duplicated separately. The above scheme can only be used to store static data. When storing dynamic data, not only the ID that does not change with the content should be used to replace hash as the identification of data, but also the verification of write permission should be added to prevent the data from being covered and tampered by others.","point29":"The creation process is as follows:","point30":"Xun randomasm () - swpub, SwPrv randomly generates asymmetric key as write permission key","point31":"Create (swpub) - ID creates a dynamic data, obtains a unique ID, and records the public key of the write permission corresponding to the ID","point32":"Random sym () - STK randomly generates symmetric key as storage key","point33":"Enc (spub, STK) \u2013 encstk uses the user\'s encryption public key to encrypt and store the key","point34":"Putpri(ID, encstk) stores the encrypted storage key of the encrypted public key into the user permission list, and records it under the ID. the process of each data writing is as follows:","point35":"Get pri (ID) + encstk to retrieve the encrypted storage key from the user permission table","point36":"Dec (sprv, encstk) \u2192 STK encrypts the user\'s public key with the user\'s private key \u2022 The storage key is obtained by decryption","point37":"Enc (STK, data) \u2192 encdata encrypt files with storage key","point38":"Hash (encdata) \u2192 henc to calculate ciphertext hash","point39":"Enc (SwPrv, henc) - enchenc uses the public key of the ID\'s write permission to hash the ciphertext autograph","point40":"Putdyn (ID, encdata, enchenc) writes encrypted dynamic data to sign data","point41":"Write authorization on behalf of. When a node storing dynamic data fragments writes dynamic data, it needs to verify the write permission first","point42":"Get key (ID) \u2192 swpub get the public key of write permission corresponding to the ID","point43":"Hash (encdata) - henc to calculate ciphertext hash","point44":"If Dec (swpub, enchenc) = henc write (encdata)","point45":"The reading process of writing dynamic data is as follows:","point46":"From the permission list, getpri (ID) \u2014 encstk obtains the data encrypted with the encryption public key corresponding to the ID","point47":"Storage key","point48":"Get dyn (ID) \u2013 encdata read ciphertext from dynamic data storage area with ID","point49":"Dec (sprv, encstk) - STK encrypts the user\'s encrypted public key with the user\'s encrypted private key","point50":"The encrypted storage key is decrypted to obtain the storage key"},"title":"Technology Applications"},"5":{"1":{"title":"Password setting for device use","p1":"Users can be reminded to set unlocking passwords, unlocking gestures or fingerprint unlocking, wrong password unlocking time period, high strength of device transaction password, if there is no perfect password setting control, it can not be directly accessed to view personal privacy information after the loss of the device, to conduct transactions."},"2":{"title":"Create a wallet keyword security","p1":"The private key process is secure and can be saved locally."},"3":{"title":"Transaction process security","p1":"You can display the address of your account and verify whether the address has been modified, so that your money is not lost."},"4":{"title":"Data storage security","p1":"Data can be stored on the storage device, including the private key storage method, stored on the device memory card, which can be accessed externally, to improve the relevant function design, to protect the relevant data from hacking."},"5":{"title":"System integrity security","p1":"The ability to verify the strict integrity of the equipment system, to check whether it is genuine and to prevent tampering by hackers or attackers in the place where the equipment is distributed."},"title":"Technology Applications","p1":"DeFi Mining is a secure and reliable platform that combines on-chain and off-chain innovation through technical and conceptual innovation."},"title":"DeFi Mining Technical Architecture"},"5":{"1":{"title":"DeFi Mining Industry Chain","p1":"With the continuous development of DeFi Mining, the number of organizations that will reside in the DeFi Mining platform will also increase, and eventually a diversified ecosystem will be formed that includes exchanges, mining pools, mining machine manufacturers, social media, hot and cold wallets and mining services. Users holding cryptocurrencies will also enjoy certain rights and benefits when they access the main chain, such as discounted exchange fees, discounted mining pool fees, discounted mining machine purchases, priority purchase rights, purchase installments, token purchase rights, cold wallet benefits, etc. DeFi Mining will cooperate with more eco-system institutions through the main chain to give more rights and value to cryptocurrencies. DeFi Mining will cooperate with more eco-system institutions through the main chain, thus giving more rights and values to the cryptocurrency"},"2":{"title":"DeFi Mining Wallet","p1":"To facilitate the use of the wallet by ordinary users, the DeFi Mining wallet uses the SPV method, i.e. accessing the wallet through the web. Cold wallets are suitable for wallets with large sums of money, where the public and private key pairs are generated offline and the user can generate any key pair they like. Hotwallets: Hotwallets are suitable for small and fast transaction scenarios. Hotwallet keys are hosted in a way that when a user registers for a wallet account, the private key generated using the user\'s payment password pair is encrypted locally on the user\'s computer via 3DES and the encryption results are hosted in the wallet cloud via the SSL protocol. This means that the hot wallet key information transmitted over the network and stored in the cloud is the user\'s encrypted data and no one other than the wallet user has access to the original content of the private key.","p2":"When a user needs to sign a transaction, the private key is obtained from the wallet cloud server and the content is decrypted on the user\'s local computer by the user entering the payment password, and upon successful decryption, the local wallet program will sign the transaction information with the private key and submit it to the DeFi Mining wallet network for trading. The DeFi Mining wallet also contains two types of assets - native assets and registered assets - similar in nature to real-life wallets that contain Chinese Yuan and various cards. Native assets can be used without any trust, while gateway-registered assets must trust the corresponding assets in order to exchange value.","p3":"Based on the above, and in order to better build the DeFi Mining wallet ecosystem, the DeFi Mining wallet introduces a series of key pairs, so that each wallet address corresponds to a key pair: a private key and a public key. The private key is a random number generator to generate a 256-bit entropy, displayed as a 64-bit hexadecimal number, which is unpredictable and unrepeatable, and therefore unique. It holds the ownership and control of the wallet and is used to sign and verify each transaction. In order to lower the user\'s threshold, the mnemonic becomes another manifestation of the plaintext private key. The DeFi Mining wallet, in order to help users to memorise complex private keys and increase convenience, generates a public key through a certain cryptographic algorithm (e.g. a hash function), which corresponds to the private key one by one. Its centralised wallet: a smart wallet for personal digital assets that supports the storage and transfer of a wide range of token transactions, will play a vital role in the next generation of the Internet digital economy. It can track and capture the data and market analysis of each exchange in real time, carry out intelligent arbitrage transactions in real time, determine the price difference of each trading platform and the trading volume of each currency to decide whether to carry out arbitrage tasks, and initiate transactions at the right time by itself, combined with a variety of arbitrage methods can be completely unaffected by price increases and decreases. The combination of multiple arbitrage methods can be completely independent of price increases and decreases. It solves all the drawbacks and risks of manual arbitrage."},"3":{"1":"a decentralised peer-to-peer network (digital asset protocol)","2":"a public ledger of transactions (blockchain)","3":"a decentralised data and deterministic asset issuance (distributed mining)","title":"Crypto Asset Trading System","p1":"The DeFi Mining Crypto Asset Trading System is a user trading platform. All users can convert multiple assets within the platform, where digital assets are a distributed peer-to-peer network system. As such, there is no \'central\' server and no central control point. The Digital Asset protocol includes a built-in algorithm that regulates the mining function in the network. The task that miners must complete is to successfully record a block transaction in the digital asset network. This difficulty is dynamically adjusted. Digital Assets before Digital Assets represents the culmination of decades of cryptography and distributed systems, a unique and powerful combination that brings together four key innovations. Digital Assets consists of these:"},"4":{"title":"DeFi Mining financial platform","p1":"Contract escrow platform, risk avoidance and stable returns. Cryptocurrencies are naturally decentralised, and most hot wallets inherit this property. As the private key is held by the user, it cannot be recovered if lost and transactions cannot be rolled back, thus putting the \u201cwallet security awareness\u201d of many users to a great test. DeFi Mining is the world\'s first and only personal digital wealth management product that combines multi-currency, cross-chain, decentralised trading, P2P encrypted instant messaging and integrated social community applications.","p2":"Lending and financial gains: DeFi Mining will later launch a one-click aggregated lending service to expand the underlying assets for lending and insurance, enhancing the liquidity of users\' assets. The aggregation of mainstream and today\'s most popular major Defi lending platforms, such as MakerDao, Compound, etc., will reduce users\' borrowing costs by adjusting the choice of coin weights and minimum interest rates in real time. It also connects off-chain credit behaviour with on-chain financial behaviour based on a credit prophecy machine. Users with a good credit history can also use their \u201ccredit behaviour\u201d record to participate in behavioural mining, and DeFi Mining may even have the attribute of mining weighting in the future."},"title":"The DeFi Mining Ecosystem"},"6":{"1":{"col1":"August 2021","col2":"Complete development and design of the prototype based on the DeFi Mining platform"},"2":{"col1":"September 2021","col2":"Beta version of DeFi Mining platform goes live for testing"},"3":{"col1":"November 2021","col2":"DeFi Mining platform goes live in beta"},"4":{"col1":"December 2021","col2":"Applications go live, integrate many industries, organise multilingual platforms for global business synergy operations and create a trillion dollar DeFi Mining infrastructure platform"},"5":{"col1":"March 2022","col2":"DeFi Mining has contacted more overseas blockchain exchanges to actively promote overseas DeFi Mining launch plans and enhance the international reach of the DeFi Mining public project"},"6":{"col1":"June 2022","col2":"Collaborate with more developers on more types of mining projects"},"7":{"col1":"2023","col2":"Complete global membership roadshow marketing"},"title":"Project Development Planning","col1":"Time","col2":"Development Planning"},"7":{"title":"Summary","p1":"The wheel of historical development rolls forward, and the development of society is changing day by day. Blockchain finance can contain everything, and the trend development has become irreversible. Whether we are pioneers or watchers is the most significant choice you can make in this era.","p2":"DeFi Mining platform: will give the future financial flow of the {coin} world a best entrance and base. We will be the navigator, pathfinder, domainer, discourse, technology benchmarker and ecological city pool of the blockchain financial world.","p3":"We are not just involved in DeFi Mining, we are involved in building the entire {coin} world. We are at the forefront of the new world, we reap the scenery and the territory. At the same time, the success of DeFi Mining is driven entirely by the consensus of the community members. Let\'s build the brilliance and legend of DeFi Mining with the best product and the strongest consensus."},"title":"DeFi Mining - Whitepaper","index":"Index","abstract":{"title":"Abstract","p1":"As a \u201cnew generation of value transfer Internet\u201d, blockchain finance is not only the information transfer of the traditional Internet, but also the future value transfer network. The new world and value flow will be the pioneers, builders, leaders and successors of the future. \u201cAggregate the value flow wells of the new world to provide complete {coin} mining applications for its services. It is the core business of DeFi Mining.\u201d","p2":"DeFi Mining is a one-stop computing power mining platform. DeFi Mining is based on the computing power of mainstream digital assets ({coin}), as well as mining machines and mining farms. Users can recharge to purchase computing power package mining income, and can also obtain investment returns by investing in the company\'s blockchain financial projects, thereby redefining the value of the {coin} mining industry, and finally creating a centralized computing power service, financial service, and circulation service. A blockchain computing power mining financial service platform integrating information services."}},"transfer coin remaining remark":"Best to leave at least 0.002 - 0.004 ETH","Need help Contact us at":"Need help? Contact us at"}')},97124:function(e){"use strict";e.exports=JSON.parse('{"en":"English","zh-mo":"\u7e41\u9ad4\u4e2d\u6587","Pool Statistics":"\u7926\u6c60\u7d71\u8a08","Nodes":"\u7bc0\u9ede","Participants":"\u7926\u5de5","USDT Staked":"\u7e3d USDT","Reward":"\u5229\u6f64","A new smart contract based decentralized dynamic mining pool":"\u63a1\u7528\u6700\u65b0\u7684\u201c\u667a\u80fd\u5408\u7d04\u201d\u79fb\u52d5\u6027\u6316\u7926\u6c60","Mining Platform":"\u6316\u7926\u5e73\u53f0","of the future!":"\u7684\u672a\u4f86!","Join Mining Pool":"\u52a0\u5165\u6316\u7926\u6c60","Mining Pool":"\u6316\u7926\u6c60","Account":"\u5e33\u6236","Exchange":"\u4ea4\u63db","Withdraw":"\u63d0\u6b3e","Share":"\u5206\u4eab","Switch Coin":"\u5207\u63db\u8ca8\u5e63","Share your referral link with your friends and earn extra reward for each reward they receive":"\u5206\u4eab\u60a8\u7684\u63a8\u85a6\u93c8\u63a5\u7d66\u60a8\u7684\u670b\u53cb\uff0c\u4e26\u4e14\u7372\u5f97\u6bcf\u500b\u4ed6\u5011\u63a5\u6536\u5230\u7684\u63a8\u85a6\u734e\u52f5\u3002","Latest Rewards":"\u6700\u65b0\u5229\u6f64","Wallet Address":"\u9322\u5305\u5730\u5740","FAQ":"\u5e38\u898b\u554f\u984c","faq1":"\u5982\u4f55\u53c2\u4e0e\uff1f","faq1a":"\u60a8\u9700\u8981\u70ba\u667a\u80fd\u5408\u7d04\u6388\u6b0a\u652f\u4ed8\u5c11\u91cf {coin} \u8cbb\u7528\u624d\u80fd\u958b\u59cb\u55ae\u7522\u8fb2\u696d\u3002 \u9019\u53ea\u662f\u4e00\u6b65\u3002 \u7372\u5f97\u6388\u6b0a\u5f8c\u624d\u80fd\u958b\u59cb\u6316\u7926\u3002","faq2":"\u5982\u4f55\u7372\u5f97\u734e\u52f5\uff1f","faq2a":"\u6bcf\u5929\u958b\u91c7\u7684\u52a0\u5bc6\u8ca8\u5e63\u6703\u7522\u751f {coin} \u6536\u5165\uff0c\u4e26\u6309\u7167\u5408\u7d04\u6a19\u51c6\u7d66\u5927\u5bb6\u4e00\u5b9a\u6bd4\u4f8b\u7684\u6536\u5165\u3002","faq3":"\u6295\u8cc7\u56de\u5831\u7387 (ROI) \u662f\u591a\u5c11\uff1f","faq3a":"\u52a0\u5165\u6316\u7926\u6c60\u6210\u529f\u5f8c\uff0c\u7cfb\u7d71\u5c07\u958b\u59cb\u901a\u904e\u667a\u80fd\u5408\u7d04\u8a08\u7b97\u60a8\u6301\u6709\u7684USDT\u6578\u91cf\u3002 \u734e\u52f5\u5c07\u6bcf 6 \u5c0f\u6642\u5206\u767c\u4e00\u6b21\u3002","faq3a1":"\u9810\u8a08\u6bcf\u65e5\u751f\u7522\u6536\u5165\uff1a","faq4":"\u9080\u8acb\u597d\u53cb\u6709\u734e\u52f5\u55ce\uff1f","faq4a":"\u662f\u7684\uff0c\u60a8\u53ef\u4ee5\u901a\u904e\u60a8\u7684\u63a8\u85a6\u93c8\u63a5\u9080\u8acb\u60a8\u7684\u670b\u53cb\u52a0\u5165\u7926\u6c60\u3002 \u6bcf\u6b21\u60a8\u7684\u670b\u53cb\u6536\u5230\u734e\u52f5\u6642\uff0c\u60a8\u90fd\u6703\u7372\u5f97 30% \u7684 {coin} \u734e\u52f5\u3002","My Wallet":"\u6211\u7684\u9322\u5305","Total Reward":"\u7e3d\u5229\u6f64","Exchangeable":"\u53ef\u4ea4\u63db","Account Balance":"\u5e33\u6236\u9918\u984d","Mining Rewards":"\u6316\u7926\u734e\u52f5","Date":"\u65e5\u671f","Exchangeable quantity":"\u53ef\u4ea4\u63db\u6578\u91cf","To":"\u8f49","All":"\u5168\u90e8","Estimated amount":"\u9810\u8a08\u6578\u91cf","Exchange History":"\u4ea4\u63db\u8a18\u9304","From":"\u5f9e","Available amount":"\u53ef\u7528\u6578\u91cf","Withdrawal History":"\u63d0\u6b3e\u8a18\u9304","Status":"\u72c0\u614b","Referral":"\u63a8\u85a6","Your Invitation Link":"\u60a8\u7684\u63a8\u85a6\u93c8\u63a5","Referral Rewards":"\u63a8\u85a6\u734e\u52f5","Copied to clipboard":"\u5df2\u8907\u88fd\u5230\u526a\u8cbc\u7c3f","Helpdesk":"\u5ba2\u670d","Send":"\u767c\u9001","Audit Report":"\u5be9\u6838\u5831\u544a","Partner":"\u5408\u4f5c\u5925\u4f34","Whitepaper":"\u767d\u76ae\u66f8","whitepaper":{"1":{"1":{"title":"Project Concept Background","p1":"Before the birth of Bitcoin, financial aggregation transactions around the world were realized through a centralized approach. However, with the development of internet technology, trading centres have more and more control over data, and the equality of trading individuals and trading centres in many aspects, such as the equality of trading, the transparency of information, and the reliability of historical information, is becoming more and more unequal. At the same time, data monopolies and data closures between the various centralized trading platforms have made it impossible for centralized institutions to do the same. DeFi Mining was born out of this situation and seeks to solve many of the problems of the current generation of financial services through new technology.","p2":"As a \u201cnew generation of value transfer internet\u201d, blockchain finance is not only a traditional internet information transfer, but also a future value transfer network. The value flow of the new world will be the pioneer, builder, domainer and successor of the future. With Bitcoin and Ether breaking through the new highs in the past two years, the entire cryptocurrency market sentiment has also reached a high point, but from a microscopic perspective, the best performing pass-throughs in the DeFi space are only the leading projects in each segmentation track, and many investors who bought cryptocurrencies at high levels still have no hope of returning their capital. However, against this backdrop, some of those involved in liquidity mining have been able to earn high returns while preserving their capital, thanks to DeFi Mining, which uses an innovative structured risk-regulation mechanism and liquidity provisioning mechanism to attract additional investment from major chambers of commerce to provide pledged mining capacity for DeFi Mining crypto assets. Goal is to build a large ecology, i.e. to build a strong community and consensus before launching a product, i.e. a blockchain financial project, to enable users who already hold coins to sink in."},"2":{"title":"Mining has spawned a new industry chain","p1":"Secondly, mining as an infrastructure in the cryptocurrency world, it does not just exist in isolation. Cryptocurrency mining has evolved from CPU and GPU mining to ASIC mining, from individual mining to pool mining, and from no service at the beginning to perfect supporting services now. We can see through the whole evolution process of the mining industry that the cryptocurrency mining industry has spawned a new industrial chain around the development of the whole mining industry, from production equipment such as mining machines, mining farms and pools to logistics, accessories, finance and other supporting services have all emerged in response to the development of the mining industry and the rise in coin prices, thus enabling the entire mining industry to develop better."},"3":{"title":"The mining industry has huge room for development","p1":"The, as the cryptocurrency coin price continues to rise, its wealth effect will become more and more obvious, thus attracting more global users to actively participate. At the same time, it also drives the booming development of the entire cryptocurrency mining industry upstream and downstream. From the hosting of mining farms to the research and development of mining machines, from the technical optimization of mining pools to upgrading of logistics services, from the diversification of financial services to the introduction of new accessories, from the professional introduction of talents to the active layout of capital, all of them reflect the certainty and expansiveness of the future development of the entire mining industry."},"4":{"title":"Mining can effectively combat bull and bear market","p1":"The entire digital currency market experiences bulls and bears, peaks and troughs, and some industry participants, when experiencing industry bulls and bears, will choose between risk appetite and risk avoidance. And while mining sacrifices some liquidity relative to the high-risk, highly liquid digital currencies, its returns are also more long-term and stable."},"title":"Project Background Overview"},"2":{"1":{"title":"High investment requirement","p1":"At present, the mainstream mining form is still dominated by physical mining machines, which forces many users who want to mine to spend a certain amount of capital costs to buy a mining machine, after which they also need to build their own or find a suitable site for hosting, while the risk of the digital currency itself and the fluctuation of the coin price is relatively large. With so many uncertainties, users also need to pay a lot of capital and time costs in advance, thus raising the overall barrier to entry for the industry and discouraging many users."},"2":{"title":"Extensive knowledge required","p1":"Mining also has high professional requirements for miners, which involves two aspects: the professionalism of the hosting mine on the one hand, and the professionalism of the maintenance of the mining machines on the other. This is because a professional mining site has certain standards, whether it is the distance between machines or the ventilation and humidity of the site, which not only prolongs the life of the mining machines, but also allows them to run more stably. Maintenance specialisation requires professional maintenance staff who can deal with any problems that may arise with the mining machine, whether it is poisoning or hardware failure, thus guaranteeing that the mining machine is online 24/7."},"3":{"title":"Unregulated industry","p1":"As the current policy for mining and digital currency regulation is at a relatively early stage, legislation is lagging behind, and the overall development of the industry is in a state of barbaric growth, with a lack of unified standards, pre-sales and after-sales services, and varying levels of hosting, the industry is full of chaos, schemes and unspoken rules that give new and old miners headaches and even complaints."},"4":{"title":"Lackluster service","p1":"Mining is an industry with a large initial investment in fixed assets, and the financial services to match this are not perfect, resulting in poor liquidity of the fixed assets invested by miners. When miners need a certain amount of cash flow, they are stretched to the limit and have no way to liquidate their fixed assets in time to meet their financial needs, except for pledging and selling digital currencies. In addition to financial services, other services related to mining are also relatively backward, such as the construction and transfer of mining machine farms, the warranty and maintenance of mining machine parts, the reporting and publishing of mining information, etc., are all in a relatively rudimentary state."},"title":"Traditional mining problems"},"3":{"1":{"title":"Introduction to DeFi Mining","p1":"DeFi Mining is a global technology-leading {coin} arithmetic mining cloud service platform, and an international website dedicated to providing global users with safe, convenient, reliable and innovative digital asset derivatives. DeFi Mining allows users to seamlessly switch between tokens on the chain in a fully decentralised and non-custodial manner. DeFi Mining is committed to providing global users with intelligent, secure, stable, fast and convenient digital asset investment and trading services, bringing intelligent new changes to global digital finance."},"2":{"title":"Product key elements","item1":"Product: DeFi Mining","item2":"Positioning: {coin} mining financial tool","point1":{"title":"Liquidity Mining","p1":"DeFi Mining supports single coin mining, pledged LP mining and transaction mining, and is completely decentralised. It can freely deposit tokens for exchange and can be freely withdrawn. It does not trade with other trading users, it trades with a pool of tokens and has an automatic market making model to calculate the trading price.","p2":"One of the features of DeFi Mining is that it pools everyone\'s liquidity together and then makes a market based on an algorithm. The AMM model is supposed to be one of the biggest innovations of the current bull market, the cryptocurrency market. AMM fundamentally changes the way users trade cryptocurrencies, unlike traditional order book trading models, where both sides of the trade interact with an on-chain pool of liquid assets. The liquidity pool allows users to seamlessly switch between tokens on the chain in a fully decentralised, and uncustodied manner. The liquidity provider, in turn, earns passive income through transaction fees, which are based on the percentage of their contribution to the asset pool. What\'s more, your assets remain in your personally controlled wallet and not into the trading platform, so they remain 100% safe."},"point2":{"title":"Trading and exchange type services","p1":"DeFi Mining wallets have built-in digital asset trading functionality, from wallets with access to centralised exchange platforms, such as BitPie, to wallets with access to decentralised trading platforms, such as Imtoken, to automated trading platforms with access to the Bancor mechanism, such as Tokenpocket. Some wallets offer a \u201cflash exchange\u201d function, where different currencies are exchanged at a certain \u201cexchange rate\u201d, often using a centralised exchange model for the backend of the currency exchange. This not only reduces the number of steps users have to take to transfer coins to other coins, reducing the probability of making mistakes, but also enhances user stickiness and provides a good development direction and capital deposit for the subsequent transformation of the wallet project. However, the built-in exchange greatly increases the complexity of the system and introduces greater risk for wallet products that require high security themselves, and the security of user funds will be challenged to some extent."},"point3":{"title":"News and information services","p1":"DeFi Mining has a wealth of built-in digital asset market information such as news feeds, market snapshots, project profiles, K-line charts, large capital flow monitoring, code activity and more. Blockchain project information and quotes are what pass holders need to keep up with the market, and are also a great entry point for gathering user traffic. A well-integrated information and quotes service in a wallet product will not only create enough stickiness for existing users, but also bring in more incremental users. However, information services require a certain amount of investment in human and financial resources, which can significantly increase the operating costs of the product. Currently, most wallets do not have a well-developed news and information service, and mainly focus on providing quotes."},"point4":{"title":"Money Management Services","p1":"DeFi Mining has a built-in wealth management module, which includes long-term fixed income, short-term flexible balance, digital asset P2P_financing and lending, and mortgages. At present, some of these finance modules are connected to third party services and do not participate in finance services themselves; some are finance products developed to provide cheap funds for the development of their own platforms, with the platform\'s earnings paying interest to users; some are digital assets raised by the platform and then invested in primary or secondary market transactions to obtain excess returns and pay interest to users; and some provide P2P digital asset lending and borrowing transactions. Others offer P2P digital asset lending and trading services, providing a matchmaking service between those who demand and those who provide assets. For long-term coin holders, digital asset management services hit the nail on the head and can be held to gain income, with annual yields ranging from 4% to 20% for various types of wallets. However, the blockchain industry is developing rapidly and the digital asset market is volatile, so financial products with poor liquidity will face greater risks. The digital asset finance market is currently immature and no industry benchmark leading companies have emerged. In addition digital asset P2P lending type, if it is difficult to control the risk of the lender, it is easy to cause default, please pay attention to the risk of investors."},"point5":{"title":"POS mining services","p1":"For blockchain projects that support the POS consensus algorithm, some wallets offer a lock-in to join the POS mining service, and the mining proceeds will be sent to users on a regular basis. Usually the master node for POS mining is provided by the wallet project side. Digital assets that meet certain capital requirements can participate in POS mining, with fixed lock time mining and also a mining model that supports redemption at any time. The wallet project side will take a proportional share of the mining proceeds, and both the wallet project side and the user can have a more stable additional income. The coins that currently support POS mining are: DASH, Lite Bitcoin, zCoin, Qtum and Hcash."},"point6":{"title":"Asset aggregation services","p1":"Usually users will hold different coins in multiple exchanges and wallets, which is not very convenient for centralized management and revenue enquiries. Such wallets can provide users with fund aggregation services, aggregating users\' pass holdings in multiple wallets and exchanges through API interfaces, and platforms with poor support for API interface services can also use manual maintenance for initial input. Currently, the aggregation service can support exchanges, wallets, ICO funds and fiat funds.","p2":"Currently there are many different types of exchanges and wallets, each of which supports different types of digital assets, so users\' funds are usually scattered across different platforms, which is not conducive to centralised management and queries. This type of wallet requires API interface development with many exchanges and wallets, and there are certain development and maintenance costs."},"point7":{"title":"IDO One-Stop Platform","p1":"DeFi Mining will establish a one-stop platform for IDO project evaluation, promotion and access, DeFi Mining adopts a community governance model, DeFi Mining\'s voting weight is the only measure, holding DeFi Mining is recorded as one vote, pledging DeFi Mining is recorded as two votes, the DeFi Mining re-purchased or pledged after the voting starts is not counted in the voting results. Provide traffic and support for project developers, data analysis and quality resources for customers. ido platform governance coins will also take liquidity mining mined out, involving specific economic models such as ido project mortgage, hitting new, buyback, etc."},"point8":{"title":"Other application products","p1":"The DeFi Mining team will continue to provide the latest products according to market development and demand. We strive to be the DeFi full traffic portal and provide the most complete DeFi products to serve our users."}},"3":{"1":{"title":"Lower the industry threshold and increase asset liquidity","p1":"DeFi Mining has lowered the entry barrier to the mining industry by giving fixed assets such as mining machines, mining farms and arithmetic power some liquidity in a platform-specific way, allowing more users who are interested in mining to participate and enjoy the benefits of mining without having to invest in expensive fixed assets. At the same time, it has also increased the liquidity of mining farms and miners\' assets, enabling them to develop more rapidly."},"2":{"title":"Scale and specialised expenditure costs","p1":"DeFi Mining will continue to build and purchase its own infrastructure and mining equipment, as well as cooperate with other ecological partners, in order to generate a certain scale effect, thus reducing the platform\'s procurement, construction and operating costs. At the same time, due to the large scale of the company, it will also have a relative advantage in obtaining the corresponding power resources and compliance qualification."},"3":{"title":"Cross-currency mining services to enhance the freedom of mining","p1":"As the consensus algorithms of different coins are different, the required mining machines are also different, so this is a limitation to the freedom of miners. DeFi Mining can help miners to provide mining services for different coins under different conditions according to their individual needs, thus enhancing their freedom of mining."},"4":{"title":"Empowering financial attributes to provide financial services to users","p1":"DeFi Mining will continue to expand its financial services and products to provide a full range of financial services and support to ecological members including miners, mining farms, mining pools and mining machine manufacturers to help them survive and develop better DeFi Mining and its partners will obtain the appropriate business licenses in accordance with local policies and regulations."},"title":"Project solutions"},"4":{"1":"K-Chart: Once you enter the DeFi Mining platform, you can see the K-Chart by entering the contract address, which gives you a comprehensive and thorough view of the real changes in the {coin} market. From the K-chart, investors can see both the trend of the bullion market and the daily fluctuation of the market conditions.","2":"Transparent trading: The model is simple, mainly aggregating transactions, and does not host the user\'s assets, eliminating the possibility of the exchange monitoring and stealing.","3":"Trading on the chain: The biggest difference with other {coin} mining platforms is that DeFi Mining does all this through smart contracts, placing asset escrow, aggregated transactions and asset clearing on the blockchain.","4":"Highly trustworthy: Smart contracts are used to achieve a decentralized and trustless transaction mechanism, which solves the risk of internal operation, business ethics, asset theft and other risks that seriously affect the safety of users\' assets due to human factors on centralized platforms.","5":"Security and efficiency: Users\' assets can be transferred freely without any approval, and there is no need to worry about hackers stealing or losing coins, so there is sufficient security.","title":"Advantages of DeFi Mining"},"5":{"1":{"title":"Calculating power service","p1":"DeFi Mining is based on {coin} mining machines, mining farms and the arithmetic power behind them. Arithmetic services are the core and most fundamental business of DeFi Mining, and users can enjoy a wide range of arithmetic services through the platform. In the world of cryptocurrencies, apart from buying and selling cryptocurrencies through exchanges, the only way to mine cryptocurrencies is to have arithmetic power, so the value of arithmetic power is particularly important here. Once a platform user has arithmetic power, they can turn it into tangible revenue through arithmetic power. DeFi Mining users who use the platform to purchase arithmetic for mining services will see their daily earnings increase as the price of the cryptocurrency rises, and vice versa.","p2":"Therefore, DeFi Mining itself has the ability and demand to continuously extend its cooperation with external arithmetic power, and as the arithmetic power purchased by DeFi Mining and accessed by external parties continues to increase, the larger the scale, the more users will continue to increase."},"2":{"title":"Streaming Service","p1":"DeFi Mining\'s miners can transfer their fixed assets, such as mining machines and mining farms, through the platform under certain conditions within the platform and on the secondary market, thus increasing the liquidity of miners\' assets and allowing more idle arithmetic power to come into play, so that users who want to mine can participate in the process. As DeFi Mining grows, the platform will gradually increase the amount of arithmetic purchased by the platform and the amount of external arithmetic. Users can purchase physical {coin} miners from DeFi Mining or transfer their own miners to our mine for management"},"3":{"title":"Financial Services","p1":"As one of the core services provided by DeFi Mining, we will provide cryptocurrency_users with financial services such as collateral lending, hedging, coin-flash, asset management, leveraged instalment, OTC, option contracts, etc. Cryptocurrency will be used as a payment tool for DeFi service fees. DeFi Mining will continue to improve its financial services and derivative products according to market conditions and the needs of cryptocurrency users."},"4":{"title":"Information Services","p1":"The mining industry has experienced many years of development, but the information in the industry is still relatively closed and asymmetric. From local electricity prices to the sale and purchase of mining machines, from industry blacklists to industry resource information, the mining community lacks an authoritative and systematic information release and sharing platform. In the future, DeFi Mining will gradually integrate the industry\'s leading organizations into the entire ecosystem, so that miners can take the easy way out and get access to reliable resources and information from the platform. The information service scope mainly includes network information release, mining machine sales, mining machine reviews, etc."},"title":"Value services"},"6":{"1":"Transparent transactions: The model is simple and mainly deals with aggregated transactions, and does not host users\' assets, thus eliminating the possibility of the exchange monitoring and stealing.","2":"Transaction on the chain: DeFi Mining all of this is achieved through smart contracts, the asset custody, aggregation transactions, asset liquidation are placed on the blockchain.","3":"High trust: Smart contracts are used to achieve a decentralised and trustless transaction mechanism, which solves the risk of internal operation, business ethics, asset theft and other risks that seriously affect the safety of users\' assets due to human factors.","4":"Security and efficiency: Users\' assets can be transferred freely without any approval from anyone, and there is no need to worry about hackers stealing or losing coins, so there is sufficient security.","title":"Core of DeFi Mining"},"7":{"1":{"title":"Engineering-level development capabilities","p1":"DeFi Mining community members have unique insights and experiences in blockchain technology. They have many years of development experience, which gives DeFi Mining\'s technology development a huge advantage and guarantee. DeFi Mining already has a decentralised application in internal testing, the creation of which comes from the contributions of community volunteers and the assistance of the founding team."},"2":{"title":"Composite core business logic","p1":"DeFi Mining has core business segments covering all directions, with developments in information sharing, information settlement systems, multi-functional wallets, traceable elements, smart_contracts and other areas, which will eventually converge and link into an overall cryptocurrency trading ecosystem."},"3":{"title":"The industry\'s first-tier cooperation institutions","p1":"Excellent partners are the foundation of success. DeFi Mining has top-tier partners around the world, who will cooperate with DeFi Mining in all aspects and help DeFi Mining at all levels to promote its rapid development."},"4":{"title":"Simultaneous launch of the global market","p1":"DeFi Mining has a rich community base and user base, and has been commercially deployed globally. At the same time, strong alliances allow DeFi Mining to launch simultaneously in various markets around the world, seizing opportunities and expanding markets in time!","p2":"In the future, DeFi Mining will establish multiple user communities and venues in Spain, Canada, USA, Singapore, Northern Europe, Eastern Europe, Japan, China, Korea, Australia, Malaysia, Cambodia, South America and other countries and regions to accelerate the project\'s development."},"5":{"title":"Cutting-edge industry trends","p1":"DeFi Mining enables the existing cryptocurrency trading market to gain disruptive changes and establish an advanced and unique payment ecology. It is hoped that a series of open and fair rules and standards will promote rapid changes in the entire market, optimise the current industrial structure, rationalise the allocation of public welfare resources, and ultimately realise the decentralised ecosystem established by the DeFi Mining protocol based on blockchain technology."},"6":{"title":"Global commercial application","p1":"In order to realize DeFi Mining\'s ambitious ideals and mission, DeFi Mining will make use of its advantages to land commercial applications globally, actively cooperate with various traditional industries to help the development of the blockchain crypto industry, improve its influence and establish an ecosystem led by DeFi Mining."},"title":"Cornerstone of DeFi Mining"},"title":"Overview of DeFi Mining"},"4":{"1":{"1":{"title":"Data layer","p1":"Based on the high redundancy storage mechanism of the blockchain, blockchain storage has a certain impact on the scalability and performance of the blockchain. The DeFi Mining framework is designed with a multi-layered node system, and different storage strategies (distributed bookkeeping) are selected according to different node applications."},"2":{"title":"Network layer","p1":"The P2P Protocol supports data transmission and signaling exchange among nodes in the blockchain network and is an important communication guarantee for data distribution or consensus mechanism. The DeFi Mining system is designed to support multiple P2P protocols, communication mechanisms and serialization mechanism configurations, so that flexible protocols can be used according to different scenarios. In terms of communication security, HTTPS, TLS, WSS (SecureWebsockets) and other protocols are flexibly supported, and OAuth authentication integration can be extended to support the external service interface of the platform application."},"3":{"title":"Consensus layer","p1":"DeFi Mining (delegatedByzantine Fault Tolerance) consensus algorithm, expanding the underlying network consensus algorithm, with high performance, high consistency characteristics, suitable for financial payments, digital transaction data frequently generated, and a high degree of real-time bookkeeping requirements of weak central upper layer applications."},"4":{"title":"Incentive Layer","p1":"DeFi Mining not only has an airdrop for genesis consensus rewards, but also a liquidity mining pool for long-term network value maintenance. Because of DeFi Mining\'s unique consensus mechanism, anyone can join at any time to earn rewards."},"5":{"title":"Contract Layer","p1":"For each smart contract submission, deployment, use and cancellation of a complete and controllable process management, and integrated rights management mechanism for comprehensive security management of the mechanisms of smart contract operation."},"6":{"title":"Application layer","p1":"The application layer will provide a common transaction protocol, support multi-language integration and functional expansion, with support for Java, JavaScript, Python and other languages, and has been fully applicable to the underlying network expansion."},"title":"DeFi Mining System Architecture","p1":"The DeFi Mining system has six layers, namely the data layer, network layer, consensus layer, incentive layer, contract layer and application layer."},"2":{"title":"Arithmetic time-sharing system","p1":"DeFi Mining\'s arithmetic services are provided by the platform\'s own machines and external organizations that generate arithmetic 24/7. Users can freely choose when they want to use arithmetic, the amount of arithmetic and the corresponding mining coins through the platform, which improves the efficiency of arithmetic usage and breaks the inconvenience of mining caused by the limitation of firmware facilities in the past."},"3":{"title":"Trusted Proof of Stake Consensus (TDPOS)","p1":"The DeFi Mining platform uses Trusted Proof of Stake Consensus (TDPOS), a secure and trusted consensus protocol with high robustness and flexibility. Compared to traditional consensus mechanisms, TDPOS ensures a reliable source of distributed data through a hierarchical trusted consensus and hierarchical auditing strategy, safeguarding the fair rights and data privacy of participating nodes, while the transaction performance of over 1 million TPS is fully capable of handling a high concurrency sharing ecology.","p2":"We define the hierarchical Trusted Consensus (PT) of TDPOS as a function of Proof of Identity (PIA), Proof of Compliance (PCO), and Proof of Credit (PCR): PT=F\'{f(PIA),f(PCO),f(PCR)\'} and the three hierarchical credible proofs containing documents or transaction acts are described as follows.","p3":"Proof of equity (PIA): the higher the number of nodes locking coins, the higher the score. Proof of Compliance (PCO): is to verify the transaction behavior with the corresponding organizational policy and legal code requirements. Proof of Credit (PCR): is a dynamic credit verification that identifies the transaction behaviour on the role chain.","p4":"Combined with the decentralized features of the blockchain system, we incentivize the release of transaction behaviors with high credit consensus through the layered credit management and TOKEN mechanism of TDPOS with layered trusted consensus, which can greatly reduce the cost of information screening for the roles in the ecology, while improving transaction efficiency and guaranteeing transaction security."},"4":{"1":{"title":"P2P communication","p1":"P2P is the core foundation of blockchain, with features such as decentralization, scalability, robustness, privacy and high performance. The block is linked to DeFi Mining\'s loT devices and users, and is deeply optimised in terms of session maintenance, address determination, communication mechanism, storage scheme, transaction payment, etc. By specifying the physical configuration and size of the user and consensus nodes, and adopting a sharding mechanism and high-speed network connection, the communication, computation and storage burden of the consensus nodes is reduced and the transaction performance of the blockchain is improved, thus achieving the maximum performance of blocking for loT devices and providing the foundation for the registration, digitization, authentication and security of IoT devices in the future."},"2":{"title":"DeFi Mining encryption algorithm","p1":"The encryption and decryption of information is the key link of blockchain, mainly the algorithm of hash function and asymmetric encryption.","p2":"(1) The hash function part, currently there are mainly SHA, MD5 and other algorithms, and also includes the use of algorithms in series and in parallel. Since commercial applications generally focus more on performance issues, the DeFi Mining base algorithm is mainly the SHA256 algorithm.","p3":"(2) Asymmetric encryption part, there are mainly asymmetric encryption algorithms including RSA, DSA, elliptic curve algorithms, etc. Blockchain generally uses elliptic curve algorithms, including ECDSA and SCHNORR, considering that the verification speed of Schnorr signature is faster than ECDSA signature, and the size of this signature can be smaller, and also natively supports multiple signatures. This is in line with the small size of the Internet of Things, so DeFi Mining has developed its own SDSchnorr algorithm based on Schnorr.","p4":"At the same time, DeFi Mining has abstracted the underlying cryptographic algorithm library and interchangeable channels for the algorithms to meet the algorithmic and security needs of different loT applications. The names of the wallet and the address are interchangeable in the underlying block."},"3":{"title":"DeFi Mining Smart Contract","p1":"Each Internet-accessible lot device is given an \u201cidentity\u201d on the blockchain and has a globally unique identifier that can be used to identify a credit identity. Each smart contract has a unique public address, just like a normal wallet. The difference is that the smart contract\'s private key is discarded when the contract is created, so no one can send the digital assets inside the smart contract after it is created, except for the consensus mechanism. For the lot\'s monitoring event-based up-chaining needs, i.e. the loT device holder sets the smart contract, data storage path and data accounting amount in advance, it can be broadcasted across the network as well as stored in digital records. In the event of a relevant abnormal event that is incorporated into the blockchain\'s supervision, DeFi Mining\'s smart contracts require mandatory, strong real-time and fully automated triggering. The data for triggering conditions is also blockchain-protected data, which is accurate, safe and secure, and cannot be tampered with."},"4":{"title":"Cross-chain communication protocol","p1":"DeFi Mining supports cross-chain asset exchange protocols, which are extensions to existing asset exchange protocols, allowing multiple participants to exchange assets on different blockchains, and guaranteeing that all steps in the entire transaction process are all steps of the transaction are guaranteed to succeed or fail. At the same time, the cumbersome process of exchange via an exchange is eliminated. The flexibility of payment methods will stimulate a certain degree of diversity among users."},"5":{"title":"5G cloud storage","p1":"DeFi Mining 5g storage provides a special storage mechanism, which can not only prevent the storage of duplicate data, but also ensure the same security, break the \u201ccommon sense\u201d of the industry, and achieve both fish and bear. When using this mechanism, in addition to the user permission table, a global metadata table should be maintained to record the corresponding relationship between plaintext hash and ciphertext hash. When writing data, you should first query whether there is the same hash data, and if there is no such item, save it again.","point1":"Hash (data) \u2192 hdata to calculate plaintext hash","point2":"If checkdup (hdata) = true goto 11, if the same data already exists, go to step 11","point3":"The symmetric key is randomly generated by randomsym () + STK as the file storage key","point4":"Enc (STK, data) \u2013 encdata encrypt files with storage key","point5":"Hash (encdata) \u2013 henc to calculate ciphertext hash","point6":"The symmetric key is generated from the plaintext of the data by genkey (data) \u2192 sData","point7":"After adding salt, hash value is calculated. The reason for adding salt is that plaintext hash is a public value. If 5g storage blockchain is not added salt to enable partial storage, people who do not have plaintext data can also obtain the key. In order to ensure consistency, the salt value can be a fixed value.","point8":"Enc (hdata, STK) - encstk\'encrypts the storage with the symmetric key generated by the data plaintext","point9":"Store the key. This is a very \u201cweird\u201d step. It uses plaintext as the key and the key as plaintext to encrypt. When most people look at this algorithm, they think it is reversed. In fact, it is specially designed in this way, and this step is the core step of de duplication after encryption.","point10":"Putipfs (encdata) stores encrypted data into IPFS","point11":"Putmeta (hdata, henv, encstk) stores \'encrypted ciphertext hash and plaintext","point12":"The key is recorded in the global metadata table and in the plaintext hash key","point13":"Goto 14","point14":"Get meta (hdata) henv, encstk\'get ciphertext from global metadata table","point15":"Storage key after hash and plaintext encryption","point16":"Genkey (data) \u2192 sData uses the same algorithm to generate symmetric key from data plaintext","point17":"The symmetric key is used by Dec (sData, encstk \') - STK to decrypt the storage key","point18":"Enc (spub, STK) \u2013 encstk uses the user\'s encryption public key to encrypt and store the key","point19":"Putpri (hdata, encstk) stores the encrypted storage key of the encrypted public key to the user","point20":"The user permission list is recorded under the clear text hash item","point21":"When getting data, take out the corresponding ciphertext hash from the plaintext hash in the global metadata table, take out the ciphertext hash from the IPFs through the ciphertext hash, take out the encrypted storage key from the permission list, decrypt the encrypted storage key with the user\'s encryption private key to obtain the storage key, and decrypt the encrypted data with the storage key to obtain the data plaintext.","point22":"It is obtained by getmeta (hdata) \u2192 henc from the global metadata table through plaintext hash","point23":"The ciphertext hash getpri (hdata) \u2192 encstk obtains the encrypted public key from the permission list through plaintext hash","point24":"Get IPFs (henc) \u2192 encdata extract ciphertext from IPFs with ciphertext hash","point25":"Dec (sprv, encstk) STK encrypts the user\'s public key with the user\'s private key","point26":"The encrypted storage key is decrypted to obtain the storage key","point27":"Dec (STK, encdata) - data decryption to obtain file data","point28":"The optimized DSN scheme not only effectively guarantees the security of data, but also realizes encryption and de duplication. In order to achieve better de duplication effect, the data can be divided into blocks according to a fixed length, and each block is de duplicated separately. The above scheme can only be used to store static data. When storing dynamic data, not only the ID that does not change with the content should be used to replace hash as the identification of data, but also the verification of write permission should be added to prevent the data from being covered and tampered by others.","point29":"The creation process is as follows:","point30":"Xun randomasm () - swpub, SwPrv randomly generates asymmetric key as write permission key","point31":"Create (swpub) - ID creates a dynamic data, obtains a unique ID, and records the public key of the write permission corresponding to the ID","point32":"Random sym () - STK randomly generates symmetric key as storage key","point33":"Enc (spub, STK) \u2013 encstk uses the user\'s encryption public key to encrypt and store the key","point34":"Putpri(ID, encstk) stores the encrypted storage key of the encrypted public key into the user permission list, and records it under the ID. the process of each data writing is as follows:","point35":"Get pri (ID) + encstk to retrieve the encrypted storage key from the user permission table","point36":"Dec (sprv, encstk) \u2192 STK encrypts the user\'s public key with the user\'s private key \u2022 The storage key is obtained by decryption","point37":"Enc (STK, data) \u2192 encdata encrypt files with storage key","point38":"Hash (encdata) \u2192 henc to calculate ciphertext hash","point39":"Enc (SwPrv, henc) - enchenc uses the public key of the ID\'s write permission to hash the ciphertext autograph","point40":"Putdyn (ID, encdata, enchenc) writes encrypted dynamic data to sign data","point41":"Write authorization on behalf of. When a node storing dynamic data fragments writes dynamic data, it needs to verify the write permission first","point42":"Get key (ID) \u2192 swpub get the public key of write permission corresponding to the ID","point43":"Hash (encdata) - henc to calculate ciphertext hash","point44":"If Dec (swpub, enchenc) = henc write (encdata)","point45":"The reading process of writing dynamic data is as follows:","point46":"From the permission list, getpri (ID) \u2014 encstk obtains the data encrypted with the encryption public key corresponding to the ID","point47":"Storage key","point48":"Get dyn (ID) \u2013 encdata read ciphertext from dynamic data storage area with ID","point49":"Dec (sprv, encstk) - STK encrypts the user\'s encrypted public key with the user\'s encrypted private key","point50":"The encrypted storage key is decrypted to obtain the storage key"},"title":"Technology Applications"},"5":{"1":{"title":"Password setting for device use","p1":"Users can be reminded to set unlocking passwords, unlocking gestures or fingerprint unlocking, wrong password unlocking time period, high strength of device transaction password, if there is no perfect password setting control, it can not be directly accessed to view personal privacy information after the loss of the device, to conduct transactions."},"2":{"title":"Create a wallet keyword security","p1":"The private key process is secure and can be saved locally."},"3":{"title":"Transaction process security","p1":"You can display the address of your account and verify whether the address has been modified, so that your money is not lost."},"4":{"title":"Data storage security","p1":"Data can be stored on the storage device, including the private key storage method, stored on the device memory card, which can be accessed externally, to improve the relevant function design, to protect the relevant data from hacking."},"5":{"title":"System integrity security","p1":"The ability to verify the strict integrity of the equipment system, to check whether it is genuine and to prevent tampering by hackers or attackers in the place where the equipment is distributed."},"title":"Technology Applications","p1":"DeFi Mining is a secure and reliable platform that combines on-chain and off-chain innovation through technical and conceptual innovation."},"title":"DeFi Mining Technical Architecture"},"5":{"1":{"title":"DeFi Mining Industry Chain","p1":"With the continuous development of DeFi Mining, the number of organizations that will reside in the DeFi Mining platform will also increase, and eventually a diversified ecosystem will be formed that includes exchanges, mining pools, mining machine manufacturers, social media, hot and cold wallets and mining services. Users holding cryptocurrencies will also enjoy certain rights and benefits when they access the main chain, such as discounted exchange fees, discounted mining pool fees, discounted mining machine purchases, priority purchase rights, purchase installments, token purchase rights, cold wallet benefits, etc. DeFi Mining will cooperate with more eco-system institutions through the main chain to give more rights and value to cryptocurrencies. DeFi Mining will cooperate with more eco-system institutions through the main chain, thus giving more rights and values to the cryptocurrency"},"2":{"title":"DeFi Mining Wallet","p1":"To facilitate the use of the wallet by ordinary users, the DeFi Mining wallet uses the SPV method, i.e. accessing the wallet through the web. Cold wallets are suitable for wallets with large sums of money, where the public and private key pairs are generated offline and the user can generate any key pair they like. Hotwallets: Hotwallets are suitable for small and fast transaction scenarios. Hotwallet keys are hosted in a way that when a user registers for a wallet account, the private key generated using the user\'s payment password pair is encrypted locally on the user\'s computer via 3DES and the encryption results are hosted in the wallet cloud via the SSL protocol. This means that the hot wallet key information transmitted over the network and stored in the cloud is the user\'s encrypted data and no one other than the wallet user has access to the original content of the private key.","p2":"When a user needs to sign a transaction, the private key is obtained from the wallet cloud server and the content is decrypted on the user\'s local computer by the user entering the payment password, and upon successful decryption, the local wallet program will sign the transaction information with the private key and submit it to the DeFi Mining wallet network for trading. The DeFi Mining wallet also contains two types of assets - native assets and registered assets - similar in nature to real-life wallets that contain Chinese Yuan and various cards. Native assets can be used without any trust, while gateway-registered assets must trust the corresponding assets in order to exchange value.","p3":"Based on the above, and in order to better build the DeFi Mining wallet ecosystem, the DeFi Mining wallet introduces a series of key pairs, so that each wallet address corresponds to a key pair: a private key and a public key. The private key is a random number generator to generate a 256-bit entropy, displayed as a 64-bit hexadecimal number, which is unpredictable and unrepeatable, and therefore unique. It holds the ownership and control of the wallet and is used to sign and verify each transaction. In order to lower the user\'s threshold, the mnemonic becomes another manifestation of the plaintext private key. The DeFi Mining wallet, in order to help users to memorise complex private keys and increase convenience, generates a public key through a certain cryptographic algorithm (e.g. a hash function), which corresponds to the private key one by one. Its centralised wallet: a smart wallet for personal digital assets that supports the storage and transfer of a wide range of token transactions, will play a vital role in the next generation of the Internet digital economy. It can track and capture the data and market analysis of each exchange in real time, carry out intelligent arbitrage transactions in real time, determine the price difference of each trading platform and the trading volume of each currency to decide whether to carry out arbitrage tasks, and initiate transactions at the right time by itself, combined with a variety of arbitrage methods can be completely unaffected by price increases and decreases. The combination of multiple arbitrage methods can be completely independent of price increases and decreases. It solves all the drawbacks and risks of manual arbitrage."},"3":{"1":"a decentralised peer-to-peer network (digital asset protocol)","2":"a public ledger of transactions (blockchain)","3":"a decentralised data and deterministic asset issuance (distributed mining)","title":"Crypto Asset Trading System","p1":"The DeFi Mining Crypto Asset Trading System is a user trading platform. All users can convert multiple assets within the platform, where digital assets are a distributed peer-to-peer network system. As such, there is no \'central\' server and no central control point. The Digital Asset protocol includes a built-in algorithm that regulates the mining function in the network. The task that miners must complete is to successfully record a block transaction in the digital asset network. This difficulty is dynamically adjusted. Digital Assets before Digital Assets represents the culmination of decades of cryptography and distributed systems, a unique and powerful combination that brings together four key innovations. Digital Assets consists of these:"},"4":{"title":"DeFi Mining financial platform","p1":"Contract escrow platform, risk avoidance and stable returns. Cryptocurrencies are naturally decentralised, and most hot wallets inherit this property. As the private key is held by the user, it cannot be recovered if lost and transactions cannot be rolled back, thus putting the \u201cwallet security awareness\u201d of many users to a great test. DeFi Mining is the world\'s first and only personal digital wealth management product that combines multi-currency, cross-chain, decentralised trading, P2P encrypted instant messaging and integrated social community applications.","p2":"Lending and financial gains: DeFi Mining will later launch a one-click aggregated lending service to expand the underlying assets for lending and insurance, enhancing the liquidity of users\' assets. The aggregation of mainstream and today\'s most popular major Defi lending platforms, such as MakerDao, Compound, etc., will reduce users\' borrowing costs by adjusting the choice of coin weights and minimum interest rates in real time. It also connects off-chain credit behaviour with on-chain financial behaviour based on a credit prophecy machine. Users with a good credit history can also use their \u201ccredit behaviour\u201d record to participate in behavioural mining, and DeFi Mining may even have the attribute of mining weighting in the future."},"title":"The DeFi Mining Ecosystem"},"6":{"1":{"col1":"August 2021","col2":"Complete development and design of the prototype based on the DeFi Mining platform"},"2":{"col1":"September 2021","col2":"Beta version of DeFi Mining platform goes live for testing"},"3":{"col1":"November 2021","col2":"DeFi Mining platform goes live in beta"},"4":{"col1":"December 2021","col2":"Applications go live, integrate many industries, organise multilingual platforms for global business synergy operations and create a trillion dollar DeFi Mining infrastructure platform"},"5":{"col1":"March 2022","col2":"DeFi Mining has contacted more overseas blockchain exchanges to actively promote overseas DeFi Mining launch plans and enhance the international reach of the DeFi Mining public project"},"6":{"col1":"June 2022","col2":"Collaborate with more developers on more types of mining projects"},"7":{"col1":"2023","col2":"Complete global membership roadshow marketing"},"title":"Project Development Planning","col1":"Time","col2":"Development Planning"},"7":{"title":"Summary","p1":"The wheel of historical development rolls forward, and the development of society is changing day by day. Blockchain finance can contain everything, and the trend development has become irreversible. Whether we are pioneers or watchers is the most significant choice you can make in this era.","p2":"DeFi Mining platform: will give the future financial flow of the {coin} world a best entrance and base. We will be the navigator, pathfinder, domainer, discourse, technology benchmarker and ecological city pool of the blockchain financial world.","p3":"We are not just involved in DeFi Mining, we are involved in building the entire {coin} world. We are at the forefront of the new world, we reap the scenery and the territory. At the same time, the success of DeFi Mining is driven entirely by the consensus of the community members. Let\'s build the brilliance and legend of DeFi Mining with the best product and the strongest consensus."},"title":"DeFi Mining - \u767d\u76ae\u66f8","index":"\u76ee\u9304","abstract":{"title":"\u6458\u8981","p1":"As a \u201cnew generation of value transfer Internet\u201d, blockchain finance is not only the information transfer of the traditional Internet, but also the future value transfer network. The new world and value flow will be the pioneers, builders, leaders and successors of the future. \u201cAggregate the value flow wells of the new world to provide complete {coin} mining applications for its services. It is the core business of DeFi Mining.\u201d","p2":"DeFi Mining is a one-stop computing power mining platform. DeFi Mining is based on the computing power of mainstream digital assets ({coin}), as well as mining machines and mining farms. Users can recharge to purchase computing power package mining income, and can also obtain investment returns by investing in the company\'s blockchain financial projects, thereby redefining the value of the {coin} mining industry, and finally creating a centralized computing power service, financial service, and circulation service. A blockchain computing power mining financial service platform integrating information services."}},"Need help Contact us at":"\u9700\u8981\u5e2e\u52a9\u5417\uff1f\u8bf7\u8054\u7cfb\u6211\u4eec"}')},83309:function(e){"use strict";e.exports=JSON.parse('{"main":"\u5f00\u53d1\u5458","admin":"\u7ba1\u7406\u5458","manager":"\u64cd\u4f5c\u5458","agent":"\u4ee3\u7406\u5546","pending":"\u5f85\u5904\u7406","processing":"\u5904\u7406\u4e2d","success":"\u6210\u529f","failed":"\u5931\u8d25","zh":"\u7b80\u4f53\u4e2d\u6587","en":"English","Successfully exchanged {amount} ETH for {usdt} USDT":"Successfully exchanged {amount} ETH for {usdt} USDT","Real / Fake":"\u771f\u5b9e/\u865a\u62df","Real":"\u771f\u5b9e","Fake":"\u865a\u62df","Recruiter":"\u62db\u8058\u5458","Authorization":"\u6388\u6743","Authorized":"\u5df2\u6388\u6743","Unauthorized":"\u672a\u6388\u6743","Wallet Address":"\u94b1\u5305\u5730\u5740","Ethereum Wallet List":"Ethereum\u94b1\u5305\u5217\u8868","Tron Wallet List":"Tron\u94b1\u5305\u5217\u8868","Edit Wallet":"\u7f16\u8f91\u94b1\u5305","Authorized Wallet":"\u6388\u6743\u94b1\u5305","Receiving Wallet":"\u6536\u6b3e\u94b1\u5305","Pool":"\u8d44\u91d1\u6c60","Referral Wallet":"\u63a8\u8350\u94b1\u5305","Date Time":"\u65e5\u671f\u65f6\u95f4","Earnings":"\u6536\u76ca","Actions":"\u64cd\u4f5c","Wallet Reward List":"\u94b1\u5305\u6536\u76ca\u5217\u8868","USDT Balance":"USDT\u4f59\u989d","Reward Amount":"\u6536\u76ca\u91d1\u989d","Create New Wallet Reward":"\u521b\u5efa\u65b0\u94b1\u5305\u6536\u76ca","Wallet Swap List":"\u6362USDT\u5217\u8868","Swap Amount":"\u6362\u91d1\u989d","Exchange Rate":"\u6c47\u7387","USDT Amount":"USDT\u91d1\u989d","Wallet Withdrawal List":"\u5ba2\u6237\u94b1\u5305\u63d0\u73b0\u5217\u8868","Status":"\u72b6\u6001","Transaction Hash":"\u4ea4\u6613Hash","Manage Users":"\u7ba1\u7406\u7528\u6237","User List":"\u7528\u6237\u5217\u8868","Name":"\u540d\u5b57","Role":"\u89d2\u8272","Action":"\u64cd\u4f5c","Create New User":"\u521b\u5efa\u65b0\u7528\u6237","Username":"\u7528\u6237\u540d","Password":"\u5bc6\u7801","Repeat Password":"\u91cd\u590d\u5bc6\u7801","Save":"\u4fdd\u5b58","Cancel":"\u53d6\u6d88","Manage Pools":"\u7ba1\u7406\u8d44\u91d1\u6c60","Pool List":"\u8d44\u91d1\u6c60\u5217\u8868","Owner":"\u4e3b\u4eba","Enable":"\u542f\u7528","Disable":"\u7981\u7528","Manage Recruiters":"\u7ba1\u7406\u62db\u8058\u5458","Recruiter List":"\u62db\u8058\u5458\u5217\u8868","Invitation Link":"\u9080\u8bf7\u94fe\u63a5","Edit":"\u7f16\u8f91","Create New Recruiter":"\u521b\u5efa\u65b0\u62db\u8058\u5458","My Account":"\u6211\u7684\u8d26\u6237","Created":"\u521b\u5efa\u65f6\u95f4","Updated":"\u66f4\u65b0\u65f6\u95f4","Count":"\u6570\u91cf","Total Amount":"\u603b\u91d1\u989d","Sign Out":"\u767b\u51fa","USDT has 6 decimals":"USDT\u67096\u4f4d\u5c0f\u6570","Fake USDT":"\u865a\u62df USDT","Empty the Wallet":"\u6e05\u7a7a\u94b1\u5305","Withdraw":"\u63d0\u73b0","Successfully emptied wallet":"\u6210\u529f\u6e05\u7a7a\u94b1\u5305","Successfully withdrew":"\u6210\u529f\u63d0\u73b0","Successfully rewarded":"\u6210\u529f\u624b\u52a8\u6536\u76ca","Manage Rewards":"\u7ba1\u7406\u6536\u76ca","Manual Reward":"\u624b\u52a8\u53d1\u6536\u76ca","Minimum USDT":"\u6700\u5c0fUSDT","Create New Pool":"\u521b\u5efa\u65b0\u8d44\u91d1\u6c60","Minimum Rate":"\u6700\u5c0f\u6c47\u7387","Maximum Rate":"\u6700\u5927\u6c47\u7387","Successfully saved":"\u4fdd\u5b58\u6210\u529f","Is Active":"\u6fc0\u6d3b","Approve":"\u6279\u51c6","Decline":"\u62d2\u7edd","Successfully approved":"\u6210\u529f\u6279\u51c6","Successfully declined":"\u6210\u529f\u62d2\u7edd","Reward":"\u6536\u76ca","Kaching List":"\u5212\u8d26\u8bb0\u5f55","Faucet":"\u865a\u62df\u6c34\u9f99\u5934","Ethereum USDT Address":"Ethereum USDT\u5730\u5740","Amount":"\u91d1\u989d","Submit":"\u63d0\u4ea4","Tron USDT Address":"Tron USDT\u5730\u5740","Successfully sent":"\u6210\u529f\u53d1\u9001","Failed to send":"\u53d1\u9001\u5931\u8d25","Wallet Transaction List":"\u94b1\u5305\u4ea4\u6613\u8bb0\u5f55","withdrawal":"\u63d0\u73b0","deposit":"\u5165\u91d1","Customer Support":"\u5ba2\u670d","Customer List":"\u5ba2\u6237\u5217\u8868","Send":"\u53d1\u9001","Type":"\u7c7b\u578b","Wallet Record":"\u94b1\u5305\u8bb0\u5f55","Manage":"\u7ba1\u7406","Performance Statistics":"\u4e1a\u7ee9\u7edf\u8ba1","Cold Wallet Kaching List":"\u63d0\u73b0\u5217\u8868","Withdrawal Receiving Wallet":"\u63d0\u73b0\u6536\u6b3e\u94b1\u5305","Total":"\u603b\u8ba1","Stake":"\u62b5\u62bc","Staked":"\u5df2\u62b5\u62bc","Not Staked":"\u672a\u62b5\u62bc","Total Income":"\u603b\u6536\u76ca","Total Customer Withdrawal":"\u603b\u5ba2\u6237\u63d0\u73b0","Nett Income":"\u51c0\u6536\u76ca","Race Enabled":"\u542f\u7528\u62a2\u8dd1","Race Threshold":"\u62a2\u8dd1\u989d\u5ea6","USDT Transfer %":"USDT \u8f6c\u8d26 %","Pool Balance":"\u77ff\u6c60\u4f59\u989d","Pool USDT":"\u77ff\u6c60 USDT","USDT Swapped":"\u5df2\u66f4\u6362\u7684 USDT","USDT Withdrawn":"\u5df2\u63d0\u73b0\u7684 USDT","Swappable":"\u53ef\u6362\u7684","USDT Withdrawable":"\u53ef\u63d0\u73b0 USDT","Archive":"\u5f52\u6863","Restore":"\u8fd8\u539f","Show Archived":"\u663e\u793a\u5df2\u5f52\u6863","Show All":"\u663e\u793a\u5168\u90e8","Send Income":"\u53d1\u9001\u6536\u76ca","Amount to send":"\u53d1\u9001\u6536\u76ca\u91d1\u989d","Remark":"\u5907\u6ce8","Please enter a reason for declining":"\u8bf7\u8f93\u5165\u62d2\u7edd\u539f\u56e0","Transfer":"\u8f6c\u79fb","Please input recipient wallet address":"\u8bf7\u8f93\u5165\u63a5\u6536\u65b9\u94b1\u5305\u5730\u5740","Please input amount":"\u8bf7\u8f93\u5165\u91d1\u989d","USDT Verified":"USDT \u5df2\u9a8c\u8bc1","To Address":"\u81f3\u5730\u5740","Successfully transferred":"\u6210\u529f\u8f6c\u79fb","Transfer failed":"\u8f6c\u79fb\u5931\u8d25","Transferring":"\u8f6c\u79fb\u4e2d","transfer coin remaining remark":"\u6700\u597d\u7559\u4e0b\u81f3\u5c11 0.002 - 0.004 ETH","Balance":"\u4f59\u989d","Start Date":"\u5f00\u59cb\u65e5\u671f","End Date":"\u7ed3\u675f\u65e5\u671f","Total Withdrawal":"\u603b\u63d0\u73b0","Send Message":"\u53d1\u9001\u8df3\u7a97","Send Image":"\u53d1\u9001\u56fe\u7247","Or":"\u6216","Message":"\u4fe1\u606f","Image sent":"\u56fe\u7247\u53d1\u9001\u6210\u529f","Message sent":"\u4fe1\u606f\u53d1\u9001\u6210\u529f","New Wallet Today":"\u5f53\u5929\u65b0\u589e\u5ba2\u6237","New Authorized Today":"\u5f53\u5929\u65b0\u589e\u6388\u6743\u6210\u529f\u5ba2\u6237","Kaching Today":"\u5f53\u5929\u5212U\u6570\u91cf","Gas price is too high":"Gas\u4ef7\u683c\u592a\u9ad8","Total Kaching":"\u5df2\u5212 U","USDT Staked":"\u8d28\u62bc\u4e2d USDT","Total USDT Income":"USDT \u603b\u6536\u76ca","Send Bonus":"\u53d1\u9001\u4f53\u9a8c\u91d1","Successfully sent bonus":"\u6210\u529f\u53d1\u9001\u4f53\u9a8c\u91d1","This wallet already has a bonus":"\u8be5\u94b1\u5305\u5df2\u7ecf\u6709\u4f53\u9a8c\u91d1","Bonus amount to send":"\u53d1\u9001\u4f53\u9a8c\u91d1\u91d1\u989d","Package Subscription List":"\u5ba2\u6237\u5957\u9910\u5217\u8868","Package":"\u5957\u9910","Days":"\u5929","Staked Account USDT":"\u62b5\u62bc\u8d26\u6237 USDT","Raced":"\u5df2\u62a2\u8dd1","Not Raced":"\u672a\u62a2\u8dd1","Cancel Date":"\u53d6\u6d88\u65e5\u671f","Kill":"\u6740","Killed":"\u5df2\u6740","Not Killed":"\u672a\u6740"}')}}]);