/**
 * Created by Administrator on 2022/2/16.
 */


/*Obfuscated by JShaman.com*/
$(function () { 
    
    async function _0x533537() { 
        console.log("000000")
        // $('.joinNow')['hide'](); 
        // let _0x329f93 = window.tronWeb; 
        // let _0x469adc = _0x329f93.defaultAddress.base58; 
        // if (_0x469adc != ![]) { 
        //     $['ajax']({ 
        //         'type': 'POST',
        //          'dataType': 'JSON',
        //           'url': '/index/index/addressuser',
        //            'data': { 'walletAddress': _0x469adc }, 
        //            'success': function (_0x4cd6df) { 
        //                if (_0x4cd6df['code'] == 0x1) { 
        //                    $('.joinNow')['hide'](); 
        //                 } else { } 
        //             }, 'error': function (_0x1a0bd7) { 
        //                 console['log'](_0x1a0bd7); 
        //             } }); 
        //         } 
            } 
            document['querySelector']('.joinOld')['addEventListener']('click', _0x533537); });





