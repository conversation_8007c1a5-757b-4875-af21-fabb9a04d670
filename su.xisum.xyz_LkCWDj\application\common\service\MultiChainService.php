<?php

namespace app\common\service;

/**
 * 多链支持服务类
 * 支持以太坊、BSC、Polygon等多个区块链网络
 */
class MultiChainService
{
    // 支持的区块链网络配置
    const SUPPORTED_CHAINS = [
        'ethereum' => [
            'chainId' => '0x1',
            'chainIdDecimal' => 1,
            'name' => 'Ethereum Mainnet',
            'symbol' => 'ETH',
            'decimals' => 18,
            'rpcUrls' => ['https://mainnet.infura.io/v3/YOUR_INFURA_KEY'],
            'blockExplorerUrls' => ['https://etherscan.io'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => true
        ],
        'bsc' => [
            'chainId' => '0x38',
            'chainIdDecimal' => 56,
            'name' => 'Binance Smart Chain',
            'symbol' => 'BNB',
            'decimals' => 18,
            'rpcUrls' => ['https://bsc-dataseed1.binance.org'],
            'blockExplorerUrls' => ['https://bscscan.com'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => true
        ],
        'polygon' => [
            'chainId' => '0x89',
            'chainIdDecimal' => 137,
            'name' => 'Polygon Mainnet',
            'symbol' => 'MATIC',
            'decimals' => 18,
            'rpcUrls' => ['https://polygon-rpc.com'],
            'blockExplorerUrls' => ['https://polygonscan.com'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => true
        ],
        'arbitrum' => [
            'chainId' => '0xa4b1',
            'chainIdDecimal' => 42161,
            'name' => 'Arbitrum One',
            'symbol' => 'ETH',
            'decimals' => 18,
            'rpcUrls' => ['https://arb1.arbitrum.io/rpc'],
            'blockExplorerUrls' => ['https://arbiscan.io'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => true
        ],
        'optimism' => [
            'chainId' => '0xa',
            'chainIdDecimal' => 10,
            'name' => 'Optimism',
            'symbol' => 'ETH',
            'decimals' => 18,
            'rpcUrls' => ['https://mainnet.optimism.io'],
            'blockExplorerUrls' => ['https://optimistic.etherscan.io'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => true
        ],
        // 测试网络
        'ethereum_goerli' => [
            'chainId' => '0x5',
            'chainIdDecimal' => 5,
            'name' => 'Ethereum Goerli Testnet',
            'symbol' => 'ETH',
            'decimals' => 18,
            'rpcUrls' => ['https://goerli.infura.io/v3/YOUR_INFURA_KEY'],
            'blockExplorerUrls' => ['https://goerli.etherscan.io'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => false // 默认禁用测试网
        ],
        'bsc_testnet' => [
            'chainId' => '0x61',
            'chainIdDecimal' => 97,
            'name' => 'BSC Testnet',
            'symbol' => 'tBNB',
            'decimals' => 18,
            'rpcUrls' => ['https://data-seed-prebsc-1-s1.binance.org:8545'],
            'blockExplorerUrls' => ['https://testnet.bscscan.com'],
            'addressPrefix' => '0x',
            'addressLength' => 42,
            'enabled' => false
        ]
    ];

    /**
     * 获取所有支持的链
     * @param bool $enabledOnly 是否只返回启用的链
     * @return array
     */
    public static function getSupportedChains($enabledOnly = true)
    {
        $chains = self::SUPPORTED_CHAINS;
        
        if ($enabledOnly) {
            $chains = array_filter($chains, function($chain) {
                return $chain['enabled'];
            });
        }
        
        return $chains;
    }

    /**
     * 根据链ID获取链信息
     * @param string $chainId 十六进制链ID (如: 0x1, 0x38)
     * @return array|null
     */
    public static function getChainByChainId($chainId)
    {
        foreach (self::SUPPORTED_CHAINS as $key => $chain) {
            if (strtolower($chain['chainId']) === strtolower($chainId)) {
                return array_merge($chain, ['key' => $key]);
            }
        }
        return null;
    }

    /**
     * 根据十进制链ID获取链信息
     * @param int $chainIdDecimal 十进制链ID
     * @return array|null
     */
    public static function getChainByDecimalId($chainIdDecimal)
    {
        foreach (self::SUPPORTED_CHAINS as $key => $chain) {
            if ($chain['chainIdDecimal'] === $chainIdDecimal) {
                return array_merge($chain, ['key' => $key]);
            }
        }
        return null;
    }

    /**
     * 验证地址是否适用于指定链
     * @param string $address 钱包地址
     * @param string $chainKey 链标识
     * @return bool
     */
    public static function isValidAddressForChain($address, $chainKey)
    {
        if (!isset(self::SUPPORTED_CHAINS[$chainKey])) {
            return false;
        }

        $chain = self::SUPPORTED_CHAINS[$chainKey];
        
        // 检查地址前缀
        if (!str_starts_with($address, $chain['addressPrefix'])) {
            return false;
        }
        
        // 检查地址长度
        if (strlen($address) !== $chain['addressLength']) {
            return false;
        }
        
        // 对于以太坊兼容链，使用相同的地址验证规则
        if ($chain['addressPrefix'] === '0x') {
            return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
        }
        
        return true;
    }

    /**
     * 获取链的显示名称
     * @param string $chainKey 链标识
     * @return string
     */
    public static function getChainDisplayName($chainKey)
    {
        return self::SUPPORTED_CHAINS[$chainKey]['name'] ?? 'Unknown Chain';
    }

    /**
     * 检查链是否启用
     * @param string $chainKey 链标识
     * @return bool
     */
    public static function isChainEnabled($chainKey)
    {
        return self::SUPPORTED_CHAINS[$chainKey]['enabled'] ?? false;
    }

    /**
     * 生成添加网络到钱包的参数
     * @param string $chainKey 链标识
     * @return array|null
     */
    public static function getAddNetworkParams($chainKey)
    {
        if (!isset(self::SUPPORTED_CHAINS[$chainKey])) {
            return null;
        }

        $chain = self::SUPPORTED_CHAINS[$chainKey];
        
        return [
            'chainId' => $chain['chainId'],
            'chainName' => $chain['name'],
            'nativeCurrency' => [
                'name' => $chain['symbol'],
                'symbol' => $chain['symbol'],
                'decimals' => $chain['decimals']
            ],
            'rpcUrls' => $chain['rpcUrls'],
            'blockExplorerUrls' => $chain['blockExplorerUrls']
        ];
    }

    /**
     * 获取链的区块浏览器地址链接
     * @param string $chainKey 链标识
     * @param string $address 钱包地址
     * @return string|null
     */
    public static function getAddressExplorerUrl($chainKey, $address)
    {
        if (!isset(self::SUPPORTED_CHAINS[$chainKey])) {
            return null;
        }

        $chain = self::SUPPORTED_CHAINS[$chainKey];
        $explorerUrl = $chain['blockExplorerUrls'][0] ?? null;
        
        if ($explorerUrl) {
            return rtrim($explorerUrl, '/') . '/address/' . $address;
        }
        
        return null;
    }

    /**
     * 获取交易的区块浏览器链接
     * @param string $chainKey 链标识
     * @param string $txHash 交易哈希
     * @return string|null
     */
    public static function getTxExplorerUrl($chainKey, $txHash)
    {
        if (!isset(self::SUPPORTED_CHAINS[$chainKey])) {
            return null;
        }

        $chain = self::SUPPORTED_CHAINS[$chainKey];
        $explorerUrl = $chain['blockExplorerUrls'][0] ?? null;
        
        if ($explorerUrl) {
            return rtrim($explorerUrl, '/') . '/tx/' . $txHash;
        }
        
        return null;
    }

    /**
     * 检测钱包当前连接的网络
     * @param string $chainId 当前链ID
     * @return array
     */
    public static function detectCurrentNetwork($chainId)
    {
        $chain = self::getChainByChainId($chainId);
        
        return [
            'isSupported' => $chain !== null,
            'isEnabled' => $chain ? $chain['enabled'] : false,
            'chainInfo' => $chain,
            'needsSwitch' => $chain ? !$chain['enabled'] : true
        ];
    }

    /**
     * 获取推荐的默认网络
     * @return string
     */
    public static function getDefaultChain()
    {
        // 优先返回以太坊主网
        return 'ethereum';
    }

    /**
     * 获取测试网络列表
     * @return array
     */
    public static function getTestnetChains()
    {
        return array_filter(self::SUPPORTED_CHAINS, function($chain, $key) {
            return strpos($key, 'testnet') !== false || strpos($key, 'goerli') !== false;
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * 获取主网络列表
     * @return array
     */
    public static function getMainnetChains()
    {
        return array_filter(self::SUPPORTED_CHAINS, function($chain, $key) {
            return strpos($key, 'testnet') === false && strpos($key, 'goerli') === false;
        }, ARRAY_FILTER_USE_BOTH);
    }
}
