﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta data-react-helmet="true" name="description" content="Find an Ethereum application to try.">
  <meta name="theme-color" content="#1c1ce1">
  <meta name="generator" content="Gatsby 4.7.1">
      <script>
  　window.onload = function(){
  　    console.log('页面加载完2成');
 　　const t = document.querySelectorAll("img[data-main-image]");
        for (let e of t) {
            e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"));
            const t = e.parentNode.querySelectorAll("source[data-srcset]");
            for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset");
            e.complete && (e.style.opacity = 1)
        }
　　}
      
  </script>
  <style data-href="/styles.92bedc857ac51bb6cf96.css" data-identity="gatsby-global-css">
    html {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 1rem
    }

    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    main,
    menu,
    nav,
    section,
    summary {
      display: block
    }

    audio,
    canvas,
    progress,
    video {
      display: inline-block
    }

    audio:not([controls]) {
      display: none;
      height: 0
    }

    progress {
      vertical-align: baseline
    }

    [hidden],
    template {
      display: none
    }

    a {
      -webkit-text-decoration-skip: objects;
      background-color: transparent;
      text-decoration: none
    }

    a:active,
    a:hover {
      outline-width: 0
    }

    abbr[title] {
      border-bottom: none;
      text-decoration: underline;
      -webkit-text-decoration: underline dotted;
      text-decoration: underline dotted
    }

    b,
    strong {
      font-weight: inherit;
      font-weight: bolder
    }

    dfn {
      font-style: italic
    }

    h1 {
      font-size: 2em;
      margin: .67em 0
    }

    mark {
      background-color: #ff0;
      color: #000
    }

    small {
      font-size: 80%
    }

    sub,
    sup {
      font-size: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline
    }

    sub {
      bottom: -.25em
    }

    sup {
      top: -.5em
    }

    img {
      border-style: none
    }

    svg:not(:root) {
      overflow: hidden
    }

    code,
    kbd,
    pre,
    samp {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: 1em
    }

    figure {
      margin: 1em 40px
    }

    hr {
      box-sizing: content-box;
      height: 0;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font: inherit;
      margin: 0
    }

    optgroup {
      font-weight: 700
    }

    button,
    input {
      overflow: visible
    }

    button,
    select {
      text-transform: none
    }

    [type=reset],
    [type=submit],
    button,
    html [type=button] {
      -webkit-appearance: button
    }

    [type=button]::-moz-focus-inner,
    [type=reset]::-moz-focus-inner,
    [type=submit]::-moz-focus-inner,
    button::-moz-focus-inner {
      border-style: none;
      padding: 0
    }

    [type=button]:-moz-focusring,
    [type=reset]:-moz-focusring,
    [type=submit]:-moz-focusring,
    button:-moz-focusring {
      outline: 1px dotted ButtonText
    }

    fieldset {
      border: 1px solid silver;
      margin: 0 2px;
      padding: .35em .625em .75em
    }

    legend {
      box-sizing: border-box;
      color: inherit;
      display: table;
      max-width: 100%;
      padding: 0;
      white-space: normal
    }

    textarea {
      overflow: auto
    }

    [type=checkbox],
    [type=radio] {
      box-sizing: border-box;
      padding: 0
    }

    [type=number]::-webkit-inner-spin-button,
    [type=number]::-webkit-outer-spin-button {
      height: auto
    }

    [type=search] {
      -webkit-appearance: textfield;
      outline-offset: -2px
    }

    [type=search]::-webkit-search-cancel-button,
    [type=search]::-webkit-search-decoration {
      -webkit-appearance: none
    }

    ::-webkit-input-placeholder {
      color: inherit;
      opacity: .54
    }

    ::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit
    }

    html {
      box-sizing: border-box;
      font: 100%/1.6em georgia, serif;
      overflow-y: scroll
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      word-wrap: break-word;
      -ms-font-feature-settings: "kern", "liga", "clig", "calt";
      -webkit-font-feature-settings: "kern", "liga", "clig", "calt";
      font-feature-settings: "kern", "liga", "clig", "calt";
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-kerning: normal;
      font-weight: 400
    }

    img {
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      max-width: 100%;
      padding: 0
    }

    h1 {
      font-size: 2.25rem;
      font-weight: 500
    }

    h1,
    h2 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 0 0 1.45rem;
      padding: 0
    }

    h2 {
      font-size: 1.62671rem;
      font-weight: 700
    }

    h3 {
      font-size: 1.38316rem;
      font-weight: 500
    }

    h3,
    h4 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 2rem 0 1.45rem;
      padding: 0
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600
    }

    h5 {
      font-size: 1rem;
      margin: 2rem 0 1.45rem
    }

    h5,
    h6 {
      text-rendering: optimizeLegibility;
      color: inherit;
      font-weight: 500;
      line-height: 1.1;
      padding: 0
    }

    h6 {
      font-size: .85028rem
    }

    h6,
    hgroup {
      margin: 0 0 1.45rem
    }

    hgroup {
      padding: 0
    }

    ol,
    ul {
      list-style-image: none;
      list-style-position: outside;
      margin: 0 0 1.45rem 1.45rem;
      padding: 0
    }

    dd,
    dl,
    figure,
    p {
      margin: 0 0 1.45rem;
      padding: 0
    }

    pre {
      word-wrap: normal;
      background: rgba(0, 0, 0, .04);
      border-radius: 3px;
      font-size: .85rem;
      line-height: 1.42;
      margin: 0 0 1.45rem;
      overflow: auto;
      padding: 1.45rem;
      white-space: pre-wrap
    }

    table {
      border-collapse: collapse;
      font-size: 1rem;
      line-height: 1.45rem;
      width: 100%
    }

    fieldset,
    table {
      margin: 0 0 1.45rem;
      padding: 0
    }

    blockquote {
      margin: 0 1.45rem 1.45rem;
      padding: 0
    }

    form,
    iframe,
    noscript {
      margin: 0 0 1.45rem;
      padding: 0
    }

    hr {
      background: rgba(0, 0, 0, .2);
      border: none;
      height: 1px;
      margin: 4rem 0 0;
      padding: 0
    }

    address {
      margin: 0 0 1.45rem;
      padding: 0
    }

    b,
    dt,
    strong,
    th {
      font-weight: 700
    }

    li {
      margin-bottom: .725rem
    }

    ol li,
    ul li {
      padding-left: 0
    }

    li>ol,
    li>ul {
      margin-bottom: .725rem;
      margin-left: 1.45rem;
      margin-top: .725rem
    }

    blockquote :last-child,
    li :last-child,
    p :last-child {
      margin-bottom: 0
    }

    li>p {
      margin-bottom: .725rem
    }

    code {
      font-size: 1em;
      line-height: 1.45em
    }

    kbd {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: .625rem;
      line-height: 1.56rem
    }

    samp {
      font-size: .85rem;
      line-height: 1.45rem
    }

    abbr,
    abbr[title],
    acronym {
      border-bottom: 1px dotted rgba(0, 0, 0, .5);
      cursor: help
    }

    abbr[title] {
      text-decoration: none
    }

    td,
    th,
    thead {
      text-align: left
    }

    td,
    th {
      font-feature-settings: "tnum";
      -moz-font-feature-settings: "tnum";
      -ms-font-feature-settings: "tnum";
      -webkit-font-feature-settings: "tnum";
      border-bottom: 1px solid hsla(0, 13%, 72%, .12);
      padding: .725rem .96667rem calc(.725rem - 1px)
    }

    td:first-child,
    th:first-child {
      padding-left: 0
    }

    td:last-child,
    th:last-child {
      padding-right: 0
    }

    tt {
      background-color: #2b2834;
      border-radius: 2px;
      color: #968af6
    }

    code,
    tt {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      padding: .2em
    }

    code {
      background-color: rgba(0, 0, 0, .04);
      border-radius: 3px
    }

    pre code {
      background: none;
      line-height: 1.42
    }

    code:before,
    pre code:after,
    pre code:before,
    pre tt:after,
    pre tt:before,
    tt:after,
    tt:before {
      content: ""
    }

    @media only screen and (max-width:480px) {
      html {
        font-size: 100%
      }
    }

    .assets-page .gatsby-resp-image-wrapper {
      max-height: 200px !important
    }

    .assets-page .gatsby-resp-image-image {
      width: auto !important
    }
  </style>
  <link rel="icon" href="/favicon-32x32.png?v=8b512faa8d4a0b019c123a771b6622aa" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="anonymous">
  <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48x48.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256x256.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="preconnect" href="https://matomo.ethstake.exchange">
  <link rel="sitemap" type="application/xml" href="/sitemap/sitemap-index.xml">
  <style type="text/css">
    .anchor.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }

    .anchor.after {
      display: inline-block;
      padding-left: 4px;
    }

    h1 .anchor svg,
    h2 .anchor svg,
    h3 .anchor svg,
    h4 .anchor svg,
    h5 .anchor svg,
    h6 .anchor svg {
      visibility: hidden;
    }

    h1:hover .anchor svg,
    h2:hover .anchor svg,
    h3:hover .anchor svg,
    h4:hover .anchor svg,
    h5:hover .anchor svg,
    h6:hover .anchor svg,
    h1 .anchor:focus svg,
    h2 .anchor:focus svg,
    h3 .anchor:focus svg,
    h4 .anchor:focus svg,
    h5 .anchor:focus svg,
    h6 .anchor:focus svg {
      visibility: visible;
    }
  </style>
  <script>
    document.addEventListener("DOMContentLoaded", function (event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
          var clientTop = document.documentElement.clientTop || document.body.clientTop || 0
          var offset = element.getBoundingClientRect().top + scrollTop - clientTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function () {
            window.scrollTo(0, offset - 0)
          }), 0)
        }
      }
    })
  </script>
  <title data-react-helmet="true">Decentralized applications (dapps) | ethstake.exchange</title>
  <link data-react-helmet="true" rel="canonical" href="index.html">
  <script data-react-helmet="true" type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": "https://ethstake.exchange",
          "email": "<EMAIL>",
          "name": "Ethereum",
          "logo": "https://ethstake.exchange/og-image.png"
        }
      </script>
  <style>
    .gatsby-image-wrapper {
      position: relative;
      overflow: hidden
    }

    .gatsby-image-wrapper picture.object-fit-polyfill {
      position: static !important
    }

    .gatsby-image-wrapper img {
      bottom: 0;
      height: 100%;
      left: 0;
      margin: 0;
      max-width: none;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      object-fit: cover
    }

    .gatsby-image-wrapper [data-main-image] {
      opacity: 0;
      transform: translateZ(0);
      transition: opacity .25s linear;
      will-change: opacity
    }

    .gatsby-image-wrapper-constrained {
      display: inline-block;
      vertical-align: top
    }
  </style><noscript>
    <style>
      .gatsby-image-wrapper noscript [data-main-image] {
        opacity: 1 !important
      }

      .gatsby-image-wrapper [data-placeholder-image] {
        opacity: 0 !important
      }
    </style>
  </noscript>
  <script
    type="module">const e = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; e && document.body.addEventListener("load", (function (e) { if (void 0 === e.target.dataset.mainImage) return; if (void 0 === e.target.dataset.gatsbyImageSsr) return; const t = e.target; let a = null, n = t; for (; null === a && n;)void 0 !== n.parentNode.dataset.gatsbyImageWrapper && (a = n.parentNode), n = n.parentNode; const o = a.querySelector("[data-placeholder-image]"), r = new Image; r.src = t.currentSrc, r.decode().catch((() => { })).then((() => { t.style.opacity = 1, o && (o.style.opacity = 0, o.style.transition = "opacity 500ms linear") })) }), !0);</script>
  <style data-styled="" data-styled-version="5.3.3">
    body {
      background-color: #ffffff;
      color: #333333;
    }

    /*!sc*/
    a {
      color: #1c1cff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    mark {
      background: rgba(143, 187, 237, .1);
      box-shadow: inset 0 -2px 0 0 rgba(69, 142, 225, .8);
    }

    /*!sc*/
    .anchor.before {
      fill: #333333;
    }

    /*!sc*/
    hr {
      background: #ecececnull#1c1cff;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
      position: absolute;
    }

    /*!sc*/
    iframe {
      display: block;
      max-width: 560px;
      margin: 32px 0;
    }

    /*!sc*/
    h1 {
      font-size: 3rem;
      line-height: 1.4;
      margin: 2rem 0;
      font-weight: 700;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h1 {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    h2 {
      font-size: 2rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 3rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h2 {
        font-size: 1.5rem;
      }
    }

    /*!sc*/
    h3 {
      font-size: 1.5rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 2.5rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h3 {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    h4 {
      font-size: 1.25rem;
      line-height: 1.4;
      font-weight: 500;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h4 {
        font-size: 1rem;
      }
    }

    /*!sc*/
    h5 {
      font-size: 1rem;
      line-height: 1.4;
      font-weight: 450;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    h6 {
      font-size: 0.9rem;
      line-height: 1.4;
      font-weight: 400;
      text-transform: uppercase;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    data-styled.g1[id="sc-global-hcwgEG1"] {
      content: "sc-global-hcwgEG1,"
    }

    /*!sc*/
    .iylOGp {
      fill: #b2b2b2;
    }

    /*!sc*/
    .iylOGp:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .hgJqYx {
      fill: #333333;
    }

    /*!sc*/
    .hgJqYx:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g2[id="Icon__StyledIcon-sc-1o8zi5s-0"] {
      content: "iylOGp,hgJqYx,"
    }

    /*!sc*/
    .gABYms:after {
      margin-left: 0.125em;
      margin-right: 0.3em;
      display: inline;
      content: "↗";
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out;
      font-style: normal;
    }

    /*!sc*/
    .gABYms:hover:after {
      -webkit-transform: translate(0.15em, -0.2em);
      -ms-transform: translate(0.15em, -0.2em);
      transform: translate(0.15em, -0.2em);
    }

    /*!sc*/
    data-styled.g3[id="Link__ExternalLink-sc-e3riao-0"] {
      content: "gABYms,"
    }

    /*!sc*/
    .gCWUlE .is-glossary {
      white-space: nowrap;
    }

    /*!sc*/
    .gCWUlE.active {
      color: #1c1cff;
    }

    /*!sc*/
    .gCWUlE:hover svg {
      fill: #1c1cff;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g4[id="Link__InternalLink-sc-e3riao-1"] {
      content: "gCWUlE,"
    }

    /*!sc*/
    .jfMIWk {
      margin: 0 0.25rem 0 0.35rem;
      fill: #4949ff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    .jfMIWk:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g6[id="Link__GlossaryIcon-sc-e3riao-3"] {
      content: "jfMIWk,"
    }

    /*!sc*/
    .gvoBKJ {
      padding-top: 3rem;
      padding-bottom: 4rem;
      padding: 1rem 2rem;
    }

    /*!sc*/
    data-styled.g7[id="Footer__StyledFooter-sc-1to993d-0"] {
      content: "gvoBKJ,"
    }

    /*!sc*/
    .kFKfdz {
      font-size: 0.875rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g8[id="Footer__FooterTop-sc-1to993d-1"] {
      content: "kFKfdz,"
    }

    /*!sc*/
    .bWGwos {
      color: #666666;
    }

    /*!sc*/
    data-styled.g9[id="Footer__LastUpdated-sc-1to993d-2"] {
      content: "bWGwos,"
    }

    /*!sc*/
    .hlbLsM {
      display: grid;
      grid-template-columns: repeat(6, auto);
      grid-gap: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1300px) {
      .hlbLsM {
        grid-template-columns: repeat(3, auto);
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .hlbLsM {
        grid-template-columns: repeat(2, auto);
      }
    }

    /*!sc*/
    @media (max-width:500px) {
      .hlbLsM {
        grid-template-columns: auto;
      }
    }

    /*!sc*/
    data-styled.g10[id="Footer__LinkGrid-sc-1to993d-3"] {
      content: "hlbLsM,"
    }

    /*!sc*/
    .bbCEKr {
      font-size: 0.875rem;
      line-height: 1.6;
      margin: 1.14em 0;
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g12[id="Footer__SectionHeader-sc-1to993d-5"] {
      content: "bbCEKr,"
    }

    /*!sc*/
    .gjQPMc {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      margin: 0;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g13[id="Footer__List-sc-1to993d-6"] {
      content: "gjQPMc,"
    }

    /*!sc*/
    .eGhJJx {
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g14[id="Footer__ListItem-sc-1to993d-7"] {
      content: "eGhJJx,"
    }

    /*!sc*/
    .gIpSoz {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #666666;
    }

    /*!sc*/
    .gIpSoz svg {
      fill: #666666;
    }

    /*!sc*/
    .gIpSoz:after {
      color: #666666;
    }

    /*!sc*/
    .gIpSoz:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover:after {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g15[id="Footer__FooterLink-sc-1to993d-8"] {
      content: "gIpSoz,"
    }

    /*!sc*/
    .kdLbod {
      margin: 1rem 0;
    }

    /*!sc*/
    data-styled.g16[id="Footer__SocialIcons-sc-1to993d-9"] {
      content: "kdLbod,"
    }

    /*!sc*/
    .iedzfy {
      margin-left: 1rem;
      width: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iedzfy {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g17[id="Footer__SocialIcon-sc-1to993d-10"] {
      content: "iedzfy,"
    }

    /*!sc*/
    .drElXa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-right: 1.5rem;
    }

    /*!sc*/
    .drElXa:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g20[id="Dropdown__DropdownTitle-sc-1yd08gi-1"] {
      content: "drElXa,"
    }

    /*!sc*/
    .ldsPWM {
      margin: 0;
      position: absolute;
      margin-top: -1rem;
      list-style-type: none;
      list-style-image: none;
      top: 100%;
      width: auto;
      border-radius: 0.5em;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;

      opacity:0;
      display:none;
      transform:rotateX(-15deg) translateZ(0)
    }

    /*!sc*/
    data-styled.g21[id="Dropdown__DropdownList-sc-1yd08gi-2"] {
      content: "ldsPWM,"
    }

    /*!sc*/
    .Mkofa {
      white-space: nowrap;
      margin: 0;
      color: #333333;
    }

    /*!sc*/
     .Mkofa:hover >ul{
      color: #1c1cff;
      opacity:1;
      display:block;
      transform: none;
    }

    /*!sc*/
    data-styled.g23[id="Dropdown__NavListItem-sc-1yd08gi-4"] {
      content: "Mkofa,"
    }

    /*!sc*/
    .lgeotR {
      margin: 0;
      color: #333333;
    }

    /*!sc*/
    .lgeotR:hover {
      color: #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g24[id="Dropdown__DropdownItem-sc-1yd08gi-5"] {
      content: "lgeotR,"
    }

    /*!sc*/
    .cTcxIB {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: block;
      padding: 0.5rem;
      color: #333333;
    }

    /*!sc*/
    .cTcxIB svg {
      fill: #666666;
    }

    /*!sc*/
    .cTcxIB:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cTcxIB:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g25[id="Dropdown__NavLink-sc-1yd08gi-6"] {
      content: "cTcxIB,"
    }

    /*!sc*/
    .ivCgwn {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .ivCgwn>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .hLjau {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .hLjau>img {
      width: 3em !important;
      height: 3em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .RDZme {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .RDZme>img {
      width: 1em !important;
      height: 1em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .gGhjlt {
      display: inline-block;
      margin-right: 1rem;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .gGhjlt>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .brrTTr {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .brrTTr>img {
      width: 2remem !important;
      height: 2remem !important;
      margin: 0 !important;
    }

    /*!sc*/
    .ihnOjq {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .ihnOjq>img {
      width: 1remem !important;
      height: 1remem !important;
      margin: 0 !important;
    }

    /*!sc*/
    .LbqQu {
      display: inline-block;
      margin-bottom: 2rem;
      margin-top: 0;
      margin-right: 0;
      margin-left: 0;
    }

    /*!sc*/
    .LbqQu>img {
      width: 6em !important;
      height: 6em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .fwugWe {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .fwugWe>img {
      width: 6em !important;
      height: 6em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .eHNQwh {
      display: inline-block;
      margin-right: 1rem;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .eHNQwh>img {
      width: 1em !important;
      height: 1em !important;
      margin: 0 !important;
    }

    /*!sc*/
    data-styled.g27[id="Emoji__StyledEmoji-sc-ihpuqw-0"] {
      content: "ivCgwn,hLjau,RDZme,gGhjlt,brrTTr,ihnOjq,LbqQu,fwugWe,eHNQwh,"
    }

    /*!sc*/
    .dUatah {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: none;
      border: none;
      color: inherit;
      display: inline-block;
      font: inherit;
      padding: initial;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g28[id="NakedButton-sc-1g43w8v-0"] {
      content: "dUatah,"
    }

    /*!sc*/
    .eoyKpR {
      margin: 0;
      position: relative;
      border-radius: 0.25em;
    }

    /*!sc*/
    data-styled.g29[id="Input__Form-sc-1utkal6-0"] {
      content: "eoyKpR,"
    }

    /*!sc*/
    .kkfPkW {
      border: 1px solid #7f7f7f;
      color: #333333;
      background: #ffffff;
      padding: 0.5rem;
      padding-right: 2rem;
      border-radius: 0.25em;
      width: 100%;
    }

    /*!sc*/
    .kkfPkW:focus {
      outline: #1c1cff auto 1px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .kkfPkW {
        padding-left: 2rem;
      }
    }

    /*!sc*/
    data-styled.g30[id="Input__StyledInput-sc-1utkal6-1"] {
      content: "kkfPkW,"
    }

    /*!sc*/
    .gFzMVg {
      position: absolute;
      right: 6px;
      top: 50%;
      margin-top: -12px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .gFzMVg {
        left: 6px;
      }
    }

    /*!sc*/
    data-styled.g31[id="Input__SearchIcon-sc-1utkal6-2"] {
      content: "gFzMVg,"
    }

    /*!sc*/
    .ggVPUc {
      border: 1px solid #7f7f7f;
      border-radius: 0.25em;
      color: #333333;
      display: none;
      margin-bottom: 0;
      padding: 0 6px;
      position: absolute;
      right: 6px;
      top: 20%;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .ggVPUc {
        display: inline-block;
      }
    }

    /*!sc*/
    data-styled.g32[id="Input__SearchSlash-sc-1utkal6-3"] {
      content: "ggVPUc,"
    }

    /*!sc*/
    .kNenpg {
      position: relative;
      display: grid;
      grid-gap: 1em;
    }

    /*!sc*/
    data-styled.g33[id="Search__Root-sc-1qm8xwy-0"] {
      content: "kNenpg,"
    }

    /*!sc*/
    .eJIgkk {
      display: none;
      max-height: 80vh;
      overflow: scroll;
      z-index: 2;
      position: absolute;
      right: 0;
      top: calc(100% + 0.5em);
      width: 80vw;
      max-width: 30em;
      box-shadow: 0 0 5px 0;
      padding: 0.5rem;
      background: #ffffff;
      border-radius: 0.25em;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eJIgkk {
        width: 100%;
      }
    }

    /*!sc*/
    .eJIgkk>*+* {
      padding-top: 1em !important;
      border-top: 2px solid black;
    }

    /*!sc*/
    .eJIgkk li {
      margin-bottom: 0.4rem;
    }

    /*!sc*/
    .eJIgkk li+li {
      padding-top: 0.7em;
      border-top: 1px solid #ececec;
    }

    /*!sc*/
    .eJIgkk ul {
      margin: 0;
      list-style: none;
    }

    /*!sc*/
    .eJIgkk mark {
      color: #1c1cff;
      box-shadow: inset 0 -2px 0 0 rgba(143, 187, 237, .5);
    }

    /*!sc*/
    .eJIgkk header {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk header h3 {
      color: #ffffff;
      background: #4c4c4c;
      padding: 0.1em 0.4em;
      border-radius: 0.25em;
    }

    /*!sc*/
    .eJIgkk h3 {
      margin: 0 0 0.5em;
    }

    /*!sc*/
    .eJIgkk h4 {
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk a {
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    data-styled.g34[id="Search__HitsWrapper-sc-1qm8xwy-1"] {
      content: "eJIgkk,"
    }

    /*!sc*/
    .fOWxWQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      background: #f7f7f7;
      border-radius: 2px;
      border: 1px solid #ececec;
      padding: 1.5rem;
    }

    /*!sc*/
    data-styled.g38[id="Card__StyledCard-sc-1x2vwsh-0"] {
      content: "fOWxWQ,"
    }

    /*!sc*/
    .dlQPfD {
      opacity: 0.8;
    }

    /*!sc*/
    data-styled.g39[id="Card__Description-sc-1x2vwsh-1"] {
      content: "dlQPfD,"
    }

    /*!sc*/
    .kCvjty {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    /*!sc*/
    data-styled.g41[id="SharedStyledComponents__Page-sc-1cr9zfr-0"] {
      content: "kCvjty,"
    }

    /*!sc*/
    .euABDx {
      margin-bottom: 4rem;
      margin-top: 4rem;
      width: 10%;
      height: 0.25rem;
      background-color: #a4a4f3;
    }

    /*!sc*/
    data-styled.g42[id="SharedStyledComponents__Divider-sc-1cr9zfr-1"] {
      content: "euABDx,"
    }

    /*!sc*/
    .lerdbF {
      margin-bottom: 4rem;
      margin-top: 4rem;
      height: 0.25rem;
      width: 10%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #a4a4f3;
    }

    /*!sc*/
    data-styled.g43[id="SharedStyledComponents__CenterDivider-sc-1cr9zfr-2"] {
      content: "lerdbF,"
    }

    /*!sc*/
    .dedPKg {
      padding: 1rem 2rem;
      width: 100%;
    }

    /*!sc*/
    data-styled.g44[id="SharedStyledComponents__Content-sc-1cr9zfr-3"] {
      content: "dedPKg,"
    }

    /*!sc*/
    .jEZlpP {
      -webkit-text-decoration: none;
      text-decoration: none;
      margin-right: 2rem;
      color: #333333;
    }

    /*!sc*/
    .jEZlpP svg {
      fill: #666666;
    }

    /*!sc*/
    .jEZlpP:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .jEZlpP:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .jEZlpP.active {
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g52[id="SharedStyledComponents__NavLink-sc-1cr9zfr-11"] {
      content: "jEZlpP,"
    }

    /*!sc*/
    .hCavpi {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(min(100%, 280px), 1fr));
      gap: 2rem;
    }

    /*!sc*/
    data-styled.g56[id="SharedStyledComponents__CardGrid-sc-1cr9zfr-15"] {
      content: "hCavpi,"
    }

    /*!sc*/
    .iuRocQ {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iuRocQ:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    data-styled.g59[id="SharedStyledComponents__Button-sc-1cr9zfr-18"] {
      content: "iuRocQ,"
    }

    /*!sc*/
    .bphJHH {
      background-color: #1c1cff;
      color: #ffffff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .bphJHH:hover {
      background-color: rgba(28, 28, 225, 0.8);
    }

    /*!sc*/
    .bphJHH:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g60[id="SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19"] {
      content: "bphJHH,"
    }

    /*!sc*/
    .fMmGqe {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .fMmGqe:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .fMmGqe:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g61[id="SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20"] {
      content: "fMmGqe,"
    }

    /*!sc*/
    .DZdKn {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      padding: 0 2rem;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .DZdKn {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g70[id="SharedStyledComponents__OptionContainer-sc-1cr9zfr-29"] {
      content: "DZdKn,"
    }

    /*!sc*/
    .knVVkh {
      border-radius: 2rem;
      border: 1px solid #1c1cff;
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      color: #1c1cff;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      padding: 1rem 1.5rem;
      margin: 0.5rem;
      cursor: pointer;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .knVVkh {
        width: 100%;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-left: 0;
        margin-right: 0;
      }
    }

    /*!sc*/
    .knVVkh:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
    }

    /*!sc*/
    .hUbzGW {
      border-radius: 2rem;
      border: 1px solid #333333;
      box-shadow: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      color: #333333;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      padding: 1rem 1.5rem;
      margin: 0.5rem;
      cursor: pointer;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hUbzGW {
        width: 100%;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-left: 0;
        margin-right: 0;
      }
    }

    /*!sc*/
    .hUbzGW:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
    }

    /*!sc*/
    data-styled.g71[id="SharedStyledComponents__Option-sc-1cr9zfr-30"] {
      content: "knVVkh,hUbzGW,"
    }

    /*!sc*/
    .fEepr {
      font-size: 24px;
      line-height: 100%;
      text-align: center;
    }

    /*!sc*/
    @media (max-width:768px) {
      .fEepr {
        font-size: 1rem;
        font-weight: 600;
      }
    }

    /*!sc*/
    .iUIsEB {
      line-height: 100%;
      text-align: center;
    }

    /*!sc*/
    @media (max-width:768px) {
      .iUIsEB {
        font-size: 1rem;
        font-weight: 600;
      }
    }

    /*!sc*/
    data-styled.g72[id="SharedStyledComponents__OptionText-sc-1cr9zfr-31"] {
      content: "fEepr,iUIsEB,"
    }

    /*!sc*/
    .hhdXUp {
      display: none;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hhdXUp {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    data-styled.g73[id="Mobile__Container-sc-zxc8gm-0"] {
      content: "hhdXUp,"
    }

    /*!sc*/
    .dUGGTH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g74[id="Mobile__MenuIcon-sc-zxc8gm-1"] {
      content: "dUGGTH,"
    }

    /*!sc*/
    .dLNRLx {
      margin-left: 1rem;
    }

    /*!sc*/
    data-styled.g75[id="Mobile__MenuButton-sc-zxc8gm-2"] {
      content: "dLNRLx,"
    }

    /*!sc*/
    .hvwyGc {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g76[id="Mobile__OtherIcon-sc-zxc8gm-3"] {
      content: "hvwyGc,"
    }

    /*!sc*/
    .bCHBHX {
      position: fixed;
      background: hsla(0, 0%, 69.8%, 0.9);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
    }

    /*!sc*/
    data-styled.g77[id="Mobile__MobileModal-sc-zxc8gm-4"] {
      content: "bCHBHX,"
    }

    /*!sc*/
    .AJukL {
      background: #ffffff;
      z-index: 99;
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
    }

    /*!sc*/
    data-styled.g78[id="Mobile__MenuContainer-sc-zxc8gm-5"] {
      content: "AJukL,"
    }

    /*!sc*/
    .gbspKa {
      margin: 0 0.125rem;
      width: 1.5rem;
      height: 2.5rem;
      position: relative;
      stroke-width: 2px;
      z-index: 100;
    }

    /*!sc*/
    .gbspKa>path {
      stroke: #333333;
      fill: none;
    }

    /*!sc*/
    .gbspKa:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gbspKa:hover>path {
      stroke: #1c1cff;
    }

    /*!sc*/
    data-styled.g79[id="Mobile__GlyphButton-sc-zxc8gm-6"] {
      content: "gbspKa,"
    }

    /*!sc*/
    .gBSEi {
      z-index: 101;
      padding: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g80[id="Mobile__SearchContainer-sc-zxc8gm-7"] {
      content: "gBSEi,"
    }

    /*!sc*/
    .iXlChz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .iXlChz>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g81[id="Mobile__SearchHeader-sc-zxc8gm-8"] {
      content: "iXlChz,"
    }

    /*!sc*/
    .jmriUx {
      z-index: 102;
      cursor: pointer;
    }

    /*!sc*/
    .jmriUx>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g82[id="Mobile__CloseIconContainer-sc-zxc8gm-9"] {
      content: "jmriUx,"
    }

    /*!sc*/
    .gYetwr {
      margin: 0;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 3rem 1rem 8rem;
    }

    /*!sc*/
    data-styled.g83[id="Mobile__MenuItems-sc-zxc8gm-10"] {
      content: "gYetwr,"
    }

    /*!sc*/
    .gXxMFO {
      margin: 0;
      margin-bottom: 3rem;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g84[id="Mobile__NavListItem-sc-zxc8gm-11"] {
      content: "gXxMFO,"
    }

    /*!sc*/
    .kuWShR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 0;
    }

    /*!sc*/
    data-styled.g85[id="Mobile__StyledNavLink-sc-zxc8gm-12"] {
      content: "kuWShR,"
    }

    /*!sc*/
    .erCTXJ {
      margin: 1rem 0;
      color: #333333;
    }

    /*!sc*/
    data-styled.g86[id="Mobile__SectionTitle-sc-zxc8gm-13"] {
      content: "erCTXJ,"
    }

    /*!sc*/
    .hghxUt {
      margin: 0;
    }

    /*!sc*/
    data-styled.g87[id="Mobile__SectionItems-sc-zxc8gm-14"] {
      content: "hghxUt,"
    }

    /*!sc*/
    .kdRQoZ {
      margin-bottom: 1rem;
      list-style-type: none;
      list-style-image: none;
      opacity: 0.7;
    }

    /*!sc*/
    .kdRQoZ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g88[id="Mobile__SectionItem-sc-zxc8gm-15"] {
      content: "kdRQoZ,"
    }

    /*!sc*/
    .iYttIj {
      background: #ffffff;
      border-top: 1px solid #ececec;
      padding-right: 1rem;
      padding-left: 1rem;
      margin-top: auto;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 108px;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      max-width: 450px;
      z-index: 99;
    }

    /*!sc*/
    data-styled.g89[id="Mobile__BottomMenu-sc-zxc8gm-16"] {
      content: "iYttIj,"
    }

    /*!sc*/
    .cnajxM {
      -webkit-flex: 1 1 120px;
      -ms-flex: 1 1 120px;
      flex: 1 1 120px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      color: #333333;
    }

    /*!sc*/
    .cnajxM>svg {
      fill: #333333;
    }

    /*!sc*/
    .cnajxM:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cnajxM:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g90[id="Mobile__BottomItem-sc-zxc8gm-17"] {
      content: "cnajxM,"
    }

    /*!sc*/
    .heSUpS {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
    }

    /*!sc*/
    .heSUpS>svg {
      fill: #333333;
    }

    /*!sc*/
    .heSUpS:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .heSUpS:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g91[id="Mobile__BottomLink-sc-zxc8gm-18"] {
      content: "heSUpS,"
    }

    /*!sc*/
    .hkZTkJ {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      margin-top: 0.5rem;
      text-transform: uppercase;
      text-align: center;
      opacity: 0.7;
    }

    /*!sc*/
    .hkZTkJ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g92[id="Mobile__BottomItemText-sc-zxc8gm-19"] {
      content: "hkZTkJ,"
    }

    /*!sc*/
    .jBipln {
      color: #333333;
      background: #f2f2f2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 10vw;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 280px;
      width: min(60vw, 280px);
      height: 280px;
      height: min(60vw, 280px);
      border-radius: 100%;
    }

    /*!sc*/
    data-styled.g93[id="Mobile__BlankSearchState-sc-zxc8gm-20"] {
      content: "jBipln,"
    }

    /*!sc*/
    .iGuESw {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 1000;
      width: 100%;
    }

    /*!sc*/
    data-styled.g94[id="Nav__NavContainer-sc-1aprtmp-0"] {
      content: "iGuESw,"
    }

    /*!sc*/
    .cpomzd {
      height: 4.75rem;
      padding: 1rem 2rem;
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g95[id="Nav__StyledNav-sc-1aprtmp-1"] {
      content: "cpomzd,"
    }

    /*!sc*/
    .faUCsG {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 1440px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .faUCsG {
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
    }

    /*!sc*/
    data-styled.g97[id="Nav__NavContent-sc-1aprtmp-3"] {
      content: "faUCsG,"
    }

    /*!sc*/
    .gjaVMk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gjaVMk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g98[id="Nav__InnerContent-sc-1aprtmp-4"] {
      content: "gjaVMk,"
    }

    /*!sc*/
    .jUJHKw {
      margin: 0;
      margin-left: 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g99[id="Nav__LeftItems-sc-1aprtmp-5"] {
      content: "jUJHKw,"
    }

    /*!sc*/
    .kQWBtS {
      margin: 0;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g100[id="Nav__RightItems-sc-1aprtmp-6"] {
      content: "kQWBtS,"
    }

    /*!sc*/
    .jODkFW {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-right: 0;
      margin-left: 1rem;
    }

    /*!sc*/
    .jODkFW:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g102[id="Nav__RightNavLink-sc-1aprtmp-8"] {
      content: "jODkFW,"
    }

    /*!sc*/
    .igUcis {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g103[id="Nav__HomeLogoNavLink-sc-1aprtmp-9"] {
      content: "igUcis,"
    }

    /*!sc*/
    .euWmfq {
      opacity: 0.85;
    }

    /*!sc*/
    .euWmfq:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g104[id="Nav__HomeLogo-sc-1aprtmp-10"] {
      content: "euWmfq,"
    }

    /*!sc*/
    .bDRFLa {
      padding-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g105[id="Nav__Span-sc-1aprtmp-11"] {
      content: "bDRFLa,"
    }

    /*!sc*/
    .hwxIMf {
      margin-left: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .hwxIMf:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g106[id="Nav__ThemeToggle-sc-1aprtmp-12"] {
      content: "hwxIMf,"
    }

    /*!sc*/
    .jOKVBH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g107[id="Nav__NavIcon-sc-1aprtmp-13"] {
      content: "jOKVBH,"
    }

    /*!sc*/
    .iMiHPL {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iMiHPL function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .iMiHPL function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g125[id="ButtonLink__StyledLinkButton-sc-8betkf-0"] {
      content: "iMiHPL,"
    }

    /*!sc*/
    .kmLdQv {
      background-color: #1c1cff;
      color: #ffffff !important;
      border: 1px solid transparent;
    }

    /*!sc*/
    .kmLdQv:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .kmLdQv:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g127[id="ButtonLink__PrimaryLink-sc-8betkf-2"] {
      content: "kmLdQv,"
    }

    /*!sc*/
    .dzWGyc {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .dzWGyc:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .dzWGyc:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g128[id="ButtonLink__SecondaryLink-sc-8betkf-3"] {
      content: "dzWGyc,"
    }

    /*!sc*/
    .elpFuD {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g131[id="TranslationBanner__H3-sc-cd94ib-0"] {
      content: "elpFuD,"
    }

    /*!sc*/
    .jJbcq {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jJbcq {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g132[id="TranslationBanner__BannerContainer-sc-cd94ib-1"] {
      content: "jJbcq,"
    }

    /*!sc*/
    .jIVPcV {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jIVPcV {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g133[id="TranslationBanner__StyledBanner-sc-cd94ib-2"] {
      content: "jIVPcV,"
    }

    /*!sc*/
    .jiZNpa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .jiZNpa {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g134[id="TranslationBanner__BannerContent-sc-cd94ib-3"] {
      content: "jiZNpa,"
    }

    /*!sc*/
    .dOewRO {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g135[id="TranslationBanner__BannerClose-sc-cd94ib-4"] {
      content: "dOewRO,"
    }

    /*!sc*/
    .cEauOV {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g136[id="TranslationBanner__BannerCloseIcon-sc-cd94ib-5"] {
      content: "cEauOV,"
    }

    /*!sc*/
    .nChYp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .nChYp {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g137[id="TranslationBanner__Row-sc-cd94ib-6"] {
      content: "nChYp,"
    }

    /*!sc*/
    .gXNXMi {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gXNXMi {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g138[id="TranslationBanner__ButtonRow-sc-cd94ib-7"] {
      content: "gXNXMi,"
    }

    /*!sc*/
    .hTWLVy {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hTWLVy {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g139[id="TranslationBanner__StyledEmoji-sc-cd94ib-8"] {
      content: "hTWLVy,"
    }

    /*!sc*/
    .kUKdfA {
      margin-left: 0.5rem;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kUKdfA {
        margin-left: 0rem;
        margin-top: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g140[id="TranslationBanner__SecondaryButtonLink-sc-cd94ib-9"] {
      content: "kUKdfA,"
    }

    /*!sc*/
    .kIfJin {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g141[id="TranslationBannerLegal__H3-sc-1df4kz4-0"] {
      content: "kIfJin,"
    }

    /*!sc*/
    .eZKsbu {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eZKsbu {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g142[id="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1"] {
      content: "eZKsbu,"
    }

    /*!sc*/
    .cEcQwp {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .cEcQwp {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g143[id="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2"] {
      content: "cEcQwp,"
    }

    /*!sc*/
    .intGem {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .intGem {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g144[id="TranslationBannerLegal__BannerContent-sc-1df4kz4-3"] {
      content: "intGem,"
    }

    /*!sc*/
    .hMvMKu {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g145[id="TranslationBannerLegal__BannerClose-sc-1df4kz4-4"] {
      content: "hMvMKu,"
    }

    /*!sc*/
    .bhaYvl {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g146[id="TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5"] {
      content: "bhaYvl,"
    }

    /*!sc*/
    .cJRPhR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cJRPhR {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g147[id="TranslationBannerLegal__Row-sc-1df4kz4-6"] {
      content: "cJRPhR,"
    }

    /*!sc*/
    .kXSENe {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kXSENe {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g148[id="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7"] {
      content: "kXSENe,"
    }

    /*!sc*/
    .dRuawC {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dRuawC {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g149[id="TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8"] {
      content: "dRuawC,"
    }

    /*!sc*/
    .cRWHVB {
      background-color: #1c1cff;
    }

    /*!sc*/
    data-styled.g150[id="SkipLink__Div-sc-1ysqk2q-0"] {
      content: "cRWHVB,"
    }

    /*!sc*/
    .kOmocm {
      line-height: 2rem;
      position: absolute;
      top: -3rem;
      margin-left: 0.5rem;
      color: #ffffff;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .kOmocm:focus {
      position: static;
    }

    /*!sc*/
    data-styled.g151[id="SkipLink__Anchor-sc-1ysqk2q-1"] {
      content: "kOmocm,"
    }

    /*!sc*/
    .mXCTw {
      position: relative;
      margin: 0px auto;
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-flow: column;
      -ms-flex-flow: column;
      flex-flow: column;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .mXCTw {
        max-width: 1504px;
      }
    }

    /*!sc*/
    data-styled.g152[id="Layout__ContentContainer-sc-19910io-0"] {
      content: "mXCTw,"
    }

    /*!sc*/
    .gqazVg {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gqazVg {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g153[id="Layout__MainContainer-sc-19910io-1"] {
      content: "gqazVg,"
    }

    /*!sc*/
    .kCJhKM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }

    /*!sc*/
    data-styled.g154[id="Layout__MainContent-sc-19910io-2"] {
      content: "kCJhKM,"
    }

    /*!sc*/
    .dliKfQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: space-around;
      -webkit-justify-content: space-around;
      -ms-flex-pack: space-around;
      justify-content: space-around;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      overflow: visible;
      width: 100%;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    /*!sc*/
    data-styled.g155[id="Layout__Main-sc-19910io-3"] {
      content: "dliKfQ,"
    }

    /*!sc*/
    .jiacsU {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      background: linear-gradient(49.21deg, rgba(127, 127, 213, 0.2) 19.87%, rgba(134, 168, 231, 0.2) 58.46%, rgba(145, 234, 228, 0.2) 97.05%);
      padding: 1.5rem;
      margin: 1rem;
      margin-top: 8rem;
      border-radius: 4px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .jiacsU {
        margin-bottom: 4rem;
      }
    }

    /*!sc*/
    data-styled.g161[id="Callout__StyledCard-sc-tgt6fi-0"] {
      content: "jiacsU,"
    }

    /*!sc*/
    .kSma-dE {
      font-size: 1.25rem;
      line-height: 140%;
      color: #666666;
    }

    /*!sc*/
    data-styled.g162[id="Callout__Description-sc-tgt6fi-1"] {
      content: "kSma-dE,"
    }

    /*!sc*/
    .jpjmqr {
      margin-top: -10rem;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      max-width: 263px;
      min-height: 200px;
    }

    /*!sc*/
    data-styled.g163[id="Callout__Image-sc-tgt6fi-2"] {
      content: "jpjmqr,"
    }

    /*!sc*/
    .ddcnaz {
      margin-top: 2.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      height: 100%;
    }

    /*!sc*/
    data-styled.g164[id="Callout__Content-sc-tgt6fi-3"] {
      content: "ddcnaz,"
    }

    /*!sc*/
    .fJLsnN {
      position: relative;
      z-index: 1;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      padding: 1.5rem;
      border-radius: 2px;
      max-width: 100%;
      color: #333;
      background: #ffe3d3;
    }

    /*!sc*/
    @media (max-width:414px) {
      .fJLsnN {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    .fJLsnN a {
      color: #994515;
    }

    /*!sc*/
    .fJLsnN a:hover {
      color: #321607;
    }

    /*!sc*/
    data-styled.g179[id="InfoBanner__Banner-sc-10dh6om-1"] {
      content: "fJLsnN,"
    }

    /*!sc*/
    .bCybtg {
      display: block;
      -webkit-align-items: auto;
      -webkit-box-align: auto;
      -ms-flex-align: auto;
      align-items: auto;
      width: auto;
      -webkit-box-pack: auto;
      -webkit-justify-content: auto;
      -ms-flex-pack: auto;
      justify-content: auto;
    }

    /*!sc*/
    @media (max-width:414px) {
      .bCybtg {
        display: block;
      }
    }

    /*!sc*/
    data-styled.g181[id="InfoBanner__Content-sc-10dh6om-3"] {
      content: "bCybtg,"
    }

    /*!sc*/
    .krObMr {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      background: #e1fefa;
      color: #333;
      text-transform: uppercase;
      text-align: center;
      display: inline-block;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: 0.25rem;
    }

    /*!sc*/
    .ilLUbu {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      background: #ffe3d3;
      color: #333;
      text-transform: uppercase;
      text-align: center;
      display: inline-block;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: 0.25rem;
    }

    /*!sc*/
    .fGTwhD {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      background: #d1d1ff;
      color: #333;
      text-transform: uppercase;
      text-align: center;
      display: inline-block;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: 0.25rem;
    }

    /*!sc*/
    data-styled.g191[id="Pill__Primary-sc-savco8-0"] {
      content: "krObMr,ilLUbu,fGTwhD,"
    }

    /*!sc*/
    .gwJlks {
      position: relative;
      z-index: 1;
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
      width: 100%;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      padding: 1rem;
      border-radius: 2px;
      color: #333333;
      border: 1px solid #e5e5e5;
    }

    /*!sc*/
    .gwJlks:hover {
      box-shadow: 0 0 1px #1c1cff;
      background: #f2f2f2;
      border-radius: 4px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .gwJlks {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g226[id="DocLink__Container-sc-fck695-0"] {
      content: "gwJlks,"
    }

    /*!sc*/
    .jaVkEX {
      -webkit-flex: 1;
      -ms-flex: 1;
      flex: 1;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      color: #333333;
    }

    /*!sc*/
    data-styled.g227[id="DocLink__TextCell-sc-fck695-1"] {
      content: "jaVkEX,"
    }

    /*!sc*/
    .fOuSKw {
      color: #4c4c4c;
      font-weight: 600;
      margin: 0;
    }

    /*!sc*/
    data-styled.g228[id="DocLink__Title-sc-fck695-2"] {
      content: "fOuSKw,"
    }

    /*!sc*/
    .egKJTh {
      margin: 0rem 1.5rem;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      min-width: 2rem;
    }

    /*!sc*/
    data-styled.g229[id="DocLink__Arrow-sc-fck695-3"] {
      content: "egKJTh,"
    }

    /*!sc*/
    .hWbCBl {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g230[id="DocLink__EmojiCell-sc-fck695-4"] {
      content: "hWbCBl,"
    }

    /*!sc*/
    .VBmjJ {
      position: relative;
      margin-top: 0.5rem;
      -webkit-align-self: stretch;
      -ms-flex-item-align: stretch;
      align-self: stretch;
    }

    /*!sc*/
    data-styled.g231[id="GhostCard__Container-sc-1ejpb5q-0"] {
      content: "VBmjJ,"
    }

    /*!sc*/
    .jNNReD {
      height: 100%;
      width: 100%;
      border-radius: 2px;
    }

    /*!sc*/
    data-styled.g232[id="GhostCard__BaseCard-sc-1ejpb5q-1"] {
      content: "jNNReD,"
    }

    /*!sc*/
    .kFPWjU {
      z-index: 2;
      padding: 1.5rem;
      text-align: left;
      border: 1px solid #e5e5e5;
      background-color: #ffffff;
    }

    /*!sc*/
    data-styled.g233[id="GhostCard__Card-sc-1ejpb5q-2"] {
      content: "kFPWjU,"
    }

    /*!sc*/
    .gQPIP {
      z-index: -1;
      position: absolute;
      bottom: 0.5rem;
      left: 0.5rem;
      border: 1px solid #e5e5e5;
      background-color: #f7f7f7;
    }

    /*!sc*/
    data-styled.g234[id="GhostCard__Ghost-sc-1ejpb5q-3"] {
      content: "gQPIP,"
    }

    /*!sc*/
    .bsJMWP {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-top: 2rem;
      margin-bottom: 0rem;
      padding: 0rem 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .bsJMWP {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        padding: 0;
      }
    }

    /*!sc*/
    data-styled.g330[id="PageHero__HeroContainer-sc-r5r57a-0"] {
      content: "bsJMWP,"
    }

    /*!sc*/
    .eyNDuU {
      max-width: 640px;
      padding: 8rem 0 8rem 2rem;
      margin-right: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eyNDuU {
        padding: 4rem 0;
        max-width: 100%;
      }
    }

    /*!sc*/
    data-styled.g331[id="PageHero__HeroContent-sc-r5r57a-1"] {
      content: "eyNDuU,"
    }

    /*!sc*/
    .cRmbnZ {
      -webkit-flex: 1 1 50%;
      -ms-flex: 1 1 50%;
      flex: 1 1 50%;
      background-size: cover;
      background-repeat: no-repeat;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      margin-top: 3rem;
      margin-left: 3rem;
      width: 100%;
      max-width: 624px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .cRmbnZ {
        margin-top: 0;
        margin-left: 0;
        max-width: 560px;
      }
    }

    /*!sc*/
    data-styled.g332[id="PageHero__HeroImg-sc-r5r57a-2"] {
      content: "cRmbnZ,"
    }

    /*!sc*/
    .kdyHZT {
      font-weight: 700;
      font-size: 3rem;
      max-width: 100%;
      margin-bottom: 0rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kdyHZT {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g333[id="PageHero__Header-sc-r5r57a-3"] {
      content: "kdyHZT,"
    }

    /*!sc*/
    .dipPYX {
      text-transform: uppercase;
      font-size: 1rem;
      font-weight: 400;
      margin-bottom: 1rem;
      color: #4c4c4c;
    }

    /*!sc*/
    data-styled.g334[id="PageHero__Title-sc-r5r57a-4"] {
      content: "dipPYX,"
    }

    /*!sc*/
    .ivcuAF {
      font-size: 1.5rem;
      line-height: 140%;
      color: #666666;
      margin-top: 1rem;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .ivcuAF {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    data-styled.g335[id="PageHero__Subtitle-sc-r5r57a-5"] {
      content: "ivcuAF,"
    }

    /*!sc*/
    .caMbSB {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 1rem;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g336[id="PageHero__ButtonRow-sc-r5r57a-6"] {
      content: "caMbSB,"
    }

    /*!sc*/
    .iUNaqu {
      margin-right: 1rem;
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .iUNaqu {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g337[id="PageHero__StyledButtonLink-sc-r5r57a-7"] {
      content: "iUNaqu,"
    }

    /*!sc*/
    .HCTlz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
      background: linear-gradient(49.21deg, rgba(127, 127, 213, 0.2) 19.87%, rgba(134, 168, 231, 0.2) 58.46%, rgba(145, 234, 228, 0.2) 97.05%);
      padding: 3rem;
      margin: 1rem;
      margin-top: 6rem;
      margin-bottom: 10rem;
      border-radius: 4px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .HCTlz {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        margin-bottom: 1rem;
        margin: 4rem 2rem;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .HCTlz {
        padding: 2rem;
      }
    }

    /*!sc*/
    data-styled.g365[id="CalloutBanner__StyledCard-sc-gj8rbv-0"] {
      content: "HCTlz,"
    }

    /*!sc*/
    .hatxbg {
      padding-left: 5rem;
      -webkit-flex: 1 0 50%;
      -ms-flex: 1 0 50%;
      flex: 1 0 50%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hatxbg {
        margin-top: 2rem;
        padding-left: 1rem;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        width: 100%;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .hatxbg {
        padding-left: 0;
      }
    }

    /*!sc*/
    data-styled.g366[id="CalloutBanner__Content-sc-gj8rbv-1"] {
      content: "hatxbg,"
    }

    /*!sc*/
    .cgkytK {
      font-size: 1.25rem;
      width: 90%;
      line-height: 140%;
      margin-bottom: 2rem;
      color: #666666;
    }

    /*!sc*/
    data-styled.g367[id="CalloutBanner__Description-sc-gj8rbv-2"] {
      content: "cgkytK,"
    }

    /*!sc*/
    .fqNEJO {
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 100%;
      max-width: 300px;
      margin-top: -6rem;
      margin-bottom: -6rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .fqNEJO {
        margin-bottom: 0rem;
        margin-top: -6rem;
      }
    }

    /*!sc*/
    data-styled.g368[id="CalloutBanner__Image-sc-gj8rbv-3"] {
      content: "fqNEJO,"
    }

    /*!sc*/
    .gvPciB {
      margin-top: 0rem;
    }

    /*!sc*/
    data-styled.g369[id="CalloutBanner__H2-sc-gj8rbv-4"] {
      content: "gvPciB,"
    }

    /*!sc*/
    .iobxnh {
      font-size: 2.5rem;
      font-weight: 400;
      margin-top: 0rem;
    }

    /*!sc*/
    data-styled.g422[id="BoxGrid__Title-sc-b0hjlf-0"] {
      content: "iobxnh,"
    }

    /*!sc*/
    .fwkjos {
      font-size: 1.25rem;
      line-height: 140%;
      color: #333;
    }

    /*!sc*/
    data-styled.g423[id="BoxGrid__Body-sc-b0hjlf-1"] {
      content: "fwkjos,"
    }

    /*!sc*/
    .bxvyFc {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      margin: 4rem 0rem;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .bxvyFc {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g424[id="BoxGrid__Grid-sc-b0hjlf-2"] {
      content: "bxvyFc,"
    }

    /*!sc*/
    .fUMhrJ {
      margin: 0.5rem;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
    }

    /*!sc*/
    .fUMhrJ:hover {
      -webkit-transition: -webkit-transform 50s;
      -webkit-transition: transform 50s;
      transition: transform 50s;
      -webkit-transform: rotate(10turn);
      -ms-transform: rotate(10turn);
      transform: rotate(10turn);
    }

    /*!sc*/
    data-styled.g425[id="BoxGrid__StyledEmoji-sc-b0hjlf-3"] {
      content: "fUMhrJ,"
    }

    /*!sc*/
    .kJbupR {
      grid-row-start: 1;
      grid-row-end: span 2;
      grid-column-start: 1;
      color: #333;
      cursor: pointer;
      background: #ffab7b;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      border: 1px solid #333333;
      padding: 1.5rem;
    }

    /*!sc*/
    .kJbupR:hover {
      background: #ffab7b;
      -webkit-transition: -webkit-transform 0.5s;
      -webkit-transition: transform 0.5s;
      transition: transform 0.5s;
      -webkit-transform: skewX(-5deg);
      -ms-transform: skewX(-5deg);
      transform: skewX(-5deg);
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kJbupR {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .kJbupR {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    .gVCgFj {
      grid-row-start: auto;
      grid-row-end: auto;
      grid-column-start: auto;
      color: #333333;
      cursor: pointer;
      background: #ffffff;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column-reverse;
      -ms-flex-direction: column-reverse;
      flex-direction: column-reverse;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      border: 1px solid #333333;
      padding: 1.5rem;
    }

    /*!sc*/
    .gVCgFj:hover {
      background: #f7f7f7;
      -webkit-transition: -webkit-transform 0.5s;
      -webkit-transition: transform 0.5s;
      transition: transform 0.5s;
      -webkit-transform: skewX(-5deg);
      -ms-transform: skewX(-5deg);
      transform: skewX(-5deg);
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gVCgFj {
        -webkit-flex-direction: row-reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .gVCgFj {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
      }
    }

    /*!sc*/
    data-styled.g426[id="BoxGrid__Box-sc-b0hjlf-4"] {
      content: "kJbupR,gVCgFj,"
    }

    /*!sc*/
    .iAYtQR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #212f46;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);
      min-height: 200px;
    }

    /*!sc*/
    .hWyMPk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #080808;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);
      min-height: 200px;
    }

    /*!sc*/
    .qWCfY {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #ffffff;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);
      min-height: 200px;
    }

    /*!sc*/
    .dxbRkp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: row;
      -ms-flex-direction: row;
      flex-direction: row;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #7e4cf2;
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.1);
      min-height: 200px;
    }

    /*!sc*/
    data-styled.g432[id="ProductCard__ImageWrapper-sc-1be5kz-0"] {
      content: "iAYtQR,hWyMPk,qWCfY,dxbRkp,"
    }

    /*!sc*/
    .iwbsNY {
      width: 100%;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      max-width: 372px;
      max-height: 257px;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iwbsNY {
        max-width: 311px;
      }
    }

    /*!sc*/
    data-styled.g433[id="ProductCard__Image-sc-1be5kz-1"] {
      content: "iwbsNY,"
    }

    /*!sc*/
    .hhUQue {
      color: #333333;
      box-shadow: 0px 14px 66px rgba(0, 0, 0, 0.07);
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #ececec;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .hhUQue:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.02);
      -ms-transform: scale(1.02);
      transform: scale(1.02);
    }

    /*!sc*/
    data-styled.g434[id="ProductCard__Card-sc-1be5kz-2"] {
      content: "hhUQue,"
    }

    /*!sc*/
    .bFVwFN {
      padding: 1.5rem;
      text-align: left;
      height: 100%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g435[id="ProductCard__Content-sc-1be5kz-3"] {
      content: "bFVwFN,"
    }

    /*!sc*/
    .taKQf {
      margin-top: 2rem;
      margin-bottom: 0.75rem;
    }

    /*!sc*/
    data-styled.g436[id="ProductCard__Title-sc-1be5kz-4"] {
      content: "taKQf,"
    }

    /*!sc*/
    .emEoCm {
      opacity: 0.8;
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
      line-height: 140%;
    }

    /*!sc*/
    data-styled.g437[id="ProductCard__Description-sc-1be5kz-5"] {
      content: "emEoCm,"
    }

    /*!sc*/
    .dQMlUE {
      margin-top: 1.25rem;
      padding: 0 1.5rem;
    }

    /*!sc*/
    data-styled.g438[id="ProductCard__SubjectContainer-sc-1be5kz-6"] {
      content: "dQMlUE,"
    }

    /*!sc*/
    .kogOj {
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g440[id="ProductCard__StyledButtonLink-sc-1be5kz-8"] {
      content: "kogOj,"
    }

    /*!sc*/
    .drfjLK {
      margin-top: 1rem;
    }

    /*!sc*/
    data-styled.g441[id="ProductCard__Children-sc-1be5kz-9"] {
      content: "drfjLK,"
    }

    /*!sc*/
    .hjQkRC {
      width: 100%;
      margin-bottom: 2rem;
      margin-top: 4rem;
    }

    /*!sc*/
    data-styled.g442[id="ProductList__Product-sc-19jtmu8-0"] {
      content: "hjQkRC,"
    }

    /*!sc*/
    .ldzVuU {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      color: #333333 !important;
      margin-bottom: 1px;
      margin-top: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .ldzVuU {
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g443[id="ProductList__Item-sc-19jtmu8-1"] {
      content: "ldzVuU,"
    }

    /*!sc*/
    .bvLdAB {
      border-bottom: 2px solid #e5e5e5;
      padding-bottom: 1rem;
      margin-bottom: 0rem;
    }

    /*!sc*/
    data-styled.g445[id="ProductList__CategoryTitle-sc-19jtmu8-3"] {
      content: "bvLdAB,"
    }

    /*!sc*/
    .gIvdmA {
      padding-bottom: 1rem;
      width: 100%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-left: 1.5rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      border-bottom: 1px solid #e5e5e5;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gIvdmA {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
        margin-left: 1rem;
      }
    }

    /*!sc*/
    data-styled.g446[id="ProductList__TextContent-sc-19jtmu8-4"] {
      content: "gIvdmA,"
    }

    /*!sc*/
    .hMAaaS {
      font-size: 0.875rem;
      margin-bottom: 0;
      opacity: 0.6;
    }

    /*!sc*/
    data-styled.g447[id="ProductList__ItemDesc-sc-19jtmu8-5"] {
      content: "hMAaaS,"
    }

    /*!sc*/
    .eEgwmt {
      width: 80px;
    }

    /*!sc*/
    data-styled.g448[id="ProductList__ImageContainer-sc-19jtmu8-6"] {
      content: "eEgwmt,"
    }

    /*!sc*/
    .dcMJIf {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g449[id="ProductList__LeftContainer-sc-19jtmu8-7"] {
      content: "dcMJIf,"
    }

    /*!sc*/
    .hLQylT {
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
      border-radius: 2px;
    }

    /*!sc*/
    data-styled.g450[id="ProductList__Image-sc-19jtmu8-8"] {
      content: "hLQylT,"
    }

    /*!sc*/
    .cXZDTs {
      margin-left: 2rem;
      padding: 0.25rem 1.5rem;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cXZDTs {
        margin-top: 1rem;
        margin-left: 0rem;
      }
    }

    /*!sc*/
    data-styled.g451[id="ProductList__StyledButton-sc-19jtmu8-9"] {
      content: "cXZDTs,"
    }

    /*!sc*/
    .bldfRL {
      background-size: cover;
      background-repeat: no-repeat;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 100%;
      min-width: 240px;
      max-width: 300px;
      margin: 2rem 6rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      .bldfRL {
        margin: 2rem 2rem;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .bldfRL {
        margin: 2rem 0rem;
      }
    }

    /*!sc*/
    data-styled.g452[id="dapps__MagiciansImage-sc-1hb30un-0"] {
      content: "bldfRL,"
    }

    /*!sc*/
    .WaLuL {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }

    /*!sc*/
    data-styled.g453[id="dapps__ImageContainer-sc-1hb30un-1"] {
      content: "WaLuL,"
    }

    /*!sc*/
    .isCBvb {
      margin-top: 0;
    }

    /*!sc*/
    data-styled.g454[id="dapps__StyledButtonSecondary-sc-1hb30un-2"] {
      content: "isCBvb,"
    }

    /*!sc*/
    .fzwbiz .ghost-card-base {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
    }

    /*!sc*/
    data-styled.g455[id="dapps__StyledGhostCard-sc-1hb30un-3"] {
      content: "fzwbiz,"
    }

    /*!sc*/
    .kWaAUo {
      font-size: 1.5rem;
      line-height: 140%;
      color: #666666;
      margin-top: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kWaAUo {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    data-styled.g456[id="dapps__Subtitle-sc-1hb30un-4"] {
      content: "kWaAUo,"
    }

    /*!sc*/
    .btkUYr {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .btkUYr {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g457[id="dapps__Row-sc-1hb30un-5"] {
      content: "btkUYr,"
    }

    /*!sc*/
    .haNNxO {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      background: #ffffff;
      border-radius: 32px;
      padding: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .haNNxO {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g458[id="dapps__IntroRow-sc-1hb30un-6"] {
      content: "haNNxO,"
    }

    /*!sc*/
    .kPrkEk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      width: 100%;
      margin-right: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kPrkEk {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
        margin-left: 0rem;
        margin-right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g459[id="dapps__TwoColumnContent-sc-1hb30un-7"] {
      content: "kPrkEk,"
    }

    /*!sc*/
    .ecBNXU {
      font-size: 1.5rem;
      font-style: normal;
      margin-top: 0.5rem;
      font-weight: 700;
      line-height: 22px;
      -webkit-letter-spacing: 0px;
      -moz-letter-spacing: 0px;
      -ms-letter-spacing: 0px;
      letter-spacing: 0px;
      text-align: left;
    }

    /*!sc*/
    data-styled.g460[id="dapps__H2-sc-1hb30un-8"] {
      content: "ecBNXU,"
    }

    /*!sc*/
    .lmGJA {
      width: 50%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .lmGJA {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g461[id="dapps__StyledInfoBanner-sc-1hb30un-9"] {
      content: "lmGJA,"
    }

    /*!sc*/
    .jHJgDO {
      margin: 8rem 0 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .jHJgDO {
        margin-bottom: 0;
      }
    }

    /*!sc*/
    data-styled.g462[id="dapps__StyledCalloutBanner-sc-1hb30un-10"] {
      content: "jHJgDO,"
    }

    /*!sc*/
    .bpEIji {
      text-align: center;
    }

    /*!sc*/
    @media (min-width:768px) {
      .bpEIji {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g463[id="dapps__MobileOptionContainer-sc-1hb30un-11"] {
      content: "bpEIji,"
    }

    /*!sc*/
    .jcQINh {
      -webkit-flex: 1 1 75%;
      -ms-flex: 1 1 75%;
      flex: 1 1 75%;
      margin-bottom: 1.5rem;
      margin-right: 2rem;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .jcQINh {
        margin-right: 0rem;
        margin-left: 0rem;
      }
    }

    /*!sc*/
    data-styled.g464[id="dapps__Column-sc-1hb30un-12"] {
      content: "jcQINh,"
    }

    /*!sc*/
    .dMgOlV {
      margin: 0rem 0rem;
      margin-bottom: 4rem;
      border-top: 1px solid #e5e5e5;
      background: #f7f7f7;
      padding: 2rem 0rem;
      padding-top: 4rem;
    }

    /*!sc*/
    data-styled.g465[id="dapps__FullWidthContainer-sc-1hb30un-13"] {
      content: "dMgOlV,"
    }

    /*!sc*/
    .hAxiYg {
      display: grid;
      gap: 1rem;
      grid-template-columns: repeat(2, 1fr);
    }

    /*!sc*/
    @media (max-width:768px) {
      .hAxiYg {
        grid-template-columns: 1fr;
      }
    }

    /*!sc*/
    data-styled.g466[id="dapps__CardContainer-sc-1hb30un-14"] {
      content: "hAxiYg,"
    }

    /*!sc*/
    .kojsPb {
      text-align: center;
    }

    /*!sc*/
    data-styled.g467[id="dapps__CenteredCard-sc-1hb30un-15"] {
      content: "kojsPb,"
    }

    /*!sc*/
    .icIavD {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      margin: 1rem 0rem;
      margin-bottom: 4rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .icIavD {
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
      }
    }

    /*!sc*/
    data-styled.g468[id="dapps__StepBoxContainer-sc-1hb30un-16"] {
      content: "icIavD,"
    }

    /*!sc*/
    .dloeJu {
      border: 1px solid #e5e5e5;
      background: #ffffff;
      padding: 0rem 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
      -webkit-text-decoration: none;
      text-decoration: none;
      width: 100%;
    }

    /*!sc*/
    .dloeJu:hover {
      background: #f7f7f7;
      -webkit-transition: -webkit-transform 0.2s;
      -webkit-transition: transform 0.2s;
      transition: transform 0.2s;
      -webkit-transform: scale(1.05);
      -ms-transform: scale(1.05);
      transform: scale(1.05);
    }

    /*!sc*/
    @media (max-width:768px) {
      .dloeJu {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
        padding-bottom: 2rem;
      }
    }

    /*!sc*/
    data-styled.g469[id="dapps__StepBox-sc-1hb30un-17"] {
      content: "dloeJu,"
    }

    /*!sc*/
    .dZawaj {
      font-size: 1.25rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      margin-top: 1.5rem;
    }

    /*!sc*/
    .dZawaj a {
      display: none;
    }

    /*!sc*/
    data-styled.g470[id="dapps__H3-sc-1hb30un-18"] {
      content: "dZawaj,"
    }

    /*!sc*/
    .beeJgW {
      text-align: center;
      max-width: 800px;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .beeJgW {
        margin: auto 1.5rem;
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g471[id="dapps__CenterText-sc-1hb30un-19"] {
      content: "beeJgW,"
    }

    /*!sc*/
    .kMsWTL {
      margin-right: 2rem;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kMsWTL {
        margin: auto 0rem;
      }
    }

    /*!sc*/
    data-styled.g472[id="dapps__LeftColumn-sc-1hb30un-20"] {
      content: "kMsWTL,"
    }

    /*!sc*/
    .fQxgud {
      margin-left: 2rem;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .fQxgud {
        margin: auto 0rem;
      }
    }

    /*!sc*/
    data-styled.g473[id="dapps__RightColumn-sc-1hb30un-21"] {
      content: "fQxgud,"
    }

    /*!sc*/
    .kPQWev {
      margin-top: 3rem;
    }

    /*!sc*/
    data-styled.g474[id="dapps__About-sc-1hb30un-22"] {
      content: "kPQWev,"
    }

    /*!sc*/
    .gkLjYS {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-top: 3rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gkLjYS {
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g475[id="dapps__Box-sc-1hb30un-23"] {
      content: "gkLjYS,"
    }

    /*!sc*/
    .kNpaqE {
      text-align: center;
      max-width: 800px;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kNpaqE {
        text-align: left;
      }
    }

    /*!sc*/
    data-styled.g476[id="dapps__BoxText-sc-1hb30un-24"] {
      content: "kNpaqE,"
    }

    /*!sc*/
    .bPsfrp {
      margin-bottom: 0rem;
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g477[id="dapps__TextNoMargin-sc-1hb30un-25"] {
      content: "bPsfrp,"
    }

    /*!sc*/
    .fBpVdK {
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      padding: 1.5rem;
      margin-top: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .fBpVdK {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g478[id="dapps__AddDapp-sc-1hb30un-26"] {
      content: "fBpVdK,"
    }

    /*!sc*/
    .dEkTNr {
      margin-left: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dEkTNr {
        margin-left: 1rem;
      }
    }

    /*!sc*/
    @media (max-width:414px) {
      .dEkTNr {
        margin-top: 2rem;
        margin-left: 0rem;
      }
    }

    /*!sc*/
    data-styled.g479[id="dapps__AddDappButton-sc-1hb30un-27"] {
      content: "dEkTNr,"
    }

    /*!sc*/
    .bAOTxx {
      -webkit-flex: 1 1 416px;
      -ms-flex: 1 1 416px;
      flex: 1 1 416px;
      min-height: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .bAOTxx {
        margin-top: 12rem;
      }
    }

    /*!sc*/
    data-styled.g480[id="dapps__StyledCallout-sc-1hb30un-28"] {
      content: "bAOTxx,"
    }

    /*!sc*/
    .bOajRC {
      margin-bottom: 4rem;
      margin-top: 4rem;
    }

    /*!sc*/
    data-styled.g481[id="dapps__StyledCardGrid-sc-1hb30un-29"] {
      content: "bOajRC,"
    }

    /*!sc*/
    .bUvmBk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 3rem;
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g482[id="dapps__MoreButtonContainer-sc-1hb30un-30"] {
      content: "bUvmBk,"
    }

    /*!sc*/
  </style>
  <link as="script" rel="preload" href="/webpack-runtime-d600da28e471609bf3f3.js">
  <link as="script" rel="preload" href="/framework-4e285adfb333f1b50c05.js">
  <link as="script" rel="preload" href="/252f366e-2705b607be296edabcea.js">
  <link as="script" rel="preload" href="/ae51ba48-34d54094a2c04f215fb8.js">
  <link as="script" rel="preload" href="/1bfc9850-0f18e2d74feedfc6e426.js">
  <link as="script" rel="preload" href="/0c428ae2-2128ff22fce458b543bd.js">
  <link as="script" rel="preload" href="/0f1ac474-e8f788f62189f421a856.js">
  <link as="script" rel="preload" href="/app-b670b5ed3a389af0ed04.js">
  <link as="script" rel="preload" href="/da0bcc46d10bae02bd9afc23823ae5fd5b6c062d-5a1ae5c4bcbe6fc6589f.js">
  <link as="script" rel="preload" href="/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js">
  <link as="fetch" rel="preload" href="/page-data/en/dapps/page-data.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1011117294.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/3003422828.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/446219633.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/app-data.json" crossorigin="anonymous">
</head>

<body>
  <div id="___gatsby">
    <div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper">
      <div class="SkipLink__Div-sc-1ysqk2q-0 cRWHVB"><a href="#main-content"
          class="SkipLink__Anchor-sc-1ysqk2q-1 kOmocm"><span>Skip to main content</span></a></div>
      <div class="TranslationBanner__BannerContainer-sc-cd94ib-1 jJbcq">
        <div class="TranslationBanner__StyledBanner-sc-cd94ib-2 jIVPcV">
          <div class="TranslationBanner__BannerContent-sc-cd94ib-3 jiZNpa">
            <div class="TranslationBanner__Row-sc-cd94ib-6 nChYp">
              <h3 class="TranslationBanner__H3-sc-cd94ib-0 elpFuD"><span>Help update this page</span></h3><span
                size="1.5" ml="0.5rem" mt="0" mr="0" mb="0"
                class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBanner__StyledEmoji-sc-cd94ib-8 hTWLVy undefined"><img
                  alt="🌏" src="https://twemoji.maxcdn.com/2/svg/1f30f.svg"
                  style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
            </div>
            <p><span>There’s a new version of this page but it’s only in English right now. Help us translate the latest
                version.</span></p>
            <div class="TranslationBanner__ButtonRow-sc-cd94ib-7 gXNXMi">
              <div><a
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                  href="..\contributing\translation-program\index.html"><span>Translate page</span></a></div>
              <div><a aria-current="page"
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc TranslationBanner__SecondaryButtonLink-sc-cd94ib-9 kUKdfA active"
                  href="index.html"><span>See English</span></a></div>
            </div>
          </div>
          <div class="TranslationBanner__BannerClose-sc-cd94ib-4 dOewRO"><svg stroke="currentColor" fill="currentColor"
              stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBanner__BannerCloseIcon-sc-cd94ib-5 iylOGp cEauOV"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1 eZKsbu">
        <div class="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2 cEcQwp">
          <div class="TranslationBannerLegal__BannerContent-sc-1df4kz4-3 intGem">
            <div class="TranslationBannerLegal__Row-sc-1df4kz4-6 cJRPhR">
              <h3 class="TranslationBannerLegal__H3-sc-1df4kz4-0 kIfJin"><span>No bugs here!</span><span size="1.5"
                  ml="0.5rem" mt="0" mr="0" mb="0"
                  class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8 dRuawC undefined"><img
                    alt="🐛" src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                    style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
            </div>
            <p><span>This page is not being translated. We've intentionally left this page in English for now.</span>
            </p>
            <div class="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7 kXSENe"><button
                class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Don't
                  show again</span></button></div>
          </div>
          <div class="TranslationBannerLegal__BannerClose-sc-1df4kz4-4 hMvMKu"><svg stroke="currentColor"
              fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5 iylOGp bhaYvl"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="Layout__ContentContainer-sc-19910io-0 mXCTw">
        <div class="Nav__NavContainer-sc-1aprtmp-0 iGuESw">
          <nav class="Nav__StyledNav-sc-1aprtmp-1 cpomzd">
            <div class="Nav__NavContent-sc-1aprtmp-3 faUCsG"><a
                class="Link__InternalLink-sc-e3riao-1 gCWUlE Nav__HomeLogoNavLink-sc-1aprtmp-9 igUcis active"
                href="..\index.html">
                <div data-gatsby-image-wrapper="" style="width:22px;height:35px"
                  class="gatsby-image-wrapper Nav__HomeLogo-sc-1aprtmp-10 euWmfq"><img aria-hidden="true"
                    data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                    src="data:image/png;base64,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"
                    alt="">
                  <picture>
                    <source type="image/webp"
                      data-srcset="/static/a110735dade3f354a46fc2446cd52476/db4de/eth-home-icon.webp 22w,/static/a110735dade3f354a46fc2446cd52476/f3a29/eth-home-icon.webp 44w"
                      sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                      decoding="async" loading="lazy"
                      data-src="/static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                      data-srcset="/static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png 22w,/static/a110735dade3f354a46fc2446cd52476/f2da0/eth-home-icon.png 44w"
                      alt="Ethereum logo">
                  </picture><noscript>
                    <picture>
                      <source type="image/webp"
                        srcset="/static/a110735dade3f354a46fc2446cd52476/db4de/eth-home-icon.webp,/static/a110735dade3f354a46fc2446cd52476/f3a29/eth-home-icon.webp"
                        sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                        decoding="async" loading="lazy"
                        src="/static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                        srcset="/static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png,/static/a110735dade3f354a46fc2446cd52476/f2da0/eth-home-icon.png"
                        alt="Ethereum logo">
                    </picture>
                  </noscript>
                  <script
                    type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                </div>
              </a>
              <div class="Nav__InnerContent-sc-1aprtmp-4 gjaVMk">
                <ul class="Nav__LeftItems-sc-1aprtmp-5 jUJHKw">
                  <li aria-label="Use Ethereum menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Use Ethereum</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a aria-current="page"
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB active"
                          href="index.html"><span>Decentralized applications (dapps)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\staking\index.html"><span>Stake ETH</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Learn menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Learn</span><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\history\index.html"><span>History of Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk"
                            height="12px" width="12px" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                            </path>
                          </svg></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\web3\index.html"><span>What is Web3?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Developers&#x27; Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span
                      tabindex="0" class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Developers</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\index.html"><span>Developers' home</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="/en/developers/docs/"><span>Documentation</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                      </li>
                    </ul>
                  </li>
                  <li aria-label="Enterprise Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Enterprise</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Community Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Community</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\index.html"><span>Community hub</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\online\index.html"><span>Online communities</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\grants\index.html"><span>Grants</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                    </ul>
                  </li>
                </ul>
                <div class="Nav__RightItems-sc-1aprtmp-6 kQWBtS">
                  <div class="Search__Root-sc-1qm8xwy-0 kNenpg">
                    <form class="Input__Form-sc-1utkal6-0 eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput-sc-1utkal6-1 kkfPkW">
                      <p class="Input__SearchSlash-sc-1utkal6-3 ggVPUc">/</p><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Input__SearchIcon-sc-1utkal6-2 iylOGp gFzMVg" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper-sc-1qm8xwy-1 eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div><button aria-label="Switch to Dark Theme"
                    class="NakedButton-sc-1g43w8v-0 Nav__ThemeToggle-sc-1aprtmp-12 dUatah hwxIMf"><svg
                      stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH" height="24"
                      width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg></button><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE SharedStyledComponents__NavLink-sc-1cr9zfr-11 Nav__RightNavLink-sc-1aprtmp-8 jEZlpP jODkFW"
                    href="..\languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH"
                      height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                      </path>
                    </svg><span class="Nav__Span-sc-1aprtmp-11 bDRFLa"><span>Languages</span></span></a>
                </div>
              </div>
              <div class="Mobile__Container hhdXUp"><button aria-label="Toggle search button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg stroke="currentColor" fill="currentColor"
                    stroke-width="0" viewbox="0 0 24 24"
                    class="Icon__StyledIcon Mobile__MenuIcon Mobile__OtherIcon iylOGp dUGGTH hvwyGc" height="24"
                    width="24" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                    </path>
                  </svg></button><button aria-label="Toggle menu button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg viewbox="0 0 24 40"
                    class="Mobile__GlyphButton gbspKa">
                    <path d="M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0"></path>
                  </svg></button>
                <div class="Mobile__MobileModal bCHBHX" style="display:none;opacity:0"></div>
                <div aria-hidden="true" class="Mobile__MenuContainer AJukL"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <ul class="Mobile__MenuItems gYetwr">
                    <li aria-label="Select Use Ethereum" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Use Ethereum</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\staking\index.html"><span>Stake ETH</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Learn" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Learn</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\history\index.html"><span>History of Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE is-glossary SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                              fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                              class="Icon__StyledIcon Link__GlossaryIcon iylOGp jfMIWk" height="12px" width="12px"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                              </path>
                            </svg></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\web3\index.html"><span>What is Web3?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Developers" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Developers</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\index.html"><span>Developers' home</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\/en/developers/docs/"><span>Documentation</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                        </li>
                      </ul>
                    </li>
                    <li aria-label="Select Enterprise" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Enterprise</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Community" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Community</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\index.html"><span>Community hub</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\online\index.html"><span>Online communities</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\grants\index.html"><span>Grants</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                      </ul>
                    </li>
                  </ul>
                </div>
                <div aria-hidden="true" class="Mobile__BottomMenu iYttIj"
                  style="transform:translateX(-100%) translateZ(0)">
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Search</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Light</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><a class="Link__InternalLink gCWUlE Mobile__BottomLink8 heSUpS"
                      href="languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                        viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                        </path>
                      </svg>
                      <div class="Mobile__BottomItemText hkZTkJ"><span>Languages</span></div>
                    </a></div>
                </div>
                <div class="Mobile__MenuContainer Mobile__SearchContainer AJukL gBSEi"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <h3 class="Mobile__SearchHeader iXlChz"><span>Search</span><span
                      class="Mobile__CloseIconContainer jmriUx"><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon iylOGp" height="24" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                        </path>
                      </svg></span></h3>
                  <div class="Search__Root kNenpg">
                    <form class="Input__Form eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput kkfPkW">
                      <p class="Input__SearchSlash ggVPUc">/</p><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon Input__SearchIcon iylOGp gFzMVg"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div>
                  <div class="Mobile__BlankSearchState jBipln"><span size="3" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji hLjau undefined"><img alt="â›µ"
                        src="https://twemoji.maxcdn.com/2/svg/26f5.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Search
                      away!</span></div>
                </div>
              </div>
            </div>
          </nav>
        </div>
        <div id="main-content"></div>
        <div class="Layout__MainContainer-sc-19910io-1 gqazVg">
          <div class="Layout__MainContent-sc-19910io-2 kCJhKM">
            <main class="Layout__Main-sc-19910io-3 dliKfQ">
              <div class="SharedStyledComponents__Page-sc-1cr9zfr-0 kCvjty">
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="PageHero__HeroContainer-sc-r5r57a-0 bsJMWP">
                    <div class="PageHero__HeroContent-sc-r5r57a-1 eyNDuU">
                      <h1 class="PageHero__Title-sc-r5r57a-4 dipPYX">Decentralized applications (dapps)</h1>
                      <h2 class="PageHero__Header-sc-r5r57a-3 kdyHZT">Ethereum-powered tools and services</h2>
                      <div class="PageHero__Subtitle-sc-r5r57a-5 ivcuAF">Dapps are a growing movement of applications
                        that use Ethereum to disrupt business models or invent new ones.</div>
                      <div class="PageHero__ButtonRow-sc-r5r57a-6 caMbSB"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv PageHero__StyledButtonLink-sc-r5r57a-7 iUNaqu"
                          href="#explore">Explore dapps</a><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc PageHero__StyledButtonLink-sc-r5r57a-7 iUNaqu"
                          href="#what-are-dapps">What are dapps?</a></div>
                    </div>
                    <div data-gatsby-image-wrapper=""
                      class="gatsby-image-wrapper gatsby-image-wrapper-constrained PageHero__HeroImg-sc-r5r57a-2 cRmbnZ">
                      <div style="max-width:624px;display:block"><img alt="" role="presentation" aria-hidden="true"
                          src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;447&#x27; width=&#x27;624&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                          style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                        data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAADxUlEQVQ4y4WSz28idRTAH9DERL2ZNTHxYvbgzWjUP8BkY2Ji1Bg1uptsjDFq3NNmk71o48Fko7urxjWu3bVuW2hpC8wMFIYCM8OP0qFIF2jl11B+SFugUH4MMDAtUOZJe9GL+r289w7v8z4v7wvwj2e/rwPfIgmu2cWngoTnWXFzDaosCyr4WuP8yXmeu+sA9y9O1blzAYhHm5DYap7FdKINQrwFO8n237DLb/CQ42PqWmYX9sLCy7XMflaguA/TpPPNYnBrqRwvaBG7gDjUGG9y/w/0rnah4I+q9mO7kODzTza3t3tt3oNykMNGwI+5jdSn9WQGmglBc+9Lz38DiakazN7fg006pQqRQXDc9T0jHxzI/fQfisAEj0vhBLZ3sp80UlkohoWJ5x6/9u9Ap7kF5HwN9DMHsOWtT2xSaWCJ4sxAkrFSlk50lsNBJt/DUq79PaICcrM/IW/heHWEGx/R8PuPpCowRavI7zhgpjgAt0cCiqgBtyypXZYeWLTSW4zhEDtCCkdjQig1OClVjjHqPYgCJFW3Jkcg0NGJfcqqQcWhmrn4K8xdJcYD1mB20jG+rF2E8NIAkEJgbO2PWX1bdCxLGHXsjJT24VgEFRz0Rm7TPrqXZTN7q/FIkQtCyWIFPKJBd3n6Me1V4gnEMui+GgOdREvtIFtgMzbP+8xdDJiOxo3SaM0qK4lIGwu5YxRiXcVlEEd+g4z+OfFK0mS9IszbyLKB5KuUJVmy0LUs5fwBoy4A3Z2ySvtzGZYfVN9l9J1hwChvBixdOUINkDN1FZpojRhKwnWT1GeXZJnXZzoNC4FFyoYd3o1yyIPDiAcrLOtHzAN47G3o1FEzNqyuGJqKWVv5/CHRa4bGpj6bpPAeGYPeI1wnOxmTSbkeNsewT2pPKtTKsOL2nLR470B6yGOW9jGnh4LpO7vq08QwW32ftXXQSbXQOiPiqq6JHnsH15jeAc/JLWauJjoW8nTM6B91VojRHmnHXXJVkYRkv1fax7gzwp4Bv51MqC697tecFtRC/cUx4JKNbF4n9bXbdkPtbXgFJ/IEd7NGWTC1tIopI4PHe3nMOQPDqt2Bw2IOh/USpt1R/xnwm8kEvPeq9+xfWZcbEPL3gV4RwbRwCHZ6BOxvEUjf0z1dmDNeC03ThqjOlW5nc8qfrnVMWtaVaioXaWQLt8P09gsj5Rjgxhdx+OA1H7xzwQu0SVRveI40dkLUWPR1tXP1RM0vxtSidgq6Rh0cmin47MLKo8yD4PMb8/6LER370qlIv1wAwRc7k/oLU7nwtpVa/FsAAAAASUVORK5CYII="
                        alt="">
                      <picture>
                        <source type="image/webp"
                          srcset="/static/5dea0acbc8484c42006d7bbed32fa019/5f7ee/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/15eb9/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/de68a/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/d1bb0/doge-computer.webp"
                          sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                          style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="eager"
                          src="/static/5dea0acbc8484c42006d7bbed32fa019/23573/doge-computer.png"
                          srcset="/static/5dea0acbc8484c42006d7bbed32fa019/37ca5/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/4f441/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/23573/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/036d0/doge-computer.png"
                          alt="Illustration of a doge using a computer">
                      </picture><noscript>
                        <picture>
                          <source type="image/webp"
                            srcset="/static/5dea0acbc8484c42006d7bbed32fa019/5f7ee/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/15eb9/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/de68a/doge-computer.webp,/static/5dea0acbc8484c42006d7bbed32fa019/d1bb0/doge-computer.webp"
                            sizes="(min-width: 624px) 624px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                            style="opacity:0" sizes="(min-width: 624px) 624px, 100vw" decoding="async" loading="eager"
                            src="/static/5dea0acbc8484c42006d7bbed32fa019/23573/doge-computer.png"
                            srcset="/static/5dea0acbc8484c42006d7bbed32fa019/37ca5/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/4f441/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/23573/doge-computer.png,/static/5dea0acbc8484c42006d7bbed32fa019/036d0/doge-computer.png"
                            alt="Illustration of a doge using a computer">
                        </picture>
                      </noscript>
                      <script
                        type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Divider-sc-1cr9zfr-1 euABDx"></div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <h2 class="dapps__H2-sc-1hb30un-8 ecBNXU"><span>Get started</span></h2>
                  <p><span>To try a dapp, you'll need a wallet and some ETH. A wallet will allow you to connect, or log
                      in. And you'll need ETH to pay any transaction fees.</span> <a
                      class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary undefined"
                      href="..\glossary\index.html#transaction-fee"><span>What are transaction fees?</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                        width="12px" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                        </path>
                      </svg></a></p>
                  <div class="dapps__Row-sc-1hb30un-5 btkUYr">
                    <div class="dapps__StepBoxContainer-sc-1hb30un-16 icIavD"><a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE dapps__StepBox-sc-1hb30un-17 dloeJu"
                        href="..\get-eth\index.html">
                        <div>
                          <h3 class="dapps__H3-sc-1hb30un-18 dZawaj">1. <span>Get some ETH</span></h3>
                          <p><span>Dapp actions cost a transaction fee</span></p>
                        </div><button
                          class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 dapps__StyledButtonSecondary-sc-1hb30un-2 iuRocQ fMmGqe isCBvb"><span>Get
                            ETH</span></button>
                      </a><a class="Link__InternalLink-sc-e3riao-1 gCWUlE dapps__StepBox-sc-1hb30un-17 dloeJu"
                        href="..\wallets\find-wallet\index.html">
                        <div>
                          <h3 class="dapps__H3-sc-1hb30un-18 dZawaj">2. <span>Set up a wallet</span></h3>
                          <p><span>A wallet is your “login” for a dapp</span></p>
                        </div><button
                          class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 dapps__StyledButtonSecondary-sc-1hb30un-2 iuRocQ fMmGqe isCBvb"><span>Find
                            wallet</span></button>
                      </a><a class="dapps__StepBox-sc-1hb30un-17 dloeJu" href="#explore">
                        <div>
                          <h3 class="dapps__H3-sc-1hb30un-18 dZawaj">3. <span>Ready?</span></h3>
                          <p><span>Choose a dapp to try out</span></p>
                        </div><button
                          class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Go</span></button>
                      </a></div>
                  </div>
                  <h3><span>Editors' choices</span> <span size="1" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji-sc-ihpuqw-0 RDZme undefined"><img alt="👍"
                        src="https://twemoji.maxcdn.com/2/svg/1f44d.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
                  <p><span>A few dapps the ethstake.exchange team are loving right now. Explore more dapps below.</span>
                  </p>
                  <div
                    class="SharedStyledComponents__CardGrid-sc-1cr9zfr-15 dapps__StyledCardGrid-sc-1hb30un-29 hCavpi bOajRC">
                    <div class="ProductCard__Card-sc-1be5kz-2 hhUQue">
                      <div class="ProductCard__ImageWrapper-sc-1be5kz-0 iAYtQR">
                        <div data-gatsby-image-wrapper="" style="width:80px;height:80px"
                          class="gatsby-image-wrapper ProductCard__Image-sc-1be5kz-1 iwbsNY"><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACnklEQVQ4y2NQ1HcjGzHAWQqUaFbTc5c1cFUCs4GkHIyhiMpA1wxSauBqrO0VoBooZeiiABYx0PFSAIsrw3QqYBjBADcYqC1YJahENlLQyKlDMq5GJjpBKRToHGlDFxkDV6BOI20vBVzOBuqXNHQpkosEapsjmryHL0dfx3MHf+Y1joItAhlN0rEN0jHauh7yWDVDkK6OZ5Ry8CyxpMO8OXo6nkGqgd8ZK/8zNP5irJsokWCq7Q0PFyw2u6r7zxNJmSeaDAwCLV0PISPnVIWwFUJpy4XSqmSigZZLG7goY2pW0XMXNXKeJJ4wQyzJUcO3RDbKQcOXz9ipXjrmNUvZJoGMWaJJ+XIR8rj8DAwzoCeBHgPaCTTFRtNHysBFQ89jpVDab8aq/wwNvxmrUxVCxQ1dlPXcEZqB1goZOSUohWwUyACGLdB4YSNnoPdU9dyBfjHQ8QSG/1SxxJcs5Rc5i4Dxhx7PwOD5yFxRIxPDbuIAjCEgktd3BdoMVFAiF3mFs/AiV0GefMRyoVSguyD6GSDWAu0BanvOWnqHvchbLQBoM9BaoIUxSiGdknF+agGxyiFAZ79gLXNV95ODBThUs4iRMzCG37CU/meoP8FdIGzkFKQaBNQWrhIENAjoEXEj59VCaUDZFIUwoGIVuJ+BxgBdCAyYu+zFz1hLgFFykDfnP0P1Zc4CIDdSOVjKwFXawBUYi38Yq4FJWMIQGluI5Cmn72qh5aOj6wlkZyqE7+TP8lEL6JaIXyiSBnQI0HInDb9+iQR4aKFHlYyhizxYjt/YyVzbO10hTEPXo1ci3kXdTwmc7YCy8lhTGBDBMxAwhoA2ACMJ6FpgfghVCdIGh7ASasZiwJXRgYqAKUEJHOwSIBfhLQzwmaLvjrUwAABr4BxbLiNfQAAAAABJRU5ErkJggg=="
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/e955217dea1b0260dc78c6c772b1bb27/1deab/uni.webp 80w,/static/e955217dea1b0260dc78c6c772b1bb27/c6aca/uni.webp 160w"
                              sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="80px" decoding="async" loading="lazy"
                              data-src="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png"
                              data-srcset="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png 80w,/static/e955217dea1b0260dc78c6c772b1bb27/40dd2/uni.png 160w"
                              alt="Uniswap logo">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/e955217dea1b0260dc78c6c772b1bb27/1deab/uni.webp,/static/e955217dea1b0260dc78c6c772b1bb27/c6aca/uni.webp"
                                sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="80px" decoding="async" loading="lazy"
                                src="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png"
                                srcset="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png,/static/e955217dea1b0260dc78c6c772b1bb27/40dd2/uni.png"
                                alt="Uniswap logo">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                      </div>
                      <div class="ProductCard__Content-sc-1be5kz-3 bFVwFN hover">
                        <div>
                          <h3 class="ProductCard__Title-sc-1be5kz-4 taKQf">Uniswap</h3>
                          <p class="ProductCard__Description-sc-1be5kz-5 emEoCm">Swap your tokens with ease. A community
                            favourite that allows you to trade tokens with folks across the network.</p>
                        </div>
                        <div class="ProductCard__Children-sc-1be5kz-9 drfjLK">
                          <div color="tagMint" class="Pill__Primary-sc-savco8-0 krObMr">finance</div>
                        </div>
                      </div>
                      <div class="ProductCard__SubjectContainer-sc-1be5kz-6 dQMlUE"></div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv ProductCard__StyledButtonLink-sc-1be5kz-8 kogOj"
                        href="https://uniswap.exchange/swap" target="_blank" rel="noopener noreferrer">Open
                        <!-- -->Uniswap
                      </a>
                    </div>
                    <div class="ProductCard__Card-sc-1be5kz-2 hhUQue">
                      <div class="ProductCard__ImageWrapper-sc-1be5kz-0 hWyMPk">
                        <div data-gatsby-image-wrapper="" style="width:80px;height:80px"
                          class="gatsby-image-wrapper ProductCard__Image-sc-1be5kz-1 iwbsNY"><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABKElEQVQ4y+2TPW6EMBCFDXjxYMxPGyltilwAiSoFSoW2WNEhUeUMlCBOQChYCVEAoskRqDkGt8lAYJOI3SpFimSEzIw1H+8xlgn8IMjfhrXluRGapl3Wa7AKIH3jdV1njKmqigylFBNZlncwAgdgd8CeAORVB1tt2y7L0nEcQojrunVdB0GgKMpFf4NR1gJ6hsPLvMcFR8DzvGmajscj5n3fN03j+z7a2dlexIGC8gYQ6th9Ck5t2w7D8AGP41gUBSac82uwDOwZaAviUWBTeS7TNEWrYRhiWVVVHMeYCCF2/0yB3QN9BfYAnKye8zzPsiyKIiy7rkuSRJKkrwPflBmAWL8CHN/zeHFg2GoYBlq1LMs0zdtHxTb/8DlwtsR8jkv8X4xfhN8Bun5Ps3KwNAkAAAAASUVORK5CYII="
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/5fd111191f0dd4ce3e49301f385faed3/1deab/darkforest.webp 80w,/static/5fd111191f0dd4ce3e49301f385faed3/c6aca/darkforest.webp 160w"
                              sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="80px" decoding="async" loading="lazy"
                              data-src="/static/5fd111191f0dd4ce3e49301f385faed3/4ed6d/darkforest.png"
                              data-srcset="/static/5fd111191f0dd4ce3e49301f385faed3/4ed6d/darkforest.png 80w,/static/5fd111191f0dd4ce3e49301f385faed3/40dd2/darkforest.png 160w"
                              alt="Dark Forest logo">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/5fd111191f0dd4ce3e49301f385faed3/1deab/darkforest.webp,/static/5fd111191f0dd4ce3e49301f385faed3/c6aca/darkforest.webp"
                                sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="80px" decoding="async" loading="lazy"
                                src="/static/5fd111191f0dd4ce3e49301f385faed3/4ed6d/darkforest.png"
                                srcset="/static/5fd111191f0dd4ce3e49301f385faed3/4ed6d/darkforest.png,/static/5fd111191f0dd4ce3e49301f385faed3/40dd2/darkforest.png"
                                alt="Dark Forest logo">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                      </div>
                      <div class="ProductCard__Content-sc-1be5kz-3 bFVwFN hover">
                        <div>
                          <h3 class="ProductCard__Title-sc-1be5kz-4 taKQf">Dark Forest</h3>
                          <p class="ProductCard__Description-sc-1be5kz-5 emEoCm">Play against others to conquer planets
                            and try out bleeding-edge Ethereum scaling/privacy technology. Maybe one for those already
                            familiar with Ethereum.</p>
                        </div>
                        <div class="ProductCard__Children-sc-1be5kz-9 drfjLK">
                          <div color="tagOrange" class="Pill__Primary-sc-savco8-0 ilLUbu">gaming</div>
                        </div>
                      </div>
                      <div class="ProductCard__SubjectContainer-sc-1be5kz-6 dQMlUE"></div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv ProductCard__StyledButtonLink-sc-1be5kz-8 kogOj"
                        href="https://zkga.me" target="_blank" rel="noopener noreferrer">Open
                        <!-- -->Dark Forest
                      </a>
                    </div>
                    <div class="ProductCard__Card-sc-1be5kz-2 hhUQue">
                      <div class="ProductCard__ImageWrapper-sc-1be5kz-0 qWCfY">
                        <div data-gatsby-image-wrapper="" style="width:80px;height:80px"
                          class="gatsby-image-wrapper ProductCard__Image-sc-1be5kz-1 iwbsNY"><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABNUlEQVQ4y2P4TwFgGOma//37B2f//fv39+/ff/78gYj/QQJwZYRtRjYRu80QFQ8fPvzy5QtEZP/+/eXl5dOmTfv+/fu9e/cmT548Y8aMmTNnTpgw4cyZMxCnodhsb2/f2dkJZCxcuJCBgYGZmRlIxsbGzpkzB8hgYmJiZWUFMioqKoBqgJ4CaYZ4bO3atUAJRUXFFy9e2NnZAdns7OxAkp+fPzExkZGREagZIlJZWQnVDHHwt2/fDAwMGMCgtLTUzc0NyGBjYwOSfHx8MTExzGDAwcEBFKmqqoJqhlhbXV0NdycvL29ERAQTGAC5fn5+U6ZMwelsoOXAgKmpqWlqampubgaG04EDBzZu3BgXF1dbW/v+/ftr164BTYfIAnUCZbEEGJmJBGgMcjL4CwOUJpLRLIkCAK0v+hXG8AUTAAAAAElFTkSuQmCC"
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/d8e1643165dd588c97f8fb6649dfed20/1deab/foundation.webp 80w,/static/d8e1643165dd588c97f8fb6649dfed20/c6aca/foundation.webp 160w"
                              sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="80px" decoding="async" loading="lazy"
                              data-src="/static/d8e1643165dd588c97f8fb6649dfed20/4ed6d/foundation.png"
                              data-srcset="/static/d8e1643165dd588c97f8fb6649dfed20/4ed6d/foundation.png 80w,/static/d8e1643165dd588c97f8fb6649dfed20/40dd2/foundation.png 160w"
                              alt="Foundation logo">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/d8e1643165dd588c97f8fb6649dfed20/1deab/foundation.webp,/static/d8e1643165dd588c97f8fb6649dfed20/c6aca/foundation.webp"
                                sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="80px" decoding="async" loading="lazy"
                                src="/static/d8e1643165dd588c97f8fb6649dfed20/4ed6d/foundation.png"
                                srcset="/static/d8e1643165dd588c97f8fb6649dfed20/4ed6d/foundation.png,/static/d8e1643165dd588c97f8fb6649dfed20/40dd2/foundation.png"
                                alt="Foundation logo">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                      </div>
                      <div class="ProductCard__Content-sc-1be5kz-3 bFVwFN hover">
                        <div>
                          <h3 class="ProductCard__Title-sc-1be5kz-4 taKQf">Foundation</h3>
                          <p class="ProductCard__Description-sc-1be5kz-5 emEoCm">Invest in culture. Buy, trade, and sell
                            unique digital artwork and fashion from some incredible artists, musicians, and brands.</p>
                        </div>
                        <div class="ProductCard__Children-sc-1be5kz-9 drfjLK">
                          <div color="tagBlue" class="Pill__Primary-sc-savco8-0 fGTwhD">collectibles</div>
                        </div>
                      </div>
                      <div class="ProductCard__SubjectContainer-sc-1be5kz-6 dQMlUE"></div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv ProductCard__StyledButtonLink-sc-1be5kz-8 kogOj"
                        href="https://foundation.app" target="_blank" rel="noopener noreferrer">Open
                        <!-- -->Foundation
                      </a>
                    </div>
                    <div class="ProductCard__Card-sc-1be5kz-2 hhUQue">
                      <div class="ProductCard__ImageWrapper-sc-1be5kz-0 dxbRkp">
                        <div data-gatsby-image-wrapper="" style="width:80px;height:80px"
                          class="gatsby-image-wrapper ProductCard__Image-sc-1be5kz-1 iwbsNY"><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABnElEQVQ4y6WUz0sCQRTH/adSM23VUolIomPZH6Pr6pqLqScNutSxQ5QFnYrALkEp3SIII4Kw6Ke68+atYzOuECr+quHtMsPMZ97Me+87FtWNfzbLoImEt20e8Z8E9mBcwvAUyjYasVHeGRfmrmJO3JiHIxXKx7RUoAcyUd0Qc4mpYbAgXZhaJJVraLVajAnj7bZIkj4SnxWHGgx7UbbTq33CgabRMpvZudiFiK33/pZut0Y6SBofJscu96B0COYW709G0g89zrtgZQbzawShyVfX3lBx6VoAKBFHhzrLLAMPR2Ig7MD8OtA2/FkFLaBnggA1AZMah+koOMRhsfrrlSpOXfMB1MVe0GDZ0Z5DgLQdYsZONvXTnM57fPT9YqQWSGzYnbtg82NNFKNSAeRpOizafXAnz893kF0BxdlbJwNhqjcfb/ChjMUdSC8RnohhFfYbsHZuqvcgqkqiYavIfz/ZC0cduMU9i2izs7wesXa0pXpGCcMsktwqVCtwvi3EoEoT6VnCxBzV/BC1c1Uakz8GkhBzwvO/l2Qc+wH2Qhkn/jwqYQAAAABJRU5ErkJggg=="
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/0c00567e9eda2398d914c54421535f76/1deab/pooltogether.webp 80w"
                              sizes="80px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="80px" decoding="async" loading="lazy"
                              data-src="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                              data-srcset="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png 80w"
                              alt="PoolTogether logo">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/0c00567e9eda2398d914c54421535f76/1deab/pooltogether.webp" sizes="80px">
                              <img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="80px"
                                decoding="async" loading="lazy"
                                src="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                                srcset="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                                alt="PoolTogether logo">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                      </div>
                      <div class="ProductCard__Content-sc-1be5kz-3 bFVwFN hover">
                        <div>
                          <h3 class="ProductCard__Title-sc-1be5kz-4 taKQf">PoolTogether</h3>
                          <p class="ProductCard__Description-sc-1be5kz-5 emEoCm">Buy a ticket for the no-loss lottery.
                            Each week, the interest generated from the entire ticket pool is sent to one lucky winner.
                            Get your money back whenever you like.</p>
                        </div>
                        <div class="ProductCard__Children-sc-1be5kz-9 drfjLK">
                          <div color="tagMint" class="Pill__Primary-sc-savco8-0 krObMr">finance</div>
                        </div>
                      </div>
                      <div class="ProductCard__SubjectContainer-sc-1be5kz-6 dQMlUE"></div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv ProductCard__StyledButtonLink-sc-1be5kz-8 kogOj"
                        href="https://pooltogether.com" target="_blank" rel="noopener noreferrer">Open
                        <!-- -->PoolTogether
                      </a>
                    </div>
                  </div>
                </div>
                <div
                  class="SharedStyledComponents__Page-sc-1cr9zfr-0 dapps__FullWidthContainer-sc-1hb30un-13 kCvjty dMgOlV">
                  <h2 id="explore"><span>Explore dapps</span></h2>
                  <p class="dapps__CenterText-sc-1hb30un-19 beeJgW"><span>A lot of dapps are still experimental, testing
                      the possibilties of decentralized networks. But there have been some successful early movers in
                      the technology, financial, gaming and collectibles categories.</span></p>
                  <h3><span>Choose category</span></h3>
                  <div class="SharedStyledComponents__OptionContainer-sc-1cr9zfr-29 DZdKn">
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 knVVkh"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="💸"
                          src="https://twemoji.maxcdn.com/2/svg/1f4b8.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div font-size="24px" class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 fEepr">Finance</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="🖼️"
                          src="https://twemoji.maxcdn.com/2/svg/1f5bc.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div font-size="24px" class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 fEepr">Arts and
                        collectibles</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="🎮"
                          src="https://twemoji.maxcdn.com/2/svg/1f3ae.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div font-size="24px" class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 fEepr">Gaming</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="⌨️"
                          src="https://twemoji.maxcdn.com/2/svg/2328.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div font-size="24px" class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 fEepr">Technology
                      </div>
                    </div>
                  </div>
                  <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                    <div class="dapps__IntroRow-sc-1hb30un-6 haNNxO">
                      <div class="dapps__Column-sc-1hb30un-12 jcQINh">
                        <h2 class="dapps__H2-sc-1hb30un-8 ecBNXU"><span>Decentralized finance</span> <span ml="0.5rem"
                            mt="0" mr="0" mb="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 brrTTr undefined"><img alt="💸"
                              src="https://twemoji.maxcdn.com/2/svg/1f4b8.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h2>
                        <div class="dapps__Subtitle-sc-1hb30un-4 kWaAUo"><span>These are applications that focus on
                            building out financial services using cryptocurrencies. They offer the likes of lending,
                            borrowing, earning interest, and private payments – no personal data required.</span></div>
                      </div>
                      <div class="InfoBanner__Banner-sc-10dh6om-1 fJLsnN dapps__StyledInfoBanner-sc-1hb30un-9 lmGJA">
                        <div class="InfoBanner__Content-sc-10dh6om-3 bCybtg">
                          <h2 class="dapps__H2-sc-1hb30un-8 ecBNXU"><span>Always do your own research</span></h2>
                          <span>Ethereum is a new technology and most applications are new. Before depositing any large
                            quantities of money, make sure you understand the risks.</span>
                        </div>
                      </div>
                    </div>
                    <div class="dapps__TwoColumnContent-sc-1hb30un-7 kPrkEk">
                      <div class="dapps__LeftColumn-sc-1hb30un-20 kMsWTL">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Lending and borrowing</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAADX0lEQVQ4y12T/08ScRjHP39Sv9TW1mqrra2yrS+Gls0KQ0UUNYIQKTU0CA5Q4kCRABHi4JALDvA4BEWtlqv1g20tv1TWKqFf+gfUnvtwfsnPbrfPbffa+3k/z/tBqiFWY0j19DG9DxN9vYlBbcKonjLfi1u7Yo6OGNkWdbdS47KITxoO3g6FG0NUwyRdP8FcC7BX/ajTxKqesJrHSe0e3yPytu6YQxkjFcBHD/Hx6wFGEkAKM6s0Yt6wy+sSQ5gf6og+VVAjHTFXe2wU9O9S/ibgw5gPxusDqJXItJvTnUb2nlB/EurXP0r0PogPaqd8w/mAlSe6ovY2ihR4rC/woRdYH8mGs63WNOgDL/gfTKn1Uzbb9I9vf7a2t+H5vl4eH2DtcsqlOMyjOw5OZs/IrRkF6Jswb0h+2ajs7Oxs4wOXjdUy0RmF+klFDPunqv5Ro5OTAg/6RLrDnJYbkhZPEQCGfbf+tbL2+Xc29Q4+vaZp8O9Q0tA/0b80jG6M5vZ4uS3bPJi0Ty7A359Xf61/3VyD98pv+PSP5KF/tm4a5ufE9XtlESTx8nt888i01JQkQovVasuVv+XyX7hsbW09fzZj6IpZVML8hfrbhP6jWn9eMo55kmtycrcsrDmyKNrdPQB7yELffdqkSVTzU+XRJf/MVZ/I3yZzDdaMKfpqByvvHcBd7oJOQw/pmGp+CMyjmmAR+FrMN4zxEnuGeLn0aaOy8qOyvPZreeXn2kZl9cumx1dSa+l+PSPkVzNlxvWj8+FizYTI13v5K44MwS5R8x8fh0pqd07r5Cz+WbawPBZc6NbF9Q+Zg/lHZ6k5ga/qY/+NnrzUnbthS4N/mTndZHipsqQ1prR6INnzf/7RaXruLDV7TuAL2D9f5+X7Em9aPDME81bjLTzyFVz0a5Ulg/Ob0h7YP3QqXjpDl6r8xWCx5jnfHCk5ix8s2SUy936Ue0+m3ob4D922rNIo5q+nX+TRCWYe84I+1H9hoiCZLEp8+ZbQbL2b6wzMykfzOv+ccmS67elu/kEfeH0CHWcXDvD7/i97+bpxvs6Vu+nkGoez+/k37u8/OppZ/J8X9C9i/8L8cP5ukTkx/3j/dvc/hY5wi8eATwn8yXjpNPiPVPtXvBQQ538w/7D/oK/E+/cPquLYrQG1yIEAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/b973c0606272b8eb8cba256e8a30f1ba/4eb25/aave.webp 20w,/static/b973c0606272b8eb8cba256e8a30f1ba/3a6ff/aave.webp 40w,/static/b973c0606272b8eb8cba256e8a30f1ba/1deab/aave.webp 80w,/static/b973c0606272b8eb8cba256e8a30f1ba/c6aca/aave.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/b973c0606272b8eb8cba256e8a30f1ba/4ed6d/aave.png"
                                    data-srcset="/static/b973c0606272b8eb8cba256e8a30f1ba/41fb7/aave.png 20w,/static/b973c0606272b8eb8cba256e8a30f1ba/996dd/aave.png 40w,/static/b973c0606272b8eb8cba256e8a30f1ba/4ed6d/aave.png 80w,/static/b973c0606272b8eb8cba256e8a30f1ba/40dd2/aave.png 160w"
                                    alt="Aave logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/b973c0606272b8eb8cba256e8a30f1ba/4eb25/aave.webp,/static/b973c0606272b8eb8cba256e8a30f1ba/3a6ff/aave.webp,/static/b973c0606272b8eb8cba256e8a30f1ba/1deab/aave.webp,/static/b973c0606272b8eb8cba256e8a30f1ba/c6aca/aave.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/b973c0606272b8eb8cba256e8a30f1ba/4ed6d/aave.png"
                                      srcset="/static/b973c0606272b8eb8cba256e8a30f1ba/41fb7/aave.png,/static/b973c0606272b8eb8cba256e8a30f1ba/996dd/aave.png,/static/b973c0606272b8eb8cba256e8a30f1ba/4ed6d/aave.png,/static/b973c0606272b8eb8cba256e8a30f1ba/40dd2/aave.png"
                                      alt="Aave logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Aave</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Lend your tokens to earn interest
                                  and withdraw any time.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://aave.com/" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACE0lEQVQ4y7VTXUsUYRQ+c96Z953Zmd0Zd20x8E4l+gC7EL0RgkAT6s4LA1Ek2t0Msww0UNA7FTVNtNg0NT/oJi/8A4LQH6gQFKPoQsv+hee46+xus24RBMMwc855zvOcL1ChyD8/8N/ApqNsV9l/BAcjrIiKxpQe4hRulLPkvG5JZgoVlpi4L8buqYoKBYotjvt3skmIEcKlPvixqu2M610tbBRWIX9RMAkmnSBwJgU/1+DzPBwsaO8HZd0VriIPHwCTNlBipENMJeXVajHQBntpAsPxGqYfsX6nBDhSpqQjppPwa0PbHjXu3pSNtbjcB4dv8cUDhWZJMDMj0bLgT/PwfRnfDcjrlwiJb56cz0xWzRTP2uiR12rEVAK+LsLuS+oZzj3UO5tx5WkA7DeArGjiqx44XsfNIaOp3mhp0LaG4WgVnyf15J1izGFPWeFTsMetmu1mwftp+LYkZlLychUuPsbXvXriNulXILNgmkjYA/7JkNPbsKnDuN4PXxbg4xxJEJMJvfUGbvRT56jzOWYK9qLAhF40S366WLSPeuct7cME60/36u1NVEs2wDpbBI8XFjICOIVfuc2jlpUXcbYbp1PqQlzF48SYq9ZhWr/bLo83f2C0YRQtbRUr54+MKN8b9gpHRaoIT/xW3m3YmavMW1vi9BtcMGf7zBe4nmwuchWeNxQJojP+rQrHNctiwZs/AYIAfjZ9HY4gAAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/d98d03d5b2d51b5e9680164c50f4b803/4eb25/compound.webp 20w,/static/d98d03d5b2d51b5e9680164c50f4b803/3a6ff/compound.webp 40w,/static/d98d03d5b2d51b5e9680164c50f4b803/1deab/compound.webp 80w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/d98d03d5b2d51b5e9680164c50f4b803/4ed6d/compound.png"
                                    data-srcset="/static/d98d03d5b2d51b5e9680164c50f4b803/41fb7/compound.png 20w,/static/d98d03d5b2d51b5e9680164c50f4b803/996dd/compound.png 40w,/static/d98d03d5b2d51b5e9680164c50f4b803/4ed6d/compound.png 80w"
                                    alt="Compound logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/d98d03d5b2d51b5e9680164c50f4b803/4eb25/compound.webp,/static/d98d03d5b2d51b5e9680164c50f4b803/3a6ff/compound.webp,/static/d98d03d5b2d51b5e9680164c50f4b803/1deab/compound.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/d98d03d5b2d51b5e9680164c50f4b803/4ed6d/compound.png"
                                      srcset="/static/d98d03d5b2d51b5e9680164c50f4b803/41fb7/compound.png,/static/d98d03d5b2d51b5e9680164c50f4b803/996dd/compound.png,/static/d98d03d5b2d51b5e9680164c50f4b803/4ed6d/compound.png"
                                      alt="Compound logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Compound</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Lend your tokens to earn interest
                                  and withdraw any time.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://compound.finance/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC1klEQVQ4y41UaU8TURSd/+BfUAxCLVDBCJoYCfgJQoWyKK1g1KhfXCIoRmLQj5pIoqiJS1KlnU5no2UeoJbFgFEWgyJRiAQCsgiRQNvp7GO9tVKrgJCcvNw3Oeee++67b7AjyLUeyhEO+A8BW0vjBE0pclsQBYDgVxbnxmIrchYjqgjRxzl7FdcAOMHZixAFH62rqsD+UYJVLVff3WT7Rh8I0llBOnOe3t/Nltc211sieufaYqitEDF2b7VEmnRih0AYQ0RKyJ0mUhk6lSZR6XZPFRDiu4DFPAta2EeemjBp4Jm9wRaz0GYR2ooFlM/jhgBuCNF7wuTOh54aoMX8sagnHOk89wAYgitJfHNFFxa0uR5t/q228E77/kHsOhV0GUQmM0RlXODuAznqj0VtzS2sp+l0mE7zP9sq9V5VvhDBJ1t4ZyLvSBB9Nn1pVOyo9DcmhKlUoJlXzH+XfRgRw6xZIZKF9kptcUhbGlVG7JACIH9sAH89NCv2XVPcxmGmAMixsvEyRFSgxlk6W3KnhlCeOtmqzfdLby7JAzfk/jq5r04evBkR95yTCOMMnQ3ksogex6K2R5FjismVCYPQcUxbHNb84+oYpU40RzDepEx4dYWXBm8BYYrOBXLU/E/D+tkSzW3kvbnKGKnOdIntFVL3WfHVGdFn1RYG1KmXfGuhRqb2sSWrGobYx57LkYY1bpN7a7XpzpA3R3huEV+UKSNPofMCuy/gMoapFKABOb5heKRyzjHJHNRdiXznSXXutfrVp053wCoP3YErDLjTddo0Sees1IzHDwkO81zdfHeZztKJZD8MhssYBOBJAcf2ALkbhmyZygIC0GJDjsUPNkwfvIRPTL5OmYCtUrsAPyiTRqV+ZvIucveAED/efz0MG3KWIhIS3/Ze97GV75lDAAhga408UtK23sOI+cMKtYEJXCYAAthGj7bxzyCaIlZefLwp8SbxE+cZ51k8osE/AAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/c9f325b9c465f03c512f16e83a0a2770/4eb25/stabledai.webp 20w,/static/c9f325b9c465f03c512f16e83a0a2770/3a6ff/stabledai.webp 40w,/static/c9f325b9c465f03c512f16e83a0a2770/1deab/stabledai.webp 80w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/c9f325b9c465f03c512f16e83a0a2770/4ed6d/stabledai.png"
                                    data-srcset="/static/c9f325b9c465f03c512f16e83a0a2770/41fb7/stabledai.png 20w,/static/c9f325b9c465f03c512f16e83a0a2770/996dd/stabledai.png 40w,/static/c9f325b9c465f03c512f16e83a0a2770/4ed6d/stabledai.png 80w"
                                    alt="Oasis logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/c9f325b9c465f03c512f16e83a0a2770/4eb25/stabledai.webp,/static/c9f325b9c465f03c512f16e83a0a2770/3a6ff/stabledai.webp,/static/c9f325b9c465f03c512f16e83a0a2770/1deab/stabledai.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/c9f325b9c465f03c512f16e83a0a2770/4ed6d/stabledai.png"
                                      srcset="/static/c9f325b9c465f03c512f16e83a0a2770/41fb7/stabledai.png,/static/c9f325b9c465f03c512f16e83a0a2770/996dd/stabledai.png,/static/c9f325b9c465f03c512f16e83a0a2770/4ed6d/stabledai.png"
                                      alt="Oasis logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Oasis</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Trade, borrow, and save with Dai,
                                  an Ethereum stablecoin.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://oasis.app/" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="dapps__RightColumn-sc-1hb30un-21 fQxgud">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Token swaps</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACnklEQVQ4y2NQ1HcjGzHAWQqUaFbTc5c1cFUCs4GkHIyhiMpA1wxSauBqrO0VoBooZeiiABYx0PFSAIsrw3QqYBjBADcYqC1YJahENlLQyKlDMq5GJjpBKRToHGlDFxkDV6BOI20vBVzOBuqXNHQpkosEapsjmryHL0dfx3MHf+Y1joItAhlN0rEN0jHauh7yWDVDkK6OZ5Ry8CyxpMO8OXo6nkGqgd8ZK/8zNP5irJsokWCq7Q0PFyw2u6r7zxNJmSeaDAwCLV0PISPnVIWwFUJpy4XSqmSigZZLG7goY2pW0XMXNXKeJJ4wQyzJUcO3RDbKQcOXz9ipXjrmNUvZJoGMWaJJ+XIR8rj8DAwzoCeBHgPaCTTFRtNHysBFQ89jpVDab8aq/wwNvxmrUxVCxQ1dlPXcEZqB1goZOSUohWwUyACGLdB4YSNnoPdU9dyBfjHQ8QSG/1SxxJcs5Rc5i4Dxhx7PwOD5yFxRIxPDbuIAjCEgktd3BdoMVFAiF3mFs/AiV0GefMRyoVSguyD6GSDWAu0BanvOWnqHvchbLQBoM9BaoIUxSiGdknF+agGxyiFAZ79gLXNV95ODBThUs4iRMzCG37CU/meoP8FdIGzkFKQaBNQWrhIENAjoEXEj59VCaUDZFIUwoGIVuJ+BxgBdCAyYu+zFz1hLgFFykDfnP0P1Zc4CIDdSOVjKwFXawBUYi38Yq4FJWMIQGluI5Cmn72qh5aOj6wlkZyqE7+TP8lEL6JaIXyiSBnQI0HInDb9+iQR4aKFHlYyhizxYjt/YyVzbO10hTEPXo1ci3kXdTwmc7YCy8lhTGBDBMxAwhoA2ACMJ6FpgfghVCdIGh7ASasZiwJXRgYqAKUEJHOwSIBfhLQzwmaLvjrUwAABr4BxbLiNfQAAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/e955217dea1b0260dc78c6c772b1bb27/4eb25/uni.webp 20w,/static/e955217dea1b0260dc78c6c772b1bb27/3a6ff/uni.webp 40w,/static/e955217dea1b0260dc78c6c772b1bb27/1deab/uni.webp 80w,/static/e955217dea1b0260dc78c6c772b1bb27/c6aca/uni.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png"
                                    data-srcset="/static/e955217dea1b0260dc78c6c772b1bb27/41fb7/uni.png 20w,/static/e955217dea1b0260dc78c6c772b1bb27/996dd/uni.png 40w,/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png 80w,/static/e955217dea1b0260dc78c6c772b1bb27/40dd2/uni.png 160w"
                                    alt="Uniswap logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/e955217dea1b0260dc78c6c772b1bb27/4eb25/uni.webp,/static/e955217dea1b0260dc78c6c772b1bb27/3a6ff/uni.webp,/static/e955217dea1b0260dc78c6c772b1bb27/1deab/uni.webp,/static/e955217dea1b0260dc78c6c772b1bb27/c6aca/uni.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png"
                                      srcset="/static/e955217dea1b0260dc78c6c772b1bb27/41fb7/uni.png,/static/e955217dea1b0260dc78c6c772b1bb27/996dd/uni.png,/static/e955217dea1b0260dc78c6c772b1bb27/4ed6d/uni.png,/static/e955217dea1b0260dc78c6c772b1bb27/40dd2/uni.png"
                                      alt="Uniswap logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Uniswap</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Swap tokens simply or provide
                                  tokens for % rewards.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://uniswap.org/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACzElEQVQ4y6WRiU/TcBTH+2+aaDQcTm4IQUUDZMCADYiEQyQaE2MMaMSYZes4CgS8AHfQtWu3tlvXdsO2GwwNY0cPfKNCloCaaPJJ89r3/bzfa4vwkpkQDQCKy1itK4EucqVzbppnoQrV0/8ig/bHoefylSvFBZ1L6ixfAYr4bzZHIFeFwfEGyZSJaCnKlVleYxM6zZXJaIliylwShhrVeSSeNC+IxvUQWYgni6lMjs8mYgpBKzircIKSZflCiCxCoDqPsAmzAm8ycX0neCLsfyfUDUyaRcUxr+j0iMNewbkqzgTkVUbMfN0rQIyzlERFNgDYZ3evGAjn/Yp7ke/1iIOoOIyKQ6g0gkrDcPsu0bMlvQ4QxyGysr9lIQxvxEAWja0v+dWPuZXUlEca8IkuVBhekkaXJBcqgu/ySHY3P7a1m9sNFjnBAAtAYgkd3oQTTj9s56fnMliQxqQ5X9qBpuyoZPelBn0puDqWhccbODX/9mgnWGCTJlgAEoufWoSIcs+A6HCpC27V599bo1fWufcbcfd6DMNCuAfLPH2em55VwpTGJH4pCM2eAhRjRmLlyScZW0uyqUPo6pb7R7Ljk9mJmYNHU4ej4wcOp2wfSr+cz9GsTrOmZSFnlUFGtTBdWlg8aulMN7RLNY3JW3f42gbe1pps6xK7+9L2IdnhVH3YMcgUY5xZJkIxeiSmE7QeCBeX137c71Prmr/Vt+7XNKfrWmBQuuOe/MCuPOxXJ2cP4WsRtBaJGWABCBnVLYJEafPz8bMXh7dblWu18vV6+YZNrm2SG9qV5k61bzDrXcmHyBIcQ0YNS0HOqgphSvfj4OdfvTnqHczY2tSbNqWmUW27mxmdyHmXj/14GY9oJG1cKAhMqmYvogXCpW1/YW0z78PyS1h+89MJDIXnOKURtFEdRuDAKgy8gmYBfyVMAzpOVQhfArEa/8Z/yT8BX1Dr9EBwtuUAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4eb25/matcha.webp 20w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/3a6ff/matcha.webp 40w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/1deab/matcha.webp 80w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/c6aca/matcha.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4ed6d/matcha.png"
                                    data-srcset="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/41fb7/matcha.png 20w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/996dd/matcha.png 40w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4ed6d/matcha.png 80w,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/40dd2/matcha.png 160w"
                                    alt="Matcha logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4eb25/matcha.webp,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/3a6ff/matcha.webp,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/1deab/matcha.webp,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/c6aca/matcha.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4ed6d/matcha.png"
                                      srcset="/static/9ec6474d406334ed53fcb9ca5ecf0cbd/41fb7/matcha.png,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/996dd/matcha.png,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/4ed6d/matcha.png,/static/9ec6474d406334ed53fcb9ca5ecf0cbd/40dd2/matcha.png"
                                      alt="Matcha logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Matcha</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Searches multiple exchanges to
                                  help find you the best prices.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://matcha.xyz" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFKklEQVQ4y32UC0yTVxTHT1ucGhAVHyA63jAVhpGXpQg4UKFSWWkpFCiFAi2PltJaLSAPKQ9lbOqQl1FEmBOQASo+hsYHU2SoY86hmUyj0QXj1Bl1mE1o79kHxHVmmye5yU2+//nlnPOd+wegwkekghBOIkCwEGzXJ9H6JGo6yFTwKjKNzufJGBYhCdQnBQTEKsE7QgbLY5TwzmAlaMA3JgOAlwwWwnQ4lJUPkJr13hPpJlidqp3QICKARzjEJmnAlRkOPqHxwFHq/hvom7ABmKIsGsRnwRSh3EqasrFKJ5QPZUdlNPokqNSUhDYBpKJjBGmh6m0mtuwk4MuzwUukAQ+hEtyj5EagZ2wWgK4aIL1oagg/re04k4/37IJw0DsC+9eKUSfT9nKTNRXj0PEj1lVN5E1bEUFPKKmdrN7U1Qhck5YPdekF9EKVDmIytzTWs6IwOznndbU7Z/TmTK/Ra/7R+HW0fGRLkuayUPtpFze7QkGlzQB7JnBVOtokxcQInB8UCz/+8pB+ZvAniCvYsffAmUs48GB47MsDnWRP3UHSKVCO9S0KwKuxSmRv2Y2K/acxsnRfL801SOgmVJlZ+Atg9spII3AeBaTKnphTy6lLnef6LuDhU6fHjp7rJc/+fE0au88iNzKNnF3K1h9P0Ywt4MhGw3Z2oUOMdmTqB36zpn4YDPYRctrfwMCUXHgzn/1dZ85evj6Inze16RM3l+Ode/fJlR9uks+a2zBXlkMGg+PRLpBPXBSVGFB66NV0K4f5pi7e4CUtMgIFm7bRY/O2Q4i80L+mtQu31jQQdsFuwkovJhf6r+LjFyOkqf0YKdvdSBQro9HWK8zglFmJHjkNt6n06eMMq1CJseXQjHyarKwWOOrSpSkFFUPReTvRMbHIYPlRDLk+dBefvnxFunuvkIEbt0huVT2a+vIMjpm1aCvMvm/utnKJ6WImWK2OM1Z4uu87kO9oMlFVHgA/aV5edOVhZPjwxioa28kfY3py9+Fjcnf4Ed7/9Sk5/81FXKvQGdwK29A+WjNMLY8Z0GfCvOA4Y4U+IvWb6zRp3dE7i2VbcQaTa3j6/CWe7Okj3X0DOPzbc/Lo2Qv8or2LuKrr9KxPjqGDQKVwFG0G5+QShvP6f7TsFp9N94hTgys/nRVc1oLvc+VEvrWaNHScIEU1TWTUgOTOg2Fs6eom6wv36O21B9FFnH/NzGGZNd3SEeYFCGhvPb0FIYmMKaxoYKzgiRJK63B7Q4uhqvkoqe84SXr6B8iNWz/jocNdZIlQrXfIa3/tmduAs/wFF23WJc029wyh1i6OtkqaawTahooZsGwdgDs7SlfThO6RaYYjPf0kXbeTmDG5ZJU4S79UnKd3yGlF94JWdJboeqzXiq0tAwVgzZHRLAKioOf7G0agn0RLA6dgqlSWXVXzkZE10mxcLlSOnv/26piuuoHYCDai08ZGdFPuum3HU8jH99UyKAasQhLpVmGpYBEofBsYJM2B2tZjjH0dJyCzvHavurwGF4pLkF3UgCnFu9A3UTtkyUnjU9IpzvGTrc0JENDnBsWBNTeTWhnR2/bFySoGZ04S3UcohyXhyd5rUjZ128fmKMzDFEqY6+U3DrKJnNwEp8QixlzWxzCHyYGF4Rlgw5b82w9duDKwploIFClghmfoxBN0lhSDeQRlrnM8KYUFLOCpGYt56UBzCZic+zrJ/zv2vs5TEKctA/z9CWjKayaAi/gbTGwEGhPqD9LdeKkTuvrmr8CWLXmn+/8F6Tc8B2o/5vwAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/4a89729afdf4544a19ec104cf2c35941/4eb25/1inch.webp 20w,/static/4a89729afdf4544a19ec104cf2c35941/3a6ff/1inch.webp 40w,/static/4a89729afdf4544a19ec104cf2c35941/1deab/1inch.webp 80w,/static/4a89729afdf4544a19ec104cf2c35941/c6aca/1inch.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/4a89729afdf4544a19ec104cf2c35941/4ed6d/1inch.png"
                                    data-srcset="/static/4a89729afdf4544a19ec104cf2c35941/41fb7/1inch.png 20w,/static/4a89729afdf4544a19ec104cf2c35941/996dd/1inch.png 40w,/static/4a89729afdf4544a19ec104cf2c35941/4ed6d/1inch.png 80w,/static/4a89729afdf4544a19ec104cf2c35941/40dd2/1inch.png 160w"
                                    alt="1inch logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/4a89729afdf4544a19ec104cf2c35941/4eb25/1inch.webp,/static/4a89729afdf4544a19ec104cf2c35941/3a6ff/1inch.webp,/static/4a89729afdf4544a19ec104cf2c35941/1deab/1inch.webp,/static/4a89729afdf4544a19ec104cf2c35941/c6aca/1inch.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/4a89729afdf4544a19ec104cf2c35941/4ed6d/1inch.png"
                                      srcset="/static/4a89729afdf4544a19ec104cf2c35941/41fb7/1inch.png,/static/4a89729afdf4544a19ec104cf2c35941/996dd/1inch.png,/static/4a89729afdf4544a19ec104cf2c35941/4ed6d/1inch.png,/static/4a89729afdf4544a19ec104cf2c35941/40dd2/1inch.png"
                                      alt="1inch logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">1inch</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Helps you avoid high price
                                  slippage by aggregating best prices.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://1inch.exchange/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="dapps__TwoColumnContent-sc-1hb30un-7 kPrkEk">
                      <div class="dapps__LeftColumn-sc-1hb30un-20 kMsWTL">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Trading and prediction markets</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACYklEQVQ4y52UXUiTURjHd6GlRrM2UzMr0zuhCPqgLBCjrW069s651HajjMpqi5IMDPKiCCkpoqKLohFGQnRh2oXSjVAtL4pwfZGzfVlzH+F5zt7vZWLnNRxUe02F5+pwfuf/P//nPEeRX4uXXIoF7ltbhwvrFgMXWCQm34JVpkSWDnL08H9YErHgPDNeWY0zNLDKCJvt6Nh1uusR3tCA8ih5eLUJL9dC1gFYXw/V5/DVx+xzD+ePiKPjfByEMhtSUZKjv+E1tUQNbbGD8ybd+5LzfuPHwmL/MNd+l956GCqcyD/Bl9lAZfoTLiCkGRdZUf8rLvlj6p2f6exJ7GuFYitk6xG5ao4BVzghFONLD/0LW7CawsRSHJKkPgVZ1yDjuMHsaCEJQbYOZezHuxzyMFkicCAqVrUiTRvc6mXcH7hQLPl6lHcNMFQH1rShYDSt7Tk4FBN3n4BMLSaNUZlIyLjpCn27j/b4WMwlw9/FTY1ITc0DO0g/sXRPPRTXg+YMXHpIv/WyP6enIpNiyTxwMCrscaJtR+C8i3k6zI6FBe9X/ombO3qNNndMBiIyttWzcAySn0PCx4DQ52Y7e1hDOy5phBUGpKiCncflApM6jNdZ0Yv3/MzM9MgX7uwdurwZKWtIzkDembIG7z0pA6eq6CCqPAUXH7BDI9x4XPT4+PuDbPNloo+2tyB/WtupIvpKI87UEE0ob0L2rkT3M9bj43wT4hsvFwOh1AZqSgZOTdLvFLJ1sEyLCi1QeRpd6KbvDSQ2NsymvZCRJANMDiIvN1eyA7lGlNJczGcwZ2eJP0na+gWyUHhmCe2rSgAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/18a288731dbbdc4594bc8be05535cbd4/4eb25/polymarket.webp 20w,/static/18a288731dbbdc4594bc8be05535cbd4/3a6ff/polymarket.webp 40w,/static/18a288731dbbdc4594bc8be05535cbd4/1deab/polymarket.webp 80w,/static/18a288731dbbdc4594bc8be05535cbd4/c6aca/polymarket.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/18a288731dbbdc4594bc8be05535cbd4/4ed6d/polymarket.png"
                                    data-srcset="/static/18a288731dbbdc4594bc8be05535cbd4/41fb7/polymarket.png 20w,/static/18a288731dbbdc4594bc8be05535cbd4/996dd/polymarket.png 40w,/static/18a288731dbbdc4594bc8be05535cbd4/4ed6d/polymarket.png 80w,/static/18a288731dbbdc4594bc8be05535cbd4/40dd2/polymarket.png 160w"
                                    alt="Polymarket logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/18a288731dbbdc4594bc8be05535cbd4/4eb25/polymarket.webp,/static/18a288731dbbdc4594bc8be05535cbd4/3a6ff/polymarket.webp,/static/18a288731dbbdc4594bc8be05535cbd4/1deab/polymarket.webp,/static/18a288731dbbdc4594bc8be05535cbd4/c6aca/polymarket.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/18a288731dbbdc4594bc8be05535cbd4/4ed6d/polymarket.png"
                                      srcset="/static/18a288731dbbdc4594bc8be05535cbd4/41fb7/polymarket.png,/static/18a288731dbbdc4594bc8be05535cbd4/996dd/polymarket.png,/static/18a288731dbbdc4594bc8be05535cbd4/4ed6d/polymarket.png,/static/18a288731dbbdc4594bc8be05535cbd4/40dd2/polymarket.png"
                                      alt="Polymarket logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Polymarket</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Bet on outcomes. Trade on
                                  information markets.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://polymarket.com" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB/UlEQVQ4y61TTU8aURQd0RodmBkGlIYpYwWMJkSJsaSuTEpidNsFi+7oR9JuTLsbt7qA7pq4tX+hJhp/if4A4w9Ad7Dk9J1L32RQYPr1khNO7j33zLnzBsO2i/gP6FvW4NeIE1vWMmynKBD+L4bazJrzBRNM4w2jZm7lhcLWJNPfSMjB5HM4uVWsXX8XkLMmr+BPDCWBW0bKeAr/6wHWrk4F5Kyx9yDleEMRpktIPXmGzOZLVO5+YHFvV0DOGnvURExjVlYrpabyKF+0UTpvIzXjCchZY892SvEJdTo74cF720DlXiXaqIW3nKnWpMYeNZGU4w2ddBkzxgKSr3bgfXojySSNAjlr7FFDrTbUSQ1tRKSVwDAyCA6P8bH5GdOKc0g/kDyhauxRQ216YNrXxmFCRyWYnc1je3sfPO8/fFEDWWQXVsMHkrPGHg+1nFGzwwkJ110RcRAcibjV+gbTXEIikZMeQW4ml6THEwTHMqN6jw01kurDbTYP0O32cHNzi3r9tRpyZT1y1nq9nmio1UYj36E25VMLhSrOzi4lycnJqYCHNd+viiZymY/fYfjZ/Fp/fr4gqRqNd+h07hTuhdOIPWoiM+NXDj8fdUmDW8/C89YF5KyxN+mv1x8FrsFf1y33TdMXkEd7UW24Mp1HYThtURBNNaxfRmzCv8VPmGA3Ea1gfDIAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/5899d367702bd4c154540519dab9804e/4eb25/augur.webp 20w,/static/5899d367702bd4c154540519dab9804e/3a6ff/augur.webp 40w,/static/5899d367702bd4c154540519dab9804e/1deab/augur.webp 80w,/static/5899d367702bd4c154540519dab9804e/c6aca/augur.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/5899d367702bd4c154540519dab9804e/4ed6d/augur.png"
                                    data-srcset="/static/5899d367702bd4c154540519dab9804e/41fb7/augur.png 20w,/static/5899d367702bd4c154540519dab9804e/996dd/augur.png 40w,/static/5899d367702bd4c154540519dab9804e/4ed6d/augur.png 80w,/static/5899d367702bd4c154540519dab9804e/40dd2/augur.png 160w"
                                    alt="Augur logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/5899d367702bd4c154540519dab9804e/4eb25/augur.webp,/static/5899d367702bd4c154540519dab9804e/3a6ff/augur.webp,/static/5899d367702bd4c154540519dab9804e/1deab/augur.webp,/static/5899d367702bd4c154540519dab9804e/c6aca/augur.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/5899d367702bd4c154540519dab9804e/4ed6d/augur.png"
                                      srcset="/static/5899d367702bd4c154540519dab9804e/41fb7/augur.png,/static/5899d367702bd4c154540519dab9804e/996dd/augur.png,/static/5899d367702bd4c154540519dab9804e/4ed6d/augur.png,/static/5899d367702bd4c154540519dab9804e/40dd2/augur.png"
                                      alt="Augur logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Augur</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Bet on outcomes of sports,
                                  economics, and more world events.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://augur.net" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABoklEQVQ4y8WUzytEURTH325khUZshIWl/0D5B2yMWQyahSGlrCWlWGjWNjaSGkWpwQL50RSjMZoiv0oiFpKRMDXNvHfvue8e577HvElvMYNyu93u7Z7P95xzz3lP478Y2t/DAD+Gged0DuXCBuOIPHkFmylBG8MoGWbWKk3eMSkjMQXrpcNkSsD8rqjw4VqyHFgFLPl9GlqHZVUAVw8dmDF1WzyZC4x8aEaR9UFcSTiwlOqqeJpmEWwHvJEStb3YGEJaCzDV7PgGYqcQv4CDS9g7h/UjuH0EUyj/GkVFSu9ZaBuRNuz9gnN5JTq3LaoD2NSPNd3Y0IfjC2b6jQuh0tEMy+1YxKz0Y/OAui7Auq4saOMPS08ndoVl6hroSORn2Mx6qmhCtAyit0d5KMB5S5cC9k3J2S1BfaOKz5Sik7Mtf3YH7aNIEdYFMWrBZLe0LygoSpKOtqVbqSwnLxkITUuPT5WKnoQkdk6EMFXnuHab0yR0TS9H+Uwsmstx8fAMT69KkbHvDt17m4yoNiSRyXLyTBuDlflVkb0Azv7nZ1Di+ACxtFIDoww/0AAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/f9cc45471985ec33d3c286c22e19bd0e/4eb25/loopring.webp 20w,/static/f9cc45471985ec33d3c286c22e19bd0e/3a6ff/loopring.webp 40w,/static/f9cc45471985ec33d3c286c22e19bd0e/1deab/loopring.webp 80w,/static/f9cc45471985ec33d3c286c22e19bd0e/c6aca/loopring.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/f9cc45471985ec33d3c286c22e19bd0e/4ed6d/loopring.png"
                                    data-srcset="/static/f9cc45471985ec33d3c286c22e19bd0e/41fb7/loopring.png 20w,/static/f9cc45471985ec33d3c286c22e19bd0e/996dd/loopring.png 40w,/static/f9cc45471985ec33d3c286c22e19bd0e/4ed6d/loopring.png 80w,/static/f9cc45471985ec33d3c286c22e19bd0e/40dd2/loopring.png 160w"
                                    alt="Loopring logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/f9cc45471985ec33d3c286c22e19bd0e/4eb25/loopring.webp,/static/f9cc45471985ec33d3c286c22e19bd0e/3a6ff/loopring.webp,/static/f9cc45471985ec33d3c286c22e19bd0e/1deab/loopring.webp,/static/f9cc45471985ec33d3c286c22e19bd0e/c6aca/loopring.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/f9cc45471985ec33d3c286c22e19bd0e/4ed6d/loopring.png"
                                      srcset="/static/f9cc45471985ec33d3c286c22e19bd0e/41fb7/loopring.png,/static/f9cc45471985ec33d3c286c22e19bd0e/996dd/loopring.png,/static/f9cc45471985ec33d3c286c22e19bd0e/4ed6d/loopring.png,/static/f9cc45471985ec33d3c286c22e19bd0e/40dd2/loopring.png"
                                      alt="Loopring logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Loopring</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Peer-to-peer trading platform
                                  built for speed.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://loopring.org/#/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABwElEQVQ4y62U2WrCQBSGfQIvRePauAStO5GIokJQL1RExV3UoAiCN0IufQB97H502pSmsVLqXAyTOec//3+WievlH8v1HHAkEgkGg6FQ6M9gkPF4vFqtZjKZcDjMDXs0Gn0MlmU5EAhsNpvZbNZoNEajEchKpdJsNv1+P1Z8EOUMhsHn883n8+Px6PF4hsNhr9dbrVbZbJYsALOXy+VYLIZAOxgbfsvlst/vb7dbSC6XC2ev1wtAkiQktFot7oWKLzDfmLGh2e12n04nwu92u1KphDey6/V6p9Ph4Cwb70QigU7S7na7KEd2LpeDGbVIuNeCj4JRIXbEW4WhhIVCgfyFg02ww5CIECxCpNPp8XhMLeEnL8iFlYNVNvuECbOiKJPJhFy4gXw6naqqSjgyz+fz3Au8HUx4KgwnrvSPzM/n8+v7MgwDJKW5O9swDwaDYrFIzoRPpVKmae73e4Iyf9frlRG0evYNjDeCqRNm+GGm7bDVajWU07zb7SbiOoAFXkyVGLv1eq3rervd5rBYLKA9HA7WqP32JIUQ8qf5dBFFVF7TtGQyKR7Pg/cMnj5RZLytOb/bqp8L/fLneuaf5A3DmsAEtLU/zwAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/4e6de45b8f744980609e79015b986e09/4eb25/dydx.webp 20w,/static/4e6de45b8f744980609e79015b986e09/3a6ff/dydx.webp 40w,/static/4e6de45b8f744980609e79015b986e09/1deab/dydx.webp 80w,/static/4e6de45b8f744980609e79015b986e09/c6aca/dydx.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/4e6de45b8f744980609e79015b986e09/4ed6d/dydx.png"
                                    data-srcset="/static/4e6de45b8f744980609e79015b986e09/41fb7/dydx.png 20w,/static/4e6de45b8f744980609e79015b986e09/996dd/dydx.png 40w,/static/4e6de45b8f744980609e79015b986e09/4ed6d/dydx.png 80w,/static/4e6de45b8f744980609e79015b986e09/40dd2/dydx.png 160w"
                                    alt="page-dapps-dydx-logo-alt">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/4e6de45b8f744980609e79015b986e09/4eb25/dydx.webp,/static/4e6de45b8f744980609e79015b986e09/3a6ff/dydx.webp,/static/4e6de45b8f744980609e79015b986e09/1deab/dydx.webp,/static/4e6de45b8f744980609e79015b986e09/c6aca/dydx.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/4e6de45b8f744980609e79015b986e09/4ed6d/dydx.png"
                                      srcset="/static/4e6de45b8f744980609e79015b986e09/41fb7/dydx.png,/static/4e6de45b8f744980609e79015b986e09/996dd/dydx.png,/static/4e6de45b8f744980609e79015b986e09/4ed6d/dydx.png,/static/4e6de45b8f744980609e79015b986e09/40dd2/dydx.png"
                                      alt="page-dapps-dydx-logo-alt">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">dYdX</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Open short or leveraged positions
                                  with leverage up to 10x. Lending and borrowing available too.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://dydx.exchange/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="dapps__RightColumn-sc-1hb30un-21 fQxgud">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Investments</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACCUlEQVQ4y52UyW/TQBTG/X/10kOFQGK5INEgoIhCywFxgEPLIq4cgB6AE6pYCncE3WSB2yxtaRIQyNCoNVQKIcLb2K7tiTNeZsxzA2nV1GnE05w8+s33lu+Zi7sEY8lJD25faPscHFyaaIR/+ehzYFUoQWlvcR2aMSWGs/bYKo1tfbnrrk96tZnQqsQ06A4nJAsb5up1TThllW7Y4oQrTXm1OaJ+CN2fMaPp8PYdlqaUmX60dNksjdviAyy9AGVfK4fOJgu9rmkzqueHlfnDqDBilm/aXyfw91fN+rsEtjdZgNPgVs6eyh9T+aMoN2wsXzFXr1nlW87aI6IsQ/9Y4HSDYxrqi2fVuUO6MKgvnkHZ83r2nCZkUGGUyIWY+nvavrdm59vD32/7dCEDr4A+HD07pPLHdeF01KjvyHTAydfQrWrCoMafAOYfnIjLb/qcypO2xr4NY3HkN+WcURjVFjKAoewQ5A9VKLMDKH8pPe0WHjgRrhFlCfpkfbwNM4fOodwFZf4IDJ8lZe9k3gm7MBWilZp1Hv94CaOGgaP8RXl2wFi5eoC3WYjBD0QrerVpLD2zxXtmcQzlR+Tp/kb1dcv16YvBaORWiboCxsLSc1u8bxbHtfcnrU93dmPpW0V9WCaA3Y1J2A0wubvxlP51COtpJSkxg631wBBpU20Pssd97un3wP0H044/IcJS0Gqas8gAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/fd6bd24104a350c4dbeaa27608802424/4eb25/set.webp 20w,/static/fd6bd24104a350c4dbeaa27608802424/3a6ff/set.webp 40w,/static/fd6bd24104a350c4dbeaa27608802424/1deab/set.webp 80w,/static/fd6bd24104a350c4dbeaa27608802424/c6aca/set.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/fd6bd24104a350c4dbeaa27608802424/4ed6d/set.png"
                                    data-srcset="/static/fd6bd24104a350c4dbeaa27608802424/41fb7/set.png 20w,/static/fd6bd24104a350c4dbeaa27608802424/996dd/set.png 40w,/static/fd6bd24104a350c4dbeaa27608802424/4ed6d/set.png 80w,/static/fd6bd24104a350c4dbeaa27608802424/40dd2/set.png 160w"
                                    alt="Token Sets logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/fd6bd24104a350c4dbeaa27608802424/4eb25/set.webp,/static/fd6bd24104a350c4dbeaa27608802424/3a6ff/set.webp,/static/fd6bd24104a350c4dbeaa27608802424/1deab/set.webp,/static/fd6bd24104a350c4dbeaa27608802424/c6aca/set.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/fd6bd24104a350c4dbeaa27608802424/4ed6d/set.png"
                                      srcset="/static/fd6bd24104a350c4dbeaa27608802424/41fb7/set.png,/static/fd6bd24104a350c4dbeaa27608802424/996dd/set.png,/static/fd6bd24104a350c4dbeaa27608802424/4ed6d/set.png,/static/fd6bd24104a350c4dbeaa27608802424/40dd2/set.png"
                                      alt="Token Sets logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Token Sets</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Crypto investment strategies that
                                  automatically rebalance.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://www.tokensets.com/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABnElEQVQ4y6WUz0sCQRTH/adSM23VUolIomPZH6Pr6pqLqScNutSxQ5QFnYrALkEp3SIII4Kw6Ke68+atYzOuECr+quHtMsPMZ97Me+87FtWNfzbLoImEt20e8Z8E9mBcwvAUyjYasVHeGRfmrmJO3JiHIxXKx7RUoAcyUd0Qc4mpYbAgXZhaJJVraLVajAnj7bZIkj4SnxWHGgx7UbbTq33CgabRMpvZudiFiK33/pZut0Y6SBofJscu96B0COYW709G0g89zrtgZQbzawShyVfX3lBx6VoAKBFHhzrLLAMPR2Ig7MD8OtA2/FkFLaBnggA1AZMah+koOMRhsfrrlSpOXfMB1MVe0GDZ0Z5DgLQdYsZONvXTnM57fPT9YqQWSGzYnbtg82NNFKNSAeRpOizafXAnz893kF0BxdlbJwNhqjcfb/ChjMUdSC8RnohhFfYbsHZuqvcgqkqiYavIfz/ZC0cduMU9i2izs7wesXa0pXpGCcMsktwqVCtwvi3EoEoT6VnCxBzV/BC1c1Uakz8GkhBzwvO/l2Qc+wH2Qhkn/jwqYQAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/0c00567e9eda2398d914c54421535f76/4eb25/pooltogether.webp 20w,/static/0c00567e9eda2398d914c54421535f76/3a6ff/pooltogether.webp 40w,/static/0c00567e9eda2398d914c54421535f76/1deab/pooltogether.webp 80w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                                    data-srcset="/static/0c00567e9eda2398d914c54421535f76/41fb7/pooltogether.png 20w,/static/0c00567e9eda2398d914c54421535f76/996dd/pooltogether.png 40w,/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png 80w"
                                    alt="PoolTogether logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/0c00567e9eda2398d914c54421535f76/4eb25/pooltogether.webp,/static/0c00567e9eda2398d914c54421535f76/3a6ff/pooltogether.webp,/static/0c00567e9eda2398d914c54421535f76/1deab/pooltogether.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                                      srcset="/static/0c00567e9eda2398d914c54421535f76/41fb7/pooltogether.png,/static/0c00567e9eda2398d914c54421535f76/996dd/pooltogether.png,/static/0c00567e9eda2398d914c54421535f76/4ed6d/pooltogether.png"
                                      alt="PoolTogether logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">PoolTogether</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">A lottery you can&#x27;t lose.
                                  Prizes every week.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://pooltogether.com/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC8UlEQVQ4y9WU3U+SYRjGr1fUOTtpa326PuxrbpKipkFJHwOF4BV5CYxIX3lBMDMSyQ8EQS1MRTLK+dGMbJVazWzoWi1rLXXrpJpbHbRaddCf0Nqa5Z7ePOgUTnuOrufetd/23Pf9XMB/eVh5AY5Ls2E8KIKdPoQbXV5cttPos9EY5bWz7CgaTCVwswwsKllsoENfDItiH1hZHh6N3fpbouajdwVzk6OJf7XPZkKosRZBV3V8wKYKDWwqCSrleYmEEMFavjYXvY9X0xNI4XWPsyrpesCNYb8rPqBOnElp9mZAvz9ri7YgY951QjUVHewKPr15pdPLHbuk3y9MK8ndCRstpeIC2lVial1qKpKRtCp0iv5ywawiYyE/iQ51k3ZWuTQTsG8abzJgwKGPDWysKMXVZjPF0VIwh0Rpbx/0fu9yli6PdrmWIgHnz556HXk3FT7+Yfoqno/4BDGBXpsRNYycUudnQZEj3NxRrfs+eydIJgfblyfCHjI90vHjUjOXGfba+ek7E2I+t9VuRKU0Q6DL381PWtLtYYRkrKPy193h8O+JgV7S33ZukbetgGpoSez+1RkU6PF4EekLpViL87/WK3eR/tOy5ddPHv7+sPCMjPf5P/K25JX2MKI4dpA5TDUYVWip0KSb5aIltnA7Oa0t/HU96Fu6dtFNPDaDP9IXwO2B3gSNaGNsYLNJlnDeqkanXZNrVeSSosxNxHQkm9SxDKmr1A2fPUkjcm8anFoCrWhDbGCbWYGhpjL0u3Srq1VifUnOts/6gq0LpYXCGfUB0foX82+oHbyv3n0eFYXpcQA5JdrtOqrz1DFc5O+BestZn8PscbKlhqC7xqhVytfo1QpUGZn4gsHHFsNbJkHQpoKVFgsWH99OnJ2IJJVrlBgV8ktvYtDA/+VazhR/2vR6HWjgNDCppXj/Mopvnz5Ce0RMLQb2oJY1oNXB4YylPH6gv5aFu8oATluEsPcMgi0ONFq0CDVZ/3lMtOz/zGb8AaaMB14oR0f+AAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/0f3d74acddd3bea1a258b20520dc00ed/4eb25/index-coop.webp 20w,/static/0f3d74acddd3bea1a258b20520dc00ed/3a6ff/index-coop.webp 40w,/static/0f3d74acddd3bea1a258b20520dc00ed/1deab/index-coop.webp 80w,/static/0f3d74acddd3bea1a258b20520dc00ed/c6aca/index-coop.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/0f3d74acddd3bea1a258b20520dc00ed/4ed6d/index-coop.png"
                                    data-srcset="/static/0f3d74acddd3bea1a258b20520dc00ed/41fb7/index-coop.png 20w,/static/0f3d74acddd3bea1a258b20520dc00ed/996dd/index-coop.png 40w,/static/0f3d74acddd3bea1a258b20520dc00ed/4ed6d/index-coop.png 80w,/static/0f3d74acddd3bea1a258b20520dc00ed/40dd2/index-coop.png 160w"
                                    alt="Index Coop logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/0f3d74acddd3bea1a258b20520dc00ed/4eb25/index-coop.webp,/static/0f3d74acddd3bea1a258b20520dc00ed/3a6ff/index-coop.webp,/static/0f3d74acddd3bea1a258b20520dc00ed/1deab/index-coop.webp,/static/0f3d74acddd3bea1a258b20520dc00ed/c6aca/index-coop.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/0f3d74acddd3bea1a258b20520dc00ed/4ed6d/index-coop.png"
                                      srcset="/static/0f3d74acddd3bea1a258b20520dc00ed/41fb7/index-coop.png,/static/0f3d74acddd3bea1a258b20520dc00ed/996dd/index-coop.png,/static/0f3d74acddd3bea1a258b20520dc00ed/4ed6d/index-coop.png,/static/0f3d74acddd3bea1a258b20520dc00ed/40dd2/index-coop.png"
                                      alt="Index Coop logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Index Coop</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">A crypto index fund that gives
                                  your portfolio exposure to top DeFi tokens.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://www.indexcoop.com/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="dapps__TwoColumnContent-sc-1hb30un-7 kPrkEk">
                      <div class="dapps__LeftColumn-sc-1hb30un-20 kMsWTL">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Payments</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACTklEQVQ4y2NgYGEmH2EVZWRlASJyNIO0MTAwsjAzc7KDRFiZmdhZsZuFzmcGCjFI6SjyiwsxoAIs+tGtZWJyK43UdjNTtNAO7MpIW9ecurrRpyERaBwW/QhtbKxA6aCujMRFVR5VMVP/7J/7/+Ssf0dn/zsx9/+pvk/bfBoTId5B1wzRKWukNvHzzsqzs6f/PjT9/8FZ/4/O/n9s8u+9U//vr744N211g1tJBEg/GwuKZiYONqBoQHvavP+np/zaN+3/wQmfdqSsqgeiCZ92ArlA5FoaYRRkL62rhHA/cghnbGyd8//4pD97JnzbqelmCgknIGPCtx3T/x+e9GWXvKm6qKIkAzMTA7JmYHwA1WVubgc79XjyynogFxhDQARkALmz/h8DBoFvUxKQy8LFgeJsUJQyMAT3ZoEC6f+xxCU1IM0cbEzsIO8kLa8FmggUT1ndgBLmUGtZmAWlRVTt9Kb+2j/9/6Hej1uUrXUhzlZzMOz/smPynz1z/p+AuAglwCAeljFQ1fWy8KiOAWoG6X+3NW5hRfyiqr4P26b9PwQMxfn/T3vXJ0BchBpVYP1eVbFJS2qqzs+Z+n/f5D/7IE4FmfXzYO2l+RM+bJfUUcASVRCXM7EwB/flTPq+a97/U7P/HZ/57ygwCICpxas2Lm1Vo19zMlgnK/bkCUo9DAzyZppB3ZmZm9syt7aH9GWrWOvq+1q7FIaDAoBAxgC7H5wPGBmYGIG0gJSIlI4SFp24siQjOzRImDjZGYFJAmuWwlUYIOdQRhbcBQMlxRAAVKncpFu+Pd4AAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/f289946a113df1f9424524d9859924bb/4eb25/tornado.webp 20w,/static/f289946a113df1f9424524d9859924bb/3a6ff/tornado.webp 40w,/static/f289946a113df1f9424524d9859924bb/1deab/tornado.webp 80w,/static/f289946a113df1f9424524d9859924bb/c6aca/tornado.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/f289946a113df1f9424524d9859924bb/4ed6d/tornado.png"
                                    data-srcset="/static/f289946a113df1f9424524d9859924bb/41fb7/tornado.png 20w,/static/f289946a113df1f9424524d9859924bb/996dd/tornado.png 40w,/static/f289946a113df1f9424524d9859924bb/4ed6d/tornado.png 80w,/static/f289946a113df1f9424524d9859924bb/40dd2/tornado.png 160w"
                                    alt="Tornado cash logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/f289946a113df1f9424524d9859924bb/4eb25/tornado.webp,/static/f289946a113df1f9424524d9859924bb/3a6ff/tornado.webp,/static/f289946a113df1f9424524d9859924bb/1deab/tornado.webp,/static/f289946a113df1f9424524d9859924bb/c6aca/tornado.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/f289946a113df1f9424524d9859924bb/4ed6d/tornado.png"
                                      srcset="/static/f289946a113df1f9424524d9859924bb/41fb7/tornado.png,/static/f289946a113df1f9424524d9859924bb/996dd/tornado.png,/static/f289946a113df1f9424524d9859924bb/4ed6d/tornado.png,/static/f289946a113df1f9424524d9859924bb/40dd2/tornado.png"
                                      alt="Tornado cash logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Tornado cash</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Send anonymous transactions on
                                  Ethereum.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://tornado.cash/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABM0lEQVQ4y2P4TwFgGHSa//3/9/f/3z8gBGSToPnfPywi//4RbfOXd38v7/x9eP6fkyv+Pb2GVSeGZrCiPycW/+i0/l6p+K1Y/HuF4vd67V9LM/99eoHpKCyaf+/t+14j9aPf+scMn+8tGt+b1X50Gf1794iQZkhQAfUf6v8x2ezvk7O/NuT9mGz59+EJsMxfYgPs9/HJP5cH/Htz8//Xt9hDEWeA/f3+7/fr36cn/1of+v/rC6zWYmoGGf/vz8/f55t+7fL+++rE7zOdv7aG/PvyHKvl2ALsaPuP2bo/l9j/XOH+a0fSr61Ovw8l/Pv+Em46Ppv/XFj+Y4rj93bt701K3xsVf0y1+H2i6v/PNwQ1w8Dn138ub/t9eNbvE4v/PTz//88fCpInyMJ/dMgYQ6AwAACzGXpPwpHr9wAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/7d012ecc82bc65037ece2a290f9de2ce/4eb25/sablier.webp 20w,/static/7d012ecc82bc65037ece2a290f9de2ce/3a6ff/sablier.webp 40w,/static/7d012ecc82bc65037ece2a290f9de2ce/1deab/sablier.webp 80w,/static/7d012ecc82bc65037ece2a290f9de2ce/c6aca/sablier.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/7d012ecc82bc65037ece2a290f9de2ce/4ed6d/sablier.png"
                                    data-srcset="/static/7d012ecc82bc65037ece2a290f9de2ce/41fb7/sablier.png 20w,/static/7d012ecc82bc65037ece2a290f9de2ce/996dd/sablier.png 40w,/static/7d012ecc82bc65037ece2a290f9de2ce/4ed6d/sablier.png 80w,/static/7d012ecc82bc65037ece2a290f9de2ce/40dd2/sablier.png 160w"
                                    alt="Sablier logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/7d012ecc82bc65037ece2a290f9de2ce/4eb25/sablier.webp,/static/7d012ecc82bc65037ece2a290f9de2ce/3a6ff/sablier.webp,/static/7d012ecc82bc65037ece2a290f9de2ce/1deab/sablier.webp,/static/7d012ecc82bc65037ece2a290f9de2ce/c6aca/sablier.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/7d012ecc82bc65037ece2a290f9de2ce/4ed6d/sablier.png"
                                      srcset="/static/7d012ecc82bc65037ece2a290f9de2ce/41fb7/sablier.png,/static/7d012ecc82bc65037ece2a290f9de2ce/996dd/sablier.png,/static/7d012ecc82bc65037ece2a290f9de2ce/4ed6d/sablier.png,/static/7d012ecc82bc65037ece2a290f9de2ce/40dd2/sablier.png"
                                      alt="Sablier logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Sablier</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Stream money in real-time.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://pay.sablier.finance/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="dapps__RightColumn-sc-1hb30un-21 fQxgud">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Crowdfunding</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACsUlEQVQ4y2MQYrYmHvEzWCBzGUjSrKztI8ppJ8hoRZpmiAYpCWdhNhshJrw2CzJZgUkoA4jEBRyEWW0EGCwJO1uE1UaM3U6U3U6EzRaEWG1F2G2B4gQ0i7LZsjAYpMc0BgcVxkRUJETWMDAocDGbCDGDXKthFijGa4/dz8JAzey2DAxq9RVTL567CUT3bj+ePXVNkFsBD5MZD4OZjIQryAmYfgbqFGKx5mIwbamZ9f///5PHLp08emnP9uOL525+8+r9upV7NKR8+RjMhVmwORvoMTYGwyn9y2/ffLhx1b7UlIbE+Jr0lEYGBnELvWigcTMmr2JnMAIqQ9cM9CpQp59zzs8fv9RlfJPi6poqZzAwaHMymCgIu505efXwifNKwh4CjJbCLDbomoHmAcNp64ZDc2esY2DQUBT2kOZ19nXKyYhvOnf82vFrl+2m5QhwWQsxgryGohloGNBICU6HOzcfBXsWAm1jZzBMj238DwY3r90X5baznpmt4R4M8jOrDbpmYKKV5XN59OC5o2kSMFSBfls0ZzNQ57efX65fvSfMYG3RnKYTE8nHYAZKYaiarQUYrYAuv37lXlxYFdDz3AymQe6FHz9+Buq/dvGOrJirxZwsdYdAfkybIQHGyKC7YtH2jWv3MTBoSXDYA/UbqAQDvb1/x6mnn9/ELmpmZzQWZsLwMyTAuBhMzHWivnz+Gu5XAgwzkF+YLDkZjIXYrGdNWvP147fkiDqgj7CENshyVltWBoPc1Lbfv/4UZ3WLgCOPmdGAhVmPgUGqtmzKpfO3gOEKzCrwdMKAlh+AGmKCKx8/enH7xsNNa/evWrpjzfJdS+ZtOXvyGjC1Aq3FqRmeziR5HBPDaiZ1L509Zc3syWuA5Jypa/JT29EUM2DLj7YCTJZALwBDDglpA0XQVAIAdccV1EzlPJIAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/e595bb47917c0fea153a5f3529c75ce6/4eb25/gitcoin.webp 20w,/static/e595bb47917c0fea153a5f3529c75ce6/3a6ff/gitcoin.webp 40w,/static/e595bb47917c0fea153a5f3529c75ce6/1deab/gitcoin.webp 80w,/static/e595bb47917c0fea153a5f3529c75ce6/c6aca/gitcoin.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/e595bb47917c0fea153a5f3529c75ce6/4ed6d/gitcoin.png"
                                    data-srcset="/static/e595bb47917c0fea153a5f3529c75ce6/41fb7/gitcoin.png 20w,/static/e595bb47917c0fea153a5f3529c75ce6/996dd/gitcoin.png 40w,/static/e595bb47917c0fea153a5f3529c75ce6/4ed6d/gitcoin.png 80w,/static/e595bb47917c0fea153a5f3529c75ce6/40dd2/gitcoin.png 160w"
                                    alt="Gitcoin Grants logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/e595bb47917c0fea153a5f3529c75ce6/4eb25/gitcoin.webp,/static/e595bb47917c0fea153a5f3529c75ce6/3a6ff/gitcoin.webp,/static/e595bb47917c0fea153a5f3529c75ce6/1deab/gitcoin.webp,/static/e595bb47917c0fea153a5f3529c75ce6/c6aca/gitcoin.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/e595bb47917c0fea153a5f3529c75ce6/4ed6d/gitcoin.png"
                                      srcset="/static/e595bb47917c0fea153a5f3529c75ce6/41fb7/gitcoin.png,/static/e595bb47917c0fea153a5f3529c75ce6/996dd/gitcoin.png,/static/e595bb47917c0fea153a5f3529c75ce6/4ed6d/gitcoin.png,/static/e595bb47917c0fea153a5f3529c75ce6/40dd2/gitcoin.png"
                                      alt="Gitcoin Grants logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Gitcoin Grants</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Crowdfunding for Ethereum
                                  community projects with amplified contributions</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://gitcoin.co/grants/?" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="dapps__TwoColumnContent-sc-1hb30un-7 kPrkEk">
                      <div class="dapps__LeftColumn-sc-1hb30un-20 kMsWTL">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Insurance</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAC70lEQVQ4y4WUuUuzQRDG3z9BsBHvI95Go5VdULETLQSxUcROiEW0k0BSC4EokWgUETwQD7wwEUzEQqKoKCiCRYQgIoIxHljn+XjmY8N6LwzsO7v725lnZl+juroa35nZbEZNTQ2Ki4uRlZWF7OxslJeXi49ras/nc8Z3IB3W2dmJpaUlTE1NoaGhAaWlpR+gfwJpFosFRUVF6Ovrw9vbG3Z2dnBycoLb21s0NjZ+ifRPIK2srAynp6cS3d7eHubn53F8fIyZmRnk5OSgtrb29wj129T88vIS4+PjeHl5wdHREba2trCxsSF66kD9rKFrxlRpdXV1KCgogNfrxf39Pdxut2hIcE9PD0wmk+xR51T6NEM5S0pKkJeXJyDqxwioVTgchhojIyMC46X5+fmorKyUbxZPQQ1O6Gxvb8f29rbo1d/fL9C0tDQ4nc4UsLu7G+np6QIaGxvD+fk59vf30dHRIQxJmZPW1lY8Pz9LNRcXF/H6+orJyUnY7XaprBpXV1cYGBgQyN3dHXw+H4LBIB4fH9Hc3CxZGmzalZUVhEIhifDi4kIKoY9kMimmfw8PD+P9/V3ORiIR0TszM/M/cH19HYFAQKA3Nzey+BuQY3R0FNfX19JGDIIXkGVQK2rABl5bW5MNTH92dhY2mw3RaDQFOTs7Q1dXF+bm5pBIJLCwsCC9yiDq6+uliFJlQik4tWG/DQ0NpYoyODiYAvb29kpRqDv9zIgXW61WqbRUmRXjpLCwUFqGm9k+6i1vbm6mgNSL61zjHjY40+T7JoMso6qqSib6j4E9mJubC5fLhXg8Do/HIz349PQEh8MhMPVSVP+RQZZEqBudNKZ8eHiI6elpaZFYLCb6HhwcSCaEqGB0MyoqKqCbkoARssd2d3exurqK5eVlaXq+Z/4cVFSfz3+JkE4VYVtbmzQt+4zRPjw8oKWlRXTkHhXAhwg/O3QoC8UX4Pf7MTExgaamJvH9BPsRqENZ6YyMDDE+rd9gtH+felm2hSpqOwAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/f327c9108c281368dbbe92f00b8f5e3f/4eb25/nexus.webp 20w,/static/f327c9108c281368dbbe92f00b8f5e3f/3a6ff/nexus.webp 40w,/static/f327c9108c281368dbbe92f00b8f5e3f/1deab/nexus.webp 80w,/static/f327c9108c281368dbbe92f00b8f5e3f/c6aca/nexus.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/f327c9108c281368dbbe92f00b8f5e3f/4ed6d/nexus.png"
                                    data-srcset="/static/f327c9108c281368dbbe92f00b8f5e3f/41fb7/nexus.png 20w,/static/f327c9108c281368dbbe92f00b8f5e3f/996dd/nexus.png 40w,/static/f327c9108c281368dbbe92f00b8f5e3f/4ed6d/nexus.png 80w,/static/f327c9108c281368dbbe92f00b8f5e3f/40dd2/nexus.png 160w"
                                    alt="Nexus Mutual logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/f327c9108c281368dbbe92f00b8f5e3f/4eb25/nexus.webp,/static/f327c9108c281368dbbe92f00b8f5e3f/3a6ff/nexus.webp,/static/f327c9108c281368dbbe92f00b8f5e3f/1deab/nexus.webp,/static/f327c9108c281368dbbe92f00b8f5e3f/c6aca/nexus.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/f327c9108c281368dbbe92f00b8f5e3f/4ed6d/nexus.png"
                                      srcset="/static/f327c9108c281368dbbe92f00b8f5e3f/41fb7/nexus.png,/static/f327c9108c281368dbbe92f00b8f5e3f/996dd/nexus.png,/static/f327c9108c281368dbbe92f00b8f5e3f/4ed6d/nexus.png,/static/f327c9108c281368dbbe92f00b8f5e3f/40dd2/nexus.png"
                                      alt="Nexus Mutual logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Nexus Mutual</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Coverage without the insurance
                                  company. Get protected against smart contract bugs and hacks.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://nexusmutual.io/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAABzklEQVQ4y8WUMUsDMRTH79oTnNwEHVwcHUREBxcVRIeil6tVdNZBQRBBXHRogoIdndxcXVQUcRL8CE5+Bz9Ak7qV+s97ufSsFtuCePDjklzul5f3chcEf3ElSgeJdKAtZDWIQVGZ7mVFJxN4WciaFeZA3t1DQc/sHMP8tggLqyQslOs+yhQSsjTHY4bGO4owlh928iLYBXNgDLIhEAmWh8VU6qJNaRWGLrJx8AruwRLGdsAeZCfob1mJkKaP5Ybfy+ymKaT88dbRXsfDBxCn20V7E9yBGRddDsJca3pI6rcASqrOBVA6Ae94YZq3ZiM0/eAc7WOKVpkn3PeF9MVsRulX4BXzLoo1tJ9JLnWB86yH0T+10WPODcau0J5wQYRfiuUTTVITCY5sFv03jB+gvyE4DY/o364qw/mTJr9crgWTR43v1ReZnMR2Kza3Sr/gbitfAtdYrAIG3TzKo527ItscpyStOr8wCi7BBdiGaMTtIOBDb9xZNYFoJxTpynxfoOpKPeVFknKcOS6/fDnuM4toZaUrEJwJbkeU+HLVn4p52ejwR6HSautDyAd8sRzd/3kYnDudJj/sSUbCTCQOkoleZG2EDtPrz9b8yL9dnyLAx4373QZcAAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/24c2613eef8a081767c462fb431cdfa8/4eb25/etherisc.webp 20w,/static/24c2613eef8a081767c462fb431cdfa8/3a6ff/etherisc.webp 40w,/static/24c2613eef8a081767c462fb431cdfa8/1deab/etherisc.webp 80w,/static/24c2613eef8a081767c462fb431cdfa8/c6aca/etherisc.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/24c2613eef8a081767c462fb431cdfa8/4ed6d/etherisc.png"
                                    data-srcset="/static/24c2613eef8a081767c462fb431cdfa8/41fb7/etherisc.png 20w,/static/24c2613eef8a081767c462fb431cdfa8/996dd/etherisc.png 40w,/static/24c2613eef8a081767c462fb431cdfa8/4ed6d/etherisc.png 80w,/static/24c2613eef8a081767c462fb431cdfa8/40dd2/etherisc.png 160w"
                                    alt="Etherisc logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/24c2613eef8a081767c462fb431cdfa8/4eb25/etherisc.webp,/static/24c2613eef8a081767c462fb431cdfa8/3a6ff/etherisc.webp,/static/24c2613eef8a081767c462fb431cdfa8/1deab/etherisc.webp,/static/24c2613eef8a081767c462fb431cdfa8/c6aca/etherisc.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/24c2613eef8a081767c462fb431cdfa8/4ed6d/etherisc.png"
                                      srcset="/static/24c2613eef8a081767c462fb431cdfa8/41fb7/etherisc.png,/static/24c2613eef8a081767c462fb431cdfa8/996dd/etherisc.png,/static/24c2613eef8a081767c462fb431cdfa8/4ed6d/etherisc.png,/static/24c2613eef8a081767c462fb431cdfa8/40dd2/etherisc.png"
                                      alt="Etherisc logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Etherisc</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">A decentralized insurance
                                  template anyone can use to create their own insurance coverage.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://etherisc.com/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="dapps__RightColumn-sc-1hb30un-21 fQxgud">
                        <div class="ProductList__Product-sc-19jtmu8-0 hjQkRC">
                          <h3 class="ProductList__CategoryTitle-sc-19jtmu8-3 bvLdAB">Portfolios</h3>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAADUElEQVQ4y7WUTU9bZxCF3+uLuomURfpT0l2TKMTY2GBwDLaxLxjkkIQvxQQKCd+FECAUCiWAFFI1XVXqr2gXlbrqokoXXXTXSGmVRImURQX4vmdy5hqwo0bsMtJofDeP58yceY352FFqhrneCNMX882tJpiBOEI3G+Fei8LtiSBUaIDpZhajMF1hOR021gJzNwkz3wYzmlCImP4YzA3+AYEBKH1ZCIKbYc1fEdMZxodhU1crsEmtrTY0koApRMQh8DMCPQK9ngacj30ujkIIDynQ4+//QbWjScLutAYwVmsITFP+08E4pC8G4RikGIEQ9DsBKXZpeqNwFKrZ1VAD3fGsSlSYM5G0rJinfBlhlhLAUBNsfxz2Rgzg7IRzFEJnuo46zdaLuR6tAV64IGY2BZep1Zu+CqF8OxJHuUTIYBToiwC9YQLDKBNqj6BZlasz7Y3UAO+1w6xkYJbT+GShHX8RKlNJ+IvdkPkCMMec7mJ2A+M5CKUqVOX/mQtLXY6SC7WSlzMILSswg/BSlrC4tT/9CBzsA2/fBCma+/8Bu3NAz0WAEu3RTC/p9tVSJ8DVLNyvsjDrHgYWk1a+n4JfPgCsDxweQI7j6a+QYc50tB0YTsGnTOH2+9RS6tMT4HoH3K9zMBt5DK2mrWzfhL83AqwVgT9+qQCthbz6F/LP38DrF8DGOPxiPaS/GYPqU2YVuJlD6Js8tDZvcEarGWtnGy1+WES1vWrgt5+BgQgsty+8pKiaXy/qBPjQg7PTCcM8u5XHc3YaQPdKkCeTwKNRzo4db5WAzVt0AOUO05/06bOBGM4QpmfqnAB3PRBq3W36cdvD7a28FXZ6uJyydiFhMcecbraYaLIYi1l7pwWHY630aQJDelF6pl+02OqWvyvA7LLDsYSl7AD6hJ2Kdrqeg/+gA+WlDMr30vC/bIcEPm3FY72qhbbKya5k/SqQAHMk2XnczeVUoOOEvtKZrnVA6FO5n4YQ8JI+HdXzVNBEEo4+KvpKvRe1UGP2g2/mp1xWlg6Ypa1m6dMMoef0ooqRAOgoVIEfjGOoyteZ7vBbt6+WUp+q+Qk0MynrsjND6cErdWoo9NvCMRTOZh4ufVpH89cR6PJMnRl2eDtReTc/erwDgVGu3ZDOTXMAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/318e701f02135ff91e7661ace1ea287c/4eb25/zapper.webp 20w,/static/318e701f02135ff91e7661ace1ea287c/3a6ff/zapper.webp 40w,/static/318e701f02135ff91e7661ace1ea287c/1deab/zapper.webp 80w,/static/318e701f02135ff91e7661ace1ea287c/c6aca/zapper.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/318e701f02135ff91e7661ace1ea287c/4ed6d/zapper.png"
                                    data-srcset="/static/318e701f02135ff91e7661ace1ea287c/41fb7/zapper.png 20w,/static/318e701f02135ff91e7661ace1ea287c/996dd/zapper.png 40w,/static/318e701f02135ff91e7661ace1ea287c/4ed6d/zapper.png 80w,/static/318e701f02135ff91e7661ace1ea287c/40dd2/zapper.png 160w"
                                    alt="Zapper logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/318e701f02135ff91e7661ace1ea287c/4eb25/zapper.webp,/static/318e701f02135ff91e7661ace1ea287c/3a6ff/zapper.webp,/static/318e701f02135ff91e7661ace1ea287c/1deab/zapper.webp,/static/318e701f02135ff91e7661ace1ea287c/c6aca/zapper.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/318e701f02135ff91e7661ace1ea287c/4ed6d/zapper.png"
                                      srcset="/static/318e701f02135ff91e7661ace1ea287c/41fb7/zapper.png,/static/318e701f02135ff91e7661ace1ea287c/996dd/zapper.png,/static/318e701f02135ff91e7661ace1ea287c/4ed6d/zapper.png,/static/318e701f02135ff91e7661ace1ea287c/40dd2/zapper.png"
                                      alt="Zapper logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Zapper</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Track your portfolio and use a
                                  range of DeFi products from one interface.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://zapper.fi/" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;80&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAADaElEQVQ4y7XUS08TURQH8FvvfAxJBAp98RChuHHpEyh9omALs9CF1BgSI42RxNCNr40LN65csnHjTpd+ADc82mmHdkpLSUQNujEoM7nH/xkKhQ9gk39OV7+ce8+5I8T/+IWSZRFKuPGEkqbsS5myf2pTDtysyMFbVXl+2pJDMzV54faWHE7X5UimIcOz23J0rikv6jseRHBOg630JU0BzA1AAVAAFAAFQDGcbgiAAqA4go7Qk6CnBfoA3gE2h+gAdYA6QB2gDlBHhzpAHeDs6NzOXUB9R2gbTJRlEEdGFoASjkwAafBWhYamq3RhxqLh2zUaSW/RSKaONAgg4cgEaIlB4NoxGIwDjJe4zgMloAf9U6bTGzWd7ojp9EQrji9edfwJywmmag66dID+baGLCMBmGwzESzIQKwkkC5hBJ5Aoq/EHlsos1dXM44aaRlKLDZV4tK2G01tI3Q7Pup3m+D6RNuiPlaQ/ZqAaWaDUB9AbKan5Z3Vq7u4jf+jb3l/i38qnPQokLBXO1G0+OpLjISEnwKghfVFD+KLFLFBCxw7uVXVPlHCHJg1MlWnl4w8qWr8pnKlS/02Lu7TRJSEuiNoGfZNFiXDNAiV07ABV/Sm+TxMdmXT5nkUXZysUSlVpaMZSmLqNqbsgchrsAdgTKQgk2zsJMGq4oD9WJnSO6ZoUTPDkeeoVTL+qsEo2UAKa4/**************************/vnLL3r3YZc6rhfJGykTJu+CiA0U3dZyvPRIG+ye2JAI1yxQF+y4uqHevv/qDuL7zwMyavu0tvmb0k+2yB/fRIeHIJLjl4S0wa7xDdk1vo66ngWKTgoOYDW3ZKn7zxtq4dW2WnzdVE/e7Kj0Uk0Fk6bCK7LR5TGI/22wc2xdIlzngRLQPz2R4gG6tM9eKdgdV4t2xzWOYWOdbCw9Fn9zHygf/xG/99PgjTUN4fqwc2yN0DHQAvEdBmJFCsYNCiXc/XTDkz96nkCf8kcEOQmues7dWOU6CvQFulzumtjI49j53kghj1XKYz/zmHweTzOPl5QHuAzwJXKJQdQzxyBj547RNeHeJ4bEkwfIC8+viJ8of0AOP3OpU585D9eTHYpWhx4+PkAN96gB1HBsDaAGUAOoAdQAagA1IBqwM60O/8vHX/wDrglyjkRCot0AAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/5c94ab79af2465c5af999927cd9ce84a/4eb25/zerion.webp 20w,/static/5c94ab79af2465c5af999927cd9ce84a/3a6ff/zerion.webp 40w,/static/5c94ab79af2465c5af999927cd9ce84a/1deab/zerion.webp 80w,/static/5c94ab79af2465c5af999927cd9ce84a/c6aca/zerion.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/5c94ab79af2465c5af999927cd9ce84a/4ed6d/zerion.png"
                                    data-srcset="/static/5c94ab79af2465c5af999927cd9ce84a/41fb7/zerion.png 20w,/static/5c94ab79af2465c5af999927cd9ce84a/996dd/zerion.png 40w,/static/5c94ab79af2465c5af999927cd9ce84a/4ed6d/zerion.png 80w,/static/5c94ab79af2465c5af999927cd9ce84a/40dd2/zerion.png 160w"
                                    alt="Zerion logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/5c94ab79af2465c5af999927cd9ce84a/4eb25/zerion.webp,/static/5c94ab79af2465c5af999927cd9ce84a/3a6ff/zerion.webp,/static/5c94ab79af2465c5af999927cd9ce84a/1deab/zerion.webp,/static/5c94ab79af2465c5af999927cd9ce84a/c6aca/zerion.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/5c94ab79af2465c5af999927cd9ce84a/4ed6d/zerion.png"
                                      srcset="/static/5c94ab79af2465c5af999927cd9ce84a/41fb7/zerion.png,/static/5c94ab79af2465c5af999927cd9ce84a/996dd/zerion.png,/static/5c94ab79af2465c5af999927cd9ce84a/4ed6d/zerion.png,/static/5c94ab79af2465c5af999927cd9ce84a/40dd2/zerion.png"
                                      alt="Zerion logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Zerion</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Manage your portfolio and simply
                                  evaluate every single DeFi asset on the market.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://app.zerion.io/" target="_blank"
                                rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                          <div class="ProductList__Item-sc-19jtmu8-1 ldzVuU">
                            <div class="ProductList__ImageContainer-sc-19jtmu8-6 eEgwmt">
                              <div data-gatsby-image-wrapper=""
                                class="gatsby-image-wrapper gatsby-image-wrapper-constrained ProductList__Image-sc-19jtmu8-8 hLQylT">
                                <div style="max-width:80px;display:block"><img alt="" role="presentation"
                                    aria-hidden="true"
                                    src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;81&#x27; width=&#x27;80&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                                    style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAFdElEQVQ4y1VTe0xTZxQ/bX3EbT4hMoS2UO5X+ri3pe+HRQTkUdrSllIfGCI6x2LEYcimRlgW55I9o277Y+qmc8vMMueQZ9AJSBRw04iA6MZAZLxiYtQxxKHce799t3Euu8kv55zvO+d3fufce8GQIHOnJEjPMNK4ZkYWX6mTxQOJJT4zI8rSqcXZerVY8PMtOghY9UB8EHyPkY74Av73mBRyMCrkTTq5FOsJCGm2Ki4WjpVtgAw6GTIZVaTIb9WLV2uVkpwUjcRv0QkNI41CThv4bYb/yB1KBaSqqShDomyYkGFaGtcfs2jxSyG7YUk6nawmhGqSuFxQR/KAKCbkukhxtl4FhU6jxGuiRYJiQTnYUaKEFII6boXBmCibsSMFJnE/GXeIqHnsMdGPSfGE18zUkeZvuA3acqKwmJy51jDJUWtXmiCHNCFkYqEplLlXwVqnUUmCgz6L7m8CniRjkvAc/8a5KRpMmjw/D9pSxgk+z9aplgvqhLXAgjnzxWka1O02arEnRc0R+Ty54EgCR4h4YvlnPkvsrM8sgJklKtmgTY9DDgMm6xnxmhgqssOEqGWQGB2tJTu8tjnow+FUK5fDIPyMKKJWIH6GyHnAloI9Zh1P1sC5TcyMQEwmbN6YZgXIcjpEUXPnCC98zqlTp9pOfn0Cb1tXwBKluIB0D9oNPEnm/UIDYoVRQ1aG32hn+AKrji9xCMSRtTwldwjkixfCvqrK+V8dPw4NDQ1vNZ09i1svXHiyt2yboJQPWBgyllEgxj6DGq/SaXCVx8Vd8au4j1YpuU0WLbfRrMI+i571mxkHrFgwD346f17c0toKNTU16bW1tXh4eHj21s2b7HuVe9iiNam8x6Dmc7VJfDjdxb2/ZR3bX+LgJsMUfzpdhkPKGHafK5nPMOnZYhuthLq6ugjOnTsH4XAYqqur2zva2/Gfk5MzBPzgwADX0drCXWxp5gZ6urj7FV5u3CfDI2EVnihE7INwEm7KU+ENNt2VuXKVJEJWX18vWIngNzY2phOluLu7m5+enp5lWZaffvQIz2CMH353iL+TuRT/kS/nR9yxTyZCCF/LS+zNjn1xrDZDvvKAXQowVUzBVCgWhoLJoo6P90p+qKmF2jPVlU1NTbO1AnFXF3744P7s1FN29t7ht7kxn5wdqwhyEx+U4cGAcghv10SRF7qeIDryL+NXZXDLj0SjIQQT+VI48OGHor5Pq6C3r09bFPBf/ezgwal2soLeG314/M5tbuhyGzc2Ovr40b27x3Bg3uJJvxRiRQBDXjlcykkQwS6XVoJLZTBaQMWPFFAxO9wZov4wrdu1tQSo6KXesle2eC91dq7+8uiRvdtLS3/Z8Xr56fUFwXeSFr2wUxoTQ5clLYFuryJ+JEitwK/JANx6Bg6t08JvHvTr3TBV05OJiu4EEMbbGTXAskUoaklFeelWqCovEwbSEizdELuw5LBLgYu00sbzRVro96Cr4yHq6q40cu2x6LqKTHRfby7Ct0Po6bepqsmHhRT+Iks96mS0/TYqAaeq0W3yUXcX2g2duXZjW6Wdnhj0UvjEGvRXyMjcvJ6D+LFCxBfbmB5wW3QVxSa68vJqhH/3owffO5M7B/Io/GOu6mSKRv2uEyWetFKJR/OM9O6ghdmdZtbt3G/RHLmRhXBXvvK6z8BUdKQhfjAfsZvt9B6BECosWmhxoG8uutD+nx3I3OJU9jzJVCwvt2lBr1aCS0UBIYQcix42W2jArui5zQ7llU4XCn9iVwu1R9tWosNvWsnIRRZGAhqnqM6ohOm1FHTbKajPUAEOSGGLjZbYVEi8UkVJCKGErEe8yUJLsOtlOGEgOT4FgNEqaiC1A14EoLGL/gFsqmKAw8JPvQAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    data-srcset="/static/d57f0cd9a8642aaab5a52a32d9af84f0/4eb25/rotki.webp 20w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/ffb07/rotki.webp 40w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/695ff/rotki.webp 80w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/0301d/rotki.webp 160w"
                                    sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                    data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                    decoding="async" loading="lazy"
                                    data-src="/static/d57f0cd9a8642aaab5a52a32d9af84f0/5ff02/rotki.png"
                                    data-srcset="/static/d57f0cd9a8642aaab5a52a32d9af84f0/41fb7/rotki.png 20w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/aae7d/rotki.png 40w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/5ff02/rotki.png 80w,/static/d57f0cd9a8642aaab5a52a32d9af84f0/b7e41/rotki.png 160w"
                                    alt="Rotki logo">
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      srcset="/static/d57f0cd9a8642aaab5a52a32d9af84f0/4eb25/rotki.webp,/static/d57f0cd9a8642aaab5a52a32d9af84f0/ffb07/rotki.webp,/static/d57f0cd9a8642aaab5a52a32d9af84f0/695ff/rotki.webp,/static/d57f0cd9a8642aaab5a52a32d9af84f0/0301d/rotki.webp"
                                      sizes="(min-width: 80px) 80px, 100vw"><img data-gatsby-image-ssr=""
                                      data-main-image="" style="opacity:0" sizes="(min-width: 80px) 80px, 100vw"
                                      decoding="async" loading="lazy"
                                      src="/static/d57f0cd9a8642aaab5a52a32d9af84f0/5ff02/rotki.png"
                                      srcset="/static/d57f0cd9a8642aaab5a52a32d9af84f0/41fb7/rotki.png,/static/d57f0cd9a8642aaab5a52a32d9af84f0/aae7d/rotki.png,/static/d57f0cd9a8642aaab5a52a32d9af84f0/5ff02/rotki.png,/static/d57f0cd9a8642aaab5a52a32d9af84f0/b7e41/rotki.png"
                                      alt="Rotki logo">
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                            </div>
                            <div class="ProductList__TextContent-sc-19jtmu8-4 gIvdmA">
                              <div class="ProductList__LeftContainer-sc-19jtmu8-7 dcMJIf">
                                <div class="ProductList__ItemTitle-sc-19jtmu8-2 leFnDH">Rotki</div>
                                <div class="ProductList__ItemDesc-sc-19jtmu8-5 hMAaaS">Open source portfolio tracking,
                                  analytics, accounting and tax reporting tool that respects your privacy.</div>
                              </div><a
                                class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc ProductList__StyledButton-sc-19jtmu8-9 cXZDTs"
                                href="https://rotki.com/" target="_blank" rel="noopener noreferrer"><span>Go</span></a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="CalloutBanner__StyledCard-sc-gj8rbv-0 HCTlz dapps__StyledCalloutBanner-sc-1hb30un-10 jHJgDO">
                      <div data-gatsby-image-wrapper=""
                        class="gatsby-image-wrapper gatsby-image-wrapper-constrained CalloutBanner__Image-sc-gj8rbv-3 fqNEJO">
                        <div style="max-width:300px;display:block"><img alt="" role="presentation" aria-hidden="true"
                            src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;398&#x27; width=&#x27;300&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                            style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                          data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                          src="data:image/png;base64,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"
                          alt="">
                        <picture>
                          <source type="image/webp"
                            data-srcset="/static/0feeac3f7182fbfa52ee3aa146840325/67550/wallet.webp 75w,/static/0feeac3f7182fbfa52ee3aa146840325/66460/wallet.webp 150w,/static/0feeac3f7182fbfa52ee3aa146840325/eea6f/wallet.webp 300w,/static/0feeac3f7182fbfa52ee3aa146840325/7752f/wallet.webp 600w"
                            sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" maximagewidth="300"
                            data-main-image="" style="opacity:0" sizes="(min-width: 300px) 300px, 100vw"
                            decoding="async" loading="lazy"
                            data-src="/static/0feeac3f7182fbfa52ee3aa146840325/9c4ad/wallet.png"
                            data-srcset="/static/0feeac3f7182fbfa52ee3aa146840325/6ed8e/wallet.png 75w,/static/0feeac3f7182fbfa52ee3aa146840325/29648/wallet.png 150w,/static/0feeac3f7182fbfa52ee3aa146840325/9c4ad/wallet.png 300w,/static/0feeac3f7182fbfa52ee3aa146840325/56426/wallet.png 600w"
                            alt="Illustration of a robot.">
                        </picture><noscript>
                          <picture>
                            <source type="image/webp"
                              srcset="/static/0feeac3f7182fbfa52ee3aa146840325/67550/wallet.webp,/static/0feeac3f7182fbfa52ee3aa146840325/66460/wallet.webp,/static/0feeac3f7182fbfa52ee3aa146840325/eea6f/wallet.webp,/static/0feeac3f7182fbfa52ee3aa146840325/7752f/wallet.webp"
                              sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" maximagewidth="300"
                              data-main-image="" style="opacity:0" sizes="(min-width: 300px) 300px, 100vw"
                              decoding="async" loading="lazy"
                              src="/static/0feeac3f7182fbfa52ee3aa146840325/9c4ad/wallet.png"
                              srcset="/static/0feeac3f7182fbfa52ee3aa146840325/6ed8e/wallet.png,/static/0feeac3f7182fbfa52ee3aa146840325/29648/wallet.png,/static/0feeac3f7182fbfa52ee3aa146840325/9c4ad/wallet.png,/static/0feeac3f7182fbfa52ee3aa146840325/56426/wallet.png"
                              alt="Illustration of a robot.">
                          </picture>
                        </noscript>
                        <script
                          type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                      </div>
                      <div class="CalloutBanner__Content-sc-gj8rbv-1 hatxbg">
                        <h2 class="CalloutBanner__H2-sc-gj8rbv-4 gvPciB"><span>View wallets</span></h2>
                        <p class="CalloutBanner__Description-sc-gj8rbv-2 cgkytK"><span>Wallets are dapps too. Find one
                            based on the features that suit you.</span></p>
                        <div><a
                            class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                            href="..\wallets\find-wallet\index.html"><span>Find wallet</span></a></div>
                      </div>
                    </div>
                  </div>
                  <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                    <div class="dapps__AddDapp-sc-1hb30un-26 fBpVdK">
                      <div>
                        <h2 class="dapps__H2-sc-1hb30un-8 ecBNXU"><span>Add dapp</span></h2>
                        <p class="dapps__TextNoMargin-sc-1hb30un-25 bPsfrp"><span>All products listed on this page are
                            not official endorsements, and are provided for informational purposes only. If you want to
                            add a product or provide feedback on the policy raise an issue in GitHub.</span> </p>
                      </div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc dapps__AddDappButton-sc-1hb30un-27 dEkTNr"
                        href="https://github.com/ethereum/ethereum-org-website/issues/new?assignees=&amp;labels=Type%3A+Feature&amp;template=suggest_dapp.md&amp;title="
                        target="_blank" rel="noopener noreferrer"><span>Suggest dapp</span></a>
                    </div>
                    <div class="SharedStyledComponents__CenterDivider-sc-1cr9zfr-2 lerdbF"></div>
                    <div class="dapps__About-sc-1hb30un-22 kPQWev">
                      <h2><span>The magic</span> <span mt="0" mr="0" mb="0" ml="0"
                          class="Emoji__StyledEmoji-sc-ihpuqw-0 ihnOjq undefined"><img alt="✨"
                            src="https://twemoji.maxcdn.com/2/svg/2728.svg"
                            style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                        <span>behind</span> <!-- -->decentralized finance</h2>
                      <p>
                      <div class="dapps__CardContainer-sc-1hb30un-14 hAxiYg">
                        <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ dapps__CenteredCard-sc-1hb30un-15 kojsPb">
                          <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                              class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="🔓"
                                src="https://twemoji.maxcdn.com/2/svg/1f513.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                            <h3>Open access</h3>
                            <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">Financial services running on Ethereum have
                              no sign up requirements. If you have funds and an internet connection, you’re good to go.
                            </p>
                          </div>
                        </div>
                        <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ dapps__CenteredCard-sc-1hb30un-15 kojsPb">
                          <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                              class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="🏦"
                                src="https://twemoji.maxcdn.com/2/svg/1f3e6.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                            <h3>A new token economy</h3>
                            <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">There’s a whole world of tokens that you
                              can interact with across these financial products. People are building new tokens on top
                              of Ethereum all the time.</p>
                          </div>
                        </div>
                        <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ dapps__CenteredCard-sc-1hb30un-15 kojsPb">
                          <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                              class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="⚖️"
                                src="https://twemoji.maxcdn.com/2/svg/2696.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                            <h3>Stablecoins</h3>
                            <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">Teams have built stablecoins – a less
                              volatile cryptocurrency. These allow you to experiment and use crypto without the risk and
                              uncertainty.</p>
                          </div>
                        </div>
                        <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ dapps__CenteredCard-sc-1hb30un-15 kojsPb">
                          <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                              class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="⛓️"
                                src="https://twemoji.maxcdn.com/2/svg/26d3.svg"
                                style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                            <h3>Interconnected financial services</h3>
                            <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">Financial products in the Ethereum space
                              are all modular and compatible with one another. New configurations of these modules are
                              hitting the market all the time, increasing what you can do with your crypto.</p>
                          </div>
                        </div>
                      </div>
                      <div class="dapps__MoreButtonContainer-sc-1hb30un-30 bUvmBk"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc"
                          href="..\defi\index.html"><span>More on decentralized finance</span></a></div>
                    </div>
                  </div>
                  <div
                    class="SharedStyledComponents__OptionContainer-sc-1cr9zfr-29 dapps__MobileOptionContainer-sc-1hb30un-11 DZdKn bpEIji">
                    <h3><span>Browse another category</span></h3>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 knVVkh"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="💸"
                          src="https://twemoji.maxcdn.com/2/svg/1f4b8.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 iUIsEB">Finance</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="🖼️"
                          src="https://twemoji.maxcdn.com/2/svg/1f5bc.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 iUIsEB">Arts and collectibles</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="🎮"
                          src="https://twemoji.maxcdn.com/2/svg/1f3ae.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 iUIsEB">Gaming</div>
                    </div>
                    <div class="SharedStyledComponents__Option-sc-1cr9zfr-30 hUbzGW"><span size="1.5" mr="1rem" mt="0"
                        mb="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 gGhjlt undefined"><img alt="⌨️"
                          src="https://twemoji.maxcdn.com/2/svg/2328.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div class="SharedStyledComponents__OptionText-sc-1cr9zfr-31 iUIsEB">Technology</div>
                    </div>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div id="what-are-dapps" class="dapps__ImageContainer-sc-1hb30un-1 WaLuL">
                    <div class="GhostCard__Container-sc-1ejpb5q-0 VBmjJ dapps__StyledGhostCard-sc-1hb30un-3 fzwbiz">
                      <div class="GhostCard__BaseCard-sc-1ejpb5q-1 GhostCard__Ghost-sc-1ejpb5q-3 jNNReD gQPIP"></div>
                      <div
                        class="GhostCard__BaseCard-sc-1ejpb5q-1 GhostCard__Card-sc-1ejpb5q-2 jNNReD kFPWjU ghost-card-base">
                        <div data-gatsby-image-wrapper=""
                          class="gatsby-image-wrapper gatsby-image-wrapper-constrained dapps__MagiciansImage-sc-1hb30un-0 bldfRL">
                          <div style="max-width:300px;display:block"><img alt="" role="presentation" aria-hidden="true"
                              src="data:image/svg+xml;charset=utf-8,%3Csvg height=&#x27;368&#x27; width=&#x27;300&#x27; xmlns=&#x27;http://www.w3.org/2000/svg&#x27; version=&#x27;1.1&#x27;%3E%3C/svg%3E"
                              style="max-width:100%;display:block;position:static"></div><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,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"
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/4148f16065ce432f2390708eef5a0011/fe697/magicians.webp 75w,/static/4148f16065ce432f2390708eef5a0011/b3bb6/magicians.webp 150w,/static/4148f16065ce432f2390708eef5a0011/f86c0/magicians.webp 300w,/static/4148f16065ce432f2390708eef5a0011/47a60/magicians.webp 600w"
                              sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                              style="opacity:0" sizes="(min-width: 300px) 300px, 100vw" decoding="async" loading="lazy"
                              data-src="/static/4148f16065ce432f2390708eef5a0011/ff9c5/magicians.png"
                              data-srcset="/static/4148f16065ce432f2390708eef5a0011/c951a/magicians.png 75w,/static/4148f16065ce432f2390708eef5a0011/d6638/magicians.png 150w,/static/4148f16065ce432f2390708eef5a0011/ff9c5/magicians.png 300w,/static/4148f16065ce432f2390708eef5a0011/f0076/magicians.png 600w"
                              alt="Illustration of magicians">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/4148f16065ce432f2390708eef5a0011/fe697/magicians.webp,/static/4148f16065ce432f2390708eef5a0011/b3bb6/magicians.webp,/static/4148f16065ce432f2390708eef5a0011/f86c0/magicians.webp,/static/4148f16065ce432f2390708eef5a0011/47a60/magicians.webp"
                                sizes="(min-width: 300px) 300px, 100vw"><img data-gatsby-image-ssr="" data-main-image=""
                                style="opacity:0" sizes="(min-width: 300px) 300px, 100vw" decoding="async"
                                loading="lazy" src="/static/4148f16065ce432f2390708eef5a0011/ff9c5/magicians.png"
                                srcset="/static/4148f16065ce432f2390708eef5a0011/c951a/magicians.png,/static/4148f16065ce432f2390708eef5a0011/d6638/magicians.png,/static/4148f16065ce432f2390708eef5a0011/ff9c5/magicians.png,/static/4148f16065ce432f2390708eef5a0011/f0076/magicians.png"
                                alt="Illustration of magicians">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="dapps__Box-sc-1hb30un-23 gkLjYS">
                    <h2><span>The magic behind dapps</span></h2>
                    <p class="dapps__BoxText-sc-1hb30un-24 kNpaqE"><span>Dapps might feel like regular apps. But behind
                        the scenes they have some special qualities because they inherit all of Ethereum’s superpowers.
                        Here's what makes dapps different from apps.</span></p><a
                      class="Link__InternalLink-sc-e3riao-1 gCWUlE" href="..\what-is-ethereum\index.html"><span>What
                        makes Ethereum great?</span></a>
                  </div>
                  <div class="BoxGrid__Grid-sc-b0hjlf-2 bxvyFc">
                    <div color="gridOrange" class="BoxGrid__Box-sc-b0hjlf-4 kJbupR"><span size="6" mb="2rem" mt="0"
                        mr="0" ml="0" class="Emoji__StyledEmoji-sc-ihpuqw-0 LbqQu undefined"><img alt="👤"
                          src="https://twemoji.maxcdn.com/2/svg/1f464.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">No owners</h3>
                        <p class="BoxGrid__Body-sc-b0hjlf-1 fwkjos">Once deployed to Ethereum, dapp code can’t be taken
                          down. And anyone can use the dapp’s features. Even if the team behind the dapp disbanded you
                          could still use it. Once on Ethereum, it stays there.</p>
                      </div>
                    </div>
                    <div color="gridRed" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="📣" src="https://twemoji.maxcdn.com/2/svg/1f4e3.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">Free from censorship</h3>
                      </div>
                    </div>
                    <div color="gridPink" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="🤑" src="https://twemoji.maxcdn.com/2/svg/1f911.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">Built-in payments</h3>
                      </div>
                    </div>
                    <div color="gridRed" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="🔌" src="https://twemoji.maxcdn.com/2/svg/1f50c.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">Plug and play</h3>
                      </div>
                    </div>
                    <div color="gridGreen" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="🕵" src="https://twemoji.maxcdn.com/2/svg/1f575.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">One anonymous login</h3>
                      </div>
                    </div>
                    <div color="gridPurple" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="🔑" src="https://twemoji.maxcdn.com/2/svg/1f511.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">Backed by cryptography</h3>
                      </div>
                    </div>
                    <div color="gridRed" class="BoxGrid__Box-sc-b0hjlf-4 gVCgFj"><span size="6" mt="0" mr="0" mb="0"
                        ml="0"
                        class="Emoji__StyledEmoji-sc-ihpuqw-0 fwugWe BoxGrid__StyledEmoji-sc-b0hjlf-3 fUMhrJ undefined"><img
                          alt="📶" src="https://twemoji.maxcdn.com/2/svg/1f4f6.svg"
                          style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      <div>
                        <h3 class="BoxGrid__Title-sc-b0hjlf-0 iobxnh">No down time</h3>
                      </div>
                    </div>
                  </div>
                  <div class="dapps__Row-sc-1hb30un-5 btkUYr">
                    <div class="dapps__LeftColumn-sc-1hb30un-20 kMsWTL">
                      <h2><span>How dapps work</span></h2>
                      <p><span>Dapps have their backend code (smart contracts) running on a decentralized network and
                          not a centralized server. They use the Ethereum blockchain for data storage and smart
                          contracts for their app logic.</span></p>
                      <p><span>A smart contract is like a set of rules that live on-chain for all to see and run exactly
                          according to those rules. Imagine a vending machine: if you supply it with enough funds and
                          the right selection, you'll get the item you want. And like vending machines, smart contracts
                          can hold funds much like your Ethereum account. This allows code to mediate agreements and
                          transactions.</span></p>
                      <p><span>Once dapps are deployed on the Ethereum network you can't change them. Dapps can be
                          decentralized because they are controlled by the logic written into the contract, not an
                          individual or a company.</span></p><a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE DocLink__Container-sc-fck695-0 gwJlks"
                        href="/en/developers/docs/dapps/">
                        <div class="DocLink__EmojiCell-sc-fck695-4 hWbCBl"><span size="1" mr="1rem" mt="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 eHNQwh undefined"><img alt="📃"
                              src="https://twemoji.maxcdn.com/2/svg/1f4c3.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></div>
                        <div class="DocLink__TextCell-sc-fck695-1 jaVkEX">
                          <p class="DocLink__Title-sc-fck695-2 fOuSKw"><span>Intro to dapps</span></p>
                        </div><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                          class="Icon__StyledIcon-sc-1o8zi5s-0 DocLink__Arrow-sc-fck695-3 hgJqYx egKJTh" height="24"
                          width="24" xmlns="http://www.w3.org/2000/svg">
                          <path fill="none" d="M0 0h24v24H0z"></path>
                          <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"></path>
                        </svg>
                      </a><a class="Link__InternalLink-sc-e3riao-1 gCWUlE DocLink__Container-sc-fck695-0 gwJlks"
                        href="/en/developers/docs/smart-contracts/">
                        <div class="DocLink__EmojiCell-sc-fck695-4 hWbCBl"><span size="1" mr="1rem" mt="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 eHNQwh undefined"><img alt="📃"
                              src="https://twemoji.maxcdn.com/2/svg/1f4c3.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></div>
                        <div class="DocLink__TextCell-sc-fck695-1 jaVkEX">
                          <p class="DocLink__Title-sc-fck695-2 fOuSKw"><span>Smart contracts</span></p>
                        </div><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                          class="Icon__StyledIcon-sc-1o8zi5s-0 DocLink__Arrow-sc-fck695-3 hgJqYx egKJTh" height="24"
                          width="24" xmlns="http://www.w3.org/2000/svg">
                          <path fill="none" d="M0 0h24v24H0z"></path>
                          <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"></path>
                        </svg>
                      </a>
                    </div>
                    <div class="dapps__RightColumn-sc-1hb30un-21 fQxgud">
                      <div class="Callout__StyledCard-sc-tgt6fi-0 jiacsU dapps__StyledCallout-sc-1hb30un-28 bAOTxx">
                        <div data-gatsby-image-wrapper="" style="width:280px;height:200px"
                          class="gatsby-image-wrapper Callout__Image-sc-tgt6fi-2 jpjmqr"><img aria-hidden="true"
                            data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAYAAAAvxDzwAAAACXBIWXMAAC4jAAAuIwF4pT92AAAD2UlEQVQ4y22Sb0xbVRjGn9sSYmY0mQnJ/IJ+cFuixsXpF52ZibqYmCxBE/1ijOC+jCBmgy1zUKCjwEYLlFEoXVcoA9ryv6V/KPJXA0zImK4sY9PJCm1vb+9tKW0pUBnjeC4mmjjf5OTee3Lu733e5zzAf8pQ7kNPncB01/DoqA6+Z5EFX5AvBZ4rmw/sV86xqLH68HYFC7Kwhc+LQlDMrjD1HAsn4ZCruIOnqq0yAKtmFf1XIzDXhQ6PZXBp2pvBN81O/rvaWyyMyxsMjv8GQgiyzgcz5Te9qPEEGL2fhWqW/X+gjQKd2ihs9QKKC7w4XRDYX1LJfnLfQvbOGKtDsDQIJzUWfr30tu+VKg8L5RQrqZ72Pw3UVYibz+JAWieGXTuSvjmC0nLf0ZzSQLZNyb/RUC+Y8dZIulUn9DVYBFL9Czt9CG1MTvHPqBxfYf4BffjaMM7nLODjIyM4uK8bPzn/TPuhdwNd1yLF3ZrwKVBxV0fZTzuKhAyHUVA0aXlyupxLXXJxRL0QaNAFw7gmxP8Fqku8eP/QEH2T49FdIjFpw9Ar2WP2jjjpb1kbq6p9dPAw8aTbjeESgyFC8qu4nTwFt3tGxe1cnuSJao79Wn2XAz541Y0vT8xgXfjbH/YhkTy4TdDewJ8Y7tkg1rZYf0td8OVj4BnkzKCmlm3PpbB8Jb97VsXv5iuF3bwrwZRswC/76oIXUF1cwqmTsxRVgjHrVrrLso7ORuGboa4kcfckRWBjX+sa1OeW0HrBD833y+/WyP1rhZcCJO9K6HFReYCoildmDcd/f16X9wdwZ4aIPkkeeoi05/oqyr69/xJVFhfVOUyJJB2Z7dJF5Ner2RdHWhM0UpEChyZCblwObtfL/U9oXsmgJrLdrgoeMSnpyL9OEXTrV8F7CVpqOXHMxUl7itg7EylHZ4LQFaKqBWrBF7bOODymbbj0McdIS5y4tNEt2oTYddHCYUMcdE+KqaFtmJrCR+lPtonBFKHfhN4umbCliMu8vuI0Jz7rN0b3DXbE4TLGpWZVCDrZcqbbEAtNtCeJoznqEkMuVqsigL3EO02J+Wn3Y0KjQoZ7kovUN631RqywqcKXMTawCaOaA1XMTA2k4NRFpXYa+t56IYvC/Poy34HuWh69akEiNsOtSSIezh63bt2ztcfOZr0z+Yy7KwmqlloQxOjAppSOy4iNWXr74lNcyUWCZtlyRlsli8GmVWZIH4OtcRXIlHRg3JbakyxCzmV7QH1Mo8qk5uYwMzf+BPM/Eshy76Gl3A9F7iIaL3rxYJRAV7IiKmPc12OYMm/io9fd+AtF9mOcLxTuOAAAAABJRU5ErkJggg=="
                            alt="">
                          <picture>
                            <source type="image/webp"
                              data-srcset="/static/810eb64d89629231aa4d8c7fe5f20ee5/d5009/developers-eth-blocks.webp 280w,/static/810eb64d89629231aa4d8c7fe5f20ee5/69de1/developers-eth-blocks.webp 560w"
                              sizes="280px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                              sizes="280px" decoding="async" loading="lazy"
                              data-src="/static/810eb64d89629231aa4d8c7fe5f20ee5/b4733/developers-eth-blocks.png"
                              data-srcset="/static/810eb64d89629231aa4d8c7fe5f20ee5/b4733/developers-eth-blocks.png 280w,/static/810eb64d89629231aa4d8c7fe5f20ee5/b9fe6/developers-eth-blocks.png 560w"
                              alt="Illustration of a hand building an ETH symbol out of lego bricks.">
                          </picture><noscript>
                            <picture>
                              <source type="image/webp"
                                srcset="/static/810eb64d89629231aa4d8c7fe5f20ee5/d5009/developers-eth-blocks.webp,/static/810eb64d89629231aa4d8c7fe5f20ee5/69de1/developers-eth-blocks.webp"
                                sizes="280px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                sizes="280px" decoding="async" loading="lazy"
                                src="/static/810eb64d89629231aa4d8c7fe5f20ee5/b4733/developers-eth-blocks.png"
                                srcset="/static/810eb64d89629231aa4d8c7fe5f20ee5/b4733/developers-eth-blocks.png,/static/810eb64d89629231aa4d8c7fe5f20ee5/b9fe6/developers-eth-blocks.png"
                                alt="Illustration of a hand building an ETH symbol out of lego bricks.">
                            </picture>
                          </noscript>
                          <script
                            type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                        </div>
                        <div class="Callout__Content-sc-tgt6fi-3 ddcnaz">
                          <div>
                            <h3><span>Learn to build a dapp</span></h3>
                            <p class="Callout__Description-sc-tgt6fi-1 kSma-dE"><span>Our community developer portal has
                                docs, tools, and frameworks to help you start building a dapp.</span></p>
                          </div>
                          <div><a
                              class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                              href="..\developers\index.html"><span>Start building</span></a></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <footer class="Footer__StyledFooter-sc-1to993d-0 gvoBKJ">
          <div class="Footer__FooterTop-sc-1to993d-1 kFKfdz">
            <div class="Footer__LastUpdated-sc-1to993d-2 bWGwos"><span>Website last updated</span>:
              <!-- -->
              <!-- -->December 1, 2020
            </div>
            <div class="Footer__SocialIcons-sc-1to993d-9 kdLbod"><a
                href="https://github.com/ethereum/ethereum-org-website" target="_blank" rel="noopener noreferrer"
                aria-label="GitHub"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 496 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z">
                  </path>
                </svg></a><a href="https://twitter.com/ethdotorg" target="_blank" rel="noopener noreferrer"
                aria-label="Twitter"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 512 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                  </path>
                </svg></a><a href="https://youtube.com/channel/UCNOfzGXD_C9YMYmnefmPH0g" target="_blank"
                rel="noopener noreferrer" aria-label="Youtube"><svg stroke="currentColor" fill="currentColor"
                  stroke-width="0" viewbox="0 0 576 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                  </path>
                </svg></a><a href="https://discord.gg/CetY6Y4" target="_blank" rel="noopener noreferrer"
                aria-label="Discord"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 640 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                  </path>
                </svg></a></div>
          </div>
          <div class="Footer__LinkGrid-sc-1to993d-3 hlbLsM">
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Use Ethereum</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a aria-current="page"
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz active"
                    href="index.html"><span>Decentralized applications (dapps)</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\staking\index.html"><span>Stake ETH</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Learn</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\history\index.html"><span>History of Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                      fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                      width="12px" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                      </path>
                    </svg></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\web3\index.html"><span>What is Web3?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Developers</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\index.html"><span>Get started</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/developers/docs/"><span>Documentation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\local-environment\index.html"><span>Set up local environment</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Ecosystem</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\community\index.html"><span>Community hub</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\foundation\index.html"><span>Ethereum Foundation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://blog.ethstake.exchange/" target="_blank" rel="noopener noreferrer"><span>Ethereum
                      Foundation Blog</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://esp.ethereum.foundation" target="_blank" rel="noopener noreferrer"><span>Ecosystem
                      Support Program</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/community/grants"><span>Ecosystem Grant Programs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\assets\index.html"><span>Ethereum brand assets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://devcon.org/" target="_blank" rel="noopener noreferrer"><span>Devcon</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Enterprise</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Enterprise</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>About ethstake.exchange</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html"><span>About us</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html#open-jobs"><span>Jobs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\contributing\index.html"><span>Contributing</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\languages\index.html"><span>Language support</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\privacy-policy\index.html"><span>Privacy policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\terms-of-use\index.html"><span>Terms of use</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\cookie-policy\index.html"><span>Cookie policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/cdn-cgi/l/email-protection#f78785928484b792839f8483969c92d9928f949f96999092" target="_blank"
                    rel="noopener noreferrer"><span>Contact</span></a></li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <div id="gatsby-announcer"
      style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0"
      aria-live="assertive" aria-atomic="true"></div>
  </div>
  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let svgIcon = document.getElementsByClassName('Mobile__GlyphButton')[0]
    let menuBtn = document.getElementsByClassName('Mobile__MenuButton')
    let hideMenu = document.getElementsByClassName('Mobile__MobileModal')[0]
    let menuContainer = document.getElementsByClassName('Mobile__MenuContainer')[0]
    let menuUl = document.getElementsByClassName('Mobile__MenuItems')[0]
    let menuBottom = document.getElementsByClassName('Mobile__BottomMenu')[0]
    let searchPage = document.getElementsByClassName('Mobile__MenuContainer')[1]
    let closeBtn = document.getElementsByClassName('Mobile__CloseIconContainer')[0]
    let searchBtn = document.getElementsByClassName('Mobile__BottomItem')[0]
    menuBtn[1].addEventListener('click', function () {
      this.toggleAttribute('open')

      let type = this.hasAttribute('open')
      let path = svgIcon.getElementsByTagName('path')[0]
      let d = path.getAttribute('d')
      path.style='transition: all ease-in 0.25s;'
      console.log(path, d,'aaaa')
      if (type) {
        hideMenu.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuContainer.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        path.setAttribute('d','M 2 19 l 10 -14 l 0 0 l 10 14 M 2 19 l 10 7 M 12 26 l 10 -7 M 2 22 l 10 15 l 0 0 l 9 -15')
        setTimeout(()=>{
          path.setAttribute('d','M 2 13 l 0 -3 l 20 0 l 0 3 M 7 14 l 10 10 M 7 24 l 10 -10 M 2 25 l 0 3 l 20 0 l 0 -3')

        },700)

      } else {
        hideMenu.style = 'transition: all ease-in 0.2s;display: none; opacity: 0;'
        menuContainer.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        path.setAttribute('d','M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0')

      }
       // menuUl.toggleAttribute('class', 'gYetwr')
    })
    menuBtn[0].addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    searchBtn.addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    closeBtn.addEventListener('click', function () {
      console.log('111')
      searchPage.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'

    })
    console.log(menuBtn, '......')
    window.dev = undefined
    if (window.dev === true || !(navigator.doNotTrack === '1' || window.doNotTrack === '1')) {
      window._paq = window._paq || [];



      window._paq.push(['setTrackerUrl', 'https://matomo.ethstake.exchange/matomo.php']);
      window._paq.push(['setSiteId', '4']);
      window._paq.push(['enableHeartBeatTimer']);
      window.start = new Date();

      (function () {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript'; g.async = true; g.defer = true; g.src = 'https://matomo.ethstake.exchange/matomo.js'; s.parentNode.insertBefore(g, s);
      })();

      if (window.dev === true) {
        console.debug('[Matomo] Tracking initialized')
        console.debug('[Matomo] matomoUrl: https://matomo.ethstake.exchange, siteId: 4')
      }
    }
  </script><noscript><img
      src="https://matomo.ethstake.exchange/matomo.php?idsite=4&rec=1&url=https://ethstake.exchange/en/dapps/"
      style="border:0" alt="tracker"></noscript>
  <script
    id="gatsby-script-loader">/*<![CDATA[*/window.pagePath = "/en/dapps/"; window.___webpackCompilationHash = "0375e64c51e377521456";/*]]>*/</script>
  <script
    id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping = { "polyfill": ["/polyfill-b6350ac254e29f22a1e4.js"], "app": ["/app-b670b5ed3a389af0ed04.js"], "component---src-pages-404-js": ["/component---src-pages-404-js-a6aee0605f3068868f92.js"], "component---src-pages-assets-js": ["/component---src-pages-assets-js-ba78d988431646b4760b.js"], "component---src-pages-community-js": ["/component---src-pages-community-js-5ca1d5f82d581db39798.js"], "component---src-pages-conditional-dapps-js": ["/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js"], "component---src-pages-conditional-eth-js": ["/component---src-pages-conditional-eth-js-21644356026cce06349e.js"], "component---src-pages-conditional-wallets-index-js": ["/component---src-pages-conditional-wallets-index-js-6c55787f35fe5cc07ef3.js"], "component---src-pages-conditional-what-is-ethereum-js": ["/component---src-pages-conditional-what-is-ethereum-js-144e7cb6cba17bff7608.js"], "component---src-pages-contributing-translation-program-acknowledgements-js": ["/component---src-pages-contributing-translation-program-acknowledgements-js-a596bd2823410bf53c11.js"], "component---src-pages-contributing-translation-program-contributors-js": ["/component---src-pages-contributing-translation-program-contributors-js-4f2a18f6f82d2a84de27.js"], "component---src-pages-developers-index-js": ["/component---src-pages-developers-index-js-eba978370f325b125b03.js"], "component---src-pages-developers-learning-tools-js": ["/component---src-pages-developers-learning-tools-js-88bd1bcad31958f723e3.js"], "component---src-pages-developers-local-environment-js": ["/component---src-pages-developers-local-environment-js-768783f49122fff8d82f.js"], "component---src-pages-developers-tutorials-js": ["/component---src-pages-developers-tutorials-js-f82f9627cb9b2ce3318b.js"], "component---src-pages-get-eth-js": ["/component---src-pages-get-eth-js-e6c689f28770388e40be.js"], "component---src-pages-index-js": ["/component---src-pages-index-js-c7245d4f213dfe2095c4.js"], "component---src-pages-languages-js": ["/component---src-pages-languages-js-b1a3a1c01ec6bdcd87ac.js"], "component---src-pages-run-a-node-js": ["/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js"], "component---src-pages-stablecoins-js": ["/component---src-pages-stablecoins-js-4bc5b567f2c38baaaa5b.js"], "component---src-pages-stakenow-js": ["/component---src-pages-stakenow-js-40c51639947629777abf.js"], "component---src-pages-staking-deposit-contract-js": ["/component---src-pages-staking-deposit-contract-js-ac8273bd9f4711b38540.js"], "component---src-pages-staking-index-js": ["/component---src-pages-staking-index-js-3d50b1ef7b3b9814f6e2.js"], "component---src-pages-studio-js": ["/component---src-pages-studio-js-c16fabe2f5808fafce99.js"], "component---src-pages-upgrades-get-involved-bug-bounty-js": ["/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js"], "component---src-pages-upgrades-get-involved-index-js": ["/component---src-pages-upgrades-get-involved-index-js-207fa70ee7aa5720b909.js"], "component---src-pages-upgrades-index-js": ["/component---src-pages-upgrades-index-js-dd49283310be18b0a199.js"], "component---src-pages-upgrades-vision-js": ["/component---src-pages-upgrades-vision-js-2c86e72cede9c6155cbf.js"], "component---src-pages-wallets-find-wallet-js": ["/component---src-pages-wallets-find-wallet-js-97c53af70032ab1edbc4.js"], "component---src-templates-static-js": ["/component---src-templates-static-js-3cd4030c1e191ee95c2b.js"], "component---src-templates-upgrade-js": ["/component---src-templates-upgrade-js-700f5089c86b74f8484f.js"], "component---src-templates-use-cases-js": ["/component---src-templates-use-cases-js-75c0d7fec84444cc8c68.js"] };/*]]>*/</script>
  <script src="/polyfill-b6350ac254e29f22a1e4.js" nomodule=""></script>
  <script src="/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js" async=""></script>
  <script src="/da0bcc46d10bae02bd9afc23823ae5fd5b6c062d-5a1ae5c4bcbe6fc6589f.js" async=""></script>
  <script src="/app-b670b5ed3a389af0ed04.js" async=""></script>
  <script src="/0f1ac474-e8f788f62189f421a856.js" async=""></script>
  <script src="/0c428ae2-2128ff22fce458b543bd.js" async=""></script>
  <script src="/1bfc9850-0f18e2d74feedfc6e426.js" async=""></script>
  <script src="/ae51ba48-34d54094a2c04f215fb8.js" async=""></script>
  <script src="/252f366e-2705b607be296edabcea.js" async=""></script>
  <script src="/framework-4e285adfb333f1b50c05.js" async=""></script>
  <script src="/webpack-runtime-d600da28e471609bf3f3.js" async=""></script>
</body>

</html>