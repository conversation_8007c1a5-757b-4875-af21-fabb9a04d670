@font-face {
    font-family: 'DINMittelschrift';
    src: url('font/DINMittelschrift.eot');
    src: url('font/DINMittelschrift.eot?#iefix') format('embedded-opentype'),
        url('font/DINMittelschrift.woff2') format('woff2'),
        url('font/DINMittelschrift.woff') format('woff'),
        url('font/DINMittelschrift.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
body, html {
    font-family: Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;
	overflow-x: hidden;
    /*line-height: 1.2;*/
    background-color: #fbfbfb;
}
body, html, *, a, a:hover, a:active, a:focus, h1, h2, h3, h4, h5, h6, ul, li {
	text-decoration: none ! important;
	outline: none ! important;
	margin: 0;
	padding: 0;
}

.custom-ctn, .nav-tabs.tab-2 h1, .tab-3 a, .submit {
	transition: all .3s;
}
/*header*/
.w-auto {
  width: auto;
}
.top-container {
    background: linear-gradient(225deg,#f0f0f5,#eddfce 100%,#f7797d 0);
}
.link-btn1 {
    background: rgba(142,87,41,.2);
	color:#8e5729;
    height: 56px;
    border-radius: 10px;
}

.link-btn{
    background: #6481e7;
    height: 56px;
    border-radius: 10px;
}
.link-btn,  h3.h3-title.tips {
    font-size: 22px;
}
.shap {
	max-width: 360px;
}
.p-receive {
	font-weight: 600;
}
.p-receive h1 {
    color: #8e5729;
    font-size: 36px;
}
.p-receive h2 {
    color: rgba(142,87,41,.5);
    font-family: PingFangSC-Semibold,PingFang SC;
}
.br-rounded {
	border-radius: 16px;
}
.custom-ctn {
	font-size: 28px;
    padding: 0 32px;
    height: 72px;
    background: #8e5729;
    color: #fff;
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 50%);
    font-weight: 600;
    border: 1px solid #8e5729;
    border-radius: 10px;
}
.custom-ctn:hover, .submit:hover {
	background: #fff;
	color: #8e5729;
}
/*Tabing section*/
.nav-tabs.tab-1 h1 {
    color: #a1a1b3;
    font-size: 32px;
}
.nav-tabs.tab-1 .active h6 {
    color: #282c3c;
    font-size: 17px;
}
.nav-tabs.tab-1 .active:after {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    content: "";
    width: 100px;
    height: 8px;
    background: #8e5729;
}
.nav-tabs.tab-2 h1 {
    width: 220px;
    height: 68px;
    background: rgba(237,206,182,.48);
    border-radius: 18px 18px 0 0;
    color: #000;
    font-size: 28px;
}
.nav-tabs.tab-2 .active h6,.nav-tabs.tab-2 h1:hover, .tab-3 a.active, .tab-3 a:hover {
    color: #fff;
    background: #8e5729;
}
#exTab2 .panel-body, .mining-body, .share_content, .content {
    box-shadow: 6px 12px 20px 0 rgb(0 0 0 / 5%);
    color: #000;
}
.p-receive h2, .mining-body h3, .toast-msg {
    font-size: 24px;
}
#accordion2 .accordion-inner {
   font-size: 26px;
}
h2.h2-title, h3.h3-title, .usdt_content span, .t1 {
    font-size: 28px;
}
h1.h1-title {
    color: #282c3c;
    font-size: 36px;
}
h3.h3-title, #accordion2 .accordion-inner, .t1-data {
    color: #a1a1b3;
}
h2.h2-title {
    font-family: DINMittelschrift;
}
.h2-title.blue, .blue {
    color: #2082fe;
}	
.left_icon {
    background: linear-gradient(1turn,rgba(90,71,217,.09),#71a8e0);
    top: 0;
    left: -32px;
    bottom: 0;
    width: 8px;
    height: 40px;
    border-radius: 4px;
}
.left_icon.red {
	background: linear-gradient(1turn,rgba(248,164,164,.2),#e1506a);
}

/*Mining animation*/
.section_title {
	color: #000;
	font-size: 36px;
}
.section_subtitle {
	color: #a1a1b3;
	font-size: 24px;
}

/*Audit report*/
.botton-icon {
    width: 200px;
}
/*Help center*/
.accordion-group {
    box-shadow: 0 4px 40px 1px rgb(0 0 0 / 3%);
}
img.arrow {
    -webkit-transform: rotate( 180deg);
}
.accordion-toggle[aria-expanded="false"] img.arrow {
    -webkit-transform: rotate( 180deg);
}
.accordion-toggle[aria-expanded="true"] img.arrow {
    -webkit-transform: rotate( 0deg);
}
/*account*/
img.usdt_icon {
  width: 44px;
}
.change_all {
    color: #8e5729;
    font-size: 24px;
      bottom: 10px;
}
input.change_input.ff {
    border: none;
    font-size: 56px;
    width: 234px;
}
.p-input ::placeholder {
    color: #c3c5cb;
}
.exchange-btn {
  width: 366px;
}
.divider {
    width: 1px;
    height: 88px;
    background: #ebedf3;
}
.tab-3 a {
    color: #000;
    height: 60px;
    padding: 0 36px;
    border: 2px solid #8e5729;
    font-size: 28px;
}
.tab-3 li:first-child a {
    border-radius: 6px 0 0 6px;
    border-right: none;
}
.tab-3 li:last-child a {
    border-radius: 0 6px 6px 0;
    border-left: none;
}
.custom-ctn.rcv-btn {
    border-radius: 50px;
}
.modal-open .modal-backdrop {
  background-color: transparent;
}
 .modal-content {
    position: absolute;
    top: 50%;
    right: 0;
    left: 50%;
    transform: translate(-50%, -50%);
}
.share-modal-content {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
}
.p-address-slider .slider, .p-address-slider .slick-slide {
  transform:rotate(180deg);
}
/*Share*/
.bg_share {
   max-width: 520px;
}
#sharemyModal .close {
    right: 15px;
    opacity: 1;
}
.submit {
    padding: 0 30px;
    height: 48px;
    background: #8e5729;
    border-radius: 10px;
    font-size: 22px;
    color: #fff;
    border: 1px solid;
}
.submits {
    padding: 0 30px;
    height: 48px;
    background: #6660b1;
    border-radius: 10px;
    font-size: 22px;
    color: #fff;
    border: 1px solid;
}
.share_content input, .share_content ::placeholder {
  color: #a1a1b3;
  font-size: 24px;
  max-width: 420px;
}
.share-icon, .submit {
    cursor: pointer;
}
.share-icon, .submits {
    cursor: pointer;
}

.toast-msg {
   position: fixed;
   top: 0;
   left: 0;
   right: 0;
   background: #e6ffe6;
   opacity: 0;
   z-index: -1;
}
/*.active */
.toast-msg.active {
opacity: 1;
z-index: 999999;
}
#sharemyModal .modal-dialog {
    max-width: 100%;
    margin: 0;
}

.popup_container{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99999;
    display: none;
}
.cover{
    position: relative;
    width: 100%;
    height: 100%;
    background: hsla(0,0%,84.7%,.5);
    display: none;
}
.popup_container .select-nextwork-modal {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    height: 16.62rem;
    background: #fff;
    display: none;
}
.popup_container .select-nextwork-modal .modal-header {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #ebedf3;
}
.popup_container .select-nextwork-modal .modal-header .modal-header-title{
    color: #282c3c;
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1.5rem;
}
.popup_container .select-nextwork-modal .modal-header .close-img{
    width: 1.9rem;
    height: 1.9rem;
}
.popup_container .select-nextwork-modal .modal-body .modal-nextwork-cell{
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: .4rem;
}
.popup_container .select-nextwork-modal .modal-body .modal-nextwork-cell .img{
    width: 2.2rem;
    height: 2.2rem;
    margin-right: 1rem;
}
 .popup_container .select-nextwork-modal .modal-body .modal-nextwork-cell .name{
    color: #282c3c;
    font-size:1.2rem;
}

.fun-mode{
    display: flex;
    align-items: center;
    font-size: 16px;
    color: rgb(146, 146, 146);
    background-color: #d4e7ff;
    margin: -8px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 8px;
}
.fun-img{
    width: 35px;
    height: 35px;
    margin-right: 20px;
}
.fun_fonts{
    font-size: 20px;
    color: rgb(41, 41, 41);
}
.fun_ul{
    padding: 6px;
    border-bottom: 1px solid #d4d4d4;
}
.fun_uls{
    padding: 6px;
    border-bottom: 1px solid #d4d4d4;
    margin-top: 16px;
    color: rgb(146, 146, 146);
}
.fun-btn {
    margin: 30px 0;
    width: 200px;
    height: 20px;
    font-size: 18px;
    padding: 20px 32px;
    background: #6481e7;
    color: #fff;
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 50%);
    font-weight: 600;
    border: 1px solid #6481e7;
    border-radius: 8px;
  }
  .fun-flex{
      display: flex;
      justify-content: space-around;
      margin-bottom: 6px;
  }
/*Media query*/
@media (max-width: 680px) {
	.p-receive h2, .mining-body h3,header h4, .mining-body h2.h2-title, .change_all, .share_content h3.h3-title.tips, .share_content input, .share_content ::placeholder, .submit {
    font-size: 13px;
}
.p-receive h2, .mining-body h3,header h4, .mining-body h2.h2-title, .change_all, .share_content h3.h3-title.tips, .share_content input, .share_content ::placeholder, .submits {
    font-size: 13px;
}
   body, p,.tab-3 a, .custom-ctn, h2.h2-title, h3.h3-title, .section_subtitle, h3.h3-title.tips, .t1, .nav-tabs.tab-2 h1, .usdt_content span {
   	font-size: 14px;
   }
   .section_title {
   font-size: 16.6px;
   }
   #accordion2 .accordion-inner {
    font-size: 16.6px;
   }
   h1.h1-title, .toast-msg, .share_content h3.h3-title {
   font-size: 15px;
   }
   .p-receive h1 {
    font-size: 18px;
   }
   .nav-tabs.tab-1 h1 {
    font-size: 20px;
   }
   .nav-tabs.tab-1 .active h6 {
   	font-size: 16.6px;
   }
   input.change_input.ff {
    font-size: 30px;
   }
   .title_icon {
    width: 102.88px;
   }
   img.change_icon {
   	width: 80%;
   }
   img.usdt_icon, .toast-msg img, .share-icon {
   	width: 23.03px;
   }
   .logo-icon1 {
    width: 17.91px;
   }
   .logo-icon {
    width: 27px;
   }
   .logo-iconsown{
       width: 17px;
   }
   img.arrow {
    width: 15.36px;
   }
   .link-btn,.link-btn1 {
    height: 29.59px;
   }
   .custom-ctn, .nav-tabs.tab-2 h1, .tab-3 a {
   	height: 30px;
   	padding: 0 16px;
   }
   .custom-ctn.rcv-btn {
    height: 42px;
   }
   .submit {
    height: 30px;
    padding: 0 10px;
   }
   .submits {
    height: 30px;
    padding: 0 10px;
   }
   .left_icon {
    width: 3px;
    height: 20px;
    left: -16px;
   }
   .nav-tabs.tab-1 .active:after {
   	width: 50px;
    height: 4px;
    bottom: -2px;
   }
   .botton-icon {
    width: 90px;
   }
   .nav-tabs.tab-2 h1 {
    width: 93px;
   }
   .exchange-btn {
    width: 160px;
   }
   .change_all {
    bottom: 4px;
   }
   .divider {
    height: 40px;
   }
   .custom-ctn, .submit, .link-btn {
    border-radius: 4px;
   }
   .custom-ctn, .submits, .link-btn {
    border-radius: 4px;
   }
   .nav-tabs.tab-2 h1 {
    border-radius: 8px 8px 0 0;
   }
   #sharemyModal .close {
    transform: scale(0.5);
    padding: 8px;
   }
   .tab-3 a {
    border-width: 1px;
}

}


@media (max-width: 360px) {
  .p-receive h2,header h4, .mining-body h2.h2-title, .change_all, .share_content h3.h3-title.tips, .share_content input, .share_content ::placeholder, .submit {
    font-size: 10px;
   }
   .p-receive h2,header h4, .mining-body h2.h2-title, .change_all, .share_content h3.h3-title.tips, .share_content input, .share_content ::placeholder, .submits {
    font-size: 10px;
   }
   body, p,.tab-3 a, .mining-body h3, #accordion2 .accordion-inner, .share_content h3.h3-title, .custom-ctn, h2.h2-title, h3.h3-title, .section_subtitle, h3.h3-title.tips, .t1, .nav-tabs.tab-2 h1, .usdt_content span {
    font-size: 12px;
   }
   .nav-tabs.tab-1 h1, .section_title {
    font-size: 14px;
   }
   .logo-icon, img.usdt_icon, .toast-msg img {
    width: 15.36px;
   }
   img.arrow {
    width: 10.23px;
   }
   .share-icon {
    width: 17.27px;
   }
   .title_icon {
    width: 81.88px;
   }
   .link-btn {
    height: 18px;
   }
   .submit {
    height: 20px;
   }
   .submits {
    height: 20px;
   }
   .nav-tabs.tab-1 .active h6 {
    font-size: 15px;
   }
}