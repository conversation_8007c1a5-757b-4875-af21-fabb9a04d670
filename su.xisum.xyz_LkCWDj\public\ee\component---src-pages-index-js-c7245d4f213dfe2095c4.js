(self.webpackChunkethereum_org_website=self.webpackChunkethereum_org_website||[]).push([[9678],{73268:function(e,t,n){n(99752),n(82376),n(73181),n(23484),n(2388),n(88621),n(60403),n(84755),n(25438),n(90332),n(40658),n(40197),n(44914),n(52420),n(60160),n(60970),n(10408),n(73689);var r=n(40857);e.exports=r.Math},69789:function(e,t,n){n(9653),n(93299),n(35192),n(33161),n(44048),n(78285),n(44363),n(55994),n(61874),n(9494),n(31354),n(56977),n(55147);var r=n(40857);e.exports=r.Number},96077:function(e,t,n){var r=n(17854),o=n(60614),i=r.String,a=r.TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw a("Can't set "+i(e)+" as a prototype")}},79587:function(e,t,n){var r=n(60614),o=n(70111),i=n(27674);e.exports=function(e,t,n){var a,l;return i&&r(a=t.constructor)&&a!==n&&o(l=a.prototype)&&l!==n.prototype&&i(e,l),e}},55988:function(e,t,n){var r=n(70111),o=Math.floor;e.exports=Number.isInteger||function(e){return!r(e)&&isFinite(e)&&o(e)===e}},66736:function(e){var t=Math.expm1,n=Math.exp;e.exports=!t||t(10)>22025.465794806718||t(10)<22025.465794806718||-2e-17!=t(-2e-17)?function(e){return 0==(e=+e)?e:e>-1e-6&&e<1e-6?e+e*e/2:n(e)-1}:t},26130:function(e,t,n){var r=n(64310),o=Math.abs,i=Math.pow,a=i(2,-52),l=i(2,-23),c=i(2,127)*(2-l),s=i(2,-126);e.exports=Math.fround||function(e){var t,n,i=o(e),d=r(e);return i<s?d*(i/s/l+1/a-1/a)*s*l:(n=(t=(1+l/a)*i)-(t-i))>c||n!=n?d*(1/0):d*n}},20403:function(e){var t=Math.log,n=Math.LOG10E;e.exports=Math.log10||function(e){return t(e)*n}},26513:function(e){var t=Math.log;e.exports=Math.log1p||function(e){return(e=+e)>-1e-8&&e<1e-8?e-e*e/2:t(1+e)}},64310:function(e){e.exports=Math.sign||function(e){return 0==(e=+e)||e!=e?e:e<0?-1:1}},77023:function(e,t,n){var r=n(17854).isFinite;e.exports=Number.isFinite||function(e){return"number"==typeof e&&r(e)}},2814:function(e,t,n){var r=n(17854),o=n(47293),i=n(1702),a=n(41340),l=n(53111).trim,c=n(81361),s=i("".charAt),d=r.parseFloat,m=r.Symbol,u=m&&m.iterator,p=1/d(c+"-0")!=-1/0||u&&!o((function(){d(Object(u))}));e.exports=p?function(e){var t=l(a(e)),n=d(t);return 0===n&&"-"==s(t,0)?-0:n}:d},83009:function(e,t,n){var r=n(17854),o=n(47293),i=n(1702),a=n(41340),l=n(53111).trim,c=n(81361),s=r.parseInt,d=r.Symbol,m=d&&d.iterator,u=/^[+-]?0x/i,p=i(u.exec),f=8!==s(c+"08")||22!==s(c+"0x16")||m&&!o((function(){s(Object(m))}));e.exports=f?function(e,t){var n=l(a(e));return s(n,t>>>0||(p(u,n)?16:10))}:s},27674:function(e,t,n){var r=n(1702),o=n(19670),i=n(96077);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(a){}return function(n,r){return o(n),i(r),t?e(n,r):n.__proto__=r,n}}():void 0)},40857:function(e,t,n){var r=n(17854);e.exports=r},58003:function(e,t,n){var r=n(3070).f,o=n(92597),i=n(5112)("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!o(e,i)&&r(e,i,{configurable:!0,value:t})}},38415:function(e,t,n){"use strict";var r=n(17854),o=n(19303),i=n(41340),a=n(84488),l=r.RangeError;e.exports=function(e){var t=i(a(this)),n="",r=o(e);if(r<0||r==1/0)throw l("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n}},53111:function(e,t,n){var r=n(1702),o=n(84488),i=n(41340),a=n(81361),l=r("".replace),c="["+a+"]",s=RegExp("^"+c+c+"*"),d=RegExp(c+c+"*$"),m=function(e){return function(t){var n=i(o(t));return 1&e&&(n=l(n,s,"")),2&e&&(n=l(n,d,"")),n}};e.exports={start:m(1),end:m(2),trim:m(3)}},50863:function(e,t,n){var r=n(1702);e.exports=r(1..valueOf)},41340:function(e,t,n){var r=n(17854),o=n(70648),i=r.String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return i(e)}},81361:function(e){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},99752:function(e,t,n){var r=n(82109),o=n(26513),i=Math.acosh,a=Math.log,l=Math.sqrt,c=Math.LN2;r({target:"Math",stat:!0,forced:!i||710!=Math.floor(i(Number.MAX_VALUE))||i(1/0)!=1/0},{acosh:function(e){return(e=+e)<1?NaN:e>94906265.62425156?a(e)+c:o(e-1+l(e-1)*l(e+1))}})},82376:function(e,t,n){var r=n(82109),o=Math.asinh,i=Math.log,a=Math.sqrt;r({target:"Math",stat:!0,forced:!(o&&1/o(0)>0)},{asinh:function e(t){return isFinite(t=+t)&&0!=t?t<0?-e(-t):i(t+a(t*t+1)):t}})},73181:function(e,t,n){var r=n(82109),o=Math.atanh,i=Math.log;r({target:"Math",stat:!0,forced:!(o&&1/o(-0)<0)},{atanh:function(e){return 0==(e=+e)?e:i((1+e)/(1-e))/2}})},23484:function(e,t,n){var r=n(82109),o=n(64310),i=Math.abs,a=Math.pow;r({target:"Math",stat:!0},{cbrt:function(e){return o(e=+e)*a(i(e),1/3)}})},2388:function(e,t,n){var r=n(82109),o=Math.floor,i=Math.log,a=Math.LOG2E;r({target:"Math",stat:!0},{clz32:function(e){return(e>>>=0)?31-o(i(e+.5)*a):32}})},88621:function(e,t,n){var r=n(82109),o=n(66736),i=Math.cosh,a=Math.abs,l=Math.E;r({target:"Math",stat:!0,forced:!i||i(710)===1/0},{cosh:function(e){var t=o(a(e)-1)+1;return(t+1/(t*l*l))*(l/2)}})},60403:function(e,t,n){var r=n(82109),o=n(66736);r({target:"Math",stat:!0,forced:o!=Math.expm1},{expm1:o})},84755:function(e,t,n){n(82109)({target:"Math",stat:!0},{fround:n(26130)})},25438:function(e,t,n){var r=n(82109),o=Math.hypot,i=Math.abs,a=Math.sqrt;r({target:"Math",stat:!0,forced:!!o&&o(1/0,NaN)!==1/0},{hypot:function(e,t){for(var n,r,o=0,l=0,c=arguments.length,s=0;l<c;)s<(n=i(arguments[l++]))?(o=o*(r=s/n)*r+1,s=n):o+=n>0?(r=n/s)*r:n;return s===1/0?1/0:s*a(o)}})},90332:function(e,t,n){var r=n(82109),o=n(47293),i=Math.imul;r({target:"Math",stat:!0,forced:o((function(){return-5!=i(4294967295,5)||2!=i.length}))},{imul:function(e,t){var n=65535,r=+e,o=+t,i=n&r,a=n&o;return 0|i*a+((n&r>>>16)*a+i*(n&o>>>16)<<16>>>0)}})},40658:function(e,t,n){n(82109)({target:"Math",stat:!0},{log10:n(20403)})},40197:function(e,t,n){n(82109)({target:"Math",stat:!0},{log1p:n(26513)})},44914:function(e,t,n){var r=n(82109),o=Math.log,i=Math.LN2;r({target:"Math",stat:!0},{log2:function(e){return o(e)/i}})},52420:function(e,t,n){n(82109)({target:"Math",stat:!0},{sign:n(64310)})},60160:function(e,t,n){var r=n(82109),o=n(47293),i=n(66736),a=Math.abs,l=Math.exp,c=Math.E;r({target:"Math",stat:!0,forced:o((function(){return-2e-17!=Math.sinh(-2e-17)}))},{sinh:function(e){return a(e=+e)<1?(i(e)-i(-e))/2:(l(e-1)-l(-e-1))*(c/2)}})},60970:function(e,t,n){var r=n(82109),o=n(66736),i=Math.exp;r({target:"Math",stat:!0},{tanh:function(e){var t=o(e=+e),n=o(-e);return t==1/0?1:n==1/0?-1:(t-n)/(i(e)+i(-e))}})},10408:function(e,t,n){n(58003)(Math,"Math",!0)},73689:function(e,t,n){var r=n(82109),o=Math.ceil,i=Math.floor;r({target:"Math",stat:!0},{trunc:function(e){return(e>0?i:o)(e)}})},9653:function(e,t,n){"use strict";var r=n(19781),o=n(17854),i=n(1702),a=n(54705),l=n(31320),c=n(92597),s=n(79587),d=n(47976),m=n(52190),u=n(57593),p=n(47293),f=n(8006).f,g=n(31236).f,h=n(3070).f,x=n(50863),y=n(53111).trim,b="Number",w=o.Number,v=w.prototype,E=o.TypeError,_=i("".slice),C=i("".charCodeAt),N=function(e){var t=u(e,"number");return"bigint"==typeof t?t:k(t)},k=function(e){var t,n,r,o,i,a,l,c,s=u(e,"number");if(m(s))throw E("Cannot convert a Symbol value to a number");if("string"==typeof s&&s.length>2)if(s=y(s),43===(t=C(s,0))||45===t){if(88===(n=C(s,2))||120===n)return NaN}else if(48===t){switch(C(s,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+s}for(a=(i=_(s,2)).length,l=0;l<a;l++)if((c=C(i,l))<48||c>o)return NaN;return parseInt(i,r)}return+s};if(a(b,!w(" 0o1")||!w("0b1")||w("+0x1"))){for(var I,M=function(e){var t=arguments.length<1?0:w(N(e)),n=this;return d(v,n)&&p((function(){x(n)}))?s(Object(t),n,M):t},S=r?f(w):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),T=0;S.length>T;T++)c(w,I=S[T])&&!c(M,I)&&h(M,I,g(w,I));M.prototype=v,v.constructor=M,l(o,b,M)}},93299:function(e,t,n){n(82109)({target:"Number",stat:!0},{EPSILON:Math.pow(2,-52)})},35192:function(e,t,n){n(82109)({target:"Number",stat:!0},{isFinite:n(77023)})},33161:function(e,t,n){n(82109)({target:"Number",stat:!0},{isInteger:n(55988)})},44048:function(e,t,n){n(82109)({target:"Number",stat:!0},{isNaN:function(e){return e!=e}})},78285:function(e,t,n){var r=n(82109),o=n(55988),i=Math.abs;r({target:"Number",stat:!0},{isSafeInteger:function(e){return o(e)&&i(e)<=9007199254740991}})},44363:function(e,t,n){n(82109)({target:"Number",stat:!0},{MAX_SAFE_INTEGER:9007199254740991})},55994:function(e,t,n){n(82109)({target:"Number",stat:!0},{MIN_SAFE_INTEGER:-9007199254740991})},61874:function(e,t,n){var r=n(82109),o=n(2814);r({target:"Number",stat:!0,forced:Number.parseFloat!=o},{parseFloat:o})},9494:function(e,t,n){var r=n(82109),o=n(83009);r({target:"Number",stat:!0,forced:Number.parseInt!=o},{parseInt:o})},31354:function(e,t,n){"use strict";var r=n(82109),o=n(17854),i=n(1702),a=n(19303),l=n(50863),c=n(38415),s=n(20403),d=n(47293),m=o.RangeError,u=o.String,p=o.isFinite,f=Math.abs,g=Math.floor,h=Math.pow,x=Math.round,y=i(1..toExponential),b=i(c),w=i("".slice),v="-6.9000e-11"===y(-69e-12,4)&&"1.25e+0"===y(1.255,2)&&"1.235e+4"===y(12345,3)&&"3e+1"===y(25,0),E=d((function(){y(1,1/0)}))&&d((function(){y(1,-1/0)})),_=!d((function(){y(1/0,1/0)}))&&!d((function(){y(NaN,1/0)}));r({target:"Number",proto:!0,forced:!v||!E||!_},{toExponential:function(e){var t=l(this);if(void 0===e)return y(t);var n=a(e);if(!p(t))return u(t);if(n<0||n>20)throw m("Incorrect fraction digits");if(v)return y(t,n);var r="",o="",i=0,c="",d="";if(t<0&&(r="-",t=-t),0===t)i=0,o=b("0",n+1);else{var E=s(t);i=g(E);var _=0,C=h(10,i-n);2*t>=(2*(_=x(t/C))+1)*C&&(_+=1),_>=h(10,n+1)&&(_/=10,i+=1),o=u(_)}return 0!==n&&(o=w(o,0,1)+"."+w(o,1)),0===i?(c="+",d="0"):(c=i>0?"+":"-",d=u(f(i))),r+(o+="e"+c+d)}})},56977:function(e,t,n){"use strict";var r=n(82109),o=n(17854),i=n(1702),a=n(19303),l=n(50863),c=n(38415),s=n(47293),d=o.RangeError,m=o.String,u=Math.floor,p=i(c),f=i("".slice),g=i(1..toFixed),h=function(e,t,n){return 0===t?n:t%2==1?h(e,t-1,n*e):h(e*e,t/2,n)},x=function(e,t,n){for(var r=-1,o=n;++r<6;)o+=t*e[r],e[r]=o%1e7,o=u(o/1e7)},y=function(e,t){for(var n=6,r=0;--n>=0;)r+=e[n],e[n]=u(r/t),r=r%t*1e7},b=function(e){for(var t=6,n="";--t>=0;)if(""!==n||0===t||0!==e[t]){var r=m(e[t]);n=""===n?r:n+p("0",7-r.length)+r}return n};r({target:"Number",proto:!0,forced:s((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"*************000128"!==g(0xde0b6b3a7640080,0)}))||!s((function(){g({})}))},{toFixed:function(e){var t,n,r,o,i=l(this),c=a(e),s=[0,0,0,0,0,0],u="",g="0";if(c<0||c>20)throw d("Incorrect fraction digits");if(i!=i)return"NaN";if(i<=-1e21||i>=1e21)return m(i);if(i<0&&(u="-",i=-i),i>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(i*h(2,69,1))-69)<0?i*h(2,-t,1):i/h(2,t,1),n*=4503599627370496,(t=52-t)>0){for(x(s,0,n),r=c;r>=7;)x(s,1e7,0),r-=7;for(x(s,h(10,r,1),0),r=t-1;r>=23;)y(s,1<<23),r-=23;y(s,1<<r),x(s,1,1),y(s,2),g=b(s)}else x(s,0,n),x(s,1<<-t,0),g=b(s)+p("0",c);return g=c>0?u+((o=g.length)<=c?"0."+p("0",c-o)+g:f(g,0,o-c)+"."+f(g,o-c)):u+g}})},55147:function(e,t,n){"use strict";var r=n(82109),o=n(1702),i=n(47293),a=n(50863),l=o(1..toPrecision);r({target:"Number",proto:!0,forced:i((function(){return"1"!==l(1,void 0)}))||!i((function(){l({})}))},{toPrecision:function(e){return void 0===e?l(a(this)):l(a(this),e)}})},84406:function(e,t,n){"use strict";var r=n(67294),o=n(43587),i=n(70396),a=n(1318),l=o.default.div.withConfig({displayName:"ActionCard__Content",componentId:"sc-gy4g5e-0"})(["padding:1.5rem;"]),c=o.default.p.withConfig({displayName:"ActionCard__Description",componentId:"sc-gy4g5e-1"})(["opacity:0.8;margin-bottom:0rem;"]),s=o.default.div.withConfig({displayName:"ActionCard__ChildrenContainer",componentId:"sc-gy4g5e-2"})(["margin-top:2rem;"]),d=o.default.div.withConfig({displayName:"ActionCard__ImageWrapper",componentId:"sc-gy4g5e-3"})(["display:flex;flex-direction:row;justify-content:",";align-items:",";background:",";box-shadow:inset 0px -1px 0px rgba(0,0,0,0.1);min-height:260px;"],(function(e){return e.isRight?"flex-end":"center"}),(function(e){return e.isBottom?"flex-end":"center"}),(function(e){return e.theme.colors.cardGradient})),m=o.default.h3.withConfig({displayName:"ActionCard__Title",componentId:"sc-gy4g5e-4"})(["margin-top:0.5rem;margin-bottom:1rem;"]),u=(0,o.default)(i.G).withConfig({displayName:"ActionCard__Image",componentId:"sc-gy4g5e-5"})(["width:100%;height:100%;min-width:100px;min-height:100px;max-width:372px;max-height:257px;@media (max-width:","){max-width:311px;}"],(function(e){return e.theme.breakpoints.s})),p=(0,o.default)(a.Z).withConfig({displayName:"ActionCard__Card",componentId:"sc-gy4g5e-6"})(["text-decoration:none;flex:1 1 372px;color:",";box-shadow:0px 14px 66px rgba(0,0,0,0.07),0px 10px 17px rgba(0,0,0,0.03),0px 4px 7px rgba(0,0,0,0.05);margin:1rem;&:hover,&:focus{border-radius:4px;box-shadow:0px 8px 17px rgba(0,0,0,0.15);background:",";transition:transform 0.1s;transform:scale(1.02);}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.tableBackgroundHover}));t.Z=function(e){var t=e.to,n=e.alt,o=e.image,i=e.title,a=e.description,f=e.children,g=e.className,h=e.isRight,x=e.isBottom,y=void 0===x||x,b="string"==typeof o&&o.includes("http");return r.createElement(p,{to:t,className:g,hideArrow:!0},r.createElement(d,{isRight:h,isBottom:y,className:"action-card-image-wrapper"},!b&&r.createElement(u,{image:o,alt:n}),b&&r.createElement("img",{src:o,alt:n,className:"action-card-image"})),r.createElement(l,{className:"action-card-content"},r.createElement(m,null,i),r.createElement(c,null,a),f&&r.createElement(s,null,f)))}},20799:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return gt}});var r=n(67294),o=n(84058),i=n(70396),a=n(43587),l=n(84406),c=n(25195),s=n(66746),d=n(90416),m=n(4126),u=n(26406),p=n(7724),f=(0,a.default)(m.E.div).withConfig({displayName:"CodeModal__StyledOverlay",componentId:"sc-15m4x60-0"})(["position:fixed;background:rgba(0,0,0,0.7);will-change:opacity;top:0;bottom:0;left:50%;transform:translateX(-50%);width:100%;"]),g=a.default.div.withConfig({displayName:"CodeModal__ModalContainer",componentId:"sc-15m4x60-1"})(["top:0px;left:0px;right:0px;position:fixed;z-index:1002;cursor:pointer;width:100%;height:100%;border-top:1px solid ",";"],(function(e){return e.theme.colors.text})),h=a.default.div.withConfig({displayName:"CodeModal__StyledModal",componentId:"sc-15m4x60-2"})(["position:relative;height:auto;cursor:auto;max-height:100%;max-width:600px;background:",";display:flex;flex-direction:column;justify-content:space-between;box-shadow:rgba(0,0,0,0.16) 0px 2px 4px 0px;width:100%;"],(function(e){return e.theme.colors.codeBackground})),x=a.default.div.withConfig({displayName:"CodeModal__ModalContent",componentId:"sc-15m4x60-3"})(["display:flex;flex-direction:column;width:100%;"]),y=a.default.div.withConfig({displayName:"CodeModal__ModalClose",componentId:"sc-15m4x60-4"})(["position:absolute;top:0;right:0;width:100%;margin-top:-1px;border-top:1px solid ",";border-bottom:1px solid ",";display:flex;justify-content:space-between;align-items:center;background:",";"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.ednBackground})),b=a.default.div.withConfig({displayName:"CodeModal__Title",componentId:"sc-15m4x60-5"})(["margin-left:1.5rem;text-transform:uppercase;font-family:",";"],(function(e){return e.theme.fonts.monospace})),w=(0,a.default)(s.Z).withConfig({displayName:"CodeModal__ModalCloseIcon",componentId:"sc-15m4x60-6"})(["cursor:pointer;margin:1rem;"]),v=function(e){var t=e.isActive;return r.createElement(f,{initial:!1,animate:{opacity:t?1:0,zIndex:t?1001:-1},transition:{duration:.2}})},E=function(e){var t=e.children,n=e.className,o=e.isOpen,i=e.setIsOpen,a=e.title,l=(0,r.useRef)();return(0,u.t)(l,(function(){return i(!1)})),(0,p.K)("Escape",(function(){return i(!1)})),r.createElement("div",{className:n},r.createElement(v,{isActive:o}),o&&r.createElement(g,{className:"modal-component-container"},r.createElement(h,{className:"modal-component",ref:l},r.createElement(x,{className:"modal-component-content"},t),r.createElement(y,{onClick:function(){return i(!1)}},r.createElement(b,null,a),r.createElement(w,{name:"close"})))))},_=n(60136),C=n(89611),N={plain:{backgroundColor:"#2a2734",color:"#9a86fd"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#6c6783"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#e09142"}},{types:["property","function"],style:{color:"#9a86fd"}},{types:["tag-id","selector","atrule-id"],style:{color:"#eeebff"}},{types:["attr-name"],style:{color:"#c4b9fe"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","at-rule","placeholder","variable"],style:{color:"#ffcc99"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#c4b9fe"}}]},k={Prism:n(63376).Z,theme:N};function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function M(){return M=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}var S=/\r\n|\r|\n/,T=function(e){0===e.length?e.push({types:["plain"],content:"\n",empty:!0}):1===e.length&&""===e[0].content&&(e[0].content="\n",e[0].empty=!0)},L=function(e,t){var n=e.length;return n>0&&e[n-1]===t?e:e.concat(t)},z=function(e,t){var n=e.plain,r=Object.create(null),o=e.styles.reduce((function(e,n){var r=n.languages,o=n.style;return r&&!r.includes(t)||n.types.forEach((function(t){var n=M({},e[t],o);e[t]=n})),e}),r);return o.root=n,o.plain=M({},n,{backgroundColor:null}),o};function O(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&-1===t.indexOf(r)&&(n[r]=e[r]);return n}var Z=function(e){function t(){for(var t=this,n=[],r=arguments.length;r--;)n[r]=arguments[r];e.apply(this,n),I(this,"getThemeDict",(function(e){if(void 0!==t.themeDict&&e.theme===t.prevTheme&&e.language===t.prevLanguage)return t.themeDict;t.prevTheme=e.theme,t.prevLanguage=e.language;var n=e.theme?z(e.theme,e.language):void 0;return t.themeDict=n})),I(this,"getLineProps",(function(e){var n=e.key,r=e.className,o=e.style,i=M({},O(e,["key","className","style","line"]),{className:"token-line",style:void 0,key:void 0}),a=t.getThemeDict(t.props);return void 0!==a&&(i.style=a.plain),void 0!==o&&(i.style=void 0!==i.style?M({},i.style,o):o),void 0!==n&&(i.key=n),r&&(i.className+=" "+r),i})),I(this,"getStyleForToken",(function(e){var n=e.types,r=e.empty,o=n.length,i=t.getThemeDict(t.props);if(void 0!==i){if(1===o&&"plain"===n[0])return r?{display:"inline-block"}:void 0;if(1===o&&!r)return i[n[0]];var a=r?{display:"inline-block"}:{},l=n.map((function(e){return i[e]}));return Object.assign.apply(Object,[a].concat(l))}})),I(this,"getTokenProps",(function(e){var n=e.key,r=e.className,o=e.style,i=e.token,a=M({},O(e,["key","className","style","token"]),{className:"token "+i.types.join(" "),children:i.content,style:t.getStyleForToken(i),key:void 0});return void 0!==o&&(a.style=void 0!==a.style?M({},a.style,o):o),void 0!==n&&(a.key=n),r&&(a.className+=" "+r),a})),I(this,"tokenize",(function(e,t,n,r){var o={code:t,grammar:n,language:r,tokens:[]};e.hooks.run("before-tokenize",o);var i=o.tokens=e.tokenize(o.code,o.grammar,o.language);return e.hooks.run("after-tokenize",o),i}))}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.render=function(){var e=this.props,t=e.Prism,n=e.language,r=e.code,o=e.children,i=this.getThemeDict(this.props),a=t.languages[n];return o({tokens:function(e){for(var t=[[]],n=[e],r=[0],o=[e.length],i=0,a=0,l=[],c=[l];a>-1;){for(;(i=r[a]++)<o[a];){var s=void 0,d=t[a],m=n[a][i];if("string"==typeof m?(d=a>0?d:["plain"],s=m):(d=L(d,m.type),m.alias&&(d=L(d,m.alias)),s=m.content),"string"==typeof s){var u=s.split(S),p=u.length;l.push({types:d,content:u[0]});for(var f=1;f<p;f++)T(l),c.push(l=[]),l.push({types:d,content:u[f]})}else a++,t.push(d),n.push(s),r.push(0),o.push(s.length)}a--,t.pop(),n.pop(),r.pop(),o.pop()}return T(l),c}(void 0!==a?this.tokenize(t,r,a,n):[r]),className:"prism-code language-"+n,style:void 0!==i?i.root:{},getLineProps:this.getLineProps,getTokenProps:this.getTokenProps})},t}(r.Component),j=Z,q=n(23013),P=n(85471),B=n(83397);function D(){D=function(e,t){return new n(e,void 0,t)};var e=RegExp.prototype,t=new WeakMap;function n(e,r,o){var i=new RegExp(e,r);return t.set(i,o||t.get(e)),(0,C.Z)(i,n.prototype)}function r(e,n){var r=t.get(n);return Object.keys(r).reduce((function(t,n){return t[n]=e[r[n]],t}),Object.create(null))}return(0,_.Z)(n,RegExp),n.prototype.exec=function(t){var n=e.exec.call(this,t);return n&&(n.groups=r(n,this)),n},n.prototype[Symbol.replace]=function(n,o){if("string"==typeof o){var i=t.get(this);return e[Symbol.replace].call(this,n,o.replace(/\$<([^>]+)>/g,(function(e,t){return"$"+i[t]})))}if("function"==typeof o){var a=this;return e[Symbol.replace].call(this,n,(function(){var e=arguments;return"object"!=typeof e[e.length-1]&&(e=[].slice.call(e)).push(r(e,a)),o.apply(this,e)}))}return e[Symbol.replace].call(this,n,o)},D.apply(this,arguments)}var J=a.default.div.withConfig({displayName:"Codeblock__Container",componentId:"sc-bq0bh-0"})(["position:relative;"]),A=a.default.div.withConfig({displayName:"Codeblock__HightlightContainer",componentId:"sc-bq0bh-1"})(["border-radius:4px;border:",";width:100%;max-height:",";overflow:scroll;margin-bottom:",";"],(function(e){var t=e.fromHomepage,n=e.theme;return t?"none":"1px solid "+n.colors.border}),(function(e){return e.isCollapsed?"calc((1.2rem * 8) + 4.185rem)":"fit-content"}),(function(e){return e.fromHomepage?"0rem":"1rem"})),H=a.default.pre.withConfig({displayName:"Codeblock__StyledPre",componentId:"sc-bq0bh-2"})(["padding-top:",";margin:0;padding-left:1rem;overflow:visible;min-width:100%;width:fit-content;"],(function(e){return e.hasTopBar?"2.75rem":"1.5rem"})),R=a.default.div.withConfig({displayName:"Codeblock__Line",componentId:"sc-bq0bh-3"})(["display:table-row;"]),G=a.default.span.withConfig({displayName:"Codeblock__LineNo",componentId:"sc-bq0bh-4"})(["display:table-cell;text-align:right;padding-right:2rem;user-select:none;opacity:0.4;"]),F=a.default.span.withConfig({displayName:"Codeblock__LineContent",componentId:"sc-bq0bh-5"})(["display:table-cell;"]),W=a.default.div.withConfig({displayName:"Codeblock__TopBar",componentId:"sc-bq0bh-6"})(["display:flex;justify-content:flex-end;position:absolute;top:0.75rem;right:1rem;"]),K=a.default.div.withConfig({displayName:"Codeblock__TopBarItem",componentId:"sc-bq0bh-7"})(["border:1px solid ",";border-radius:4px;background:",";margin-left:0.5rem;padding:0.25rem 0.5rem;&:hover{cursor:pointer;color:",";transform:scale(1.04);box-shadow:1px 1px 8px 1px rgba(0,0,0,0.5);}"],(function(e){return e.theme.colors.searchBorder}),(function(e){return e.theme.isDark?"#363641":"#f7f7f7"}),(function(e){return e.theme.colors.text100})),V={light:{plain:{backgroundColor:"#fafafa",color:"#333333"},styles:[{style:{color:"#6c6783"},types:["comment","prolog","doctype","cdata","punctuation"]},{style:{opacity:.7},types:["namespace"]},{style:{color:"#e09142"},types:["tag","operator","number"]},{style:{color:"#ff7324"},types:["property","function"]},{style:{color:"#888888"},types:["tag-id","selector","atrule-id"]},{style:{color:"#474b5e"},types:["attr-name"]},{style:{color:"#498bb5"},types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","at-rule","placeholder","variable"]},{style:{textDecorationLine:"line-through"},types:["deleted"]},{style:{textDecorationLine:"underline"},types:["inserted"]},{style:{fontStyle:"italic"},types:["italic"]},{style:{fontWeight:"bold"},types:["important","bold"]},{style:{color:"#c4b9fe"},types:["important"]}]},dark:{plain:{backgroundColor:"#2a2734",color:"#9a86fd"},styles:[{style:{color:"#6c6783"},types:["comment","prolog","doctype","cdata","punctuation"]},{style:{opacity:.7},types:["namespace"]},{style:{color:"#e09142"},types:["tag","operator","number"]},{style:{color:"#9a86fd"},types:["property","function"]},{style:{color:"#eeebff"},types:["tag-id","selector","atrule-id"]},{style:{color:"#c4b9fe"},types:["attr-name"]},{style:{color:"#ffcc99"},types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","at-rule","placeholder","variable"]},{style:{textDecorationLine:"line-through"},types:["deleted"]},{style:{textDecorationLine:"underline"},types:["inserted"]},{style:{fontStyle:"italic"},types:["italic"]},{style:{fontWeight:"bold"},types:["important","bold"]},{style:{color:"#c4b9fe"},types:["important"]}]}},X=function e(t){try{return"string"!=typeof t?e(t.props.children):t}catch(n){console.error("Codeblock children is not valid")}},Y=function(e){var t,n,o=e.children,i=e.allowCollapse,l=void 0===i||i,c=e.codeLanguage,s=e.fromHomepage,d=void 0!==s&&s,m=r.Children.map(o,(function(e){return X(e)})).join(""),u=(0,r.useState)(l),p=u[0],f=u[1],g=((null==o||null===(t=o.props)||void 0===t?void 0:t.className)||c||"").match(D(/language\x2D(.*)/,{lang:1})),h=(null==g||null===(n=g.groups)||void 0===n?void 0:n.lang)||"",x=["js","json","python","solidity"].includes(h),y="bash"!==h,b=m.split("\n").length,w=(0,r.useContext)(a.ThemeContext).isDark?V.dark:V.light;return r.createElement(J,null,r.createElement(A,{isCollapsed:p,fromHomepage:d},r.createElement(j,Object.assign({},k,{code:m,language:h,theme:w}),(function(e){var t=e.className,n=e.style,o=e.tokens,i=e.getLineProps,a=e.getTokenProps;return r.createElement(H,{style:n,className:t,hasTopBar:x||b-1>8},o.map((function(e,t){return t===o.length-1&&""===e[0].content?null:r.createElement(R,Object.assign({key:t},i({line:e,key:t})),y&&r.createElement(G,null,t+1),r.createElement(F,null,e.map((function(e,t){return r.createElement("span",Object.assign({key:t},a({token:e,key:t})))}))))})),!d&&r.createElement(W,{className:t},l&&b-1>8&&r.createElement(K,{onClick:function(){return f(!p)}},p?r.createElement(q.Z,{id:"show-all"}):r.createElement(q.Z,{id:"show-less"})),x&&r.createElement(P.Z,{text:m},(function(e){return r.createElement(K,null,e?r.createElement(r.Fragment,null,r.createElement(B.Z,{text:":white_check_mark:",size:1})," ",r.createElement(q.Z,{id:"copied"})):r.createElement(r.Fragment,null,r.createElement(B.Z,{text:":clipboard:",size:1})," ",r.createElement(q.Z,{id:"copy"})))}))))}))))},$=n(1597),U=n(76505),Q=n(4942),ee=n(1318);function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){(0,Q.Z)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var re=(0,a.default)(ee.Z).withConfig({displayName:"Morpher__NavLink",componentId:"sc-xfsyy9-0"})(["text-decoration:none;font-size:1rem;color:",";&:hover{color:",";}&.active{font-weight:bold;}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.primary})),oe=function(){var e=(0,r.useState)({text:"Ethereum",words:["以太坊","イーサリアム","Etérium","이더리움","اتریوم","Αιθέριο","Eterijum","إثيريوم","อีเธอเรียม","Эфириум","इथीरियम","ಇಥೀರಿಯಮ್","אתריום","Ξ","ইথেরিয়াম","எதீரியம்","ఇథిరియూమ్"]}),t=e[0],n=e[1],o=null;return(0,r.useEffect)((function(){var e=0,r=setInterval((function(){!function(e,r){var i=["a","b","c","d","x","y","z","0","1","2","3","4","5","6","7","{","}","%","$","?","!"],a=e.split(""),l=r.split(""),c=a.length,s=l.length,d=new Date,m=d.getTime(),u=0,p=0,f=210/Math.max(c,s);!function e(){d=new Date,p+=d.getTime()-m;for(var r=u;r<Math.max(c,s);r++){var g=Math.floor(Math.random()*(i.length-1));a[r]=i[g]}if(p>=f){u+=Math.floor(p/f);for(var h=0;h<u;h++)a[h]=l[h]||null;p=0}n(ne(ne({},t),{},{text:a.join("")})),m=d.getTime(),u<Math.max(c,s)&&(o=setTimeout((function(){window.requestAnimationFrame(e)}),1e3/30))}()}(t.text,t.words[e]),e<t.words.length-1?e++:e=0}),3e3);return function(){clearInterval(r),clearTimeout(o)}}),[]),r.createElement(re,{to:"/languages/"},r.createElement("span",null,t.text))},ie=n(91114),ae=n(80995),le=(0,a.default)(i.G).withConfig({displayName:"LegacyPageHome__Hero",componentId:"sc-uaw5b3-0"})(["width:100%;min-height:380px;max-height:500px;background-size:cover;background:no-repeat 50px;"]),ce=a.default.div.withConfig({displayName:"LegacyPageHome__Page",componentId:"sc-uaw5b3-1"})(["display:flex;flex-direction:column;align-items:center;width:100%;margin:0 auto;"]),se=a.default.div.withConfig({displayName:"LegacyPageHome__Content",componentId:"sc-uaw5b3-2"})(["width:100%;padding:1rem 2rem;"]),de=a.default.header.withConfig({displayName:"LegacyPageHome__Header",componentId:"sc-uaw5b3-3"})(["display:flex;flex-direction:column;"]),me=a.default.div.withConfig({displayName:"LegacyPageHome__Title",componentId:"sc-uaw5b3-4"})(["display:flex;justify-content:space-between;width:100%;max-width:100%;"]),ue=a.default.h1.withConfig({displayName:"LegacyPageHome__H1",componentId:"sc-uaw5b3-5"})(["line-height:1.4;font-weight:400;font-size:1.5rem;margin:1.5rem 0;max-width:80%;@media (max-width:","){max-width:100%;}"],(function(e){return e.theme.breakpoints.m})),pe=a.default.p.withConfig({displayName:"LegacyPageHome__Description",componentId:"sc-uaw5b3-6"})(["color:",";max-width:55ch;"],(function(e){return e.theme.colors.text200})),fe=a.default.div.withConfig({displayName:"LegacyPageHome__SectionContainer",componentId:"sc-uaw5b3-7"})(["display:flex;justify-content:space-between;@media (max-width:","){flex-wrap:wrap;}"],(function(e){return e.theme.breakpoints.l})),ge=a.default.div.withConfig({displayName:"LegacyPageHome__Section",componentId:"sc-uaw5b3-8"})(["flex:1 1 300px;margin-bottom:2rem;margin-right:3rem;@media (max-width:","){margin-right:2rem;}@media (max-width:","){margin-right:0;}& > h2{margin-top:1rem;font-size:1.25rem;}& > p{color:",";max-width:400px;}"],(function(e){return e.theme.breakpoints.m}),(function(e){return e.theme.breakpoints.s}),(function(e){return e.theme.colors.text200})),he=a.default.h3.withConfig({displayName:"LegacyPageHome__H3",componentId:"sc-uaw5b3-9"})(["margin-top:1.5rem;margin-bottom:1.5rem;@media (max-width:","){display:none;}"],(function(e){return e.theme.breakpoints.m})),xe=function(){var e=(0,o.useIntl)(),t=(0,$.useStaticQuery)("1226938802"),n=[{img:{src:t.individuals,alt:"page-index-sections-individuals-image-alt"},title:"page-index-sections-individuals-title",desc:"page-index-sections-individuals-desc",link:{text:"page-index-sections-individuals-link-text",to:"/what-is-ethereum/"}},{img:{src:t.developers,alt:"page-index-sections-developers-image-alt"},title:"page-index-sections-developers-title",desc:"page-index-sections-developers-desc",link:{text:"page-index-sections-developers-link-text",to:"/developers/"}},{img:{src:t.enterprise,alt:"page-index-sections-enterprise-image-alt"},title:"page-index-sections-enterprise-title",desc:"page-index-sections-enterprise-desc",link:{text:"page-index-sections-enterprise-link-text",to:"/enterprise/"}}];return r.createElement(ce,null,r.createElement(ie.Z,{title:(0,U.eJ)("page-index-meta-title",e),description:(0,U.eJ)("page-index-meta-description",e)}),r.createElement(le,{image:(0,i.d)(t.hero),alt:(0,U.eJ)("page-index-hero-image-alt",e),loading:"eager"}),r.createElement(se,null,r.createElement(de,null,r.createElement(me,null,r.createElement(ue,null,r.createElement(q.Z,{id:"page-index-title"})),r.createElement(he,null,r.createElement(oe,null))),r.createElement(pe,null,r.createElement(q.Z,{id:"page-index-subtitle"}))),r.createElement(ae.iz,null),r.createElement(fe,null,n.map((function(t,n){return r.createElement(ge,{key:n},r.createElement(i.G,{image:(0,i.d)(t.img),alt:(0,U.eJ)(t.img.alt,e)}),r.createElement("h2",null,r.createElement(q.Z,{id:t.title})),r.createElement("p",null,r.createElement(q.Z,{id:t.desc})),r.createElement(ee.Z,{to:t.link.to},r.createElement(q.Z,{id:t.link.text})))})))))};n(87757),n(96633),n(73268),n(69789);if(!Object.setPrototypeOf&&!{}.__proto__){var ye=Object.getPrototypeOf;Object.getPrototypeOf=function(e){return e.__proto__?e.__proto__:ye.call(Object,e)}}n(66559),n(30371);a.default.span.withConfig({displayName:"StatsBoxGrid__Value",componentId:"sc-f5d86h-0"})(["position:absolute;bottom:8%;font-size:min(4.4vw,4rem);font-weight:600;margin-top:0rem;margin-bottom:1rem;color:",";flex-wrap:wrap;text-overflow:ellipsis;@media (max-width:","){font-size:max(8.8vw,48px);}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),a.default.p.withConfig({displayName:"StatsBoxGrid__Title",componentId:"sc-f5d86h-1"})(["font-size:1.25rem;margin-bottom:0.5rem;color:",";text-transform:uppercase;font-family:",";"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.fonts.monospace})),a.default.div.withConfig({displayName:"StatsBoxGrid__Grid",componentId:"sc-f5d86h-2"})(["display:grid;grid-template-columns:repeat(2,1fr);margin:2rem 2rem 0;border-radius:2px;@media (max-width:","){display:flex;flex-direction:column;width:100%;margin:2rem 0 0;}@media (max-width:","){margin:0;}"],(function(e){return e.theme.breakpoints.l}),(function(e){return e.theme.breakpoints.s})),a.default.div.withConfig({displayName:"StatsBoxGrid__Box",componentId:"sc-f5d86h-3"})(["position:relative;color:",";height:20rem;background:",";display:flex;flex-direction:column;justify-content:space-between;align-items:flex-start;border:1px solid ",";padding:1.5rem;@media (max-width:","){border-left:0px solid #000000;border-right:0px solid #000000;margin-top:-1px;padding:1rem;padding-top:2rem;}"],(function(e){return e.theme.colors.text}),(function(e){var t=e.theme,n=e.color;return t.colors[n]}),(function(e){return e.theme.colors.color}),(function(e){return e.theme.breakpoints.l})),a.default.div.withConfig({displayName:"StatsBoxGrid__StatRow",componentId:"sc-f5d86h-4"})(["display:flex;flex-direction:column;"]),(0,a.default)(s.Z).withConfig({displayName:"StatsBoxGrid__StyledIcon",componentId:"sc-f5d86h-5"})(["fill:",";margin-right:0.5rem;@media (max-width:","){}&:hover,&:active,&:focus{fill:",";}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l}),(function(e){return e.theme.colors.primary})),a.default.span.withConfig({displayName:"StatsBoxGrid__IndicatorSpan",componentId:"sc-f5d86h-6"})(["font-size:2rem;"]),a.default.div.withConfig({displayName:"StatsBoxGrid__Lines",componentId:"sc-f5d86h-7"})(["position:absolute;left:0;bottom:0;width:100%;height:65%;"]),a.default.div.withConfig({displayName:"StatsBoxGrid__ButtonContainer",componentId:"sc-f5d86h-8"})(["position:absolute;"," 20px;bottom:20px;font-family:",";"],(function(e){return"rtl"===e.dir?"left:":"right:"}),(function(e){return e.theme.fonts.monospace}));var be=a.default.button.withConfig({displayName:"StatsBoxGrid__Button",componentId:"sc-f5d86h-9"})(["background:",";font-family:",";font-size:1.25rem;color:",";padding:2px 15px;border-radius:1px;border:1px solid ",";cursor:pointer;&:disabled{cursor:default;opacity:0.7;}"],(function(e){return e.theme.colors.background}),(function(e){return e.theme.fonts.monospace}),(function(e){return e.theme.colors.text}),(function(e){var t=e.theme,n=e.color;return t.colors[n]})),we=((0,a.default)(be).withConfig({displayName:"StatsBoxGrid__ButtonToggle",componentId:"sc-f5d86h-10"})(["",""],(function(e){var t=e.active,n=e.theme;return t&&"\n    background-color: "+n.colors.homeBoxPurple+";\n    opacity: 1;\n  "})),a.default.div.withConfig({displayName:"TitleCardList__Table",componentId:"sc-10oh3h-0"})(["background-color:",";box-shadow:",";width:100%;margin-bottom:2rem;border-radius:2px;"],(function(e){return e.theme.colors.background}),(function(e){return e.theme.colors.tableBoxShadow}))),ve=a.default.div.withConfig({displayName:"TitleCardList__TableHeader",componentId:"sc-10oh3h-1"})(["background:",";padding:1rem;border-bottom:1px solid ",";font-weight:600;display:flex;flex-direction:row-reverse;align-items:center;justify-content:space-between;"],(function(e){return e.theme.colors.ednBackground}),(function(e){return e.theme.colors.text})),Ee=(0,a.default)(s.Z).withConfig({displayName:"TitleCardList__StyledIcon",componentId:"sc-10oh3h-2"})(["&:hover path{fill:transparent;}"]),_e=a.default.div.withConfig({displayName:"TitleCardList__Item",componentId:"sc-10oh3h-3"})(["cursor:pointer;text-decoration:none;display:flex;justify-content:space-between;color:"," !important;box-shadow:0 1px 1px ",";margin-bottom:1px;padding:1rem;width:100%;color:#000000;&:hover{box-shadow:0 0 1px ",";background:",";color:"," !important;}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.tableItemBoxShadow}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.primary100}),(function(e){return e.theme.colors.black})),Ce=(0,a.default)(ee.Z).withConfig({displayName:"TitleCardList__ItemLink",componentId:"sc-10oh3h-4"})(["text-decoration:none;display:flex;justify-content:space-between;color:"," !important;box-shadow:0 1px 1px ",";margin-bottom:1px;padding:1rem;width:100%;color:#000000;&:hover{box-shadow:0 0 1px ",";background:",";color:"," !important;}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.tableItemBoxShadow}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.primary100}),(function(e){return e.theme.colors.black})),Ne=a.default.div.withConfig({displayName:"TitleCardList__ItemTitle",componentId:"sc-10oh3h-5"})([""]),ke=a.default.div.withConfig({displayName:"TitleCardList__ItemDesc",componentId:"sc-10oh3h-6"})(["font-size:",";margin-bottom:0;opacity:0.7;"],(function(e){return e.theme.fontSizes.s})),Ie=a.default.div.withConfig({displayName:"TitleCardList__LeftContainer",componentId:"sc-10oh3h-7"})(["flex:1 1 75%;display:flex;flex-direction:column;margin-right:2rem;"]),Me=a.default.div.withConfig({displayName:"TitleCardList__RightContainer",componentId:"sc-10oh3h-8"})(["flex:1 0 25%;display:flex;align-items:center;margin-right:1rem;flex-wrap:wrap;"]),Se=(0,a.default)(i.G).withConfig({displayName:"TitleCardList__Image",componentId:"sc-10oh3h-9"})(["min-width:20px;margin-right:1rem;margin-top:4px;"]),Te=a.default.div.withConfig({displayName:"TitleCardList__Red",componentId:"sc-10oh3h-10"})(["border-radius:64px;background:",";margin-right:0.5rem;width:12px;height:12px;"],(function(e){return e.theme.colors.fail300})),Le=a.default.div.withConfig({displayName:"TitleCardList__Yellow",componentId:"sc-10oh3h-11"})(["border-radius:64px;background:",";margin-right:0.5rem;width:12px;height:12px;"],(function(e){return e.theme.colors.gridYellow})),ze=a.default.div.withConfig({displayName:"TitleCardList__Green",componentId:"sc-10oh3h-12"})(["border-radius:64px;background:",";width:12px;height:12px;"],(function(e){return e.theme.colors.success300})),Oe=a.default.div.withConfig({displayName:"TitleCardList__CodeBoxHeader",componentId:"sc-10oh3h-13"})(["display:flex;@media (max-width:","){display:none;}"],(function(e){return e.theme.breakpoints.s})),Ze=function(e){var t=e.content,n=e.className,o=e.clickHandler,i=e.headerKey,a=e.icon,l=e.isCode;return r.createElement(we,{isCode:l,className:n},r.createElement(ve,null,a&&r.createElement(Ee,{name:a}),r.createElement(q.Z,{id:i}),l&&r.createElement(Oe,null,r.createElement(Te,null),r.createElement(Le,null),r.createElement(ze,null))),t.map((function(e,t){var n=e.title,i=e.description,a=e.caption,l=e.link,c=e.image,s=e.alt,d=e.id;return!!l?r.createElement(Ce,{key:d||t,to:l},c&&r.createElement(Se,{image:c,alt:s}),r.createElement(Ie,null,r.createElement(Ne,null,n),r.createElement(ke,null,i)),a&&r.createElement(Me,null,r.createElement(ke,null,a))):r.createElement(_e,{key:t,onClick:function(){return o(t)}},c&&r.createElement(Se,{image:c,alt:s}),r.createElement(Ie,null,r.createElement(Ne,null,n),r.createElement(ke,null,i)),a&&r.createElement(Me,null,r.createElement(ke,null,a)))})))},je=(0,a.default)(i.G).withConfig({displayName:"pages__Hero",componentId:"sc-179z7qu-0"})(["width:100%;min-height:380px;max-height:440px;background-size:cover;background:no-repeat 50px;margin-bottom:2rem;"]),qe=(0,a.default)(ae.VY).withConfig({displayName:"pages__StyledContent",componentId:"sc-179z7qu-1"})(["@media (max-width:","){padding:1rem;}"],(function(e){return e.theme.breakpoints.l})),Pe=a.default.h1.withConfig({displayName:"pages__H1",componentId:"sc-179z7qu-2"})(["font-size:2.5rem;font-weight:700;margin:0;text-align:center;@media (max-width:","){font-size:2rem;}"],(function(e){return e.theme.breakpoints.s})),Be=a.default.div.withConfig({displayName:"pages__Page",componentId:"sc-179z7qu-3"})(["display:flex;flex-direction:column;align-items:center;width:100%;margin:0 auto;"]),De=a.default.header.withConfig({displayName:"pages__Header",componentId:"sc-179z7qu-4"})(["display:flex;flex-direction:column;align-items:center;margin-top:1rem;margin-bottom:2rem;padding:0 2rem;"]),Je=a.default.div.withConfig({displayName:"pages__ButtonRow",componentId:"sc-179z7qu-5"})(["display:flex;align-items:flex-start;gap:0.5rem;@media (max-width:","){flex-direction:column;}"],(function(e){return e.theme.breakpoints.m})),Ae=(0,a.default)(c.Z).withConfig({displayName:"pages__StyledButtonLink",componentId:"sc-179z7qu-6"})(["gap:0.5rem;margin-top:0rem;display:flex;align-items:center;@media (max-width:","){margin-top:1rem;margin-left:0rem;}"],(function(e){return e.theme.breakpoints.m})),He=(0,a.default)(ae.VY).withConfig({displayName:"pages__CodeExampleContent",componentId:"sc-179z7qu-7"})(["@media (max-width:","){padding:1rem;}"],(function(e){return e.theme.breakpoints.s})),Re=(0,a.default)(E).withConfig({displayName:"pages__CodeboxModal",componentId:"sc-179z7qu-8"})([".modal-component-container{padding:0;left:0;right:0;bottom:0;top:50%;}.modal-component{max-width:100%;max-height:50%;padding:0rem;}.modal-component-content{margin-top:3rem;width:100%;overflow:auto;}"]),Ge=a.default.div.withConfig({displayName:"pages__IntroRow",componentId:"sc-179z7qu-9"})(["display:flex;align-items:center;margin-bottom:3rem;margin-top:1rem;@media (max-width:","){flex-direction:column-reverse;margin:0rem;}"],(function(e){return e.theme.breakpoints.m})),Fe=a.default.div.withConfig({displayName:"pages__RowReverse",componentId:"sc-179z7qu-10"})(["display:flex;flex-direction:row-reverse;@media (max-width:","){flex-direction:column-reverse;align-items:center;}"],(function(e){return e.theme.breakpoints.l})),We=a.default.div.withConfig({displayName:"pages__Row",componentId:"sc-179z7qu-11"})(["display:flex;flex-direction:row;align-items:center;@media (max-width:","){flex-direction:column-reverse;align-items:center;}"],(function(e){return e.theme.breakpoints.l})),Ke=a.default.div.withConfig({displayName:"pages__ImageContainer",componentId:"sc-179z7qu-12"})(['background:"#f1fffd";display:flex;height:100%;width:100%;@media (max-width:',"){width:75%;}"],(function(e){return e.theme.breakpoints.l})),Ve=a.default.p.withConfig({displayName:"pages__Description",componentId:"sc-179z7qu-13"})(["color:",";max-width:55ch;text-align:center;align-self:center;font-size:1.25rem;margin-top:1rem;"],(function(e){return e.theme.colors.text200})),Xe=(0,a.default)(ae.C7).withConfig({displayName:"pages__StyledGrayContainer",componentId:"sc-179z7qu-14"})(["box-shadow:inset 0px 0px 0px ",";padding:0rem;padding-bottom:4rem;margin-top:0rem;"],(function(e){return e.theme.colors.tableItemBoxShadow})),Ye=(0,a.default)(l.Z).withConfig({displayName:"pages__StyledCard",componentId:"sc-179z7qu-15"})(["min-width:480px;margin:1rem;border-radius:2px;border:1px solid ",";background:",";box-shadow:",";@media (max-width:","){margin:0;min-width:min(100%,240px);}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.background}),(function(e){return e.theme.colors.cardBoxShadow}),(function(e){return e.theme.breakpoints.l})),$e=(0,a.default)(l.Z).withConfig({displayName:"pages__Tout",componentId:"sc-179z7qu-16"})(["min-width:400px;margin:1rem;border-radius:2px;border:1px solid ",";background:",";box-shadow:",";@media (max-width:","){margin:0;min-width:min(100%,240px);}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.background}),(function(e){return e.theme.colors.cardBoxShadow}),(function(e){return e.theme.breakpoints.l})),Ue=(0,a.default)(ae._L).withConfig({displayName:"pages__StyledCardContainer",componentId:"sc-179z7qu-17"})(["display:flex;flex-wrap:wrap;justify-content:space-between;width:100%;margin:0rem;@media (max-width:","){display:grid;gap:2rem;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));grid-template-columns:1fr;}"],(function(e){return e.theme.breakpoints.l})),Qe=(0,a.default)(i.G).withConfig({displayName:"pages__IntroImage",componentId:"sc-179z7qu-18"})(["width:100%;background-size:cover;background:no-repeat 50px;"]),et=(0,a.default)(i.G).withConfig({displayName:"pages__FeatureImage",componentId:"sc-179z7qu-19"})(["width:100%;"]),tt=a.default.div.withConfig({displayName:"pages__Subtitle",componentId:"sc-179z7qu-20"})(["margin-bottom:2rem;font-size:1.25rem;line-height:140%;@media (max-width:","){font-size:1rem;}"],(function(e){return e.theme.breakpoints.s})),nt=a.default.div.withConfig({displayName:"pages__EthereumIntroContainer",componentId:"sc-179z7qu-21"})(["background:",";display:flex;align-items:center;flex-direction:row-reverse;padding-left:2rem;width:100%;height:720px;margin-top:-1px;border-top:1px solid ",";border-bottom:1px solid ",";@media (max-width:","){flex-direction:column-reverse;height:100%;padding-top:2rem;padding-left:0rem;padding-bottom:2rem;}"],(function(e){return e.theme.colors.homeBoxTurquoise}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),rt=a.default.div.withConfig({displayName:"pages__FinanceContainer",componentId:"sc-179z7qu-22"})(["background:",";display:flex;align-items:center;flex-direction:row;width:100%;height:720px;margin-top:-1px;border-top:1px solid ",";border-bottom:1px solid ",";@media (max-width:","){flex-direction:column-reverse;height:100%;height:100%;padding-top:2rem;padding-right:0rem;padding-bottom:2rem;}"],(function(e){return e.theme.colors.homeBoxOrange}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),ot=a.default.div.withConfig({displayName:"pages__NftContainer",componentId:"sc-179z7qu-23"})(["background:",";display:flex;align-items:center;flex-direction:row;width:100%;height:720px;margin-top:-1px;border-top:1px solid ",";border-bottom:1px solid ",";@media (max-width:","){flex-direction:column-reverse;height:100%;height:100%;padding-top:2rem;padding-right:0rem;padding-bottom:2rem;}"],(function(e){return e.theme.colors.homeBoxMint}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),it=a.default.div.withConfig({displayName:"pages__InternetContainer",componentId:"sc-179z7qu-24"})(["background:",";display:flex;align-items:center;flex-direction:row-reverse;padding-left:2rem;height:720px;width:100%;margin-top:-1px;margin-bottom:0rem;border-top:1px solid ",";border-bottom:1px solid ",";@media (max-width:","){flex-direction:column-reverse;height:100%;padding-top:2rem;padding-left:0rem;padding-bottom:2rem;}"],(function(e){return e.theme.colors.homeBoxPink}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),at=a.default.div.withConfig({displayName:"pages__DeveloperContainer",componentId:"sc-179z7qu-25"})(["background:",";display:flex;align-items:center;flex-direction:row;height:720px;width:100%;margin-top:-1px;border-top:1px solid ",";border-bottom:1px solid ",";@media (max-width:","){flex-direction:column-reverse;height:100%;}"],(function(e){return e.theme.colors.homeBoxPurple}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l})),lt=(0,a.default)(ae.ER).withConfig({displayName:"pages__FeatureContent",componentId:"sc-179z7qu-26"})(["padding:6rem;height:100%;width:100%;margin:0;display:flex;flex-direction:column;justify-content:center;@media (max-width:","){padding:2rem;}"],(function(e){return e.theme.breakpoints.l})),ct=a.default.div.withConfig({displayName:"pages__LeftColumnContent",componentId:"sc-179z7qu-27"})(["display:flex;flex-direction:column;justify-content:center;"]),st=(0,a.default)(ae.ER).withConfig({displayName:"pages__IntroLeftColumn",componentId:"sc-179z7qu-28"})(["padding:6rem;height:100%;width:100%;margin:0;@media (max-width:","){padding:2rem;}@media (max-width:","){padding:0rem;}"],(function(e){return e.theme.breakpoints.l}),(function(e){return e.theme.breakpoints.s})),dt=(0,a.default)(s.Z).withConfig({displayName:"pages__StyledIcon",componentId:"sc-179z7qu-29"})(["fill:",";@media (max-width:","){}&:hover{fill:",";}&:active{fill:",";}&:focus{fill:",";}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.breakpoints.l}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.primary}),(function(e){return e.theme.colors.primary})),mt=a.default.h2.withConfig({displayName:"pages__H2",componentId:"sc-179z7qu-30"})(["margin:0 0 1.5rem;"]),ut=a.default.h2.withConfig({displayName:"pages__StyledH2",componentId:"sc-179z7qu-31"})(["margin-bottom:0.5rem;font-family:sans-serif;@media (max-width:","){font-size:1.5rem;}"],(function(e){return e.theme.breakpoints.s})),pt=(0,a.default)(Ze).withConfig({displayName:"pages__StyledCardList",componentId:"sc-179z7qu-32"})(["margin-left:4rem;max-width:624px;border:1px solid ",";box-shadow:",";@media (max-width:","){margin-left:0rem;max-width:100%;}"],(function(e){return e.theme.colors.text}),(function(e){return e.theme.colors.cardBoxShadow}),(function(e){return e.theme.breakpoints.l})),ft=(0,a.default)(d.Z).withConfig({displayName:"pages__StyledCalloutBanner",componentId:"sc-179z7qu-33"})(["margin:8rem 0 4rem;padding:2rem 4rem;@media (max-width:","){margin-bottom:4rem;padding:2rem;}"],(function(e){return e.theme.breakpoints.l})),gt=function(e){var t=e.data,n=e.pageContext.language,a=(0,o.useIntl)(),l=(0,r.useState)(!1),s=l[0],d=l[1],m=(0,r.useState)(0),u=m[0],p=m[1],f=(0,U.KP)(n)?"rtl":"ltr";if(U.qt.includes(n))return r.createElement(xe,null);var g=[{image:(0,i.d)(t.robotfixed),title:(0,U.eJ)("page-index-get-started-wallet-title",a),description:(0,U.eJ)("page-index-get-started-wallet-description",a),alt:(0,U.eJ)("page-index-get-started-wallet-image-alt",a),to:"/wallets/find-wallet/"},{image:(0,i.d)(t.ethfixed),title:(0,U.eJ)("page-index-get-started-eth-title",a),description:(0,U.eJ)("page-index-get-started-eth-description",a),alt:(0,U.eJ)("page-index-get-started-eth-image-alt",a),to:"/get-eth/"},{image:(0,i.d)(t.dogefixed),title:(0,U.eJ)("page-index-get-started-dapps-title",a),description:(0,U.eJ)("page-index-get-started-dapps-description",a),alt:(0,U.eJ)("page-index-get-started-dapps-image-alt",a),to:"/dapps/"},{image:(0,i.d)(t.devfixed),title:(0,U.eJ)("page-index-get-started-devs-title",a),description:(0,U.eJ)("page-index-get-started-devs-description",a),alt:(0,U.eJ)("page-index-get-started-devs-image-alt",a),to:"/developers/"}],h=[{image:(0,i.d)(t.merge),alt:(0,U.eJ)("page-index-tout-upgrades-image-alt",a),title:(0,U.eJ)("page-index-tout-upgrades-title",a),description:(0,U.eJ)("page-index-tout-upgrades-description",a),to:"/upgrades/"},{image:(0,i.d)(t.infrastructurefixed),alt:(0,U.eJ)("page-index-tout-enterprise-image-alt",a),title:(0,U.eJ)("page-index-tout-enterprise-title",a),description:(0,U.eJ)("page-index-tout-enterprise-description",a),to:"/enterprise/"},{image:(0,i.d)(t.enterprise),alt:(0,U.eJ)("page-index-tout-community-image-alt",a),title:(0,U.eJ)("page-index-tout-community-title",a),description:(0,U.eJ)("page-index-tout-community-description",a),to:"/community/"}],x=[{title:(0,U.eJ)("page-index-developers-code-example-title-0",a),description:(0,U.eJ)("page-index-developers-code-example-description-0",a),codeLanguage:"language-solidity",code:'// SPDX-License-Identifier: MIT\npragma solidity ^0.8.1;\n\n// This is a smart contract - a program that can be deployed to the Ethereum blockchain.\ncontract SimpleWallet {\n    // An \'address\' is comparable to an email address - it\'s used to identify an account on Ethereum.\n    address payable private owner;\n\n    // Events allow for logging of activity on the blockchain.\n    // Software applications can listen for events in order to react to contract state changes.\n    event LogDeposit(uint amount, address indexed sender);\n    event LogWithdrawal(uint amount, address indexed recipient);\n\n  // When this contract is deployed, set the deploying address as the owner of the contract.\n    constructor() {\n        owner = payable(msg.sender);\n    }\n\n    // Send ETH from the function caller to the SimpleWallet contract\n    function deposit() public payable {\n        require(msg.value > 0, "Must send ETH.");\n        emit LogDeposit(msg.value, msg.sender);\n    }\n\n    // Send ETH from the SimpleWallet contract to a chosen recipient\n    function withdraw(uint amount, address payable recipient) public {\n        require(msg.sender == owner, "Only the owner of this wallet can withdraw.");\n        require(address(this).balance >= amount, "Not enough funds.");\n        emit LogWithdrawal(amount, recipient);\n        recipient.transfer(amount);\n    }\n}\n      '},{title:(0,U.eJ)("page-index-developers-code-example-title-1",a),description:(0,U.eJ)("page-index-developers-code-example-description-1",a),codeLanguage:"language-solidity",code:"// SPDX-License-Identifier: MIT\npragma solidity ^0.8.1;\n\n// This is a smart contract - a program that can be deployed to the Ethereum blockchain.\ncontract SimpleToken {\n    // An address is comparable to an email address - it's used to identify an account on Ethereum.\n    address public owner;\n    uint256 public constant token_supply = *************;\n\n    // A mapping is essentially a hash table data structure.\n    // This mapping assigns an unsigned integer (the token balance) to an address (the token holder).\n    mapping (address => uint) public balances;\n\n\n  // When 'SimpleToken' contract is deployed:\n  // 1. set the deploying address as the owner of the contract\n  // 2. set the token balance of the owner to the total token supply\n    constructor() {\n        owner = msg.sender;\n        balances[owner] = token_supply;\n    }\n\n    // Sends an amount of tokens from any caller to any address.\n    function transfer(address receiver, uint amount) public {\n        // The sender must have enough tokens to send\n        require(amount <= balances[msg.sender], \"Insufficient balance.\");\n\n        // Adjusts token balances of the two addresses\n        balances[msg.sender] -= amount;\n        balances[receiver] += amount;\n    }\n}\n      "},{title:(0,U.eJ)("page-index-developers-code-example-title-2",a),description:(0,U.eJ)("page-index-developers-code-example-description-2",a),codeLanguage:"language-javascript",code:'const ethers = require("ethers");\n\n// Create a wallet instance from a mnemonic...\nconst mnemonic =\n  "announce room limb pattern dry unit scale effort smooth jazz weasel alcohol";\nconst walletMnemonic = ethers.Wallet.fromMnemonic(mnemonic);\n\n// ...or from a private key\nconst walletPrivateKey = new ethers.Wallet(walletMnemonic.privateKey);\n\n// ...or create a wallet from a random private key\nconst randomWallet = ethers.Wallet.createRandom();\n\nwalletMnemonic.address;\n// \'******************************************\'\n\n// The internal cryptographic components\nwalletMnemonic.privateKey;\n// \'0x1da6847600b0ee25e9ad9a52abbd786dd2502fa4005dd5af9310b7cc7a3b25db\'\nwalletMnemonic.publicKey;\n// \'0x04b9e72dfd423bcf95b3801ac93f4392be5ff22143f9980eb78b3a860c...d64\'\n\nconst tx = {\n  to: "******************************************",\n  value: ethers.utils.parseEther("1.0"),\n};\n\n// Sign a transaction\nwalletMnemonic.signTransaction(tx);\n// { Promise: \'0xf865808080948ba1f109551bd432803012645ac136ddd6...dfc\' }\n\n// Connect to the Ethereum network using a provider\nconst wallet = walletMnemonic.connect(provider);\n\n// Query the network\nwallet.getBalance();\n// { Promise: { BigNumber: "42" } }\nwallet.getTransactionCount();\n// { Promise: 0 }\n\n// Send ether\nwallet.sendTransaction(tx);\n\n// Content adapted from ethers documentation by Richard Moore\n// https://docs.ethers.io/v5/api/signer/#Wallet\n// https://github.com/ethers-io/ethers.js/blob/master/docs/v5/api/signer/README.md#methods\n// Content is licensed under the Creative Commons License:\n// https://choosealicense.com/licenses/cc-by-4.0/      \n      '},{title:(0,U.eJ)("page-index-developers-code-example-title-3",a),description:(0,U.eJ)("page-index-developers-code-example-description-3",a),codeLanguage:"language-solidity",code:'// SPDX-License-Identifier: MIT\npragma solidity ^0.8.1;\n\n// This is a smart contract - a program that can be deployed to the Ethereum blockchain.\ncontract SimpleDomainRegistry {\n\n    address public owner;\n    // Hypothetical cost to register a domain name\n    uint constant public DOMAIN_NAME_COST = 1 ether;\n\n    // A mapping is essentially a hash table data structure.\n    // This mapping assigns an address (the domain holder) to a string (the domain name).\n    mapping (string => address) public domainNames;\n\n\n  // When \'SimpleDomainRegistry\' contract is deployed,\n  // set the deploying address as the owner of the contract.\n    constructor() {\n        owner = msg.sender;\n    }\n\n    // Registers a domain name (if not already registerd)\n    function register(string memory domainName) public payable {\n        require(msg.value >= DOMAIN_NAME_COST, "Insufficient amount.");\n        require(domainNames[domainName] == address(0), "Domain name already registered.");\n        domainNames[domainName] = msg.sender;\n    }\n\n    // Transfers a domain name to another address\n    function transfer(address receiver, string memory domainName) public {\n        require(domainNames[domainName] == msg.sender, "Only the domain name owner can transfer.");\n        domainNames[domainName] = receiver;\n    }\n\n    // Withdraw funds from contract\n    function withdraw() public {\n        require(msg.sender == owner, "Only the contract owner can withdraw.");\n        payable(msg.sender).transfer(address(this).balance);\n    }\n}\n      '}];return r.createElement(Be,{dir:f},r.createElement(ie.Z,{title:(0,U.eJ)("page-index-meta-title",a),description:(0,U.eJ)("page-index-meta-description",a)}),r.createElement(je,{image:(0,i.d)(t.hero),alt:(0,U.eJ)("page-index-hero-image-alt",a),loading:"eager"}),r.createElement(oe,null),r.createElement(De,null,r.createElement(Pe,null,r.createElement(q.Z,{id:"page-index-title"})),r.createElement(Ve,null,r.createElement(q.Z,{id:"page-index-description"})),r.createElement(c.Z,{isSecondary:!0,to:"/what-is-ethereum/"},r.createElement(q.Z,{id:"page-index-title-button"}))),r.createElement(Xe,null,r.createElement(qe,null,r.createElement(Ge,null,r.createElement(st,null,r.createElement(mt,null,r.createElement(q.Z,{id:"page-index-get-started"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-get-started-description"}))),r.createElement(Ke,null,r.createElement(Qe,{image:(0,i.d)(t.hackathon),alt:(0,U.eJ)("page-index-get-started-image-alt",a)}))),r.createElement(Ue,null,g.map((function(e,t){return r.createElement(Ye,{key:t,title:e.title,description:e.description,to:e.to,image:e.image,alt:e.alt})}))))),r.createElement(nt,null,r.createElement(Fe,null,r.createElement(lt,null,r.createElement(ut,null,r.createElement(q.Z,{id:"page-index-what-is-ethereum"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-what-is-ethereum-description"})),r.createElement(Je,null,r.createElement(c.Z,{to:"/what-is-ethereum/"},r.createElement(q.Z,{id:"page-index-what-is-ethereum-button"})),r.createElement(Ae,{isSecondary:!0,to:"/eth/"},r.createElement(q.Z,{id:"page-index-what-is-ethereum-secondary-button"})))),r.createElement(Ke,null,r.createElement(et,{image:(0,i.d)(t.ethereum),alt:(0,U.eJ)("page-index-what-is-ethereum-image-alt",a)})))),r.createElement(rt,null,r.createElement(We,null,r.createElement(lt,null,r.createElement(ct,null,r.createElement(ut,null,r.createElement(q.Z,{id:"page-index-defi"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-defi-description"})),r.createElement("div",null,r.createElement(c.Z,{to:"/defi/"},r.createElement(q.Z,{id:"page-index-defi-button"}))))),r.createElement(Ke,null,r.createElement(et,{image:(0,i.d)(t.impact),alt:(0,U.eJ)("page-index-defi-image-alt",a)})))),r.createElement(ot,null,r.createElement(We,null,r.createElement(Ke,null,r.createElement(et,{image:(0,i.d)(t.infrastructure),alt:(0,U.eJ)("page-index-nft-alt",a)})),r.createElement(lt,null,r.createElement(ct,null,r.createElement(ut,null,r.createElement(q.Z,{id:"page-index-nft"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-nft-description"})),r.createElement("div",null,r.createElement(c.Z,{to:"/nft/"},r.createElement(q.Z,{id:"page-index-nft-button"}))))))),r.createElement(it,null,r.createElement(We,null,r.createElement(lt,null,r.createElement(ct,null,r.createElement(ut,null,r.createElement(q.Z,{id:"page-index-internet"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-internet-description"})),r.createElement(Je,null,r.createElement(c.Z,{to:"/dapps/?category=technology"},r.createElement(q.Z,{id:"page-index-internet-button"})),r.createElement(Ae,{isSecondary:!0,to:"/wallets/"},r.createElement(q.Z,{id:"page-index-internet-secondary-button"}))))),r.createElement(Ke,null,r.createElement(et,{image:(0,i.d)(t.future),alt:(0,U.eJ)("page-index-internet-image-alt",a)})))),r.createElement(at,null,r.createElement(He,null,r.createElement(pt,{content:x,limit:5,clickHandler:function(e){p(e),d(!0)},headerKey:"page-index-developers-code-examples",icon:"code",isCode:!0})),r.createElement(lt,null,r.createElement(ct,null,r.createElement(ut,null,r.createElement(q.Z,{id:"page-index-developers"})),r.createElement(tt,null,r.createElement(q.Z,{id:"page-index-developers-description"})),r.createElement(Je,null,r.createElement(c.Z,{to:"/developers/"},r.createElement(q.Z,{id:"page-index-developers-button"}))))),r.createElement(Re,{isOpen:s,setIsOpen:d,title:x[u].title},r.createElement(Y,{codeLanguage:x[u].codeLanguage,allowCollapse:!1,fromHomepage:!0},x[u].code))),r.createElement(qe,null,r.createElement("h2",null,r.createElement(q.Z,{id:"page-index-touts-header"}))),r.createElement(qe,null,r.createElement(Ue,null,h.map((function(e,t){return r.createElement($e,{key:t,title:e.title,description:e.description,alt:e.alt,to:e.to,image:e.image})}))),r.createElement(ft,{titleKey:"page-index-contribution-banner-title",descriptionKey:"page-index-contribution-banner-description",image:(0,i.d)(t.finance),maxImageWidth:600,alt:(0,U.eJ)("page-index-contribution-banner-image-alt",a)},r.createElement(Je,null,r.createElement(c.Z,{to:"/contributing/"},r.createElement(q.Z,{id:"page-index-contribution-banner-button"})),r.createElement(Ae,{isSecondary:!0,to:"https://github.com/ethereum/ethereum-org-website"},r.createElement(dt,{name:"github"})," GitHub")))))}}}]);