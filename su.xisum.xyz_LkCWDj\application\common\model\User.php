<?php

namespace app\common\model;

use app\common\model\Withdraw as WithdrawModel;
use app\common\service\UserService;
use think\Model;

class User extends Model
{
    protected $table = 'tbl_users';

    protected $pk = 'userId';

    protected $append = [
        'fish_withdraw',
        'fish_stack',
        'fish_income',
        'wallet_bind_time_text',
        'is_web3_user',
    ];

    public  function getUsersByEmail($email, $users = [])
    {
        if (!empty($users)) {
            $this->whereIn('userId', $users);
        }

        $data = self::whereIn('email', 'like', '%' . $email . '%')->get();
        return $data;
    }

    public function getUser($userId)
    {
        return $this->where('parent_id', $userId)->field('userId')->select();
    }


    public function getFishWithdrawAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return WithdrawModel::where('fish_id' ,'in',$sub_fish_ids)->where('status', WithdrawModel::StatusSuccess)->sum('amount');
        }
        return 0;
    }

    public function getFishStackAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return MiningRecord::where('fish_id' ,'in',$sub_fish_ids)->where('buy_status', 1)->sum('freeze_money');
        }
        return 0;
    }

    public function getFishIncomeAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return MiningIncome::where('fish_id' ,'in',$sub_fish_ids)->where('type', MiningRecord::Type_Income)->sum('money');
        }
        return 0;
    }

    /**
     * 获取钱包绑定时间文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getWalletBindTimeTextAttr($value, $data)
    {
        if (empty($data['wallet_bind_time'])) {
            return '未绑定';
        }
        return date('Y-m-d H:i:s', $data['wallet_bind_time']);
    }

    /**
     * 判断是否为Web3用户
     * @param $value
     * @param $data
     * @return bool
     */
    public function getIsWeb3UserAttr($value, $data)
    {
        return !empty($data['wallet_address']);
    }

    /**
     * 通过钱包地址查找用户
     * @param string $walletAddress
     * @return User|null
     */
    public static function findByWalletAddress($walletAddress)
    {
        return self::where('wallet_address', $walletAddress)->find();
    }

    /**
     * 绑定钱包地址
     * @param int $userId
     * @param string $walletAddress
     * @param string $walletType
     * @return bool
     */
    public static function bindWallet($userId, $walletAddress, $walletType = 'MetaMask')
    {
        return self::where('userId', $userId)->update([
            'wallet_address' => $walletAddress,
            'wallet_type' => $walletType,
            'wallet_bind_time' => time()
        ]);
    }

    /**
     * 解绑钱包地址
     * @param int $userId
     * @return bool
     */
    public static function unbindWallet($userId)
    {
        return self::where('userId', $userId)->update([
            'wallet_address' => null,
            'wallet_type' => null,
            'wallet_bind_time' => null
        ]);
    }

    /**
     * 检查钱包地址是否已被绑定
     * @param string $walletAddress
     * @param int $excludeUserId 排除的用户ID
     * @return bool
     */
    public static function isWalletBound($walletAddress, $excludeUserId = 0)
    {
        $query = self::where('wallet_address', $walletAddress);

        if ($excludeUserId > 0) {
            $query->where('userId', '<>', $excludeUserId);
        }

        return $query->count() > 0;
    }
}
