<?php

namespace app\admin\controller;

use app\common\service\SettingService;

class Qr extends Base
{
    public function index()
    {
        $trc = SettingService::getAddress($this->user_id, 'trc')->address ?? '';
        $erc = SettingService::getAddress($this->user_id, 'erc')->address ?? '';
        return $this->fetch('', ['userId' => $this->user_id, 'address' => compact('trc', 'erc')]);
    }
}
