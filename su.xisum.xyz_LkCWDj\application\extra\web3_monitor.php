<?php

/**
 * Web3监控告警配置
 */
return [
    // 监控开关
    'enabled' => true,
    
    // 监控间隔（秒）
    'check_interval' => 300, // 5分钟
    
    // 告警阈值配置
    'thresholds' => [
        // 登录相关
        'failed_login_rate' => 0.3,        // 失败登录率阈值 30%
        'rapid_login_count' => 20,         // 快速登录次数阈值（5分钟内）
        'suspicious_ip_count' => 10,       // 可疑IP数量阈值
        'new_wallet_rate' => 0.8,          // 新钱包比例阈值 80%
        
        // 签名安全相关
        'invalid_signature_rate' => 0.2,   // 无效签名率阈值 20%
        'duplicate_signature_count' => 5,  // 重复签名次数阈值
        
        // 系统性能相关
        'expired_nonce_count' => 1000,     // 过期nonce数量阈值
        'log_table_size' => 100000,        // 日志表大小阈值
        'memory_usage_rate' => 0.8,        // 内存使用率阈值 80%
        
        // 网络相关
        'chain_switch_rate' => 0.5,        // 链切换比例阈值 50%
        'unsupported_chain_rate' => 0.1,   // 不支持链比例阈值 10%
    ],
    
    // 告警通知配置
    'notifications' => [
        // 邮件通知
        'email' => [
            'enabled' => true,
            'smtp_host' => 'smtp.example.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your_password',
            'from_email' => '<EMAIL>',
            'from_name' => 'Web3监控系统',
            'to_emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            // 告警级别过滤（只发送这些级别的告警）
            'alert_levels' => ['critical', 'error']
        ],
        
        // 短信通知（可选）
        'sms' => [
            'enabled' => false,
            'provider' => 'aliyun', // aliyun, tencent, etc.
            'access_key' => '',
            'access_secret' => '',
            'template_id' => '',
            'sign_name' => '',
            'phone_numbers' => [
                '13800138000'
            ],
            'alert_levels' => ['critical']
        ],
        
        // Webhook通知（可选）
        'webhook' => [
            'enabled' => false,
            'url' => 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
            'method' => 'POST',
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'alert_levels' => ['critical', 'error', 'warning']
        ],
        
        // 钉钉机器人通知（可选）
        'dingtalk' => [
            'enabled' => false,
            'webhook_url' => 'https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN',
            'secret' => '', // 加签密钥
            'alert_levels' => ['critical', 'error']
        ]
    ],
    
    // 监控项目配置
    'monitors' => [
        'login_anomalies' => [
            'enabled' => true,
            'time_range' => 3600, // 1小时
            'checks' => [
                'failed_login_rate',
                'suspicious_ips',
                'rapid_logins',
                'new_wallet_rate'
            ]
        ],
        
        'signature_security' => [
            'enabled' => true,
            'time_range' => 3600,
            'checks' => [
                'invalid_signature_rate',
                'duplicate_signatures',
                'signature_patterns'
            ]
        ],
        
        'wallet_behavior' => [
            'enabled' => true,
            'time_range' => 3600,
            'checks' => [
                'frequent_wallets',
                'wallet_switching',
                'unusual_patterns'
            ]
        ],
        
        'system_performance' => [
            'enabled' => true,
            'checks' => [
                'database_health',
                'cache_health',
                'storage_usage',
                'memory_usage'
            ]
        ],
        
        'multi_chain' => [
            'enabled' => true,
            'time_range' => 3600,
            'checks' => [
                'chain_distribution',
                'unsupported_chains',
                'chain_switching_patterns'
            ]
        ]
    ],
    
    // 数据保留策略
    'data_retention' => [
        'alert_logs' => 30,      // 告警日志保留天数
        'monitoring_stats' => 7, // 监控统计保留天数
        'login_logs' => 90,      // 登录日志保留天数
        'nonce_cache' => 1       // nonce缓存保留天数
    ],
    
    // 自动处理配置
    'auto_actions' => [
        // 自动清理过期数据
        'auto_cleanup' => [
            'enabled' => true,
            'schedule' => '0 2 * * *', // 每天凌晨2点执行
            'actions' => [
                'clean_expired_nonces',
                'archive_old_logs',
                'clean_cache'
            ]
        ],
        
        // 自动封禁可疑IP
        'auto_ban_ips' => [
            'enabled' => false,
            'threshold' => 50,      // 失败次数阈值
            'time_window' => 3600,  // 时间窗口（秒）
            'ban_duration' => 86400 // 封禁时长（秒）
        ],
        
        // 自动限流
        'rate_limiting' => [
            'enabled' => false,
            'max_requests_per_minute' => 60,
            'max_requests_per_hour' => 1000
        ]
    ],
    
    // 报告配置
    'reports' => [
        'daily_report' => [
            'enabled' => true,
            'send_time' => '09:00',
            'recipients' => [
                '<EMAIL>'
            ],
            'include_charts' => true
        ],
        
        'weekly_report' => [
            'enabled' => true,
            'send_day' => 'monday',
            'send_time' => '09:00',
            'recipients' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'include_trends' => true
        ]
    ],
    
    // 调试配置
    'debug' => [
        'enabled' => false,
        'log_level' => 'info',
        'log_file' => 'web3_monitor.log'
    ]
];
