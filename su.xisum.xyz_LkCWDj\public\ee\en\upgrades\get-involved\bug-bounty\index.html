﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta data-react-helmet="true" name="description"
    content="An overview of the consensus layer bug bounty program: how to get involved and reward information.">
  <meta name="theme-color" content="#1c1ce1">
  <meta name="generator" content="Gatsby 4.7.1">
  <style data-href="/styles.92bedc857ac51bb6cf96.css" data-identity="gatsby-global-css">
    html {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 1rem
    }

    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    main,
    menu,
    nav,
    section,
    summary {
      display: block
    }

    audio,
    canvas,
    progress,
    video {
      display: inline-block
    }

    audio:not([controls]) {
      display: none;
      height: 0
    }

    progress {
      vertical-align: baseline
    }

    [hidden],
    template {
      display: none
    }

    a {
      -webkit-text-decoration-skip: objects;
      background-color: transparent;
      text-decoration: none
    }

    a:active,
    a:hover {
      outline-width: 0
    }

    abbr[title] {
      border-bottom: none;
      text-decoration: underline;
      -webkit-text-decoration: underline dotted;
      text-decoration: underline dotted
    }

    b,
    strong {
      font-weight: inherit;
      font-weight: bolder
    }

    dfn {
      font-style: italic
    }

    h1 {
      font-size: 2em;
      margin: .67em 0
    }

    mark {
      background-color: #ff0;
      color: #000
    }

    small {
      font-size: 80%
    }

    sub,
    sup {
      font-size: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline
    }

    sub {
      bottom: -.25em
    }

    sup {
      top: -.5em
    }

    img {
      border-style: none
    }

    svg:not(:root) {
      overflow: hidden
    }

    code,
    kbd,
    pre,
    samp {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: 1em
    }

    figure {
      margin: 1em 40px
    }

    hr {
      box-sizing: content-box;
      height: 0;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font: inherit;
      margin: 0
    }

    optgroup {
      font-weight: 700
    }

    button,
    input {
      overflow: visible
    }

    button,
    select {
      text-transform: none
    }

    [type=reset],
    [type=submit],
    button,
    html [type=button] {
      -webkit-appearance: button
    }

    [type=button]::-moz-focus-inner,
    [type=reset]::-moz-focus-inner,
    [type=submit]::-moz-focus-inner,
    button::-moz-focus-inner {
      border-style: none;
      padding: 0
    }

    [type=button]:-moz-focusring,
    [type=reset]:-moz-focusring,
    [type=submit]:-moz-focusring,
    button:-moz-focusring {
      outline: 1px dotted ButtonText
    }

    fieldset {
      border: 1px solid silver;
      margin: 0 2px;
      padding: .35em .625em .75em
    }

    legend {
      box-sizing: border-box;
      color: inherit;
      display: table;
      max-width: 100%;
      padding: 0;
      white-space: normal
    }

    textarea {
      overflow: auto
    }

    [type=checkbox],
    [type=radio] {
      box-sizing: border-box;
      padding: 0
    }

    [type=number]::-webkit-inner-spin-button,
    [type=number]::-webkit-outer-spin-button {
      height: auto
    }

    [type=search] {
      -webkit-appearance: textfield;
      outline-offset: -2px
    }

    [type=search]::-webkit-search-cancel-button,
    [type=search]::-webkit-search-decoration {
      -webkit-appearance: none
    }

    ::-webkit-input-placeholder {
      color: inherit;
      opacity: .54
    }

    ::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit
    }

    html {
      box-sizing: border-box;
      font: 100%/1.6em georgia, serif;
      overflow-y: scroll
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      word-wrap: break-word;
      -ms-font-feature-settings: "kern", "liga", "clig", "calt";
      -webkit-font-feature-settings: "kern", "liga", "clig", "calt";
      font-feature-settings: "kern", "liga", "clig", "calt";
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-kerning: normal;
      font-weight: 400
    }

    img {
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      max-width: 100%;
      padding: 0
    }

    h1 {
      font-size: 2.25rem;
      font-weight: 500
    }

    h1,
    h2 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 0 0 1.45rem;
      padding: 0
    }

    h2 {
      font-size: 1.62671rem;
      font-weight: 700
    }

    h3 {
      font-size: 1.38316rem;
      font-weight: 500
    }

    h3,
    h4 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 2rem 0 1.45rem;
      padding: 0
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600
    }

    h5 {
      font-size: 1rem;
      margin: 2rem 0 1.45rem
    }

    h5,
    h6 {
      text-rendering: optimizeLegibility;
      color: inherit;
      font-weight: 500;
      line-height: 1.1;
      padding: 0
    }

    h6 {
      font-size: .85028rem
    }

    h6,
    hgroup {
      margin: 0 0 1.45rem
    }

    hgroup {
      padding: 0
    }

    ol,
    ul {
      list-style-image: none;
      list-style-position: outside;
      margin: 0 0 1.45rem 1.45rem;
      padding: 0
    }

    dd,
    dl,
    figure,
    p {
      margin: 0 0 1.45rem;
      padding: 0
    }

    pre {
      word-wrap: normal;
      background: rgba(0, 0, 0, .04);
      border-radius: 3px;
      font-size: .85rem;
      line-height: 1.42;
      margin: 0 0 1.45rem;
      overflow: auto;
      padding: 1.45rem;
      white-space: pre-wrap
    }

    table {
      border-collapse: collapse;
      font-size: 1rem;
      line-height: 1.45rem;
      width: 100%
    }

    fieldset,
    table {
      margin: 0 0 1.45rem;
      padding: 0
    }

    blockquote {
      margin: 0 1.45rem 1.45rem;
      padding: 0
    }

    form,
    iframe,
    noscript {
      margin: 0 0 1.45rem;
      padding: 0
    }

    hr {
      background: rgba(0, 0, 0, .2);
      border: none;
      height: 1px;
      margin: 4rem 0 0;
      padding: 0
    }

    address {
      margin: 0 0 1.45rem;
      padding: 0
    }

    b,
    dt,
    strong,
    th {
      font-weight: 700
    }

    li {
      margin-bottom: .725rem
    }

    ol li,
    ul li {
      padding-left: 0
    }

    li>ol,
    li>ul {
      margin-bottom: .725rem;
      margin-left: 1.45rem;
      margin-top: .725rem
    }

    blockquote :last-child,
    li :last-child,
    p :last-child {
      margin-bottom: 0
    }

    li>p {
      margin-bottom: .725rem
    }

    code {
      font-size: 1em;
      line-height: 1.45em
    }

    kbd {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: .625rem;
      line-height: 1.56rem
    }

    samp {
      font-size: .85rem;
      line-height: 1.45rem
    }

    abbr,
    abbr[title],
    acronym {
      border-bottom: 1px dotted rgba(0, 0, 0, .5);
      cursor: help
    }

    abbr[title] {
      text-decoration: none
    }

    td,
    th,
    thead {
      text-align: left
    }

    td,
    th {
      font-feature-settings: "tnum";
      -moz-font-feature-settings: "tnum";
      -ms-font-feature-settings: "tnum";
      -webkit-font-feature-settings: "tnum";
      border-bottom: 1px solid hsla(0, 13%, 72%, .12);
      padding: .725rem .96667rem calc(.725rem - 1px)
    }

    td:first-child,
    th:first-child {
      padding-left: 0
    }

    td:last-child,
    th:last-child {
      padding-right: 0
    }

    tt {
      background-color: #2b2834;
      border-radius: 2px;
      color: #968af6
    }

    code,
    tt {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      padding: .2em
    }

    code {
      background-color: rgba(0, 0, 0, .04);
      border-radius: 3px
    }

    pre code {
      background: none;
      line-height: 1.42
    }

    code:before,
    pre code:after,
    pre code:before,
    pre tt:after,
    pre tt:before,
    tt:after,
    tt:before {
      content: ""
    }

    @media only screen and (max-width:480px) {
      html {
        font-size: 100%
      }
    }

    .assets-page .gatsby-resp-image-wrapper {
      max-height: 200px !important
    }

    .assets-page .gatsby-resp-image-image {
      width: auto !important
    }
  </style>
  <link rel="icon" href="/favicon-32x32.png?v=8b512faa8d4a0b019c123a771b6622aa" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="anonymous">
  <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48x48.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256x256.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="preconnect" href="https://matomo.ethstake.exchange">
  <link rel="sitemap" type="application/xml" href="/sitemap/sitemap-index.xml">
  <style type="text/css">
    .anchor.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }

    .anchor.after {
      display: inline-block;
      padding-left: 4px;
    }

    h1 .anchor svg,
    h2 .anchor svg,
    h3 .anchor svg,
    h4 .anchor svg,
    h5 .anchor svg,
    h6 .anchor svg {
      visibility: hidden;
    }

    h1:hover .anchor svg,
    h2:hover .anchor svg,
    h3:hover .anchor svg,
    h4:hover .anchor svg,
    h5:hover .anchor svg,
    h6:hover .anchor svg,
    h1 .anchor:focus svg,
    h2 .anchor:focus svg,
    h3 .anchor:focus svg,
    h4 .anchor:focus svg,
    h5 .anchor:focus svg,
    h6 .anchor:focus svg {
      visibility: visible;
    }
  </style>
  <script>
    document.addEventListener("DOMContentLoaded", function (event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
          var clientTop = document.documentElement.clientTop || document.body.clientTop || 0
          var offset = element.getBoundingClientRect().top + scrollTop - clientTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function () {
            window.scrollTo(0, offset - 0)
          }), 0)
        }
      }
    })
  </script>
  <title data-react-helmet="true">Consensus layer bug hunting bounty program | ethstake.exchange</title>
  <link data-react-helmet="true" rel="canonical" href="index.html">
  <script data-react-helmet="true" type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": "https://ethstake.exchange",
          "email": "<EMAIL>",
          "name": "Ethereum",
          "logo": "https://ethstake.exchange/og-image.png"
        }
      </script>
  <style>
    .gatsby-image-wrapper {
      position: relative;
      overflow: hidden
    }

    .gatsby-image-wrapper picture.object-fit-polyfill {
      position: static !important
    }

    .gatsby-image-wrapper img {
      bottom: 0;
      height: 100%;
      left: 0;
      margin: 0;
      max-width: none;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      object-fit: cover
    }

    .gatsby-image-wrapper [data-main-image] {
      opacity: 0;
      transform: translateZ(0);
      transition: opacity .25s linear;
      will-change: opacity
    }

    .gatsby-image-wrapper-constrained {
      display: inline-block;
      vertical-align: top
    }
  </style><noscript>
    <style>
      .gatsby-image-wrapper noscript [data-main-image] {
        opacity: 1 !important
      }

      .gatsby-image-wrapper [data-placeholder-image] {
        opacity: 0 !important
      }
    </style>
  </noscript>
  <script
    type="module">const e = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; e && document.body.addEventListener("load", (function (e) { if (void 0 === e.target.dataset.mainImage) return; if (void 0 === e.target.dataset.gatsbyImageSsr) return; const t = e.target; let a = null, n = t; for (; null === a && n;)void 0 !== n.parentNode.dataset.gatsbyImageWrapper && (a = n.parentNode), n = n.parentNode; const o = a.querySelector("[data-placeholder-image]"), r = new Image; r.src = t.currentSrc, r.decode().catch((() => { })).then((() => { t.style.opacity = 1, o && (o.style.opacity = 0, o.style.transition = "opacity 500ms linear") })) }), !0);</script>
  <style data-styled="" data-styled-version="5.3.3">
    body {
      background-color: #ffffff;
      color: #333333;
    }

    /*!sc*/
    a {
      color: #1c1cff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    mark {
      background: rgba(143, 187, 237, .1);
      box-shadow: inset 0 -2px 0 0 rgba(69, 142, 225, .8);
    }

    /*!sc*/
    .anchor.before {
      fill: #333333;
    }

    /*!sc*/
    hr {
      background: #ecececnull#1c1cff;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
      position: absolute;
    }

    /*!sc*/
    iframe {
      display: block;
      max-width: 560px;
      margin: 32px 0;
    }

    /*!sc*/
    h1 {
      font-size: 3rem;
      line-height: 1.4;
      margin: 2rem 0;
      font-weight: 700;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h1 {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    h2 {
      font-size: 2rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 3rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h2 {
        font-size: 1.5rem;
      }
    }

    /*!sc*/
    h3 {
      font-size: 1.5rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 2.5rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h3 {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    h4 {
      font-size: 1.25rem;
      line-height: 1.4;
      font-weight: 500;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h4 {
        font-size: 1rem;
      }
    }

    /*!sc*/
    h5 {
      font-size: 1rem;
      line-height: 1.4;
      font-weight: 450;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    h6 {
      font-size: 0.9rem;
      line-height: 1.4;
      font-weight: 400;
      text-transform: uppercase;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    data-styled.g1[id="sc-global-hcwgEG1"] {
      content: "sc-global-hcwgEG1,"
    }

    /*!sc*/
    .iylOGp {
      fill: #b2b2b2;
    }

    /*!sc*/
    .iylOGp:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g2[id="Icon__StyledIcon-sc-1o8zi5s-0"] {
      content: "iylOGp,"
    }

    /*!sc*/
    .gABYms:after {
      margin-left: 0.125em;
      margin-right: 0.3em;
      display: inline;
      content: "â†—";
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out;
      font-style: normal;
    }

    /*!sc*/
    .gABYms:hover:after {
      -webkit-transform: translate(0.15em, -0.2em);
      -ms-transform: translate(0.15em, -0.2em);
      transform: translate(0.15em, -0.2em);
    }

    /*!sc*/
    data-styled.g3[id="Link__ExternalLink-sc-e3riao-0"] {
      content: "gABYms,"
    }

    /*!sc*/
    .gCWUlE .is-glossary {
      white-space: nowrap;
    }

    /*!sc*/
    .gCWUlE.active {
      color: #1c1cff;
    }

    /*!sc*/
    .gCWUlE:hover svg {
      fill: #1c1cff;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g4[id="Link__InternalLink-sc-e3riao-1"] {
      content: "gCWUlE,"
    }

    /*!sc*/
    .iEXhBV.active {
      color: #1c1cff;
    }

    /*!sc*/
    data-styled.g5[id="Link__ExplicitLangInternalLink-sc-e3riao-2"] {
      content: "iEXhBV,"
    }

    /*!sc*/
    .jfMIWk {
      margin: 0 0.25rem 0 0.35rem;
      fill: #4949ff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    .jfMIWk:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g6[id="Link__GlossaryIcon-sc-e3riao-3"] {
      content: "jfMIWk,"
    }

    /*!sc*/
    .gvoBKJ {
      padding-top: 3rem;
      padding-bottom: 4rem;
      padding: 1rem 2rem;
    }

    /*!sc*/
    data-styled.g7[id="Footer__StyledFooter-sc-1to993d-0"] {
      content: "gvoBKJ,"
    }

    /*!sc*/
    .kFKfdz {
      font-size: 0.875rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g8[id="Footer__FooterTop-sc-1to993d-1"] {
      content: "kFKfdz,"
    }

    /*!sc*/
    .bWGwos {
      color: #666666;
    }

    /*!sc*/
    data-styled.g9[id="Footer__LastUpdated-sc-1to993d-2"] {
      content: "bWGwos,"
    }

    /*!sc*/
    .hlbLsM {
      display: grid;
      grid-template-columns: repeat(6, auto);
      grid-gap: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1300px) {
      .hlbLsM {
        grid-template-columns: repeat(3, auto);
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .hlbLsM {
        grid-template-columns: repeat(2, auto);
      }
    }

    /*!sc*/
    @media (max-width:500px) {
      .hlbLsM {
        grid-template-columns: auto;
      }
    }

    /*!sc*/
    data-styled.g10[id="Footer__LinkGrid-sc-1to993d-3"] {
      content: "hlbLsM,"
    }

    /*!sc*/
    .bbCEKr {
      font-size: 0.875rem;
      line-height: 1.6;
      margin: 1.14em 0;
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g12[id="Footer__SectionHeader-sc-1to993d-5"] {
      content: "bbCEKr,"
    }

    /*!sc*/
    .gjQPMc {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      margin: 0;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g13[id="Footer__List-sc-1to993d-6"] {
      content: "gjQPMc,"
    }

    /*!sc*/
    .eGhJJx {
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g14[id="Footer__ListItem-sc-1to993d-7"] {
      content: "eGhJJx,"
    }

    /*!sc*/
    .gIpSoz {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #666666;
    }

    /*!sc*/
    .gIpSoz svg {
      fill: #666666;
    }

    /*!sc*/
    .gIpSoz:after {
      color: #666666;
    }

    /*!sc*/
    .gIpSoz:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover:after {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g15[id="Footer__FooterLink-sc-1to993d-8"] {
      content: "gIpSoz,"
    }

    /*!sc*/
    .kdLbod {
      margin: 1rem 0;
    }

    /*!sc*/
    data-styled.g16[id="Footer__SocialIcons-sc-1to993d-9"] {
      content: "kdLbod,"
    }

    /*!sc*/
    .iedzfy {
      margin-left: 1rem;
      width: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iedzfy {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g17[id="Footer__SocialIcon-sc-1to993d-10"] {
      content: "iedzfy,"
    }

    /*!sc*/
    .drElXa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-right: 1.5rem;
    }

    /*!sc*/
    .drElXa:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g20[id="Dropdown__DropdownTitle-sc-1yd08gi-1"] {
      content: "drElXa,"
    }

    /*!sc*/
    .ldsPWM {
      margin: 0;
      position: absolute;
      margin-top: -1rem;
      list-style-type: none;
      list-style-image: none;
      top: 100%;
      width: auto;
      border-radius: 0.5em;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      opacity:0;
      display:none;
      transform:rotateX(-15deg) translateZ(0)
    }

    /*!sc*/
    data-styled.g21[id="Dropdown__DropdownList-sc-1yd08gi-2"] {
      content: "ldsPWM,"
    }

    /*!sc*/
    .Mkofa {
      white-space: nowrap;
      margin: 0;
      color: #333333;
    }

    /*!sc*/
     .Mkofa:hover >ul{
      color: #1c1cff;
      opacity:1;
      display:block;
      transform: none;
    }

    /*!sc*/
    data-styled.g23[id="Dropdown__NavListItem-sc-1yd08gi-4"] {
      content: "Mkofa,"
    }

    /*!sc*/
    .lgeotR {
      margin: 0;
      color: #333333;
    }

    /*!sc*/
    .lgeotR:hover {
      color: #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g24[id="Dropdown__DropdownItem-sc-1yd08gi-5"] {
      content: "lgeotR,"
    }

    /*!sc*/
    .cTcxIB {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: block;
      padding: 0.5rem;
      color: #333333;
    }

    /*!sc*/
    .cTcxIB svg {
      fill: #666666;
    }

    /*!sc*/
    .cTcxIB:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cTcxIB:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g25[id="Dropdown__NavLink-sc-1yd08gi-6"] {
      content: "cTcxIB,"
    }

    /*!sc*/
    .ivCgwn {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .ivCgwn>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .hLjau {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .hLjau>img {
      width: 3em !important;
      height: 3em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .RDZme {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .RDZme>img {
      width: 1em !important;
      height: 1em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .jCfhGS {
      display: inline-block;
      margin-right: 2rem;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .jCfhGS>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    data-styled.g27[id="Emoji__StyledEmoji-sc-ihpuqw-0"] {
      content: "ivCgwn,hLjau,RDZme,jCfhGS,"
    }

    /*!sc*/
    .dUatah {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: none;
      border: none;
      color: inherit;
      display: inline-block;
      font: inherit;
      padding: initial;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g28[id="NakedButton-sc-1g43w8v-0"] {
      content: "dUatah,"
    }

    /*!sc*/
    .eoyKpR {
      margin: 0;
      position: relative;
      border-radius: 0.25em;
    }

    /*!sc*/
    data-styled.g29[id="Input__Form-sc-1utkal6-0"] {
      content: "eoyKpR,"
    }

    /*!sc*/
    .kkfPkW {
      border: 1px solid #7f7f7f;
      color: #333333;
      background: #ffffff;
      padding: 0.5rem;
      padding-right: 2rem;
      border-radius: 0.25em;
      width: 100%;
    }

    /*!sc*/
    .kkfPkW:focus {
      outline: #1c1cff auto 1px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .kkfPkW {
        padding-left: 2rem;
      }
    }

    /*!sc*/
    data-styled.g30[id="Input__StyledInput-sc-1utkal6-1"] {
      content: "kkfPkW,"
    }

    /*!sc*/
    .gFzMVg {
      position: absolute;
      right: 6px;
      top: 50%;
      margin-top: -12px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .gFzMVg {
        left: 6px;
      }
    }

    /*!sc*/
    data-styled.g31[id="Input__SearchIcon-sc-1utkal6-2"] {
      content: "gFzMVg,"
    }

    /*!sc*/
    .ggVPUc {
      border: 1px solid #7f7f7f;
      border-radius: 0.25em;
      color: #333333;
      display: none;
      margin-bottom: 0;
      padding: 0 6px;
      position: absolute;
      right: 6px;
      top: 20%;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .ggVPUc {
        display: inline-block;
      }
    }

    /*!sc*/
    data-styled.g32[id="Input__SearchSlash-sc-1utkal6-3"] {
      content: "ggVPUc,"
    }

    /*!sc*/
    .kNenpg {
      position: relative;
      display: grid;
      grid-gap: 1em;
    }

    /*!sc*/
    data-styled.g33[id="Search__Root-sc-1qm8xwy-0"] {
      content: "kNenpg,"
    }

    /*!sc*/
    .eJIgkk {
      display: none;
      max-height: 80vh;
      overflow: scroll;
      z-index: 2;
      position: absolute;
      right: 0;
      top: calc(100% + 0.5em);
      width: 80vw;
      max-width: 30em;
      box-shadow: 0 0 5px 0;
      padding: 0.5rem;
      background: #ffffff;
      border-radius: 0.25em;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eJIgkk {
        width: 100%;
      }
    }

    /*!sc*/
    .eJIgkk>*+* {
      padding-top: 1em !important;
      border-top: 2px solid black;
    }

    /*!sc*/
    .eJIgkk li {
      margin-bottom: 0.4rem;
    }

    /*!sc*/
    .eJIgkk li+li {
      padding-top: 0.7em;
      border-top: 1px solid #ececec;
    }

    /*!sc*/
    .eJIgkk ul {
      margin: 0;
      list-style: none;
    }

    /*!sc*/
    .eJIgkk mark {
      color: #1c1cff;
      box-shadow: inset 0 -2px 0 0 rgba(143, 187, 237, .5);
    }

    /*!sc*/
    .eJIgkk header {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk header h3 {
      color: #ffffff;
      background: #4c4c4c;
      padding: 0.1em 0.4em;
      border-radius: 0.25em;
    }

    /*!sc*/
    .eJIgkk h3 {
      margin: 0 0 0.5em;
    }

    /*!sc*/
    .eJIgkk h4 {
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk a {
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    data-styled.g34[id="Search__HitsWrapper-sc-1qm8xwy-1"] {
      content: "eJIgkk,"
    }

    /*!sc*/
    .fOWxWQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      background: #f7f7f7;
      border-radius: 2px;
      border: 1px solid #ececec;
      padding: 1.5rem;
    }

    /*!sc*/
    data-styled.g38[id="Card__StyledCard-sc-1x2vwsh-0"] {
      content: "fOWxWQ,"
    }

    /*!sc*/
    .dlQPfD {
      opacity: 0.8;
    }

    /*!sc*/
    data-styled.g39[id="Card__Description-sc-1x2vwsh-1"] {
      content: "dlQPfD,"
    }

    /*!sc*/
    .kCvjty {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      margin: 0 auto;
    }

    /*!sc*/
    data-styled.g41[id="SharedStyledComponents__Page-sc-1cr9zfr-0"] {
      content: "kCvjty,"
    }

    /*!sc*/
    .dedPKg {
      padding: 1rem 2rem;
      width: 100%;
    }

    /*!sc*/
    data-styled.g44[id="SharedStyledComponents__Content-sc-1cr9zfr-3"] {
      content: "dedPKg,"
    }

    /*!sc*/
    .iRRQao {
      width: 100%;
      padding: 4rem 0rem;
      margin-top: 2rem;
      background: #fcfcfc;
      box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g49[id="SharedStyledComponents__GrayContainer-sc-1cr9zfr-8"] {
      content: "iRRQao,"
    }

    /*!sc*/
    .kKRvUp {
      width: 100%;
      padding: 4rem 0rem;
      margin-top: 2rem;
      background: radial-gradient(46.28% 66.31% at 66.95% 58.35%, #e6e6f7 0%, #e7edfa 50%, #e9fbfa 100%);
      box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g50[id="SharedStyledComponents__GradientContainer-sc-1cr9zfr-9"] {
      content: "kKRvUp,"
    }

    /*!sc*/
    .oxZAg {
      font-weight: 800;
      font-size: 3rem;
      line-height: 140%;
      max-width: 720px;
      margin-top: 1rem;
      background-clip: text;
      background-image: linear-gradient(285.24deg, #F7CBC0 0%, #F4B1AB 29.8%, #8476D9 49.78%, #85ACF9 54.14%, #1C1CE1 61.77%, #000000 69.77%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 0rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .oxZAg {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g51[id="SharedStyledComponents__SloganGradient-sc-1cr9zfr-10"] {
      content: "oxZAg,"
    }

    /*!sc*/
    .jEZlpP {
      -webkit-text-decoration: none;
      text-decoration: none;
      margin-right: 2rem;
      color: #333333;
    }

    /*!sc*/
    .jEZlpP svg {
      fill: #666666;
    }

    /*!sc*/
    .jEZlpP:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .jEZlpP:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .jEZlpP.active {
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g52[id="SharedStyledComponents__NavLink-sc-1cr9zfr-11"] {
      content: "jEZlpP,"
    }

    /*!sc*/
    .eOjDVk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      margin-left: -1rem;
      margin-right: -1rem;
    }

    /*!sc*/
    data-styled.g55[id="SharedStyledComponents__CardContainer-sc-1cr9zfr-14"] {
      content: "eOjDVk,"
    }

    /*!sc*/
    .iuRocQ {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iuRocQ:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    data-styled.g59[id="SharedStyledComponents__Button-sc-1cr9zfr-18"] {
      content: "iuRocQ,"
    }

    /*!sc*/
    .bphJHH {
      background-color: #1c1cff;
      color: #ffffff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .bphJHH:hover {
      background-color: rgba(28, 28, 225, 0.8);
    }

    /*!sc*/
    .bphJHH:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g60[id="SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19"] {
      content: "bphJHH,"
    }

    /*!sc*/
    .hhdXUp {
      display: none;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hhdXUp {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    data-styled.g73[id="Mobile__Container-sc-zxc8gm-0"] {
      content: "hhdXUp,"
    }

    /*!sc*/
    .dUGGTH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g74[id="Mobile__MenuIcon-sc-zxc8gm-1"] {
      content: "dUGGTH,"
    }

    /*!sc*/
    .dLNRLx {
      margin-left: 1rem;
    }

    /*!sc*/
    data-styled.g75[id="Mobile__MenuButton-sc-zxc8gm-2"] {
      content: "dLNRLx,"
    }

    /*!sc*/
    .hvwyGc {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g76[id="Mobile__OtherIcon-sc-zxc8gm-3"] {
      content: "hvwyGc,"
    }

    /*!sc*/
    .bCHBHX {
      position: fixed;
      background: hsla(0, 0%, 69.8%, 0.9);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
    }

    /*!sc*/
    data-styled.g77[id="Mobile__MobileModal-sc-zxc8gm-4"] {
      content: "bCHBHX,"
    }

    /*!sc*/
    .AJukL {
      background: #ffffff;
      z-index: 99;
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
    }

    /*!sc*/
    data-styled.g78[id="Mobile__MenuContainer-sc-zxc8gm-5"] {
      content: "AJukL,"
    }

    /*!sc*/
    .gbspKa {
      margin: 0 0.125rem;
      width: 1.5rem;
      height: 2.5rem;
      position: relative;
      stroke-width: 2px;
      z-index: 100;
    }

    /*!sc*/
    .gbspKa>path {
      stroke: #333333;
      fill: none;
    }

    /*!sc*/
    .gbspKa:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gbspKa:hover>path {
      stroke: #1c1cff;
    }

    /*!sc*/
    data-styled.g79[id="Mobile__GlyphButton-sc-zxc8gm-6"] {
      content: "gbspKa,"
    }

    /*!sc*/
    .gBSEi {
      z-index: 101;
      padding: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g80[id="Mobile__SearchContainer-sc-zxc8gm-7"] {
      content: "gBSEi,"
    }

    /*!sc*/
    .iXlChz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .iXlChz>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g81[id="Mobile__SearchHeader-sc-zxc8gm-8"] {
      content: "iXlChz,"
    }

    /*!sc*/
    .jmriUx {
      z-index: 102;
      cursor: pointer;
    }

    /*!sc*/
    .jmriUx>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g82[id="Mobile__CloseIconContainer-sc-zxc8gm-9"] {
      content: "jmriUx,"
    }

    /*!sc*/
    .gYetwr {
      margin: 0;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 3rem 1rem 8rem;
    }

    /*!sc*/
    data-styled.g83[id="Mobile__MenuItems-sc-zxc8gm-10"] {
      content: "gYetwr,"
    }

    /*!sc*/
    .gXxMFO {
      margin: 0;
      margin-bottom: 3rem;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g84[id="Mobile__NavListItem-sc-zxc8gm-11"] {
      content: "gXxMFO,"
    }

    /*!sc*/
    .kuWShR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 0;
    }

    /*!sc*/
    data-styled.g85[id="Mobile__StyledNavLink-sc-zxc8gm-12"] {
      content: "kuWShR,"
    }

    /*!sc*/
    .erCTXJ {
      margin: 1rem 0;
      color: #333333;
    }

    /*!sc*/
    data-styled.g86[id="Mobile__SectionTitle-sc-zxc8gm-13"] {
      content: "erCTXJ,"
    }

    /*!sc*/
    .hghxUt {
      margin: 0;
    }

    /*!sc*/
    data-styled.g87[id="Mobile__SectionItems-sc-zxc8gm-14"] {
      content: "hghxUt,"
    }

    /*!sc*/
    .kdRQoZ {
      margin-bottom: 1rem;
      list-style-type: none;
      list-style-image: none;
      opacity: 0.7;
    }

    /*!sc*/
    .kdRQoZ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g88[id="Mobile__SectionItem-sc-zxc8gm-15"] {
      content: "kdRQoZ,"
    }

    /*!sc*/
    .iYttIj {
      background: #ffffff;
      border-top: 1px solid #ececec;
      padding-right: 1rem;
      padding-left: 1rem;
      margin-top: auto;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 108px;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      max-width: 450px;
      z-index: 99;
    }

    /*!sc*/
    data-styled.g89[id="Mobile__BottomMenu-sc-zxc8gm-16"] {
      content: "iYttIj,"
    }

    /*!sc*/
    .cnajxM {
      -webkit-flex: 1 1 120px;
      -ms-flex: 1 1 120px;
      flex: 1 1 120px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      color: #333333;
    }

    /*!sc*/
    .cnajxM>svg {
      fill: #333333;
    }

    /*!sc*/
    .cnajxM:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cnajxM:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g90[id="Mobile__BottomItem-sc-zxc8gm-17"] {
      content: "cnajxM,"
    }

    /*!sc*/
    .heSUpS {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
    }

    /*!sc*/
    .heSUpS>svg {
      fill: #333333;
    }

    /*!sc*/
    .heSUpS:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .heSUpS:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g91[id="Mobile__BottomLink-sc-zxc8gm-18"] {
      content: "heSUpS,"
    }

    /*!sc*/
    .hkZTkJ {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      margin-top: 0.5rem;
      text-transform: uppercase;
      text-align: center;
      opacity: 0.7;
    }

    /*!sc*/
    .hkZTkJ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g92[id="Mobile__BottomItemText-sc-zxc8gm-19"] {
      content: "hkZTkJ,"
    }

    /*!sc*/
    .jBipln {
      color: #333333;
      background: #f2f2f2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 10vw;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 280px;
      width: min(60vw, 280px);
      height: 280px;
      height: min(60vw, 280px);
      border-radius: 100%;
    }

    /*!sc*/
    data-styled.g93[id="Mobile__BlankSearchState-sc-zxc8gm-20"] {
      content: "jBipln,"
    }

    /*!sc*/
    .iGuESw {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 1000;
      width: 100%;
    }

    /*!sc*/
    data-styled.g94[id="Nav__NavContainer-sc-1aprtmp-0"] {
      content: "iGuESw,"
    }

    /*!sc*/
    .cpomzd {
      height: 4.75rem;
      padding: 1rem 2rem;
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g95[id="Nav__StyledNav-sc-1aprtmp-1"] {
      content: "cpomzd,"
    }

    /*!sc*/
    .faUCsG {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 1440px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .faUCsG {
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
    }

    /*!sc*/
    data-styled.g97[id="Nav__NavContent-sc-1aprtmp-3"] {
      content: "faUCsG,"
    }

    /*!sc*/
    .gjaVMk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gjaVMk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g98[id="Nav__InnerContent-sc-1aprtmp-4"] {
      content: "gjaVMk,"
    }

    /*!sc*/
    .jUJHKw {
      margin: 0;
      margin-left: 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g99[id="Nav__LeftItems-sc-1aprtmp-5"] {
      content: "jUJHKw,"
    }

    /*!sc*/
    .kQWBtS {
      margin: 0;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g100[id="Nav__RightItems-sc-1aprtmp-6"] {
      content: "kQWBtS,"
    }

    /*!sc*/
    .jODkFW {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-right: 0;
      margin-left: 1rem;
    }

    /*!sc*/
    .jODkFW:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g102[id="Nav__RightNavLink-sc-1aprtmp-8"] {
      content: "jODkFW,"
    }

    /*!sc*/
    .igUcis {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g103[id="Nav__HomeLogoNavLink-sc-1aprtmp-9"] {
      content: "igUcis,"
    }

    /*!sc*/
    .euWmfq {
      opacity: 0.85;
    }

    /*!sc*/
    .euWmfq:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g104[id="Nav__HomeLogo-sc-1aprtmp-10"] {
      content: "euWmfq,"
    }

    /*!sc*/
    .bDRFLa {
      padding-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g105[id="Nav__Span-sc-1aprtmp-11"] {
      content: "bDRFLa,"
    }

    /*!sc*/
    .hwxIMf {
      margin-left: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .hwxIMf:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g106[id="Nav__ThemeToggle-sc-1aprtmp-12"] {
      content: "hwxIMf,"
    }

    /*!sc*/
    .jOKVBH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g107[id="Nav__NavIcon-sc-1aprtmp-13"] {
      content: "jOKVBH,"
    }

    /*!sc*/
    .iMiHPL {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iMiHPL function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .iMiHPL function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g125[id="ButtonLink__StyledLinkButton-sc-8betkf-0"] {
      content: "iMiHPL,"
    }

    /*!sc*/
    .kmLdQv {
      background-color: #1c1cff;
      color: #ffffff !important;
      border: 1px solid transparent;
    }

    /*!sc*/
    .kmLdQv:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .kmLdQv:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g127[id="ButtonLink__PrimaryLink-sc-8betkf-2"] {
      content: "kmLdQv,"
    }

    /*!sc*/
    .dzWGyc {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .dzWGyc:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .dzWGyc:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g128[id="ButtonLink__SecondaryLink-sc-8betkf-3"] {
      content: "dzWGyc,"
    }

    /*!sc*/
    .elpFuD {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g131[id="TranslationBanner__H3-sc-cd94ib-0"] {
      content: "elpFuD,"
    }

    /*!sc*/
    .jJbcq {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jJbcq {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g132[id="TranslationBanner__BannerContainer-sc-cd94ib-1"] {
      content: "jJbcq,"
    }

    /*!sc*/
    .jIVPcV {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jIVPcV {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g133[id="TranslationBanner__StyledBanner-sc-cd94ib-2"] {
      content: "jIVPcV,"
    }

    /*!sc*/
    .jiZNpa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .jiZNpa {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g134[id="TranslationBanner__BannerContent-sc-cd94ib-3"] {
      content: "jiZNpa,"
    }

    /*!sc*/
    .dOewRO {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g135[id="TranslationBanner__BannerClose-sc-cd94ib-4"] {
      content: "dOewRO,"
    }

    /*!sc*/
    .cEauOV {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g136[id="TranslationBanner__BannerCloseIcon-sc-cd94ib-5"] {
      content: "cEauOV,"
    }

    /*!sc*/
    .nChYp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .nChYp {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g137[id="TranslationBanner__Row-sc-cd94ib-6"] {
      content: "nChYp,"
    }

    /*!sc*/
    .gXNXMi {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gXNXMi {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g138[id="TranslationBanner__ButtonRow-sc-cd94ib-7"] {
      content: "gXNXMi,"
    }

    /*!sc*/
    .hTWLVy {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hTWLVy {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g139[id="TranslationBanner__StyledEmoji-sc-cd94ib-8"] {
      content: "hTWLVy,"
    }

    /*!sc*/
    .kUKdfA {
      margin-left: 0.5rem;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kUKdfA {
        margin-left: 0rem;
        margin-top: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g140[id="TranslationBanner__SecondaryButtonLink-sc-cd94ib-9"] {
      content: "kUKdfA,"
    }

    /*!sc*/
    .kIfJin {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g141[id="TranslationBannerLegal__H3-sc-1df4kz4-0"] {
      content: "kIfJin,"
    }

    /*!sc*/
    .eZKsbu {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eZKsbu {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g142[id="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1"] {
      content: "eZKsbu,"
    }

    /*!sc*/
    .cEcQwp {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .cEcQwp {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g143[id="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2"] {
      content: "cEcQwp,"
    }

    /*!sc*/
    .intGem {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .intGem {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g144[id="TranslationBannerLegal__BannerContent-sc-1df4kz4-3"] {
      content: "intGem,"
    }

    /*!sc*/
    .hMvMKu {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g145[id="TranslationBannerLegal__BannerClose-sc-1df4kz4-4"] {
      content: "hMvMKu,"
    }

    /*!sc*/
    .bhaYvl {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g146[id="TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5"] {
      content: "bhaYvl,"
    }

    /*!sc*/
    .cJRPhR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cJRPhR {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g147[id="TranslationBannerLegal__Row-sc-1df4kz4-6"] {
      content: "cJRPhR,"
    }

    /*!sc*/
    .kXSENe {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kXSENe {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g148[id="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7"] {
      content: "kXSENe,"
    }

    /*!sc*/
    .dRuawC {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dRuawC {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g149[id="TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8"] {
      content: "dRuawC,"
    }

    /*!sc*/
    .cRWHVB {
      background-color: #1c1cff;
    }

    /*!sc*/
    data-styled.g150[id="SkipLink__Div-sc-1ysqk2q-0"] {
      content: "cRWHVB,"
    }

    /*!sc*/
    .kOmocm {
      line-height: 2rem;
      position: absolute;
      top: -3rem;
      margin-left: 0.5rem;
      color: #ffffff;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .kOmocm:focus {
      position: static;
    }

    /*!sc*/
    data-styled.g151[id="SkipLink__Anchor-sc-1ysqk2q-1"] {
      content: "kOmocm,"
    }

    /*!sc*/
    .mXCTw {
      position: relative;
      margin: 0px auto;
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-flow: column;
      -ms-flex-flow: column;
      flex-flow: column;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .mXCTw {
        max-width: 1504px;
      }
    }

    /*!sc*/
    data-styled.g152[id="Layout__ContentContainer-sc-19910io-0"] {
      content: "mXCTw,"
    }

    /*!sc*/
    .gqazVg {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gqazVg {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g153[id="Layout__MainContainer-sc-19910io-1"] {
      content: "gqazVg,"
    }

    /*!sc*/
    .kCJhKM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }

    /*!sc*/
    data-styled.g154[id="Layout__MainContent-sc-19910io-2"] {
      content: "kCJhKM,"
    }

    /*!sc*/
    .dliKfQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: space-around;
      -webkit-justify-content: space-around;
      -ms-flex-pack: space-around;
      justify-content: space-around;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      overflow: visible;
      width: 100%;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    /*!sc*/
    data-styled.g155[id="Layout__Main-sc-19910io-3"] {
      content: "dliKfQ,"
    }

    /*!sc*/
    .llMzWl {
      margin: 0;
      font-size: 0.875rem;
      line-height: 140%;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      font-weight: normal;
    }

    /*!sc*/
    data-styled.g156[id="Breadcrumbs__Crumb-sc-1hkiaxl-0"] {
      content: "llMzWl,"
    }

    /*!sc*/
    .bBESuw {
      margin: 0;
      margin-bottom: 2rem;
      list-style-type: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;
    }

    /*!sc*/
    data-styled.g157[id="Breadcrumbs__List-sc-1hkiaxl-1"] {
      content: "bBESuw,"
    }

    /*!sc*/
    .krvHSI {
      margin: 0;
      margin-right: 0.5rem;
    }

    /*!sc*/
    data-styled.g158[id="Breadcrumbs__ListItem-sc-1hkiaxl-2"] {
      content: "krvHSI,"
    }

    /*!sc*/
    .iAwOZI {
      margin-left: 0.5rem;
      color: #7f7f7f;
    }

    /*!sc*/
    data-styled.g159[id="Breadcrumbs__Slash-sc-1hkiaxl-3"] {
      content: "iAwOZI,"
    }

    /*!sc*/
    .hXgzJL {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #7f7f7f;
    }

    /*!sc*/
    .hXgzJL:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .hXgzJL.active {
      color: #1c1cff;
    }

    /*!sc*/
    data-styled.g160[id="Breadcrumbs__CrumbLink-sc-1hkiaxl-4"] {
      content: "hXgzJL,"
    }

    /*!sc*/
    .dDgpBS {
      background-color: #ffffff;
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
      width: 100%;
      margin-bottom: 2rem;
    }

    /*!sc*/
    data-styled.g293[id="CardList__Table-sc-ablrq5-0"] {
      content: "dDgpBS,"
    }

    /*!sc*/
    .kUQIQU {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      color: #333333 !important;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      margin-bottom: 1px;
      padding: 1rem;
      width: 100%;
      color: #000000;
    }

    /*!sc*/
    .kUQIQU:hover {
      border-radius: 4px;
      box-shadow: 0 0 1px #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g295[id="CardList__ItemLink-sc-ablrq5-2"] {
      content: "kUQIQU,"
    }

    /*!sc*/
    .gpMYtH {
      font-size: 0.875rem;
      margin-bottom: 0;
      opacity: 0.6;
    }

    /*!sc*/
    data-styled.g297[id="CardList__ItemDesc-sc-ablrq5-4"] {
      content: "gpMYtH,"
    }

    /*!sc*/
    .vdTcS {
      -webkit-flex: 1 1 75%;
      -ms-flex: 1 1 75%;
      flex: 1 1 75%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-right: 2rem;
    }

    /*!sc*/
    data-styled.g298[id="CardList__LeftContainer-sc-ablrq5-5"] {
      content: "vdTcS,"
    }

    /*!sc*/
    .jzdati {
      min-width: 20px;
      margin-right: 1rem;
      margin-top: 4px;
    }

    /*!sc*/
    data-styled.g300[id="CardList__Image-sc-ablrq5-7"] {
      content: "jzdati,"
    }

    /*!sc*/
    .dVEoNO {
      position: relative;
      display: -webkit-inline-box;
      display: -webkit-inline-flex;
      display: -ms-inline-flexbox;
      display: inline-flex;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g370[id="Tooltip__Container-sc-e34rs3-0"] {
      content: "dVEoNO,"
    }

    /*!sc*/
    .gGjywO {
      background-color: #ffffff;
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
      width: 100%;
      margin-bottom: 2rem;
    }

    /*!sc*/
    data-styled.g504[id="Leaderboard__Table-sc-9p0qku-0"] {
      content: "gGjywO,"
    }

    /*!sc*/
    .cOccLj {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333 !important;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
      margin-bottom: 1px;
      padding: 1rem;
      width: 100%;
      color: #000000;
    }

    /*!sc*/
    .cOccLj:hover {
      border-radius: 4px;
      box-shadow: 0 0 1px #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g505[id="Leaderboard__Item-sc-9p0qku-1"] {
      content: "cOccLj,"
    }

    /*!sc*/
    .euYmLE {
      font-size: 0.875rem;
      margin-bottom: 0;
      opacity: 0.6;
    }

    /*!sc*/
    data-styled.g507[id="Leaderboard__ItemDesc-sc-9p0qku-3"] {
      content: "euYmLE,"
    }

    /*!sc*/
    .fNfHlp {
      -webkit-flex: 1 1 75%;
      -ms-flex: 1 1 75%;
      flex: 1 1 75%;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-right: 2rem;
    }

    /*!sc*/
    data-styled.g508[id="Leaderboard__TextContainer-sc-9p0qku-4"] {
      content: "fNfHlp,"
    }

    /*!sc*/
    .cXPtSw {
      margin-right: 1rem;
      height: 40px;
      width: 40px;
      border-radius: 50%;
    }

    /*!sc*/
    @media (max-width:320px) {
      .cXPtSw {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g509[id="Leaderboard__Avatar-sc-9p0qku-5"] {
      content: "cXPtSw,"
    }

    /*!sc*/
    .kyweyX {
      margin-right: 1rem;
      opacity: 0.4;
    }

    /*!sc*/
    data-styled.g510[id="Leaderboard__ItemNumber-sc-9p0qku-6"] {
      content: "kyweyX,"
    }

    /*!sc*/
    .iJMFiU {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin: 4rem 1rem;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g527[id="BugBountyCards__CardRow-sc-14nc59p-0"] {
      content: "iJMFiU,"
    }

    /*!sc*/
    .jGhkrb {
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g528[id="BugBountyCards__StyledButton-sc-14nc59p-1"] {
      content: "jGhkrb,"
    }

    /*!sc*/
    .edYxsk {
      -webkit-flex: 1 1 260px;
      -ms-flex: 1 1 260px;
      flex: 1 1 260px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      border-radius: 2px;
      box-shadow: 0 14px 66px rgba(0, 0, 0, .07), 0 10px 17px rgba(0, 0, 0, .03), 0 4px 7px rgba(0, 0, 0, .05);
      border: 1px solid #e5e5e5;
      margin: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1228px) {
      .edYxsk {
        -webkit-flex: 1 1 360px;
        -ms-flex: 1 1 360px;
        flex: 1 1 360px;
      }
    }

    /*!sc*/
    .edYxsk:hover {
      border-radius: 4px;
      box-shadow: 0px 8px 17px rgba(0, 0, 0, 0.15);
      background: #f2f2f2;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.02);
      -ms-transform: scale(1.02);
      transform: scale(1.02);
    }

    /*!sc*/
    data-styled.g529[id="BugBountyCards__Card-sc-14nc59p-2"] {
      content: "edYxsk,"
    }

    /*!sc*/
    .kssiHo {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      font-size: 0.875rem;
      text-transform: uppercase;
      border-top-left-radius: 1px;
      border-top-right-radius: 1px;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      border-bottom: 1px solid #e5e5e5;
      padding: 0.25rem 0rem;
    }

    /*!sc*/
    data-styled.g530[id="BugBountyCards__Label-sc-14nc59p-3"] {
      content: "kssiHo,"
    }

    /*!sc*/
    .cSXeWd {
      background: #ffe3d3;
      color: #333;
    }

    /*!sc*/
    data-styled.g531[id="BugBountyCards__LowLabel-sc-14nc59p-4"] {
      content: "cSXeWd,"
    }

    /*!sc*/
    .gOzWri {
      background: #ffab7b;
      color: #333;
    }

    /*!sc*/
    data-styled.g532[id="BugBountyCards__MediumLabel-sc-14nc59p-5"] {
      content: "gOzWri,"
    }

    /*!sc*/
    .fVDDsK {
      background: #c63333;
      color: #ffffff;
    }

    /*!sc*/
    data-styled.g533[id="BugBountyCards__HighLabel-sc-14nc59p-6"] {
      content: "fVDDsK,"
    }

    /*!sc*/
    .eGaHlB {
      background: #930000;
      color: #ffffff;
    }

    /*!sc*/
    data-styled.g534[id="BugBountyCards__CriticalLabel-sc-14nc59p-7"] {
      content: "eGaHlB,"
    }

    /*!sc*/
    .eManNj {
      font-size: 1.5rem;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      -webkit-letter-spacing: 0px;
      -moz-letter-spacing: 0px;
      -ms-letter-spacing: 0px;
      letter-spacing: 0px;
      padding: 1rem;
      text-align: left;
      margin-bottom: -0.5rem;
      margin-top: 0.5rem;
    }

    /*!sc*/
    data-styled.g535[id="BugBountyCards__H2-sc-14nc59p-8"] {
      content: "eManNj,"
    }

    /*!sc*/
    .gnJa-Dz {
      font-size: 1.25rem;
      padding: 1rem;
      padding-top: 0rem;
      padding-bottom: 0rem;
      opacity: 0.6;
    }

    /*!sc*/
    data-styled.g536[id="BugBountyCards__Description-sc-14nc59p-9"] {
      content: "gnJa-Dz,"
    }

    /*!sc*/
    .gtmKFl {
      border-bottom: 1px solid #e5e5e5;
    }

    /*!sc*/
    data-styled.g537[id="BugBountyCards__Divider-sc-14nc59p-10"] {
      content: "gtmKFl,"
    }

    /*!sc*/
    .NtwCD {
      text-transform: uppercase;
      font-size: 0.875rem;
      margin-left: 1rem;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
      opacity: 0.6;
    }

    /*!sc*/
    data-styled.g538[id="BugBountyCards__SubHeader-sc-14nc59p-11"] {
      content: "NtwCD,"
    }

    /*!sc*/
    .jcjOzJ {
      margin: 1rem;
      margin-top: 0.5rem;
    }

    /*!sc*/
    data-styled.g539[id="BugBountyCards__Text-sc-14nc59p-12"] {
      content: "jcjOzJ,"
    }

    /*!sc*/
    .kqqgIW {
      -webkit-flex: 1 1 560px;
      -ms-flex: 1 1 560px;
      flex: 1 1 560px;
      padding: 1.5rem;
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      margin: 0 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .kqqgIW {
        margin: 2rem 0;
      }
    }

    /*!sc*/
    data-styled.g540[id="BugBountyPoints__PointsExchange-sc-9pc1p-0"] {
      content: "kqqgIW,"
    }

    /*!sc*/
    .aOvnK {
      text-transform: uppercase;
      font-size: 0.875rem;
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g541[id="BugBountyPoints__PointsExchangeLabel-sc-9pc1p-1"] {
      content: "aOvnK,"
    }

    /*!sc*/
    .dYCTbn {
      font-family: "SFMono-Regular", Consolas, "Roboto Mono", "Droid Sans Mono", "Liberation Mono", Menlo, Courier, monospace;
      font-size: 1.5rem;
      font-weight: 700;
      text-transform: uppercase;
      margin-top: 0rem;
    }

    /*!sc*/
    data-styled.g542[id="BugBountyPoints__PointsExchangeTitle-sc-9pc1p-2"] {
      content: "dYCTbn,"
    }

    /*!sc*/
    .hsLHLc {
      margin-left: 0.5rem;
      fill: #666666;
    }

    /*!sc*/
    data-styled.g543[id="BugBountyPoints__InfoIcon-sc-9pc1p-3"] {
      content: "hsLHLc,"
    }

    /*!sc*/
    .gWBxYB {
      margin-bottom: 0rem;
    }

    /*!sc*/
    data-styled.g544[id="BugBountyPoints__TextNoMargin-sc-9pc1p-4"] {
      content: "gWBxYB,"
    }

    /*!sc*/
    .edrfQi {
      font-size: 1.25rem;
      margin: 0rem;
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g546[id="BugBountyPoints__TokenValue-sc-9pc1p-6"] {
      content: "edrfQi,"
    }

    /*!sc*/
    .jbLwFO {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g547[id="BugBountyPoints__Row-sc-9pc1p-7"] {
      content: "jbLwFO,"
    }

    /*!sc*/
    .hlhhKJ {
      margin-bottom: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hlhhKJ {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g548[id="BugBountyPoints__ValueRow-sc-9pc1p-8"] {
      content: "hlhhKJ,"
    }

    /*!sc*/
    .cyRDlh {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .cyRDlh {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        padding-left: 0;
        padding-right: 0;
        margin-top: -2rem;
      }
    }

    /*!sc*/
    data-styled.g549[id="bug-bounty__HeroCard-sc-lfsew9-0"] {
      content: "cyRDlh,"
    }

    /*!sc*/
    .cHwvdv {
      -webkit-flex: 1 1 50%;
      -ms-flex: 1 1 50%;
      flex: 1 1 50%;
      padding-left: 2rem;
      padding-right: 2rem;
      padding-top: 8rem;
      padding-bottom: 8rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .cHwvdv {
        padding-top: 6rem;
        padding-bottom: 4rem;
        padding-left: 0;
        padding-right: 0;
      }
    }

    /*!sc*/
    data-styled.g550[id="bug-bounty__HeroContainer-sc-lfsew9-1"] {
      content: "cHwvdv,"
    }

    /*!sc*/
    .cdtRNx {
      -webkit-flex: 1 1 50%;
      -ms-flex: 1 1 50%;
      flex: 1 1 50%;
      padding-left: 0rem;
      padding-right: 2rem;
      padding-top: 6rem;
      padding-bottom: 8rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .cdtRNx {
        padding: 0;
      }
    }

    /*!sc*/
    data-styled.g551[id="bug-bounty__LeaderboardContainer-sc-lfsew9-2"] {
      content: "cdtRNx,"
    }

    /*!sc*/
    .fTMhhC {
      text-transform: uppercase;
      font-size: 0.875rem;
      color: #333333;
      margin-bottom: 0rem;
      margin-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g552[id="bug-bounty__Title-sc-lfsew9-3"] {
      content: "fTMhhC,"
    }

    /*!sc*/
    .bvSbGR {
      font-size: 1.5rem;
      line-height: 140%;
      color: #666666;
      max-width: 480px;
      margin-top: 1rem;
    }

    /*!sc*/
    data-styled.g553[id="bug-bounty__Subtitle-sc-lfsew9-4"] {
      content: "bvSbGR,"
    }

    /*!sc*/
    .dBNcVS {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .dBNcVS {
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
      }
    }

    /*!sc*/
    data-styled.g554[id="bug-bounty__Row-sc-lfsew9-5"] {
      content: "dBNcVS,"
    }

    /*!sc*/
    .gJnxkk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gJnxkk {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g555[id="bug-bounty__ClientRow-sc-lfsew9-6"] {
      content: "gJnxkk,"
    }

    /*!sc*/
    .jKrhOR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-top: 1rem;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g556[id="bug-bounty__ButtonRow-sc-lfsew9-7"] {
      content: "jKrhOR,"
    }

    /*!sc*/
    .ctsHVE {
      -webkit-flex: 0 1 7.75rem;
      -ms-flex: 0 1 7.75rem;
      flex: 0 1 7.75rem;
      margin-right: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .ctsHVE {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g557[id="bug-bounty__StyledButton-sc-lfsew9-8"] {
      content: "ctsHVE,"
    }

    /*!sc*/
    .fyNJjH {
      margin-top: 2rem;
      margin-bottom: 3rem;
    }

    /*!sc*/
    data-styled.g558[id="bug-bounty__StyledCardContainer-sc-lfsew9-9"] {
      content: "fyNJjH,"
    }

    /*!sc*/
    .cEcLXE {
      font-size: 1.5rem;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      -webkit-letter-spacing: 0px;
      -moz-letter-spacing: 0px;
      -ms-letter-spacing: 0px;
      letter-spacing: 0px;
      text-align: left;
    }

    /*!sc*/
    data-styled.g559[id="bug-bounty__H2-sc-lfsew9-10"] {
      content: "cEcLXE,"
    }

    /*!sc*/
    .XblBh {
      -webkit-flex: 1 1 464px;
      -ms-flex: 1 1 464px;
      flex: 1 1 464px;
      margin: 1rem;
      padding: 1.5rem;
      -webkit-box-pack: start;
      -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
      justify-content: flex-start;
    }

    /*!sc*/
    data-styled.g560[id="bug-bounty__StyledCard-sc-lfsew9-11"] {
      content: "XblBh,"
    }

    /*!sc*/
    .ctmwUo {
      width: 8px;
      height: 8px;
      background: #3fb181;
      border-radius: 64px;
    }

    /*!sc*/
    data-styled.g561[id="bug-bounty__On-sc-lfsew9-12"] {
      content: "ctmwUo,"
    }

    /*!sc*/
    .fwUYlM {
      margin-bottom: 3rem;
      padding-bottom: 2rem;
    }

    /*!sc*/
    data-styled.g562[id="bug-bounty__StyledGrayContainer-sc-lfsew9-13"] {
      content: "fwUYlM,"
    }

    /*!sc*/
    .XnWND {
      margin: 2rem auto;
      padding: 0 2rem;
      max-width: 768px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g563[id="bug-bounty__FullLeaderboardContainer-sc-lfsew9-14"] {
      content: "XnWND,"
    }

    /*!sc*/
    .kXMIOZ {
      margin: 4rem;
      margin-top: 1rem;
      margin-bottom: 3rem;
    }

    /*!sc*/
    data-styled.g564[id="bug-bounty__Client-sc-lfsew9-15"] {
      content: "kXMIOZ,"
    }

    /*!sc*/
    .eujjXS {
      text-transform: uppercase;
      font-size: 0.875rem;
      color: #4c4c4c;
      font-weight: 600;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eujjXS {
        margin-top: 3rem;
      }
    }

    /*!sc*/
    data-styled.g565[id="bug-bounty__ClientIntro-sc-lfsew9-16"] {
      content: "eujjXS,"
    }

    /*!sc*/
    .fudkTE {
      margin: 0 auto;
      max-width: 768px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g566[id="bug-bounty__Rules-sc-lfsew9-17"] {
      content: "fudkTE,"
    }

    /*!sc*/
    .dxamNL {
      -webkit-flex: 1 1 600px;
      -ms-flex: 1 1 600px;
      flex: 1 1 600px;
      margin-right: 2rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .dxamNL {
        margin-right: 0;
      }
    }

    /*!sc*/
    data-styled.g567[id="bug-bounty__SubmitInstructions-sc-lfsew9-18"] {
      content: "dxamNL,"
    }

    /*!sc*/
    .fmMDKK {
      margin-bottom: 0rem;
    }

    /*!sc*/
    data-styled.g568[id="bug-bounty__TextNoMargin-sc-lfsew9-19"] {
      content: "fmMDKK,"
    }

    /*!sc*/
    .gJnclD {
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      padding: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 3rem 8rem;
      width: 80%;
    }

    /*!sc*/
    data-styled.g569[id="bug-bounty__Contact-sc-lfsew9-20"] {
      content: "gJnclD,"
    }

    /*!sc*/
  </style>
  <link as="script" rel="preload" href="/webpack-runtime-d600da28e471609bf3f3.js">
  <link as="script" rel="preload" href="/framework-4e285adfb333f1b50c05.js">
  <link as="script" rel="preload" href="/252f366e-2705b607be296edabcea.js">
  <link as="script" rel="preload" href="/ae51ba48-34d54094a2c04f215fb8.js">
  <link as="script" rel="preload" href="/1bfc9850-0f18e2d74feedfc6e426.js">
  <link as="script" rel="preload" href="/0c428ae2-2128ff22fce458b543bd.js">
  <link as="script" rel="preload" href="/0f1ac474-e8f788f62189f421a856.js">
  <link as="script" rel="preload" href="/app-b670b5ed3a389af0ed04.js">
  <link as="script" rel="preload" href="/25d596b65775ea7afe354c15642381979021d6cd-5667baf5a2a2bad6de51.js">
  <link as="script" rel="preload"
    href="/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js">
  <link as="fetch" rel="preload" href="/page-data/en/upgrades/get-involved/bug-bounty/page-data.json"
    crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1011117294.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/3003422828.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/316175914.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/446219633.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/app-data.json" crossorigin="anonymous">
</head>

<body>
  <div id="___gatsby">
    <div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper">
      <div class="SkipLink__Div-sc-1ysqk2q-0 cRWHVB"><a href="#main-content"
          class="SkipLink__Anchor-sc-1ysqk2q-1 kOmocm"><span>Skip to main content</span></a></div>
      <div class="TranslationBanner__BannerContainer-sc-cd94ib-1 jJbcq">
        <div class="TranslationBanner__StyledBanner-sc-cd94ib-2 jIVPcV">
          <div class="TranslationBanner__BannerContent-sc-cd94ib-3 jiZNpa">
            <div class="TranslationBanner__Row-sc-cd94ib-6 nChYp">
              <h3 class="TranslationBanner__H3-sc-cd94ib-0 elpFuD"><span>Help update this page</span></h3><span
                size="1.5" ml="0.5rem" mt="0" mr="0" mb="0"
                class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBanner__StyledEmoji-sc-cd94ib-8 hTWLVy undefined"><img
                  alt="ðŸŒ" src="https://twemoji.maxcdn.com/2/svg/1f30f.svg"
                  style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
            </div>
            <p><span>Thereâ€™s a new version of this page but itâ€™s only in English right now. Help us translate the
                latest version.</span></p>
            <div class="TranslationBanner__ButtonRow-sc-cd94ib-7 gXNXMi">
              <div><a
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                  href="..\..\..\contributing\translation-program\index.html"><span>Translate page</span></a></div>
              <div><a aria-current="page"
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc TranslationBanner__SecondaryButtonLink-sc-cd94ib-9 kUKdfA active"
                  href="index.html"><span>See English</span></a></div>
            </div>
          </div>
          <div class="TranslationBanner__BannerClose-sc-cd94ib-4 dOewRO"><svg stroke="currentColor" fill="currentColor"
              stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBanner__BannerCloseIcon-sc-cd94ib-5 iylOGp cEauOV"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1 eZKsbu">
        <div class="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2 cEcQwp">
          <div class="TranslationBannerLegal__BannerContent-sc-1df4kz4-3 intGem">
            <div class="TranslationBannerLegal__Row-sc-1df4kz4-6 cJRPhR">
              <h3 class="TranslationBannerLegal__H3-sc-1df4kz4-0 kIfJin"><span>No bugs here!</span><span size="1.5"
                  ml="0.5rem" mt="0" mr="0" mb="0"
                  class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8 dRuawC undefined"><img
                    alt="ðŸ›" src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                    style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
            </div>
            <p><span>This page is not being translated. We've intentionally left this page in English for now.</span>
            </p>
            <div class="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7 kXSENe"><button
                class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Don't
                  show again</span></button></div>
          </div>
          <div class="TranslationBannerLegal__BannerClose-sc-1df4kz4-4 hMvMKu"><svg stroke="currentColor"
              fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5 iylOGp bhaYvl"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="Layout__ContentContainer-sc-19910io-0 mXCTw">
        <div class="Nav__NavContainer-sc-1aprtmp-0 iGuESw">
          <nav class="Nav__StyledNav-sc-1aprtmp-1 cpomzd">
            <div class="Nav__NavContent-sc-1aprtmp-3 faUCsG"><a
                class="Link__InternalLink-sc-e3riao-1 gCWUlE Nav__HomeLogoNavLink-sc-1aprtmp-9 igUcis active"
                href="..\..\..\index.html">
                <div data-gatsby-image-wrapper="" style="width:22px;height:35px"
                  class="gatsby-image-wrapper Nav__HomeLogo-sc-1aprtmp-10 euWmfq"><img aria-hidden="true"
                    data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                    src="data:image/png;base64,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"
                    alt="">
                  <picture>
                    <source type="image/webp"
                      sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                      decoding="async" loading="lazy"
                      src="../../../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                      >
                  </picture><noscript>
                    <picture>
                      <source type="image/webp"
                       sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                        decoding="async" loading="lazy"
                        src="../../../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                        >
                    </picture>
                  </noscript>
                  <script
                    type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                </div>
              </a>
              <div class="Nav__InnerContent-sc-1aprtmp-4 gjaVMk">
                <ul class="Nav__LeftItems-sc-1aprtmp-5 jUJHKw">
                  <li aria-label="Use Ethereum menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Use Ethereum</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\get-eth\index.html"><span>Get ETH</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\run-a-node\index.html"><span>Run a node</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\staking\index.html"><span>Stake ETH</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Learn menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Learn</span><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a>
                      </li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\security\index.html"><span>Ethereum security and scam prevention</span></a>
                      </li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\history\index.html"><span>History of Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\index.html"><span>Ethereum upgrades</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk"
                            height="12px" width="12px" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                            </path>
                          </svg></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\governance\index.html"><span>Ethereum governance</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a>
                      </li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\web3\index.html"><span>What is Web3?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\learn\index.html"><span>Community guides and resources</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Developers&#x27; Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span
                      tabindex="0" class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Developers</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\developers\index.html"><span>Developers' home</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="/en/developers/docs/"><span>Documentation</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\developers\local-environment\index.html"><span>Set up local
                            environment</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Enterprise Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Enterprise</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Community Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Community</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\index.html"><span>Community hub</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\online\index.html"><span>Online communities</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\events\index.html"><span>Ethereum events</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\get-involved\index.html"><span>Get involved</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\grants\index.html"><span>Grants</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\support\index.html"><span>Ethereum support</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\..\..\community\language-resources\index.html"><span>Language resources</span></a>
                      </li>
                    </ul>
                  </li>
                </ul>
                <div class="Nav__RightItems-sc-1aprtmp-6 kQWBtS">
                  <div class="Search__Root-sc-1qm8xwy-0 kNenpg">
                    <form class="Input__Form-sc-1utkal6-0 eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput-sc-1utkal6-1 kkfPkW">
                      <p class="Input__SearchSlash-sc-1utkal6-3 ggVPUc">/</p><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Input__SearchIcon-sc-1utkal6-2 iylOGp gFzMVg" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper-sc-1qm8xwy-1 eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div><button aria-label="Switch to Dark Theme"
                    class="NakedButton-sc-1g43w8v-0 Nav__ThemeToggle-sc-1aprtmp-12 dUatah hwxIMf"><svg
                      stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH" height="24"
                      width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg></button><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE SharedStyledComponents__NavLink-sc-1cr9zfr-11 Nav__RightNavLink-sc-1aprtmp-8 jEZlpP jODkFW"
                    href="..\..\..\languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH"
                      height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                      </path>
                    </svg><span class="Nav__Span-sc-1aprtmp-11 bDRFLa"><span>Languages</span></span></a>
                </div>
              </div>
              <div class="Mobile__Container hhdXUp"><button aria-label="Toggle search button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg stroke="currentColor" fill="currentColor"
                    stroke-width="0" viewbox="0 0 24 24"
                    class="Icon__StyledIcon Mobile__MenuIcon Mobile__OtherIcon iylOGp dUGGTH hvwyGc" height="24"
                    width="24" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                    </path>
                  </svg></button><button aria-label="Toggle menu button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg viewbox="0 0 24 40"
                    class="Mobile__GlyphButton gbspKa">
                    <path d="M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0"></path>
                  </svg></button>
                <div class="Mobile__MobileModal bCHBHX" style="display:none;opacity:0"></div>
                <div aria-hidden="true" class="Mobile__MenuContainer AJukL"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <ul class="Mobile__MenuItems gYetwr">
                    <li aria-label="Select Use Ethereum" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Use Ethereum</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\get-eth\index.html"><span>Get ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\run-a-node\index.html"><span>Run a node</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\staking\index.html"><span>Stake ETH</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Learn" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Learn</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\history\index.html"><span>History of Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE is-glossary SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                              fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                              class="Icon__StyledIcon Link__GlossaryIcon iylOGp jfMIWk" height="12px" width="12px"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                              </path>
                            </svg></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\governance\index.html"><span>Ethereum governance</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\web3\index.html"><span>What is Web3?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\learn\index.html"><span>Community guides and resources</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Developers" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Developers</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\developers\index.html"><span>Developers' home</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\/en/developers/docs/"><span>Documentation</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                        </li>
                      </ul>
                    </li>
                    <li aria-label="Select Enterprise" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Enterprise</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Community" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Community</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\index.html"><span>Community hub</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\online\index.html"><span>Online communities</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\events\index.html"><span>Ethereum events</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\get-involved\index.html"><span>Get involved</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\grants\index.html"><span>Grants</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\support\index.html"><span>Ethereum support</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\..\..\community\language-resources\index.html"><span>Language resources</span></a></li>
                      </ul>
                    </li>
                  </ul>
                </div>
                <div aria-hidden="true" class="Mobile__BottomMenu iYttIj"
                  style="transform:translateX(-100%) translateZ(0)">
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Search</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Light</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><a class="Link__InternalLink gCWUlE Mobile__BottomLink8 heSUpS"
                      href="languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                        viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                        </path>
                      </svg>
                      <div class="Mobile__BottomItemText hkZTkJ"><span>Languages</span></div>
                    </a></div>
                </div>
                <div class="Mobile__MenuContainer Mobile__SearchContainer AJukL gBSEi"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <h3 class="Mobile__SearchHeader iXlChz"><span>Search</span><span
                      class="Mobile__CloseIconContainer jmriUx"><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon iylOGp" height="24" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                        </path>
                      </svg></span></h3>
                  <div class="Search__Root kNenpg">
                    <form class="Input__Form eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput kkfPkW">
                      <p class="Input__SearchSlash ggVPUc">/</p><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon Input__SearchIcon iylOGp gFzMVg"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div>
                  <div class="Mobile__BlankSearchState jBipln"><span size="3" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji hLjau undefined"><img alt="â›µ"
                        src="https://twemoji.maxcdn.com/2/svg/26f5.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Search
                      away!</span></div>
                </div>
              </div>
            </div>
          </nav>
        </div>
        <div id="main-content"></div>
        <div class="Layout__MainContainer-sc-19910io-1 gqazVg">
          <div class="Layout__MainContent-sc-19910io-2 kCJhKM">
            <main class="Layout__Main-sc-19910io-3 dliKfQ">
              <div class="SharedStyledComponents__Page-sc-1cr9zfr-0 kCvjty">
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="bug-bounty__HeroCard-sc-lfsew9-0 cyRDlh">
                    <div class="bug-bounty__HeroContainer-sc-lfsew9-1 cHwvdv">
                      <ul class="Breadcrumbs__List-sc-1hkiaxl-1 bBESuw" dir="auto">
                        <li class="Breadcrumbs__ListItem-sc-1hkiaxl-2 krvHSI">
                          <h4 class="Breadcrumbs__Crumb-sc-1hkiaxl-0 llMzWl"><a
                              class="Link__ExplicitLangInternalLink-sc-e3riao-2 iEXhBV Breadcrumbs__CrumbLink-sc-1hkiaxl-4 hXgzJL"
                              href="..\..\index.html">UPGRADES</a><span
                              class="Breadcrumbs__Slash-sc-1hkiaxl-3 iAwOZI">/</span></h4>
                        </li>
                        <li class="Breadcrumbs__ListItem-sc-1hkiaxl-2 krvHSI">
                          <h4 class="Breadcrumbs__Crumb-sc-1hkiaxl-0 llMzWl"><a
                              class="Link__ExplicitLangInternalLink-sc-e3riao-2 iEXhBV Breadcrumbs__CrumbLink-sc-1hkiaxl-4 hXgzJL"
                              href="..\index.html">GET INVOLVED</a><span
                              class="Breadcrumbs__Slash-sc-1hkiaxl-3 iAwOZI">/</span></h4>
                        </li>
                        <li class="Breadcrumbs__ListItem-sc-1hkiaxl-2 krvHSI">
                          <h4 class="Breadcrumbs__Crumb-sc-1hkiaxl-0 llMzWl"><a aria-current="page"
                              class="Link__ExplicitLangInternalLink-sc-e3riao-2 iEXhBV Breadcrumbs__CrumbLink-sc-1hkiaxl-4 hXgzJL active"
                              href="index.html">BUG BOUNTY</a></h4>
                        </li>
                      </ul>
                      <div class="bug-bounty__Row-sc-lfsew9-5 dBNcVS">
                        <div class="bug-bounty__On-sc-lfsew9-12 ctmwUo"></div>
                        <p class="bug-bounty__Title-sc-lfsew9-3 fTMhhC"><span>Open for submissions</span></p>
                      </div>
                      <div class="SharedStyledComponents__SloganGradient-sc-1cr9zfr-10 oxZAg"><span>Consensus layer bug
                          bounties</span> <span size="1" mt="0" mr="0" mb="0" ml="0"
                          class="Emoji__StyledEmoji-sc-ihpuqw-0 RDZme undefined"><img alt="ðŸ›"
                            src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                            style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></div>
                      <div class="bug-bounty__Subtitle-sc-lfsew9-4 bvSbGR"><span>Earn up to $50,000 USD and a place on
                          the leaderboard by finding consensus layer protocol and client bugs.</span></div>
                      <div class="bug-bounty__ButtonRow-sc-lfsew9-7 jKrhOR"><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv bug-bounty__StyledButton-sc-lfsew9-8 ctsHVE"
                          href="https://forms.gle/Gnh4gzGh66Yc3V7G8" target="_blank"
                          rel="noopener noreferrer"><span>Submit a bug</span></a><a
                          class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc bug-bounty__StyledButton-sc-lfsew9-8 ctsHVE"
                          href="#rules"><span>Read rules</span></a></div>
                    </div>
                    <div class="bug-bounty__LeaderboardContainer-sc-lfsew9-2 cdtRNx">
                      <div class="Leaderboard__Table-sc-9p0qku-0 gGjywO"><a
                          class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                          href="https://github.com/protolambda" target="_blank" rel="noopener noreferrer">
                          <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">1</div><img
                            src="https://github.com/protolambda.png?size=40"
                            class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                          <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                            <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">protolambda</div>
                            <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">42400
                              <!-- --> <span>points</span>
                            </div>
                          </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ†"
                              src="https://twemoji.maxcdn.com/2/svg/1f3c6.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                        </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                          href="https://github.com/cryptosubtlety" target="_blank" rel="noopener noreferrer">
                          <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">2</div><img
                            src="https://github.com/cryptosubtlety.png?size=40"
                            class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                          <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                            <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Quan Thoi Minh Nguyen</div>
                            <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">19650
                              <!-- --> <span>points</span>
                            </div>
                          </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ¥ˆ"
                              src="https://twemoji.maxcdn.com/2/svg/1f948.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                        </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                          href="https://github.com/guidovranken" target="_blank" rel="noopener noreferrer">
                          <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">3</div><img
                            src="https://github.com/guidovranken.png?size=40"
                            class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                          <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                            <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Guido Vranken</div>
                            <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">16600
                              <!-- --> <span>points</span>
                            </div>
                          </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ¥‰"
                              src="https://twemoji.maxcdn.com/2/svg/1f949.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                        </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                          href="https://github.com/jrhea" target="_blank" rel="noopener noreferrer">
                          <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">4</div><img
                            src="https://github.com/jrhea.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                          <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                            <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Jonny Rhea</div>
                            <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">15500
                              <!-- --> <span>points</span>
                            </div>
                          </div>
                        </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                          href="https://github.com/sifraitech" target="_blank" rel="noopener noreferrer">
                          <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">5</div><img
                            src="https://github.com/sifraitech.png?size=40"
                            class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                          <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                            <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Grandine team</div>
                            <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">9000
                              <!-- --> <span>points</span>
                            </div>
                          </div>
                        </a></div><a
                        class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc"
                        href="#leaderboard"><span>See full leaderboard</span></a>
                    </div>
                  </div>
                </div>
                <p class="bug-bounty__ClientIntro-sc-lfsew9-16 eujjXS"><span>Clients featured in the bounties</span></p>
                <div class="bug-bounty__ClientRow-sc-lfsew9-6 gJnxkk">
                  <div data-gatsby-image-wrapper="" style="width:60px;height:51px"
                    class="gatsby-image-wrapper bug-bounty__Client-sc-lfsew9-15 kXMIOZ"><img aria-hidden="true"
                      data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAARCAYAAADdRIy+AAAACXBIWXMAABYlAAAWJQFJUiTwAAADUUlEQVQ4y42UW2wUVRjH/zMLBbNCEasYAkQqhEhISMRoqiKJjYGAbwqJIZEEwgP6Ag8mJsWYQEARBewNotJtl15sFMqlRS69bSm7oSRIeSGSiC0PsG1Imto42+7MOX+/c3a20AcDJ/nnOzPznd/5f+cywP81Mi83jJtFK2xfa2fy+1O1fPKjgc+JMqLmxydxAmLpOWLZBeKV06IW/UR3EYGaWC5iqPfCySJOVmBnFRa30H39nOdy+OBTlKr1KpGWfgAtLzT7QDsJFnYw8mbrvw4aJE/EN3ZNZZlxXSFwI5kv97J1prUvUaRN/7PpGYHule+1REE9S2c38lucYsFUoIxLihZIOXaNyI9DWCBRS4KySdTp6EO1yD3O94uaebm0Y4SxP3s4NvDTR5OwvyXvnjgsEWfFOWezRXfDdQtyYAvXxuWzgxza2J5m490u+jePkV9Xkutr2ieB2z0PK5XCedmIERk3ovWhEm3Ly7q5qEOFUPJa/0ly92GqNbH0xLz6vaowvhhLWoh5fcS2G6O4M6ZcSlU+uVzysxdy7rRjyn0kusoC9bt96Szn1H4xFGmaS8TAojqguIl4uY14q1VFfvTo/uoRQ+PjLf8EAbNB4K+ViCDQbi7avpHj+8puELnhnYuDKDuSnMHlcUcOIyMlpzzH/UbWbYyIZvnJC4MDfD6RUC+mUrowmdRIJjlFV68SqVTgJHo4LX5ioHjzjmfw1ZfA0WoHM5sF9B0x42e+PbNVbkKGZmaF/n6F9naiu1vOUpcRQ2n7rrubqI1NoPwHCuiwCKiqdBH9hR/Maeb5DZce8tP7txnlhL0RUqJyrl8P0NlJ9PRoJBI5GdCVKxpNjT4qyonqKgqoU+I0icCHHenUiTsJ+v2y9fsqeHPr6eGiYa/XQB3PM+X51lUiQQvr7Q1w8jcVwkZEO0URVFVBohy32wfWcb+codU1D8bn1+8j9iy0NyQINkn8C6OjxiHFqS8wH2fOEJUVxlWdlLnAQoyzPPAPdBRkChu2ZKINL6lZcRBl5lDnrl36QVTifqTTE1Im0dZm1uuGuCvFoe9h1y1fqpF1iM/BgjpoNw71Wm2Erx538n+Tx34UK3Dr1u+I1ZTZ54MHgPIjrgAcVIcgA5T2H1ocwRFgGolUAAAAAElFTkSuQmCC"
                      alt="">
                    <picture>
                      <source type="image/webp"
                        sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                        decoding="async" loading="lazy"
                        src="../../../../static/427c302494c2bbb4b3723df5ef02694f/7b4ee/prysm.png"
                       >
                    </picture><noscript>
                      <picture>
                        <source type="image/webp"
                          sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                          decoding="async" loading="lazy" src="../../../../static/427c302494c2bbb4b3723df5ef02694f/7b4ee/prysm.png"
                         >
                      </picture>
                    </noscript>
                    <script
                      type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                  </div>
                  <div data-gatsby-image-wrapper="" style="width:60px;height:60px"
                    class="gatsby-image-wrapper bug-bounty__Client-sc-lfsew9-15 kXMIOZ"><img aria-hidden="true"
                      data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAACnElEQVQ4y72Tz2sTURDHZzdJoYimFhXd5O2+3WxT01iwGmkFbdOkUduFJmkiPdQmerAp/mitVoVGBUF79+ZVRC9K74IePPkneBEE0UMvUlARvKzz3s5uW6T+yMEHX+a94c1nZ2fmAbS4pmu1QLVaHW1d2pbWvfvLcHpyEsoTFZhpNJRcPq+MjjkwnMu1Bmw2b8OFi5egMTurnJ9pQLFchsKJk8qx44P/Dru2eB2ePV+Bufkr6uW5eaifPXe0WCrp48USjBQKyqbLSc2EhE5iQhwsZiqWjmLrGnfGVdd1ARVB6If+gYEng0NZGB7OqQHMEsEE8iBcpb30b7RiVWT9ZmtD2ayradpaTzq9P9ndDZuBPkwGc5FhO+41BMXQxqRFJa0uhiE7l5q3eoul8hvbtp8ezmR2pHt7CaYZPlAh24bZLaPe4/4bQjwxqa9Ykh947yPVdCk/MjKRLxQAs/V+2TC4BCYMC2zdEsAVlIsQF/1uwpfOgzPatV3Rzm03Fm/CoSMZiHZ0wMG+Pi9Dk0tgyMuOV2WQB3uAyiIgg/YO+T6jTqH6e5KpsIhncaaIJkXa2ggYN0Rn1YQsOn9IgS/o7Msh/yqoEemL79kHDg70VG0aKtUqjDnOekNsw1JRYv+YAh/RCLWLBuH5DPk/mczYbsR0GcfjXv03LVPTxUiEvLHgC5askfkF7Shl2Yl6TcCXtpGApNn1KygAel+jDvMo6h0Ff0e9Et2ms1CR7oW03Xu3AMb9seEq/d4BtG+D5sjuylFZoDIoIia2FVDWUYA8qQTHoeZTeL6L9ioq5Y2WKAEmIJMwfgMUb1e8Dp0yZZxei/+mN/hJf1zBZT2wIQSGERgWNRZwm5l/B9u4GGPSplIpfEUG6Lou5fv/6/oJ0u3EeBVVcJAAAAAASUVORK5CYII="
                      alt="">
                    <picture>
                      <source type="image/webp"
                        sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                        decoding="async" loading="lazy"
                        src="../../../../static/1baf0a92d1f6c94d4438bd3c45fe85e4/43a9d/lighthouse-light.png"
                        >
                    </picture><noscript>
                      <picture>
                        <source type="image/webp"
                         sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                          decoding="async" loading="lazy"
                          src="../../../../static/1baf0a92d1f6c94d4438bd3c45fe85e4/43a9d/lighthouse-light.png"
                         >
                      </picture>
                    </noscript>
                    <script
                      type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                  </div>
                  <div data-gatsby-image-wrapper="" style="width:60px;height:62px"
                    class="gatsby-image-wrapper bug-bounty__Client-sc-lfsew9-15 kXMIOZ"><img aria-hidden="true"
                      data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAVCAYAAABG1c6oAAAACXBIWXMAAAsTAAALEwEAmpwYAAACI0lEQVQ4y43VS0hVURTG8X0fYToJyklBTSKJkKjQjCY9BhGRRaWTRkFBE0PBTHtOempNgqiJIFqglxCJEpRKkAoUSpIm4kTDSFF7ImlEj/+O78Rie9QW/DiXu89eZz/W2cc555JI6ZpGws2OZoxhfUxbOsgRGytxBG34hN/Gc5zE2rk6b8c+FOIi+vAT0+jGcZSiHMXI4IOSD6AOW3AARS54uteObW7hyEdT0HfGaUTj6MB7fMQQnqIWu7AbR7FZ032AQY10FI/xGS99wmrc01OXYSsq0YphTd+Owid5ggvYiVz1fYbLTg3N80xtKe5jZIGleIEb/sd5k3CRKYFU0CHL/E4F5RIlvB6N8K5JmDI1lQzqMmnqNUqaNlP+m/AsHrn/i8Q8bX3aRFemxfa72496VKFT9emCka7WPRW4hVeYVI5z/obT6MIe3FYx/9ANXcGa+bhpdrwHd1CCt7gSrWFTMPxCddgbrJ2PTWqrDvq8jqbsd7lFf2abUpnCOpPQxpR52GJd3+BaNMIWs8s+Vuhd3hiTMFttJea0iRL699rVoFF/5ui6HN+wISZhlhIeCkbop3zJmY2wkatOBWbkafNQmzCKIe3+vx3rxRkdRWvwFavmqLkv2I8lOIyHJo9r0Gnh35Z3+IXvaszopPEbcAx5OKG2UZ04vVq7TrN0s07rg7iqop0xT5/QSXMKO3Q6xb5OiZjDwJlFz+ibkh/Tbr9FiT9VQZE+k1HciQAAAABJRU5ErkJggg=="
                      alt="">
                    <picture>
                      <source type="image/webp" sizes="60px">
                      <img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px" decoding="async"
                        loading="lazy" src="../../../../static/0d207e4ed25a6b9953122b0c293b22c2/f34cf/teku-dark.png">
                    </picture><noscript>
                      <picture>
                        <source type="image/webp"
                          sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                          decoding="async" loading="lazy"
                          src="../../../../static/0d207e4ed25a6b9953122b0c293b22c2/f34cf/teku-dark.png">
                      </picture>
                    </noscript>
                    <script
                      type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                  </div>
                  <div data-gatsby-image-wrapper="" style="width:60px;height:37px"
                    class="gatsby-image-wrapper bug-bounty__Client-sc-lfsew9-15 kXMIOZ"><img aria-hidden="true"
                      data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAMCAYAAABiDJ37AAAACXBIWXMAAB2HAAAdhwGP5fFlAAACfElEQVQoz42SX0hTcRTHz3anDQzLKCSJ/g0jFBMK6qEw9pYvBj0FPZVELxFCRX8fiqjoISnT/XFzObcm6SqJIiFXPSQFku0hCwqs0FEPLsjUbXf3nm/n3q1tiA89HM7v3t/vfM73fH8/imdA6F1P8K8l7t9J6C5X4LMr2Ui9DWeI0i/Pr+GeqkvsLR+Di6Zwh+LwVbTPvXXV4KrsP3Aq2EMEIkokEkQc2kAIbyFEGigFGEBChxzoJFIfH9jK3RWT7LZAQMg82o/Z0XZowY2Am76nh4824qKcizZZ1fu7KDE9LYU3JCLbCKFGmgAsHFx3kAMrz2aHWnawb/lrUQUBptFFmt67SZ8fadNmPo+l1cEmwGP7lExiBYa7SI82089vX0XylzjhNtlwWtT2VHXAUOMWiMemm9lFeg5KLFBGO3E2sh3Jj69U9leCg5sP4brU9jkU9UmrqPOWKbhbTdqLk1XsUVJmsYsy+cwGyISZawvES8Yt4t/jD7PpoRZIk5u4LJxrZONwg0U8s5N+r66OfRVDpiI3GcpKQSj9Nv3sFJXRfZoWrpc95Y02sHc3akjGdhqXUlvH3mWzchhSrOcz8qBi5MZGXikL1ASzrxLw2sVP62DqXb+dxPg+4wZFeord1n+FzO5FwHwUxveUsVGnRp3zvz7EpvRAtYDLr5BsTqgDTuhBhy7PhfNQs8gYdckoeJtTuvC8bSY1fOyP/H9P0uXpwrNWJCdiGfavAlylfhUhvCgXfHWZ02VFMeQeRkkPOZqNMedip7AwcsEcg3PPpejlEsElubAOrD5COCG3E6o9LF0mpWuGPdYs/iPknGqErDPsLfshb/gcjhP9BcUQRoe/MSCeAAAAAElFTkSuQmCC"
                      alt="">
                    <picture>
                      <source type="image/webp"
                       sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                        decoding="async" loading="lazy"
                        src="../../../../static/e096b8b1a6cb2933981f378b8b6d88d9/244f4/nimbus-cloud.png"
                        >
                    </picture><noscript>
                      <picture>
                        <source type="image/webp"
                          sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                          decoding="async" loading="lazy"
                          src="../../../../static/e096b8b1a6cb2933981f378b8b6d88d9/244f4/nimbus-cloud.png"
                         >
                      </picture>
                    </noscript>
                    <script
                      type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                  </div>
                  <div data-gatsby-image-wrapper="" style="width:60px;height:59px"
                    class="gatsby-image-wrapper bug-bounty__Client-sc-lfsew9-15 kXMIOZ"><img aria-hidden="true"
                      data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEuElEQVQ4y31UC1CUVRQ+y5Iz5DQ2xSAzPHaX3QWBBRZYFljY9y4xOmmWBCY+UtHRYhidnEwxKMaxd2lIKEhpU2hSPsoBE+0hoqLig5eAgoC84rVqEC3E6Zx/m9TpcWd2/v3vf893vu8751wAWuEhATA4YIM3cyPBmKiEmvMG8WMevrDyxVBATHXLWK4SnaxMpP8lwvf/XSXFWliaHgJBcinsL40T8x4HA3h7J9sDNYjzQeorgQs1BvdYjRxeWRcOV68YYZZC+u+AiAi5W9Rifu79VMtb03Z8pMlsaTZ39/fb8PCh+CIC9yHWwvnPSrSU1BN8ZvrDqhUqCPCX/BNwfVa48NyaF5lO2ZuGBm14vcmEP/6Q6Bz4xYY3Ws0juwpjXuVkH2/X0Nk80eaNajeO4RUTKX8YcPnS0NkkqWZgwIbtbRZsbDQ5b94w488/JWJHh2WqqdGEDHzporFp08aI5xBzIHVBMMXOES+YHyw6UBrHXkNokIzIP+4n+6YsfpwZEbvfu7ut2NdrxVOVifveeSsqs/aS0XHqZCLecdidk5NPIX+nRBXkbxQBstewbWsUWZbhYkjmKm53WUfOnTVMjY0l4dlqfRVVV8/Ms19Tr6Ukw/fu2bGlxTx55FD8H5RgoqfHil1d1qmyg/E7Aby8Ml8KI4ZzXYASH4mcZDrablqE7BaDMvr1zWrIygx7llhia4sZr101Tt69aydmerZA+NVdM00MktdXLhuHXkgNyaj8PgEokQhkvhLFrXaLg6qKPQSY90akIX+HBt5/N3oeJyDGTg7c/2XceWIj/eC96BQC6xoasnGiicu1Riw/puv/uygyP4lCYEjF6CUpzzw9K4KrTk2exlKPlyeMV53WIyXtL/0iLputQJwAkp9D+2OUEMnjA4h7aL/AJZn8GCIPkSqNxbu1BRxkNiiDm6+bnewrPSfJQ3Q47NhQb7pN7BcjLgLPGX6Sl9eEpREvd+5HnjigEVOS9in2kDKNj4wIQZ0kfRHAk4GVJxL2sjwaTSR5v3XcsuAwvROzcyuWhcYSc/B6wg9Wr1SJsjepBdXiT3bGZNPBUTae/BF6kHuS+u4EWRIdGy3XEVgVW9BLLUWVd/b1WbG/z4bfHdWVRoUHKHlq9hRpRQJiQ4OJ6cu+PaL7nA/xYQoa7+q0CEHHKxLyATykG9aHp1Eh2i5eMCC1jxMxeaKxwYQF+ZpaxELysIbh3KG11SxWyqS0kQLpC0MM1Wf0p1kWyyMAgU1np+VOcZF2AzGq5vf2douTWDv53JkqfYurWOgaPcTZQFcUzebzbq4mbeMqL6mvN7UND9uxudk8xUzYX26lujrTBEtnW+hmOrZscaiGe5daSwQBEimY9HzHzRDkv70t6sGbZzpdb7nUVr8yMBXNybP+V1Gq1q5WJSFmClfa0cM6IjLv/iUhJ+CcbDXodQqYPs0HDn7luhup4cHjER8Zge2rKE9A6r066oAUxG7YQqwQs9youm5fl8XfH78HV4QqgD4k88CD3RwoIrnuOq2C9haCRi3nvni0aFeMYNX2DzWUdCZ4e/rDmlUqgdR/LotRKQSNjibBuqxw8neJm1nvuv5pYsQ0EDB3ThDsLox5COhPHsTQLtB6eZ0AAAAASUVORK5CYII="
                      alt="">
                    <picture>
                      <source type="image/webp"
                        sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                        decoding="async" loading="lazy"
                        src="../../../../static/3cbd05955da968243fb9c38f275e9de6/1461c/lodestar.png"
                        >
                    </picture><noscript>
                      <picture>
                        <source type="image/webp"
                         sizes="60px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="60px"
                          decoding="async" loading="lazy"
                          src="../../../../static/3cbd05955da968243fb9c38f275e9de6/1461c/lodestar.png"
                          >
                      </picture>
                    </noscript>
                    <script
                      type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                  </div>
                </div>
                <div id="rules"
                  class="SharedStyledComponents__GrayContainer-sc-1cr9zfr-8 bug-bounty__StyledGrayContainer-sc-lfsew9-13 iRRQao fwUYlM">
                  <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                    <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Valid bugs</span></h2>
                    <p><span>This bug bounty program is focused on finding bugs in the core consensus layer Beacon Chain
                        specification and the Lighthouse, Nimbus, Teku, Prysm, and Lodestar client
                        implementations.</span></p>
                    <div
                      class="SharedStyledComponents__CardContainer-sc-1cr9zfr-14 bug-bounty__StyledCardContainer-sc-lfsew9-9 eOjDVk fyNJjH">
                      <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ bug-bounty__StyledCard-sc-lfsew9-11 XblBh">
                        <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="ðŸ“’"
                              src="https://twemoji.maxcdn.com/2/svg/1f4d2.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                          <h3>The Beacon Chain specification bugs</h3>
                          <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">The Beacon Chain specification details the
                            design rationale and proposed changes to Ethereum via the Beacon Chain upgrade.</p>
                        </div><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                          href="https://github.com/ethereum/consensus-specs" target="_blank"
                          rel="noopener noreferrer"><span>Read the full spec</span></a><br>
                        <div>
                          <p><span>It might be helpful to check out the following annotations:</span></p>
                          <ul>
                            <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                                href="https://benjaminion.xyz/eth2-annotated-spec/" target="_blank"
                                rel="noopener noreferrer">Ben Edgington&#x27;s
                                <!-- --> <span>annotated spec</span>
                              </a></li>
                            <li><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                                href="https://github.com/ethereum/annotated-spec" target="_blank"
                                rel="noopener noreferrer">Vitalik Buterin&#x27;s
                                <!-- --> <span>annotated spec</span>
                              </a></li>
                          </ul>
                        </div>
                        <div>
                          <h4><span>Types of bugs</span></h4>
                          <ul>
                            <li><span>Safety/finality-breaking bugs</span></li>
                            <li><span>Denial of service (DOS) vectors</span></li>
                            <li><span>Inconsistencies in assumptions, like situations where honest validators can be
                                slashed</span></li>
                            <li><span>Calculation or parameter inconsistencies</span></li>
                          </ul>
                        </div>
                        <div>
                          <h4><span>Specification documents</span></h4>
                          <div class="CardList__Table-sc-ablrq5-0 dDgpBS"><a
                              class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://github.com/ethereum/consensus-specs/blob/dev/specs/phase0/beacon-chain.md"
                              target="_blank" rel="noopener noreferrer">
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG"><span>Beacon Chain</span></div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://github.com/ethereum/consensus-specs/blob/dev/specs/phase0/fork-choice.md"
                              target="_blank" rel="noopener noreferrer">
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG"><span>Fork choice</span></div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://github.com/ethereum/consensus-specs/blob/dev/specs/phase0/deposit-contract.md"
                              target="_blank" rel="noopener noreferrer">
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG"><span>Solidity deposit
                                    contract</span></div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://github.com/ethereum/consensus-specs/blob/dev/specs/phase0/p2p-interface.md"
                              target="_blank" rel="noopener noreferrer">
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG"><span>Peer-to-peer networking</span>
                                </div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a></div>
                        </div>
                      </div>
                      <div class="Card__StyledCard-sc-1x2vwsh-0 fOWxWQ bug-bounty__StyledCard-sc-lfsew9-11 XblBh">
                        <div class="Card__TopContent-sc-1x2vwsh-2 fHxdyy"><span size="3" mt="0" mr="0" mb="0" ml="0"
                            class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="ðŸ’»"
                              src="https://twemoji.maxcdn.com/2/svg/1f4bb.svg"
                              style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                          <h3>Consensus layer client bugs</h3>
                          <p class="Card__Description-sc-1x2vwsh-1 dlQPfD">The clients will run the Beacon Chain once
                            the upgrade has been deployed. Clients will need to follow the logic set out in the
                            specification and be secure against potential attacks. The bugs we want to find are related
                            to the implementation of the protocol.</p>
                        </div>
                        <div>
                          <p><span>Currently Lighthouse, Nimbus, Teku, and Prysm bugs are eligible for the full bounty
                              rewards. Lodestar is also eligible, but until further audits have been completed the
                              points and rewards are limited to 10% (max payout is 5,000 DAI). More clients may be added
                              as they complete audits and become production ready.</span></p>
                          <h4><span>Types of bugs</span></h4>
                          <ul>
                            <li><span>Spec non-compliance issues</span></li>
                            <li><span>Unexpected crashes or denial of service (DOS) vulnerabilities</span></li>
                            <li> <span>Any issues causing irreparable consensus splits from the rest of the
                                network</span></li>
                          </ul>
                        </div>
                        <div>
                          <h4><span>Helpful links</span></h4>
                          <div class="CardList__Table-sc-ablrq5-0 dDgpBS"><a
                              class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://prylabs.net/" target="_blank" rel="noopener noreferrer">
                              <div data-gatsby-image-wrapper="" style="width:24px;height:20px"
                                class="gatsby-image-wrapper CardList__Image-sc-ablrq5-7 jzdati"><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAARCAYAAADdRIy+AAAACXBIWXMAABYlAAAWJQFJUiTwAAADUUlEQVQ4y42UW2wUVRjH/zMLBbNCEasYAkQqhEhISMRoqiKJjYGAbwqJIZEEwgP6Ag8mJsWYQEARBewNotJtl15sFMqlRS69bSm7oSRIeSGSiC0PsG1Imto42+7MOX+/c3a20AcDJ/nnOzPznd/5f+cywP81Mi83jJtFK2xfa2fy+1O1fPKjgc+JMqLmxydxAmLpOWLZBeKV06IW/UR3EYGaWC5iqPfCySJOVmBnFRa30H39nOdy+OBTlKr1KpGWfgAtLzT7QDsJFnYw8mbrvw4aJE/EN3ZNZZlxXSFwI5kv97J1prUvUaRN/7PpGYHule+1REE9S2c38lucYsFUoIxLihZIOXaNyI9DWCBRS4KySdTp6EO1yD3O94uaebm0Y4SxP3s4NvDTR5OwvyXvnjgsEWfFOWezRXfDdQtyYAvXxuWzgxza2J5m490u+jePkV9Xkutr2ieB2z0PK5XCedmIERk3ovWhEm3Ly7q5qEOFUPJa/0ly92GqNbH0xLz6vaowvhhLWoh5fcS2G6O4M6ZcSlU+uVzysxdy7rRjyn0kusoC9bt96Szn1H4xFGmaS8TAojqguIl4uY14q1VFfvTo/uoRQ+PjLf8EAbNB4K+ViCDQbi7avpHj+8puELnhnYuDKDuSnMHlcUcOIyMlpzzH/UbWbYyIZvnJC4MDfD6RUC+mUrowmdRIJjlFV68SqVTgJHo4LX5ioHjzjmfw1ZfA0WoHM5sF9B0x42e+PbNVbkKGZmaF/n6F9naiu1vOUpcRQ2n7rrubqI1NoPwHCuiwCKiqdBH9hR/Maeb5DZce8tP7txnlhL0RUqJyrl8P0NlJ9PRoJBI5GdCVKxpNjT4qyonqKgqoU+I0icCHHenUiTsJ+v2y9fsqeHPr6eGiYa/XQB3PM+X51lUiQQvr7Q1w8jcVwkZEO0URVFVBohy32wfWcb+codU1D8bn1+8j9iy0NyQINkn8C6OjxiHFqS8wH2fOEJUVxlWdlLnAQoyzPPAPdBRkChu2ZKINL6lZcRBl5lDnrl36QVTifqTTE1Im0dZm1uuGuCvFoe9h1y1fqpF1iM/BgjpoNw71Wm2Erx538n+Tx34UK3Dr1u+I1ZTZ54MHgPIjrgAcVIcgA5T2H1ocwRFgGolUAAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                   sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                    sizes="24px" decoding="async" loading="lazy"
                                    src="../../../../static/427c302494c2bbb4b3723df5ef02694f/57f7f/prysm.png"
                                    >
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                     sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                      sizes="24px" decoding="async" loading="lazy"
                                      src="../../../../static/427c302494c2bbb4b3723df5ef02694f/57f7f/prysm.png"
                                      >
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG">Prysm</div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://lighthouse-book.sigmaprime.io/" target="_blank" rel="noopener noreferrer">
                              <div data-gatsby-image-wrapper="" style="width:24px;height:24px"
                                class="gatsby-image-wrapper CardList__Image-sc-ablrq5-7 jzdati"><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAACnElEQVQ4y72Tz2sTURDHZzdJoYimFhXd5O2+3WxT01iwGmkFbdOkUduFJmkiPdQmerAp/mitVoVGBUF79+ZVRC9K74IePPkneBEE0UMvUlARvKzz3s5uW6T+yMEHX+a94c1nZ2fmAbS4pmu1QLVaHW1d2pbWvfvLcHpyEsoTFZhpNJRcPq+MjjkwnMu1Bmw2b8OFi5egMTurnJ9pQLFchsKJk8qx44P/Dru2eB2ePV+Bufkr6uW5eaifPXe0WCrp48USjBQKyqbLSc2EhE5iQhwsZiqWjmLrGnfGVdd1ARVB6If+gYEng0NZGB7OqQHMEsEE8iBcpb30b7RiVWT9ZmtD2ayradpaTzq9P9ndDZuBPkwGc5FhO+41BMXQxqRFJa0uhiE7l5q3eoul8hvbtp8ezmR2pHt7CaYZPlAh24bZLaPe4/4bQjwxqa9Ykh947yPVdCk/MjKRLxQAs/V+2TC4BCYMC2zdEsAVlIsQF/1uwpfOgzPatV3Rzm03Fm/CoSMZiHZ0wMG+Pi9Dk0tgyMuOV2WQB3uAyiIgg/YO+T6jTqH6e5KpsIhncaaIJkXa2ggYN0Rn1YQsOn9IgS/o7Msh/yqoEemL79kHDg70VG0aKtUqjDnOekNsw1JRYv+YAh/RCLWLBuH5DPk/mczYbsR0GcfjXv03LVPTxUiEvLHgC5askfkF7Shl2Yl6TcCXtpGApNn1KygAel+jDvMo6h0Ff0e9Et2ms1CR7oW03Xu3AMb9seEq/d4BtG+D5sjuylFZoDIoIia2FVDWUYA8qQTHoeZTeL6L9ioq5Y2WKAEmIJMwfgMUb1e8Dp0yZZxei/+mN/hJf1zBZT2wIQSGERgWNRZwm5l/B9u4GGPSplIpfEUG6Lou5fv/6/oJ0u3EeBVVcJAAAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                   sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                    sizes="24px" decoding="async" loading="lazy"
                                    src="../../../../static/1baf0a92d1f6c94d4438bd3c45fe85e4/7e080/lighthouse-light.png"
                                    >
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                      sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                      sizes="24px" decoding="async" loading="lazy"
                                      src="../../../../static/1baf0a92d1f6c94d4438bd3c45fe85e4/7e080/lighthouse-light.png"
                                      >
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG">Lighthouse</div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://pegasys.tech/teku" target="_blank" rel="noopener noreferrer">
                              <div data-gatsby-image-wrapper="" style="width:24px;height:25px"
                                class="gatsby-image-wrapper CardList__Image-sc-ablrq5-7 jzdati"><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAVCAYAAABG1c6oAAAACXBIWXMAAAsTAAALEwEAmpwYAAACI0lEQVQ4y43VS0hVURTG8X0fYToJyklBTSKJkKjQjCY9BhGRRaWTRkFBE0PBTHtOempNgqiJIFqglxCJEpRKkAoUSpIm4kTDSFF7ImlEj/+O78Rie9QW/DiXu89eZz/W2cc555JI6ZpGws2OZoxhfUxbOsgRGytxBG34hN/Gc5zE2rk6b8c+FOIi+vAT0+jGcZSiHMXI4IOSD6AOW3AARS54uteObW7hyEdT0HfGaUTj6MB7fMQQnqIWu7AbR7FZ032AQY10FI/xGS99wmrc01OXYSsq0YphTd+Owid5ggvYiVz1fYbLTg3N80xtKe5jZIGleIEb/sd5k3CRKYFU0CHL/E4F5RIlvB6N8K5JmDI1lQzqMmnqNUqaNlP+m/AsHrn/i8Q8bX3aRFemxfa72496VKFT9emCka7WPRW4hVeYVI5z/obT6MIe3FYx/9ANXcGa+bhpdrwHd1CCt7gSrWFTMPxCddgbrJ2PTWqrDvq8jqbsd7lFf2abUpnCOpPQxpR52GJd3+BaNMIWs8s+Vuhd3hiTMFttJea0iRL699rVoFF/5ui6HN+wISZhlhIeCkbop3zJmY2wkatOBWbkafNQmzCKIe3+vx3rxRkdRWvwFavmqLkv2I8lOIyHJo9r0Gnh35Z3+IXvaszopPEbcAx5OKG2UZ04vVq7TrN0s07rg7iqop0xT5/QSXMKO3Q6xb5OiZjDwJlFz+ibkh/Tbr9FiT9VQZE+k1HciQAAAABJRU5ErkJggg=="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                   sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                    sizes="24px" decoding="async" loading="lazy"
                                    src="../../../../static/0d207e4ed25a6b9953122b0c293b22c2/f895c/teku-dark.png"
                                    >
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                     sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                      sizes="24px" decoding="async" loading="lazy"
                                      src="../../../../static/0d207e4ed25a6b9953122b0c293b22c2/f895c/teku-dark.png"
                                      >
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG">Teku</div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://our.status.im/tag/nimbus/" target="_blank" rel="noopener noreferrer">
                              <div data-gatsby-image-wrapper="" style="width:24px;height:15px"
                                class="gatsby-image-wrapper CardList__Image-sc-ablrq5-7 jzdati"><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAMCAYAAABiDJ37AAAACXBIWXMAAB2HAAAdhwGP5fFlAAACfElEQVQoz42SX0hTcRTHz3anDQzLKCSJ/g0jFBMK6qEw9pYvBj0FPZVELxFCRX8fiqjoISnT/XFzObcm6SqJIiFXPSQFku0hCwqs0FEPLsjUbXf3nm/n3q1tiA89HM7v3t/vfM73fH8/imdA6F1P8K8l7t9J6C5X4LMr2Ui9DWeI0i/Pr+GeqkvsLR+Di6Zwh+LwVbTPvXXV4KrsP3Aq2EMEIkokEkQc2kAIbyFEGigFGEBChxzoJFIfH9jK3RWT7LZAQMg82o/Z0XZowY2Am76nh4824qKcizZZ1fu7KDE9LYU3JCLbCKFGmgAsHFx3kAMrz2aHWnawb/lrUQUBptFFmt67SZ8fadNmPo+l1cEmwGP7lExiBYa7SI82089vX0XylzjhNtlwWtT2VHXAUOMWiMemm9lFeg5KLFBGO3E2sh3Jj69U9leCg5sP4brU9jkU9UmrqPOWKbhbTdqLk1XsUVJmsYsy+cwGyISZawvES8Yt4t/jD7PpoRZIk5u4LJxrZONwg0U8s5N+r66OfRVDpiI3GcpKQSj9Nv3sFJXRfZoWrpc95Y02sHc3akjGdhqXUlvH3mWzchhSrOcz8qBi5MZGXikL1ASzrxLw2sVP62DqXb+dxPg+4wZFeord1n+FzO5FwHwUxveUsVGnRp3zvz7EpvRAtYDLr5BsTqgDTuhBhy7PhfNQs8gYdckoeJtTuvC8bSY1fOyP/H9P0uXpwrNWJCdiGfavAlylfhUhvCgXfHWZ02VFMeQeRkkPOZqNMedip7AwcsEcg3PPpejlEsElubAOrD5COCG3E6o9LF0mpWuGPdYs/iPknGqErDPsLfshb/gcjhP9BcUQRoe/MSCeAAAAAElFTkSuQmCC"
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                    sizes="24px" decoding="async" loading="lazy"
                                    src="../../../../static/e096b8b1a6cb2933981f378b8b6d88d9/e019f/nimbus-cloud.png"
                                   >
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                     sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                      sizes="24px" decoding="async" loading="lazy"
                                      src="../../../../static/e096b8b1a6cb2933981f378b8b6d88d9/e019f/nimbus-cloud.png"
                                      >
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG">Nimbus</div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms CardList__ItemLink-sc-ablrq5-2 kUQIQU"
                              href="https://chainsafe.github.io/lodestar/" target="_blank" rel="noopener noreferrer">
                              <div data-gatsby-image-wrapper="" style="width:24px;height:24px"
                                class="gatsby-image-wrapper CardList__Image-sc-ablrq5-7 jzdati"><img aria-hidden="true"
                                  data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear"
                                  decoding="async"
                                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEuElEQVQ4y31UC1CUVRQ+y5Iz5DQ2xSAzPHaX3QWBBRZYFljY9y4xOmmWBCY+UtHRYhidnEwxKMaxd2lIKEhpU2hSPsoBE+0hoqLig5eAgoC84rVqEC3E6Zx/m9TpcWd2/v3vf893vu8751wAWuEhATA4YIM3cyPBmKiEmvMG8WMevrDyxVBATHXLWK4SnaxMpP8lwvf/XSXFWliaHgJBcinsL40T8x4HA3h7J9sDNYjzQeorgQs1BvdYjRxeWRcOV68YYZZC+u+AiAi5W9Rifu79VMtb03Z8pMlsaTZ39/fb8PCh+CIC9yHWwvnPSrSU1BN8ZvrDqhUqCPCX/BNwfVa48NyaF5lO2ZuGBm14vcmEP/6Q6Bz4xYY3Ws0juwpjXuVkH2/X0Nk80eaNajeO4RUTKX8YcPnS0NkkqWZgwIbtbRZsbDQ5b94w488/JWJHh2WqqdGEDHzporFp08aI5xBzIHVBMMXOES+YHyw6UBrHXkNokIzIP+4n+6YsfpwZEbvfu7ut2NdrxVOVifveeSsqs/aS0XHqZCLecdidk5NPIX+nRBXkbxQBstewbWsUWZbhYkjmKm53WUfOnTVMjY0l4dlqfRVVV8/Ms19Tr6Ukw/fu2bGlxTx55FD8H5RgoqfHil1d1qmyg/E7Aby8Ml8KI4ZzXYASH4mcZDrablqE7BaDMvr1zWrIygx7llhia4sZr101Tt69aydmerZA+NVdM00MktdXLhuHXkgNyaj8PgEokQhkvhLFrXaLg6qKPQSY90akIX+HBt5/N3oeJyDGTg7c/2XceWIj/eC96BQC6xoasnGiicu1Riw/puv/uygyP4lCYEjF6CUpzzw9K4KrTk2exlKPlyeMV53WIyXtL/0iLputQJwAkp9D+2OUEMnjA4h7aL/AJZn8GCIPkSqNxbu1BRxkNiiDm6+bnewrPSfJQ3Q47NhQb7pN7BcjLgLPGX6Sl9eEpREvd+5HnjigEVOS9in2kDKNj4wIQZ0kfRHAk4GVJxL2sjwaTSR5v3XcsuAwvROzcyuWhcYSc/B6wg9Wr1SJsjepBdXiT3bGZNPBUTae/BF6kHuS+u4EWRIdGy3XEVgVW9BLLUWVd/b1WbG/z4bfHdWVRoUHKHlq9hRpRQJiQ4OJ6cu+PaL7nA/xYQoa7+q0CEHHKxLyATykG9aHp1Eh2i5eMCC1jxMxeaKxwYQF+ZpaxELysIbh3KG11SxWyqS0kQLpC0MM1Wf0p1kWyyMAgU1np+VOcZF2AzGq5vf2douTWDv53JkqfYurWOgaPcTZQFcUzebzbq4mbeMqL6mvN7UND9uxudk8xUzYX26lujrTBEtnW+hmOrZscaiGe5daSwQBEimY9HzHzRDkv70t6sGbZzpdb7nUVr8yMBXNybP+V1Gq1q5WJSFmClfa0cM6IjLv/iUhJ+CcbDXodQqYPs0HDn7luhup4cHjER8Zge2rKE9A6r066oAUxG7YQqwQs9youm5fl8XfH78HV4QqgD4k88CD3RwoIrnuOq2C9haCRi3nvni0aFeMYNX2DzWUdCZ4e/rDmlUqgdR/LotRKQSNjibBuqxw8neJm1nvuv5pYsQ0EDB3ThDsLox5COhPHsTQLtB6eZ0AAAAASUVORK5CYII="
                                  alt="">
                                <picture>
                                  <source type="image/webp"
                                    sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                    sizes="24px" decoding="async" loading="lazy"
                                    src="../../../../static/3cbd05955da968243fb9c38f275e9de6/7e080/lodestar.png"
                                    >
                                </picture><noscript>
                                  <picture>
                                    <source type="image/webp"
                                     sizes="24px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0"
                                      sizes="24px" decoding="async" loading="lazy"
                                      src="../../../../static/3cbd05955da968243fb9c38f275e9de6/7e080/lodestar.png"
                                      >
                                  </picture>
                                </noscript>
                                <script
                                  type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                              </div>
                              <div class="CardList__LeftContainer-sc-ablrq5-5 vdTcS">
                                <div class="CardList__ItemTitle-sc-ablrq5-3 fXXbG">Lodestar</div>
                                <div class="CardList__ItemDesc-sc-ablrq5-4 gpMYtH"></div>
                              </div>
                            </a></div>
                        </div>
                      </div>
                    </div>
                    <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Not included</span></h2>
                    <p><span>The Merge and shard chain upgrades are still in active development and so are not yet
                        included as part of this bounty program.</span></p>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="bug-bounty__Row-sc-lfsew9-5 dBNcVS">
                    <div class="bug-bounty__SubmitInstructions-sc-lfsew9-18 dxamNL">
                      <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Submit a bug</span></h2>
                      <p><span>For each bug you find youâ€™ll be rewarded points. The points you earn depend on the
                          severity of the bug. Lodestar bugs are currently being awarded 10% of points listed below, as
                          additional audits are under way to be completed. The Ethereum Foundation (EF) determine
                          severity using the OWASP method.</span> <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                          href="https://www.owasp.org/index.php/OWASP_Risk_Rating_Methodology" target="_blank"
                          rel="noopener noreferrer"><span>View OWASP method</span></a></p>
                      <p><span>The EF will also award points based on:</span></p>
                      <p><b><span>Quality of description</span></b><span>: Higher rewards are paid for clear,
                          well-written submissions.</span></p>
                      <p><b><span>Quality of reproducibility</span></b><span>: Please include test code, scripts and
                          detailed instructions. The easier it is for us to reproduce and verify the vulnerability, the
                          higher the reward.</span></p>
                      <p><span><strong>Quality of fix</strong>, if included: Higher rewards are paid for submissions
                          with clear description of how to fix the issue.</span></p>
                    </div>
                    <div class="BugBountyPoints__PointsExchange-sc-9pc1p-0 kqqgIW">
                      <div class="BugBountyPoints__PointsExchangeLabel-sc-9pc1p-1 aOvnK"><span>Points Exchange</span>
                        <div title="More info" class="Tooltip__Container-sc-e34rs3-0 dVEoNO"><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 BugBountyPoints__InfoIcon-sc-9pc1p-3 iylOGp hsLHLc"
                            height="14" width="14" xmlns="http://www.w3.org/2000/svg">
                            <path fill="none" d="M0 0h24v24H0V0z"></path>
                            <path
                              d="M11 7h2v2h-2V7zm0 4h2v6h-2v-6zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z">
                            </path>
                          </svg></div>
                      </div>
                      <h2 class="BugBountyPoints__PointsExchangeTitle-sc-9pc1p-2 dYCTbn"><span>1 point</span></h2>
                      <div class="BugBountyPoints__Row-sc-9pc1p-7 BugBountyPoints__ValueRow-sc-9pc1p-8 jbLwFO hlhhKJ">
                        <p class="BugBountyPoints__TokenValue-sc-9pc1p-6 edrfQi"><span>Loading data...</span></p>
                      </div>
                      <p><span>The Ethereum Foundation will pay out the value of USD in ETH or DAI.</span></p>
                      <p class="BugBountyPoints__TextNoMargin-sc-9pc1p-4 gWBxYB"><em><span>The Ethereum Foundation
                            reserves the right to change this without prior notice.</span></em></p>
                    </div>
                  </div>
                </div>
                <div class="BugBountyCards__CardRow-sc-14nc59p-0 iJMFiU">
                  <div class="BugBountyCards__Card-sc-14nc59p-2 edYxsk">
                    <div class="BugBountyCards__Label-sc-14nc59p-3 BugBountyCards__LowLabel-sc-14nc59p-4 kssiHo cSXeWd">
                      <span>Up to 1,000 points</span></div>
                    <h2 class="BugBountyCards__H2-sc-14nc59p-8 eManNj"><span>Low</span></h2>
                    <p class="BugBountyCards__Description-sc-14nc59p-9 gnJa-Dz"><span>Up to 2,000 DAI</span></p>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Severity</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ">
                      <ul>
                        <li><span>Low impact, medium likelihood</span></li>
                        <li><span>Medium impact, low likelihood</span></li>
                      </ul>
                    </div>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Example</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ"><span>Attacker can sometimes put a node in a
                        state that causes it to drop one out of every one hundred attestations made by a
                        validator</span></div><a
                      class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv BugBountyCards__StyledButton-sc-14nc59p-1 jGhkrb"
                      href="https://forms.gle/Gnh4gzGh66Yc3V7G8" target="_blank" rel="noopener noreferrer"><span>Submit
                        low risk bug</span></a>
                  </div>
                  <div class="BugBountyCards__Card-sc-14nc59p-2 edYxsk">
                    <div
                      class="BugBountyCards__Label-sc-14nc59p-3 BugBountyCards__MediumLabel-sc-14nc59p-5 kssiHo gOzWri">
                      <span>Up to 5,000 points</span></div>
                    <h2 class="BugBountyCards__H2-sc-14nc59p-8 eManNj"><span>Medium</span></h2>
                    <p class="BugBountyCards__Description-sc-14nc59p-9 gnJa-Dz"><span>Up to 10,000 DAI</span></p>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Severity</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ">
                      <ul>
                        <li><span>High impact, low likelihood</span></li>
                        <li><span>Medium impact, medium likelihood</span></li>
                        <li><span>Low impact, high likelihood</span></li>
                      </ul>
                    </div>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Example</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ"><span>Attacker can successfully conduct
                        eclipse attacks on nodes with peer-ids with 4 leading zero bytes</span></div><a
                      class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv BugBountyCards__StyledButton-sc-14nc59p-1 jGhkrb"
                      href="https://forms.gle/Gnh4gzGh66Yc3V7G8" target="_blank" rel="noopener noreferrer"><span>Submit
                        medium risk bug</span></a>
                  </div>
                  <div class="BugBountyCards__Card-sc-14nc59p-2 edYxsk">
                    <div
                      class="BugBountyCards__Label-sc-14nc59p-3 BugBountyCards__HighLabel-sc-14nc59p-6 kssiHo fVDDsK">
                      <span>Up to 10,000 points</span></div>
                    <h2 class="BugBountyCards__H2-sc-14nc59p-8 eManNj"><span>High</span></h2>
                    <p class="BugBountyCards__Description-sc-14nc59p-9 gnJa-Dz"><span>Up to 20,000 DAI</span></p>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Severity</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ">
                      <ul>
                        <li><span>High impact, medium likelihood</span></li>
                        <li><span>Medium impact, high likelihood</span></li>
                      </ul>
                    </div>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Example</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ"><span>There is a consensus bug between two
                        clients, but it is difficult or impractical for the attacker to trigger the event.</span></div>
                    <a class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv BugBountyCards__StyledButton-sc-14nc59p-1 jGhkrb"
                      href="https://forms.gle/Gnh4gzGh66Yc3V7G8" target="_blank" rel="noopener noreferrer"><span>Submit
                        high risk bug</span></a>
                  </div>
                  <div class="BugBountyCards__Card-sc-14nc59p-2 edYxsk">
                    <div
                      class="BugBountyCards__Label-sc-14nc59p-3 BugBountyCards__CriticalLabel-sc-14nc59p-7 kssiHo eGaHlB">
                      <span>Up to 25,000 points</span></div>
                    <h2 class="BugBountyCards__H2-sc-14nc59p-8 eManNj"><span>Critical</span></h2>
                    <p class="BugBountyCards__Description-sc-14nc59p-9 gnJa-Dz"><span>Up to 50,000 DAI</span></p>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Severity</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ">
                      <ul>
                        <li><span>High impact, high likelihood</span></li>
                      </ul>
                    </div>
                    <div class="BugBountyCards__Divider-sc-14nc59p-10 gtmKFl"></div>
                    <p class="BugBountyCards__SubHeader-sc-14nc59p-11 NtwCD"><span>Example</span></p>
                    <div class="BugBountyCards__Text-sc-14nc59p-12 jcjOzJ"><span>There is a consensus bug between two
                        clients, and it is trivial for an attacker to trigger the event.</span></div><a
                      class="ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv BugBountyCards__StyledButton-sc-14nc59p-1 jGhkrb"
                      href="https://forms.gle/Gnh4gzGh66Yc3V7G8" target="_blank" rel="noopener noreferrer"><span>Submit
                        critical risk bug</span></a>
                  </div>
                </div>
                <div class="SharedStyledComponents__Content-sc-1cr9zfr-3 dedPKg">
                  <div class="bug-bounty__Rules-sc-lfsew9-17 fudkTE">
                    <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Bug hunting rules</span></h2>
                    <p><em><span>The bug bounty program is an experimental and discretionary rewards program for our
                          active Ethereum community to encourage and reward those who are helping to improve the
                          platform. It is not a competition. You should know that we can cancel the program at any time,
                          and awards are at the sole discretion of Ethereum Foundation bug bounty panel. In addition, we
                          are not able to issue awards to individuals who are on sanctions lists or who are in countries
                          on sanctions lists (e.g. North Korea, Iran, etc). You are responsible for all taxes. All
                          awards are subject to applicable law. Finally, your testing must not violate any law or
                          compromise any data that is not yours.</span></em></p>
                    <ul>
                      <li><span>Issues that have already been submitted by another user or are already known to spec and
                          client maintainers are not eligible for bounty rewards.</span></li>
                      <li><span>Public disclosure of a vulnerability makes it ineligible for a bounty.</span></li>
                      <li><span>Ethereum Foundation researchers and employees of consensus layer client teams are not
                          eligible for rewards.</span></li>
                      <li id="leaderboard"><span>Ethereum bounty program considers a number of variables in determining
                          rewards. Determinations of eligibility, score and all terms related to an award are at the
                          sole and final discretion of the Ethereum Foundation bug bounty panel.</span></li>
                    </ul>
                  </div>
                </div>
                <div class="SharedStyledComponents__GradientContainer-sc-1cr9zfr-9 kKRvUp">
                  <div class="bug-bounty__FullLeaderboardContainer-sc-lfsew9-14 XnWND">
                    <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Bug hunting leaderboard</span></h2>
                    <p><span>Find consensus layer bugs to get added to this leaderboard</span></p>
                    <div class="Leaderboard__Table-sc-9p0qku-0 gGjywO"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/protolambda" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">1</div><img
                          src="https://github.com/protolambda.png?size=40"
                          class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">protolambda</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">42400
                            <!-- --> <span>points</span>
                          </div>
                        </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                          class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ†"
                            src="https://twemoji.maxcdn.com/2/svg/1f3c6.svg"
                            style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/cryptosubtlety" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">2</div><img
                          src="https://github.com/cryptosubtlety.png?size=40"
                          class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Quan Thoi Minh Nguyen</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">19650
                            <!-- --> <span>points</span>
                          </div>
                        </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                          class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ¥ˆ"
                            src="https://twemoji.maxcdn.com/2/svg/1f948.svg"
                            style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/guidovranken" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">3</div><img
                          src="https://github.com/guidovranken.png?size=40"
                          class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Guido Vranken</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">16600
                            <!-- --> <span>points</span>
                          </div>
                        </div><span size="1.5" mr="2rem" mt="0" mb="0" ml="0"
                          class="Emoji__StyledEmoji-sc-ihpuqw-0 jCfhGS undefined"><img alt="ðŸ¥‰"
                            src="https://twemoji.maxcdn.com/2/svg/1f949.svg"
                            style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/jrhea" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">4</div><img
                          src="https://github.com/jrhea.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Jonny Rhea</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">15500
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/sifraitech" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">5</div><img
                          src="https://github.com/sifraitech.png?size=40"
                          class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Grandine team</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">9000
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/kilic" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">6</div><img
                          src="https://github.com/kilic.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Onur KÄ±lÄ±Ã§</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">6000
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/atoulme" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">7</div><img
                          src="https://github.com/atoulme.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Antoine Toulme</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">5000
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/asanso" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">8</div><img
                          src="https://github.com/asanso.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Antonio Sanso</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">4000
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/itsunixiknowthis" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">9</div><img
                          src="https://github.com/itsunixiknowthis.png?size=40"
                          class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">ItsUnixIKnowThis</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">2850
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/AlexSSD7" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">10</div><img
                          src="https://github.com/AlexSSD7.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Alexander Sadovskyi</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">2500
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/tintinweb" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">11</div><img
                          src="https://github.com/tintinweb.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">tintin</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">2500
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/holiman" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">12</div><img
                          src="https://github.com/holiman.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Martin Holst Swende</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">2500
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/none" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">13</div><img
                          src="https://github.com/none.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Akincibor</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">1750
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a><a class="Link__ExternalLink-sc-e3riao-0 gABYms Leaderboard__Item-sc-9p0qku-1 cOccLj"
                        href="https://github.com/mcdee" target="_blank" rel="noopener noreferrer">
                        <div class="Leaderboard__ItemNumber-sc-9p0qku-6 kyweyX">14</div><img
                          src="https://github.com/mcdee.png?size=40" class="Leaderboard__Avatar-sc-9p0qku-5 cXPtSw">
                        <div class="Leaderboard__TextContainer-sc-9p0qku-4 fNfHlp">
                          <div class="Leaderboard__ItemTitle-sc-9p0qku-2 uudTP">Jim McDonald</div>
                          <div class="Leaderboard__ItemDesc-sc-9p0qku-3 euYmLE">200
                            <!-- --> <span>points</span>
                          </div>
                        </div>
                      </a></div>
                  </div>
                </div>
                <div class="bug-bounty__Contact-sc-lfsew9-20 gJnclD">
                  <div>
                    <h2 class="bug-bounty__H2-sc-lfsew9-10 cEcLXE"><span>Questions?</span></h2>
                    <p class="bug-bounty__TextNoMargin-sc-lfsew9-19 fmMDKK"><span>Email us:</span> <a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="/cdn-cgi/l/email-protection#9cfef3e9f2e8e5dcf9e8f4efe8fdf7f9b2f9e4fff4fdf2fbf9"
                        target="_blank" rel="noopener noreferrer"><span class="__cf_email__"
                          data-cfemail="ccaea3b9a2b8b58ca9b8a4bfb8ada7a9e2a9b4afa4ada2aba9">[email&#160;protected]</span></a>
                    </p>
                  </div><span size="3" mt="0" mr="0" mb="0" ml="0"
                    class="Emoji__StyledEmoji-sc-ihpuqw-0 hLjau undefined"><img alt="âœ‰ï¸"
                      src="https://twemoji.maxcdn.com/2/svg/2709.svg"
                      style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
                </div>
              </div>
            </main>
          </div>
        </div>
        <footer class="Footer__StyledFooter-sc-1to993d-0 gvoBKJ">
          <div class="Footer__FooterTop-sc-1to993d-1 kFKfdz">
            <div class="Footer__LastUpdated-sc-1to993d-2 bWGwos"><span>Website last updated</span>:
              <!-- -->
              <!-- -->December 1, 2020
            </div>
            <div class="Footer__SocialIcons-sc-1to993d-9 kdLbod"><a
                href="https://github.com/ethereum/ethereum-org-website" target="_blank" rel="noopener noreferrer"
                aria-label="GitHub"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 496 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z">
                  </path>
                </svg></a><a href="https://twitter.com/ethdotorg" target="_blank" rel="noopener noreferrer"
                aria-label="Twitter"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 512 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                  </path>
                </svg></a><a href="https://youtube.com/channel/UCNOfzGXD_C9YMYmnefmPH0g" target="_blank"
                rel="noopener noreferrer" aria-label="Youtube"><svg stroke="currentColor" fill="currentColor"
                  stroke-width="0" viewbox="0 0 576 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                  </path>
                </svg></a><a href="https://discord.gg/CetY6Y4" target="_blank" rel="noopener noreferrer"
                aria-label="Discord"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 640 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                  </path>
                </svg></a></div>
          </div>
          <div class="Footer__LinkGrid-sc-1to993d-3 hlbLsM">
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Use Ethereum</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\get-eth\index.html"><span>Get ETH</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\run-a-node\index.html"><span>Run a node</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\staking\index.html"><span>Stake ETH</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Learn</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\learn\index.html"><span>Community guides and resources</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\history\index.html"><span>History of Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\index.html"><span>Ethereum upgrades</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                      fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                      width="12px" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                      </path>
                    </svg></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\governance\index.html"><span>Ethereum governance</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\web3\index.html"><span>What is Web3?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Developers</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\developers\index.html"><span>Get started</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/developers/docs/"><span>Documentation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                </li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Ecosystem</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\community\index.html"><span>Community hub</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\foundation\index.html"><span>Ethereum Foundation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://blog.ethstake.exchange/" target="_blank" rel="noopener noreferrer"><span>Ethereum
                      Foundation Blog</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://esp.ethereum.foundation" target="_blank" rel="noopener noreferrer"><span>Ecosystem
                      Support Program</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/community/grants"><span>Ecosystem Grant Programs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\assets\index.html"><span>Ethereum brand assets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://devcon.org/" target="_blank" rel="noopener noreferrer"><span>Devcon</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Enterprise</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\enterprise\index.html"><span>Enterprise</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>About ethstake.exchange</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\about\index.html"><span>About us</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\about\index.html#open-jobs"><span>Jobs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\contributing\index.html"><span>Contributing</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\languages\index.html"><span>Language support</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\privacy-policy\index.html"><span>Privacy policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\terms-of-use\index.html"><span>Terms of use</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\..\..\cookie-policy\index.html"><span>Cookie policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/cdn-cgi/l/email-protection#f18183948282b19485998285909a94df94899299909f9694" target="_blank"
                    rel="noopener noreferrer"><span>Contact</span></a></li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <div id="gatsby-announcer"
      style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0"
      aria-live="assertive" aria-atomic="true"></div>
  </div>
  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let svgIcon = document.getElementsByClassName('Mobile__GlyphButton')[0]
    let menuBtn = document.getElementsByClassName('Mobile__MenuButton')
    let hideMenu = document.getElementsByClassName('Mobile__MobileModal')[0]
    let menuContainer = document.getElementsByClassName('Mobile__MenuContainer')[0]
    let menuUl = document.getElementsByClassName('Mobile__MenuItems')[0]
    let menuBottom = document.getElementsByClassName('Mobile__BottomMenu')[0]
    let searchPage = document.getElementsByClassName('Mobile__MenuContainer')[1]
    let closeBtn = document.getElementsByClassName('Mobile__CloseIconContainer')[0]
    let searchBtn = document.getElementsByClassName('Mobile__BottomItem')[0]
    menuBtn[1].addEventListener('click', function () {
      this.toggleAttribute('open')

      let type = this.hasAttribute('open')
      let path = svgIcon.getElementsByTagName('path')[0]
      let d = path.getAttribute('d')
      path.style='transition: all ease-in 0.25s;'
      console.log(path, d,'aaaa')
      if (type) {
        hideMenu.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuContainer.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        path.setAttribute('d','M 2 19 l 10 -14 l 0 0 l 10 14 M 2 19 l 10 7 M 12 26 l 10 -7 M 2 22 l 10 15 l 0 0 l 9 -15')
        setTimeout(()=>{
          path.setAttribute('d','M 2 13 l 0 -3 l 20 0 l 0 3 M 7 14 l 10 10 M 7 24 l 10 -10 M 2 25 l 0 3 l 20 0 l 0 -3')

        },700)

      } else {
        hideMenu.style = 'transition: all ease-in 0.2s;display: none; opacity: 0;'
        menuContainer.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        path.setAttribute('d','M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0')

      }
       // menuUl.toggleAttribute('class', 'gYetwr')
    })
    menuBtn[0].addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    searchBtn.addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    closeBtn.addEventListener('click', function () {
      console.log('111')
      searchPage.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'

    })
    console.log(menuBtn, '......')
    window.dev = undefined
    if (window.dev === true || !(navigator.doNotTrack === '1' || window.doNotTrack === '1')) {
      window._paq = window._paq || [];



      window._paq.push(['setTrackerUrl', 'https://matomo.ethstake.exchange/matomo.php']);
      window._paq.push(['setSiteId', '4']);
      window._paq.push(['enableHeartBeatTimer']);
      window.start = new Date();

      (function () {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript'; g.async = true; g.defer = true; g.src = 'https://matomo.ethstake.exchange/matomo.js'; s.parentNode.insertBefore(g, s);
      })();

      if (window.dev === true) {
        console.debug('[Matomo] Tracking initialized')
        console.debug('[Matomo] matomoUrl: https://matomo.ethstake.exchange, siteId: 4')
      }
    }
  </script><noscript><img
      src="https://matomo.ethstake.exchange/matomo.php?idsite=4&rec=1&url=https://ethstake.exchange/en/upgrades/get-involved/bug-bounty/"
      style="border:0" alt="tracker"></noscript>
  <script
    id="gatsby-script-loader">/*<![CDATA[*/window.pagePath = "/en/upgrades/get-involved/bug-bounty/"; window.___webpackCompilationHash = "0375e64c51e377521456";/*]]>*/</script>
  <script
    id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping = { "polyfill": ["/polyfill-b6350ac254e29f22a1e4.js"], "app": ["/app-b670b5ed3a389af0ed04.js"], "component---src-pages-404-js": ["/component---src-pages-404-js-a6aee0605f3068868f92.js"], "component---src-pages-assets-js": ["/component---src-pages-assets-js-ba78d988431646b4760b.js"], "component---src-pages-community-js": ["/component---src-pages-community-js-5ca1d5f82d581db39798.js"], "component---src-pages-conditional-dapps-js": ["/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js"], "component---src-pages-conditional-eth-js": ["/component---src-pages-conditional-eth-js-21644356026cce06349e.js"], "component---src-pages-conditional-wallets-index-js": ["/component---src-pages-conditional-wallets-index-js-6c55787f35fe5cc07ef3.js"], "component---src-pages-conditional-what-is-ethereum-js": ["/component---src-pages-conditional-what-is-ethereum-js-144e7cb6cba17bff7608.js"], "component---src-pages-contributing-translation-program-acknowledgements-js": ["/component---src-pages-contributing-translation-program-acknowledgements-js-a596bd2823410bf53c11.js"], "component---src-pages-contributing-translation-program-contributors-js": ["/component---src-pages-contributing-translation-program-contributors-js-4f2a18f6f82d2a84de27.js"], "component---src-pages-developers-index-js": ["/component---src-pages-developers-index-js-eba978370f325b125b03.js"], "component---src-pages-developers-learning-tools-js": ["/component---src-pages-developers-learning-tools-js-88bd1bcad31958f723e3.js"], "component---src-pages-developers-local-environment-js": ["/component---src-pages-developers-local-environment-js-768783f49122fff8d82f.js"], "component---src-pages-developers-tutorials-js": ["/component---src-pages-developers-tutorials-js-f82f9627cb9b2ce3318b.js"], "component---src-pages-get-eth-js": ["/component---src-pages-get-eth-js-e6c689f28770388e40be.js"], "component---src-pages-index-js": ["/component---src-pages-index-js-c7245d4f213dfe2095c4.js"], "component---src-pages-languages-js": ["/component---src-pages-languages-js-b1a3a1c01ec6bdcd87ac.js"], "component---src-pages-run-a-node-js": ["/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js"], "component---src-pages-stablecoins-js": ["/component---src-pages-stablecoins-js-4bc5b567f2c38baaaa5b.js"], "component---src-pages-stakenow-js": ["/component---src-pages-stakenow-js-40c51639947629777abf.js"], "component---src-pages-staking-deposit-contract-js": ["/component---src-pages-staking-deposit-contract-js-ac8273bd9f4711b38540.js"], "component---src-pages-staking-index-js": ["/component---src-pages-staking-index-js-3d50b1ef7b3b9814f6e2.js"], "component---src-pages-studio-js": ["/component---src-pages-studio-js-c16fabe2f5808fafce99.js"], "component---src-pages-upgrades-get-involved-bug-bounty-js": ["/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js"], "component---src-pages-upgrades-get-involved-index-js": ["/component---src-pages-upgrades-get-involved-index-js-207fa70ee7aa5720b909.js"], "component---src-pages-upgrades-index-js": ["/component---src-pages-upgrades-index-js-dd49283310be18b0a199.js"], "component---src-pages-upgrades-vision-js": ["/component---src-pages-upgrades-vision-js-2c86e72cede9c6155cbf.js"], "component---src-pages-wallets-find-wallet-js": ["/component---src-pages-wallets-find-wallet-js-97c53af70032ab1edbc4.js"], "component---src-templates-static-js": ["/component---src-templates-static-js-3cd4030c1e191ee95c2b.js"], "component---src-templates-upgrade-js": ["/component---src-templates-upgrade-js-700f5089c86b74f8484f.js"], "component---src-templates-use-cases-js": ["/component---src-templates-use-cases-js-75c0d7fec84444cc8c68.js"] };/*]]>*/</script>
  <script src="/polyfill-b6350ac254e29f22a1e4.js" nomodule=""></script>
  <script src="/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js" async=""></script>
  <script src="/25d596b65775ea7afe354c15642381979021d6cd-5667baf5a2a2bad6de51.js" async=""></script>
  <script src="/app-b670b5ed3a389af0ed04.js" async=""></script>
  <script src="/0f1ac474-e8f788f62189f421a856.js" async=""></script>
  <script src="/0c428ae2-2128ff22fce458b543bd.js" async=""></script>
  <script src="/1bfc9850-0f18e2d74feedfc6e426.js" async=""></script>
  <script src="/ae51ba48-34d54094a2c04f215fb8.js" async=""></script>
  <script src="/252f366e-2705b607be296edabcea.js" async=""></script>
  <script src="/framework-4e285adfb333f1b50c05.js" async=""></script>
  <script src="/webpack-runtime-d600da28e471609bf3f3.js" async=""></script>
</body>

</html>