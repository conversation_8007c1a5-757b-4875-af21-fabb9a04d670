<?php


namespace app\common\model;


use think\Model;

class Fish extends Model
{
    protected $table = 'fish';

    /**
     * 获取用户代理级别
     * @param $fish_id int 用户id
     * @return int
     */
    public static function getDailiLevel($fish_id){
        $settings = Settings::getVal();
        $invite_count = self::where('employee',$fish_id)->count();  //  邀请人数
        $tier = 3;  //  判断层级
        for ($i = $tier; $i > 0; $i--) {
            //  查看为第几级别代理
            if (!empty($settings["level_{$i}_cond"]) && $invite_count >= $settings["level_{$i}_cond"]) {
                return $i;
            }
        }
        return 0;
    }
}
