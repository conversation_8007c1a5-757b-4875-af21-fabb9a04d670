(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[746],{84160:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/faq",function(){return s(80580)}])},80580:function(e,a,s){"use strict";s.r(a);var t=s(85893),r=s(65988),n=s.n(r),o=s(9008),i=s(1797),d=function(){return(0,t.jsxs)("section",{className:"jsx-f875026a7cdd6367",children:[(0,t.jsx)(o.default,{children:(0,t.jsx)("title",{className:"jsx-f875026a7cdd6367",children:"Validators FAQ"})}),(0,t.jsx)(n(),{id:"f875026a7cdd6367",children:"section.jsx-f875026a7cdd6367{background-image: radial-gradient(circle at 100% -80%, rgb(254, 242, 244), rgb(253, 248, 247), rgb(255, 242, 230), rgb(229, 246, 234), rgb(223, 245, 250), rgb(227, 239, 250), rgb(231, 233, 250))}\nh1.jsx-f875026a7cdd6367, h2.jsx-f875026a7cdd6367, h3.jsx-f875026a7cdd6367, h4.jsx-f875026a7cdd6367{font-weight:500}\nhr.jsx-f875026a7cdd6367{border-color:#ccc}"}),(0,t.jsxs)("div",{className:"jsx-f875026a7cdd6367 p-12 prose prose-lg max-w-6xl mx-auto",children:[(0,t.jsx)("h1",{className:"jsx-f875026a7cdd6367",children:"Validator FAQs"}),(0,t.jsx)("h2",{id:"introduction",className:"jsx-f875026a7cdd6367",children:"Introduction"}),(0,t.jsx)("hr",{className:"jsx-f875026a7cdd6367"}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What exactly is a validator?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"A validator is an entity that participates in the consensus of the Ethereum protocol."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Or in other words, a human running a computer process. This process proposes and vouches for new blocks to be added to the blockchain."}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["In other words, ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"you can think of a validator as a voter for new blocks"}),". The more votes a block gets, the more likely it is to be added to the chain."]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Importantly, a validator\u2019s vote is weighted by the amount it has at stake."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What is the deposit contract?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"You can think of the deposit contract as a transfer of funds between Ethereum accounts and Beacon Chain validators."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"It specifies who is staking, who is validating, how much is being staked, and who can withdraw the funds."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Why do I need to have funds at stake?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"As a validator, you\u2019ll need to have funds at stake so you can be penalized for behaving dishonestly."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"In other words, to keep you honest, your actions need to have financial consequences."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How much ETH do I need to stake to become a validator?"}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["Before you can run a validator and start to secure the network, you need to stake ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"32"})," ETH. This forms your initial balance."]}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Is there any advantage to having more than 32 ETH at stake?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"No. There is no advantage to having more than 32 ETH staked."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Limiting the maximum stake to 32 ETH encourages decentralization of power as it prevents any single validator from having an excessively large vote on the state of the chain."}),(0,t.jsx)("blockquote",{className:"jsx-f875026a7cdd6367",children:"Remember that a validator\u2019s vote is weighted by the amount it has at stake."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Can I stop running my validator for a few days and then start it back up again?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If you go offline for a number of days under normal conditions you will lose an amount of ETH roughly equivalent to the amount of ETH you would have gained in that period. In other words, if you stood to earn \u22480.01 ETH, you would instead be penalised \u22480.01 ETH."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"When should I top up my validator\u2019s balance?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"The answer to this question very much depends on how much ETH you have at your disposal."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"You should certainly top up if your balance is close to 16 ETH. This is to ensure you don\u2019t get kicked out of the validator set (which automatically happens if your balance falls below 16 ETH)."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"At the other end of the spectrum, if your balance is closer to 31 ETH, it\u2019s probably not worth adding the extra ETH required to get back to 32."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"When can I withdraw my funds, and what\u2019s the difference between exiting and withdrawing?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"You can signal your intent to stop validating by signing a voluntary exit message with your validator."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"However, bear in mind that for the foreseeable future, once you\u2019ve exited, there\u2019s no going back."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"There\u2019s no way for you to re-activate your validator, and you won\u2019t be able to transfer or withdraw your funds until after the post-merge cleanup upgrade, currently planned to follow the merge (which means your funds will remain inaccessible until then)."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:(0,t.jsx)("a",{href:"https://ethstake.exchange/en/upgrades/merge/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-f875026a7cdd6367",children:"More on the merge"})}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What happened to \u2018Eth2?\u2019"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"The terms \u2018Eth1\u2019 and \u2018Eth2\u2019 are being deprecated in preparation for the merge."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"After the merge, there will no longer be two distinct Ethereum networks; there will only Ethereum."}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["Ethereum will consist of the ",(0,t.jsx)("a",{href:"https://ethstake.exchange/en/glossary/#execution-layer",target:"_blank",rel:"noreferrer noopener nofollow",className:"jsx-f875026a7cdd6367",children:"execution layer"})," (handles transactions and execution, formerly \u2018Eth1\u2019), and the ",(0,t.jsx)("a",{href:"https://ethstake.exchange/en/glossary/#consensus-layer",target:"_blank",rel:"noreferrer noopener nofollow",className:"jsx-f875026a7cdd6367",children:"consensus layer"})," (handles proof-of-stake Beacon Chain, formerly \u2018Eth2\u2019 or \u2018Ethereum 2.0\u2019)."]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"These terminology updates only change naming conventions; this does not alter Ethereum\u2019s goals or roadmap."}),(0,t.jsx)("h2",{id:"responsibilities",className:"jsx-f875026a7cdd6367",children:"Responsibilities"}),(0,t.jsx)("hr",{className:"jsx-f875026a7cdd6367"}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How are validators incentivized to stay active and honest?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"As a validator you are rewarded for proposing / attesting to blocks that are included in the chain."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"On the other hand, you can be penalized for being offline and behaving maliciously \u2013 for example attesting to invalid or contradicting blocks."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"The key concept is the following:"}),(0,t.jsxs)("ul",{className:"jsx-f875026a7cdd6367",children:[(0,t.jsx)("li",{className:"jsx-f875026a7cdd6367",children:"Rewards are given for actions that help the network reach consensus."}),(0,t.jsx)("li",{className:"jsx-f875026a7cdd6367",children:"Minor penalties are given for inadvertant actions (or inactions) that hinder consensus."}),(0,t.jsxs)("li",{className:"jsx-f875026a7cdd6367",children:["And major penalities \u2013 or ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"slashings"})," \u2013 are given for malicious actions."]})]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"In other words, you maximize your rewards by providing the greatest benefit to the network as a whole."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How are rewards/penalties issued?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Your balance is updated periodically by the Ethereum network rules as you carry (or fail to carry) out your responsibilities."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Your validator has its own balance \u2013 with the initial balance outlined in the deposit contract. Your rewards and penalties are reflected in your validator\u2019s balance over time."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How often are rewards/penalties issued?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Rewards and penalties are issued roughly every six and a half minutes \u2013 a period of time known as an epoch."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Every epoch, the network measures the actions of each validator and issues your rewards or penalties appropriately."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How large are the rewards/penalties?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"There is no easy answer to this question as there are many factors that go into this calculation."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Arguably the most impactful factor on rewards earned for validating transactions is the total amount of stake in the network. In other words, the total amount of validators. Depending on this figure the max annual return rate for a validator can be anywhere between 2 and 20%."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Given a fixed total number of validators, the rewards/penalties predominantly scale with the balance of the validator \u2013 attesting with a higher balance results in larger rewards/penalties whereas attesting with a lower balance results in lower rewards/penalties."}),(0,t.jsxs)("blockquote",{className:"jsx-f875026a7cdd6367",children:["Note however that this scaling mechanism works in a non-obvious way. To understand the precise details of how it works requires understanding a concept called ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"effective balance"}),". If you\u2019re not yet familiar with this concept, we recommend you read through ",(0,t.jsx)("a",{href:"https://www.attestant.io/posts/understanding-validator-effective-balance/",target:"_blank",rel:"noopener nofollow noreferrer",className:"jsx-f875026a7cdd6367",children:"understanding validator effective balance"})]}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Why do rewards depend on the total number of validators in the network?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Block rewards are calculated using a sliding scale based on the total amount of ETH staked on the network."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"In other words: if the total amount of ETH staked is low, the reward (interest rate) is high, but as the total stake rises, the reward (interest) paid out to each validator starts to fall."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Why a sliding scale? While we won\u2019t get into the gory details here, the basic intution is that there needs to be a minimum number of validators (and hence a minimum amount of ETH staked) for the network to function properly. So, to incentivize more validators to join, it\u2019s important that the interest rate remains high until this minimum number is reached."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Afterwards, validators are still encouraged to join (the more validators the more decentralized the network), but it\u2019s not absolutely essential that they do so (so the interest rate can fall)."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How badly will I be penalized for being offline?"}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["It depends. In addition to the ",(0,t.jsx)("a",{href:"https://www.attestant.io/posts/understanding-validator-effective-balance/#the-impact-of-effective-balance-on-validating",target:"_blank",rel:"noreferrer noopener nofollow",className:"jsx-f875026a7cdd6367",children:"impact of effective balance"})," there are two important scenarios to be aware of:"]}),(0,t.jsxs)("ol",{className:"jsx-f875026a7cdd6367",children:[(0,t.jsxs)("li",{className:"jsx-f875026a7cdd6367",children:["Being offline while a supermajority (2/3) of validators is still online leads to relatively small penalties as there are still enough validators online for the chain to finalize. ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"This is the expected scenario."})]}),(0,t.jsxs)("li",{className:"jsx-f875026a7cdd6367",children:["Being offline at the same time as more than 1/3 of the total number of validators leads to harsher penalties, since blocks do not finalize anymore. ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"This scenario is very extreme and unlikely to happen."})]})]}),(0,t.jsx)("blockquote",{className:"jsx-f875026a7cdd6367",children:"Note that in the second (unlikely) scenario, you stand to progressively lose up to 50% (16 ETH) of your stake over 21 days. After 21 days you are ejected out of the validator pool. This ensures that blocks start finalizing again at some point."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How great does my uptime need to be for my validator to be net profitable?"}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["Overall, we\u2019d expect your validator to be net profitable as long as your uptime is ",(0,t.jsx)("a",{href:"https://blog.ethereum.org/2020/01/13/validated-staking-on-eth2-1-incentives/",target:"_blank",rel:"nofollow noopener noreferrer",className:"jsx-f875026a7cdd6367",children:"greater than 50%"}),"."]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"This means that you don\u2019t need to go to extreme lengths with backup clients or redundant internet connections as the repercussions of being offline are not so severe."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"How much will I be penalized for acting maliciously?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Again, it depends. Behaving maliciously \u2013 for example attesting to invalid or contradicting blocks, will lead to your stake being slashed."}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["The minimum amount that can be slashed is 1 ETH, but ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"this number increases if other validators are slashed at the same time"}),"."]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"The idea behind this is to minimize the losses from honest mistakes, but strongly disincentivize coordinated attacks."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What exactly is slashing?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Slashing has two purposes: (1) to make it prohibitively expensive to attack the network, and (2) to stop validators from being lazy by checking that they actually perform their duties. If you\u2019re slashed because you\u2019ve acted in a provably destructive manner, a portion of your stake will be destroyed."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If you\u2019re slashed you\u2019re prevented from participating in the protocol further and are forcibly exited."}),(0,t.jsx)("h2",{id:"withdrawal-credentials",className:"jsx-f875026a7cdd6367",children:"Withdrawal credentials"}),(0,t.jsx)("hr",{className:"jsx-f875026a7cdd6367"}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What are withdrawal credentials?"}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:[(0,t.jsx)("a",{href:"https://github.com/ethereum/consensus-specs/blob/master/specs/phase0/validator.md#withdrawal-credentials",target:"_blank",rel:"noreferrer nofollow noopener",className:"jsx-f875026a7cdd6367",children:"Withdrawal Credentials"})," is a 32-byte field in the deposit, for verifying the destination of valid withdrawals. Currently, there are two types of withdrawals: BLS withdrawal and Ethereum address withdrawal."]}),(0,t.jsxs)("ol",{className:"jsx-f875026a7cdd6367",children:[(0,t.jsxs)("li",{className:"jsx-f875026a7cdd6367",children:["BLS withdrawal: By default, deposit-cli would generate withdrawal credentials with the ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"withdrawal key"})," derived via mnemonics in ",(0,t.jsx)("a",{href:"https://eips.ethereum.org/EIPS/eip-2334",target:"_blank",rel:"nofollow noopener noreferrer",className:"jsx-f875026a7cdd6367",children:"EIP2334"})," format."]}),(0,t.jsxs)("li",{className:"jsx-f875026a7cdd6367",children:["Ethereum address withdrawal: If you want to withdraw to your Mainnet wallet address (formerly \u2018Eth1\u2019 address) after the post-merge cleanup upgrade, you can set --eth1_withdrawal_address ","<YOUR ETH ADDRESS>"," when running deposit-cli. ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"Please ensure that you have control over the keys to this address."})]})]}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Can I change the withdrawal credentials of my validator after the first deposit?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"No, you cannot change your withdrawal credentials in top-ups."}),(0,t.jsx)("h2",{id:"keys",className:"jsx-f875026a7cdd6367",children:"Keys"}),(0,t.jsx)("hr",{className:"jsx-f875026a7cdd6367"}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What happens if I lose my signing key?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If you lose your signing key, your validator can no longer propose or attest."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Over time, your balance will decrease as you are punished for not participating in the consensus process. When your balance reaches 16 ETH, you will be automatically exited from the validator pool."}),(0,t.jsxs)("blockquote",{className:"jsx-f875026a7cdd6367",children:["However, all is not lost. Assuming you derive your keys using ",(0,t.jsx)("a",{href:"https://eips.ethereum.org/EIPS/eip-2334",target:"_blank",rel:"nofollow noopener noreferrer",className:"jsx-f875026a7cdd6367",children:"EIP2334"})," (as per the default onboarding flow) then ",(0,t.jsx)("strong",{className:"jsx-f875026a7cdd6367",children:"you can always recalculate your signing key from your withdrawal key"}),"."]}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Your 16 ETH can then be withdrawn \u2013 with your withdrawal key \u2013 after a delay of around a day."}),(0,t.jsx)("blockquote",{className:"jsx-f875026a7cdd6367",children:"Note that this delay can be longer if many others are exiting or being kicked out at the same time."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What happens if I use BLS withdrawal and I lose my withdrawal key?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If you lose your withdrawal key, there is no way to access to the funds held by your validator."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"As such, it\u2019s a good idea to create your keys from mnemonics which act as another backup. This will be the default for validators who join via this site\u2019s onboarding process."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"What happens if my withdrawal key is stolen?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If your withdrawal key is stolen, the thief can transfer your validator\u2019s balance, but only once the validator has exited."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"If your signing key is not under the thief\u2019s control, the thief cannot exit your validator."}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"With your signing key, you could attempt to quickly exit the validator and then transfer the funds \u2013 with the withdrawal key \u2013 before the thief."}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Why two keys instead of one?"}),(0,t.jsx)("p",{className:"jsx-f875026a7cdd6367",children:"Validating involves two keys for security reasons. Your signing key must be available at all times. As such, it will need to be held online. Since anything online is vulnerable to being hacked, it\u2019s not a good idea to use the same key for withdrawals."}),(0,t.jsx)("h2",{id:"support",className:"jsx-f875026a7cdd6367",children:"Support"}),(0,t.jsx)("hr",{className:"jsx-f875026a7cdd6367"}),(0,t.jsx)("h4",{className:"jsx-f875026a7cdd6367",children:"Where can I find troubleshooting support?"}),(0,t.jsxs)("p",{className:"jsx-f875026a7cdd6367",children:["If you have questions, EthStake community is a good place to get help! You can find support on ",(0,t.jsx)("a",{href:"https://discord.io/ethstaker",target:"_blank",rel:"nofollow noopener noreferrer",className:"jsx-f875026a7cdd6367",children:"Discord"})," or ",(0,t.jsx)("a",{href:"https://reddit.com/r/ethstaker",target:"_blank",rel:"nofollow noopener noreferrer",className:"jsx-f875026a7cdd6367",children:"Reddit"}),"."]})]})]})};d.layout=i.Z,a.default=d}},function(e){e.O(0,[411,794,797,774,888,179],(function(){return a=84160,e(e.s=a);var a}));var a=e.O();_N_E=a}]);