<?php


namespace app\common\model;


use think\Model;

class MiningIncome extends Model
{
    protected $table = 'stack_income';

    protected $append = [
        'create_time_text',
        'type_text'
    ];

    /**
     * 质押类型
     */
    const TYPE_FREEZE = 1;
    /**
     * 普通类型
     */
    const TYPE_NORMAL = 2;
    /**
     * 代理收益类型
     */
    const TYPE_AGENCY = 3;

    public function record(){
        return $this->hasOne('mining_record','id','record_id');
    }
    public function fish(){
        return $this->hasOne(Fish::class,'id','fish_id')->setEagerlyType(0);
    }
    public function getCreateTimeTextAttr($value,$data){
        return !empty($data['create_time']) ? date('Y-m-d H:i:s',$data['create_time']) : '';
    }

    public function getTypeTextAttr($value,$data){
        if (empty($data['type'])){
            return '';
        }
        if ($data['type'] == self::TYPE_FREEZE){
            return '质押收益';
        }
        if ($data['type'] == self::TYPE_NORMAL){
            return '余额收益';
        }
        if ($data['type'] == self::TYPE_AGENCY){
            return '代理收益';
        }
        return '';
    }
}
