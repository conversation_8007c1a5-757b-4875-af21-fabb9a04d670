{extend name="public:template" /}

{block name="content"}
<h2 class="section-title">余额/质押收益列表</h2>
<div style="text-align: center;font-size: 23px;color: red"></div>

 <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-form ml-auto" style="margin: 0!important;">
                        <form action="">
                            <div style="display: flex">

                                <div class="col-4">
                                    <input type="text" class="form-control" name="record_id"  placeholder="质押编号" value="{$params.record_id}">
                                </div>

                                <div class="col-4">
                                    <input type="text" class="form-control" name="fish_id"  placeholder="用户编号" value="{$params.fish_id}">
                                </div>

                                <div class="col-4">
                                    <select class="col-4" style="background-color: rgba(0,0,0,0.5);color: #fff;" name="type">
                                        <option value="0">类型</option>
                                        <option value="1" {if condition="$params.type eq 1"} selected{/if}>质押收益</option>
                                        <option value="2" {if condition="$params.type eq 2"} selected{/if}>矿机收益</option>
                                        <option value="3" {if condition="$params.type eq 3"} selected{/if}>代理收益</option>
                                    </select>
                                </div>

                                <div class="col-3">
                                    <button  class="btn btn-icon icon-left btn-primary">搜索</button >
                                </div>
                            </div>

                        </form>
                    </div>


                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-md">
                            <tr>
                                <th>编号</th>
                                <th>质押编号</th>
                                <th>期数</th>
                                <th>类型</th>
                                <th>用户编号</th>
                                <th>收益金额</th>
                                <th>备注</th>
                                <th>记录时间</th>
<!--                                <th class="text-center">操作</th>-->
                            </tr>
                            {volist name="data" id="vo" key="key"}
                            <tr>
                                <td>{$vo.id}</td>
                                <td>{$vo.record_id}</td>
                                <td>{$vo.period}</td>
                                <td>{$vo.type_text}</td>
                                <td>{$vo.fish_id}</td>
                                <td>{$vo.money}</td>
                                <td>{$vo.remark}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            </tr>
                            {/volist}
                        </table>
                    </div>
                </div>

                <div class="card-footer text-right">
                    <nav class="d-inline-block">
                        {$data|raw}
                    </nav>
                </div>
            </div>
        </div>
    </div>
{/block}


{block name="extend-script"}
<script type="text/javascript">
    // function enable(id) {
    //     $.ajax({
    //         url: "{:url('Mining/enable')}",
    //         method: 'post',
    //         data: {id: id},
    //         dataType: 'json',
    //         success(res) {
    //             if (res.success === true) {
    //                 swal('操作成功', {buttons: false, icon: 'success'});
    //                 setTimeout(function () { location.reload() }, 1500)
    //             }
    //             if (res.success === false) swal('出现错误', res.err_msg, 'error');
    //         }
    //     })
    // }

    function delImage(id) {
        swal({
            title: '确定删除该数据？',
            icon: 'warning',
            buttons: ['取消', '确认'],
            dangerMode: true,
        })
            .then((willDelete) => {
                if (! willDelete)
                    return;

                $.ajax({
                    url: "{:url('Mining/destroy_record')}",
                    method: 'post',
                    data: {id: id},
                    dataType: 'json',
                    success(res) {
                        if (res.success === true) {
                            swal('操作成功', {buttons: false, icon: 'success'});
                            setTimeout(function () { location.reload() }, 1500)
                        }
                        if (res.success === false) swal('出现错误', res.err_msg, 'error');
                    }
                })
            });
    }
</script>
{/block}
