<?php

namespace app\admin\controller;

use app\common\service\Web3MonitorService;
use think\Request;
use think\Db;

/**
 * Web3监控告警控制器
 */
class Web3Monitor extends Base
{
    public function __construct()
    {
        parent::__construct();
        $this->checkAdmin();
    }

    /**
     * 监控仪表板
     */
    public function dashboard()
    {
        // 获取监控统计
        $stats = Web3MonitorService::getMonitoringStats();
        
        // 获取最近24小时的登录统计
        $loginStats = $this->getLoginStats(24);
        
        // 获取链使用统计
        $chainStats = $this->getChainStats(24);
        
        // 获取最新告警
        $recentAlerts = array_slice($stats['alerts'], 0, 10);

        return $this->fetch('', [
            'stats' => $stats,
            'loginStats' => $loginStats,
            'chainStats' => $chainStats,
            'recentAlerts' => $recentAlerts
        ]);
    }

    /**
     * 执行监控检查
     */
    public function runCheck(Request $request)
    {
        if (!$request->isPost()) {
            return $this->errorJson(405, '请求方法不允许');
        }

        $timeRange = $request->post('time_range', 3600); // 默认1小时

        try {
            $alerts = Web3MonitorService::runFullMonitoring($timeRange);
            
            // 发送告警通知
            Web3MonitorService::sendAlertNotifications($alerts);

            return $this->successJson('监控检查完成', [
                'alert_count' => count($alerts),
                'alerts' => $alerts
            ]);

        } catch (\Exception $e) {
            return $this->errorJson(500, '监控检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 告警列表
     */
    public function alerts()
    {
        $stats = Web3MonitorService::getMonitoringStats();
        $alerts = $stats['alerts'] ?? [];

        // 按级别分组
        $groupedAlerts = [
            'critical' => [],
            'error' => [],
            'warning' => [],
            'info' => []
        ];

        foreach ($alerts as $alert) {
            $level = $alert['level'];
            if (isset($groupedAlerts[$level])) {
                $groupedAlerts[$level][] = $alert;
            }
        }

        return $this->fetch('', [
            'groupedAlerts' => $groupedAlerts,
            'totalAlerts' => count($alerts),
            'lastCheckTime' => $stats['last_check_time']
        ]);
    }

    /**
     * 登录分析
     */
    public function loginAnalysis()
    {
        $timeRange = input('time_range', 24); // 默认24小时
        
        // 获取登录趋势数据
        $trendData = $this->getLoginTrend($timeRange);
        
        // 获取失败原因分析
        $failureAnalysis = $this->getFailureAnalysis($timeRange);
        
        // 获取IP地理分布
        $ipDistribution = $this->getIPDistribution($timeRange);
        
        // 获取钱包类型分布
        $walletTypeDistribution = $this->getWalletTypeDistribution($timeRange);

        return $this->fetch('', [
            'trendData' => $trendData,
            'failureAnalysis' => $failureAnalysis,
            'ipDistribution' => $ipDistribution,
            'walletTypeDistribution' => $walletTypeDistribution,
            'timeRange' => $timeRange
        ]);
    }

    /**
     * 安全分析
     */
    public function securityAnalysis()
    {
        $timeRange = input('time_range', 24); // 默认24小时
        
        // 获取可疑活动
        $suspiciousActivities = $this->getSuspiciousActivities($timeRange);
        
        // 获取签名安全统计
        $signatureStats = $this->getSignatureStats($timeRange);
        
        // 获取风险IP列表
        $riskIPs = $this->getRiskIPs($timeRange);

        return $this->fetch('', [
            'suspiciousActivities' => $suspiciousActivities,
            'signatureStats' => $signatureStats,
            'riskIPs' => $riskIPs,
            'timeRange' => $timeRange
        ]);
    }

    /**
     * 系统健康检查
     */
    public function healthCheck()
    {
        $healthData = [
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'storage' => $this->checkStorageHealth(),
            'performance' => $this->checkPerformanceHealth()
        ];

        $overallHealth = $this->calculateOverallHealth($healthData);

        return $this->fetch('', [
            'healthData' => $healthData,
            'overallHealth' => $overallHealth
        ]);
    }

    /**
     * 导出监控报告
     */
    public function exportReport()
    {
        $timeRange = input('time_range', 24);
        $format = input('format', 'json'); // json, csv, pdf

        try {
            $reportData = $this->generateReport($timeRange);

            switch ($format) {
                case 'csv':
                    return $this->exportCSV($reportData);
                case 'pdf':
                    return $this->exportPDF($reportData);
                default:
                    return json($reportData);
            }

        } catch (\Exception $e) {
            return $this->errorJson(500, '导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取登录统计
     * @param int $hours 小时数
     * @return array
     */
    private function getLoginStats($hours)
    {
        $startTime = time() - ($hours * 3600);

        $totalLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->count();

        $successLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 1)
            ->count();

        $failedLogins = $totalLogins - $successLogins;
        $successRate = $totalLogins > 0 ? ($successLogins / $totalLogins) * 100 : 0;

        return [
            'total' => $totalLogins,
            'success' => $successLogins,
            'failed' => $failedLogins,
            'success_rate' => round($successRate, 2)
        ];
    }

    /**
     * 获取链使用统计
     * @param int $hours 小时数
     * @return array
     */
    private function getChainStats($hours)
    {
        $startTime = time() - ($hours * 3600);

        $chainStats = Db::table('web3_login_logs l')
            ->join('wallet_nonces n', 'l.nonce = n.nonce', 'left')
            ->where('l.create_time', '>=', $startTime)
            ->where('l.login_status', 1)
            ->group('n.chain_key')
            ->field('n.chain_key, count(*) as login_count')
            ->select();

        return $chainStats;
    }

    /**
     * 获取登录趋势数据
     * @param int $hours 小时数
     * @return array
     */
    private function getLoginTrend($hours)
    {
        $startTime = time() - ($hours * 3600);
        $interval = $hours > 24 ? 3600 : 1800; // 超过24小时用1小时间隔，否则用30分钟

        $trendData = [];
        for ($i = 0; $i < $hours; $i++) {
            $periodStart = $startTime + ($i * $interval);
            $periodEnd = $periodStart + $interval;

            $successCount = Db::table('web3_login_logs')
                ->where('create_time', '>=', $periodStart)
                ->where('create_time', '<', $periodEnd)
                ->where('login_status', 1)
                ->count();

            $failedCount = Db::table('web3_login_logs')
                ->where('create_time', '>=', $periodStart)
                ->where('create_time', '<', $periodEnd)
                ->where('login_status', 0)
                ->count();

            $trendData[] = [
                'time' => date('H:i', $periodStart),
                'success' => $successCount,
                'failed' => $failedCount
            ];
        }

        return $trendData;
    }

    /**
     * 获取失败原因分析
     * @param int $hours 小时数
     * @return array
     */
    private function getFailureAnalysis($hours)
    {
        $startTime = time() - ($hours * 3600);

        // 这里可以根据错误信息或其他字段分析失败原因
        $failedLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->field('ip_address, wallet_address, create_time')
            ->select();

        // 简单的失败原因分类
        $reasons = [
            'invalid_signature' => 0,
            'expired_nonce' => 0,
            'network_error' => 0,
            'other' => 0
        ];

        foreach ($failedLogins as $log) {
            // 这里可以根据具体的错误信息进行分类
            $reasons['other']++;
        }

        return $reasons;
    }

    /**
     * 获取可疑活动
     * @param int $hours 小时数
     * @return array
     */
    private function getSuspiciousActivities($hours)
    {
        $startTime = time() - ($hours * 3600);

        // 频繁失败的IP
        $suspiciousIPs = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->group('ip_address')
            ->having('count(*) >= ?', [5])
            ->field('ip_address, count(*) as attempt_count')
            ->order('attempt_count', 'desc')
            ->select();

        return $suspiciousIPs;
    }

    /**
     * 检查数据库健康状态
     * @return array
     */
    private function checkDatabaseHealth()
    {
        try {
            $start = microtime(true);
            Db::query('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => 'healthy',
                'response_time' => round($responseTime, 2) . 'ms'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查缓存健康状态
     * @return array
     */
    private function checkCacheHealth()
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test';
            
            cache($testKey, $testValue, 60);
            $retrieved = cache($testKey);
            
            if ($retrieved === $testValue) {
                return ['status' => 'healthy'];
            } else {
                return ['status' => 'error', 'error' => 'Cache read/write failed'];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查存储健康状态
     * @return array
     */
    private function checkStorageHealth()
    {
        $logTableSize = Db::table('web3_login_logs')->count();
        $nonceTableSize = Db::table('wallet_nonces')->count();

        return [
            'status' => 'healthy',
            'log_table_size' => $logTableSize,
            'nonce_table_size' => $nonceTableSize
        ];
    }

    /**
     * 检查性能健康状态
     * @return array
     */
    private function checkPerformanceHealth()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');

        return [
            'status' => 'healthy',
            'memory_usage' => $this->formatBytes($memoryUsage),
            'memory_limit' => $memoryLimit
        ];
    }

    /**
     * 计算总体健康状态
     * @param array $healthData
     * @return string
     */
    private function calculateOverallHealth($healthData)
    {
        foreach ($healthData as $component) {
            if ($component['status'] === 'error') {
                return 'error';
            }
        }
        return 'healthy';
    }

    /**
     * 格式化字节数
     * @param int $bytes
     * @return string
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
