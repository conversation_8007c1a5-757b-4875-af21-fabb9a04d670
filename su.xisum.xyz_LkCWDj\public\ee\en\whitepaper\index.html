﻿<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta data-react-helmet="true" name="description"
    content="An introductory paper to Ethereum, published in 2013 before its launch.">
  <meta name="theme-color" content="#1c1ce1">
  <meta name="generator" content="Gatsby 4.7.1">
      <script>
  　window.onload = function(){
  　    console.log('页面加载完2成');
 　　const t = document.querySelectorAll("img[data-main-image]");
        for (let e of t) {
            e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"));
            const t = e.parentNode.querySelectorAll("source[data-srcset]");
            for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset");
            e.complete && (e.style.opacity = 1)
        }
　　}
      
  </script>
  <style data-href="/styles.92bedc857ac51bb6cf96.css" data-identity="gatsby-global-css">
    html {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-size: 1rem
    }

    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    main,
    menu,
    nav,
    section,
    summary {
      display: block
    }

    audio,
    canvas,
    progress,
    video {
      display: inline-block
    }

    audio:not([controls]) {
      display: none;
      height: 0
    }

    progress {
      vertical-align: baseline
    }

    [hidden],
    template {
      display: none
    }

    a {
      -webkit-text-decoration-skip: objects;
      background-color: transparent;
      text-decoration: none
    }

    a:active,
    a:hover {
      outline-width: 0
    }

    abbr[title] {
      border-bottom: none;
      text-decoration: underline;
      -webkit-text-decoration: underline dotted;
      text-decoration: underline dotted
    }

    b,
    strong {
      font-weight: inherit;
      font-weight: bolder
    }

    dfn {
      font-style: italic
    }

    h1 {
      font-size: 2em;
      margin: .67em 0
    }

    mark {
      background-color: #ff0;
      color: #000
    }

    small {
      font-size: 80%
    }

    sub,
    sup {
      font-size: 75%;
      line-height: 0;
      position: relative;
      vertical-align: baseline
    }

    sub {
      bottom: -.25em
    }

    sup {
      top: -.5em
    }

    img {
      border-style: none
    }

    svg:not(:root) {
      overflow: hidden
    }

    code,
    kbd,
    pre,
    samp {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: 1em
    }

    figure {
      margin: 1em 40px
    }

    hr {
      box-sizing: content-box;
      height: 0;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font: inherit;
      margin: 0
    }

    optgroup {
      font-weight: 700
    }

    button,
    input {
      overflow: visible
    }

    button,
    select {
      text-transform: none
    }

    [type=reset],
    [type=submit],
    button,
    html [type=button] {
      -webkit-appearance: button
    }

    [type=button]::-moz-focus-inner,
    [type=reset]::-moz-focus-inner,
    [type=submit]::-moz-focus-inner,
    button::-moz-focus-inner {
      border-style: none;
      padding: 0
    }

    [type=button]:-moz-focusring,
    [type=reset]:-moz-focusring,
    [type=submit]:-moz-focusring,
    button:-moz-focusring {
      outline: 1px dotted ButtonText
    }

    fieldset {
      border: 1px solid silver;
      margin: 0 2px;
      padding: .35em .625em .75em
    }

    legend {
      box-sizing: border-box;
      color: inherit;
      display: table;
      max-width: 100%;
      padding: 0;
      white-space: normal
    }

    textarea {
      overflow: auto
    }

    [type=checkbox],
    [type=radio] {
      box-sizing: border-box;
      padding: 0
    }

    [type=number]::-webkit-inner-spin-button,
    [type=number]::-webkit-outer-spin-button {
      height: auto
    }

    [type=search] {
      -webkit-appearance: textfield;
      outline-offset: -2px
    }

    [type=search]::-webkit-search-cancel-button,
    [type=search]::-webkit-search-decoration {
      -webkit-appearance: none
    }

    ::-webkit-input-placeholder {
      color: inherit;
      opacity: .54
    }

    ::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit
    }

    html {
      box-sizing: border-box;
      font: 100%/1.6em georgia, serif;
      overflow-y: scroll
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      word-wrap: break-word;
      -ms-font-feature-settings: "kern", "liga", "clig", "calt";
      -webkit-font-feature-settings: "kern", "liga", "clig", "calt";
      font-feature-settings: "kern", "liga", "clig", "calt";
      font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
      font-kerning: normal;
      font-weight: 400
    }

    img {
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      max-width: 100%;
      padding: 0
    }

    h1 {
      font-size: 2.25rem;
      font-weight: 500
    }

    h1,
    h2 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 0 0 1.45rem;
      padding: 0
    }

    h2 {
      font-size: 1.62671rem;
      font-weight: 700
    }

    h3 {
      font-size: 1.38316rem;
      font-weight: 500
    }

    h3,
    h4 {
      text-rendering: optimizeLegibility;
      color: inherit;
      line-height: 1.1;
      margin: 2rem 0 1.45rem;
      padding: 0
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600
    }

    h5 {
      font-size: 1rem;
      margin: 2rem 0 1.45rem
    }

    h5,
    h6 {
      text-rendering: optimizeLegibility;
      color: inherit;
      font-weight: 500;
      line-height: 1.1;
      padding: 0
    }

    h6 {
      font-size: .85028rem
    }

    h6,
    hgroup {
      margin: 0 0 1.45rem
    }

    hgroup {
      padding: 0
    }

    ol,
    ul {
      list-style-image: none;
      list-style-position: outside;
      margin: 0 0 1.45rem 1.45rem;
      padding: 0
    }

    dd,
    dl,
    figure,
    p {
      margin: 0 0 1.45rem;
      padding: 0
    }

    pre {
      word-wrap: normal;
      background: rgba(0, 0, 0, .04);
      border-radius: 3px;
      font-size: .85rem;
      line-height: 1.42;
      margin: 0 0 1.45rem;
      overflow: auto;
      padding: 1.45rem;
      white-space: pre-wrap
    }

    table {
      border-collapse: collapse;
      font-size: 1rem;
      line-height: 1.45rem;
      width: 100%
    }

    fieldset,
    table {
      margin: 0 0 1.45rem;
      padding: 0
    }

    blockquote {
      margin: 0 1.45rem 1.45rem;
      padding: 0
    }

    form,
    iframe,
    noscript {
      margin: 0 0 1.45rem;
      padding: 0
    }

    hr {
      background: rgba(0, 0, 0, .2);
      border: none;
      height: 1px;
      margin: 4rem 0 0;
      padding: 0
    }

    address {
      margin: 0 0 1.45rem;
      padding: 0
    }

    b,
    dt,
    strong,
    th {
      font-weight: 700
    }

    li {
      margin-bottom: .725rem
    }

    ol li,
    ul li {
      padding-left: 0
    }

    li>ol,
    li>ul {
      margin-bottom: .725rem;
      margin-left: 1.45rem;
      margin-top: .725rem
    }

    blockquote :last-child,
    li :last-child,
    p :last-child {
      margin-bottom: 0
    }

    li>p {
      margin-bottom: .725rem
    }

    code {
      font-size: 1em;
      line-height: 1.45em
    }

    kbd {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      font-size: .625rem;
      line-height: 1.56rem
    }

    samp {
      font-size: .85rem;
      line-height: 1.45rem
    }

    abbr,
    abbr[title],
    acronym {
      border-bottom: 1px dotted rgba(0, 0, 0, .5);
      cursor: help
    }

    abbr[title] {
      text-decoration: none
    }

    td,
    th,
    thead {
      text-align: left
    }

    td,
    th {
      font-feature-settings: "tnum";
      -moz-font-feature-settings: "tnum";
      -ms-font-feature-settings: "tnum";
      -webkit-font-feature-settings: "tnum";
      border-bottom: 1px solid hsla(0, 13%, 72%, .12);
      padding: .725rem .96667rem calc(.725rem - 1px)
    }

    td:first-child,
    th:first-child {
      padding-left: 0
    }

    td:last-child,
    th:last-child {
      padding-right: 0
    }

    tt {
      background-color: #2b2834;
      border-radius: 2px;
      color: #968af6
    }

    code,
    tt {
      font-family: SFMono-Regular, Consolas, Roboto Mono, Droid Sans Mono, Liberation Mono, Menlo, Courier, monospace;
      padding: .2em
    }

    code {
      background-color: rgba(0, 0, 0, .04);
      border-radius: 3px
    }

    pre code {
      background: none;
      line-height: 1.42
    }

    code:before,
    pre code:after,
    pre code:before,
    pre tt:after,
    pre tt:before,
    tt:after,
    tt:before {
      content: ""
    }

    @media only screen and (max-width:480px) {
      html {
        font-size: 100%
      }
    }

    .assets-page .gatsby-resp-image-wrapper {
      max-height: 200px !important
    }

    .assets-page .gatsby-resp-image-image {
      width: auto !important
    }
  </style>
  <link rel="icon" href="/favicon-32x32.png?v=8b512faa8d4a0b019c123a771b6622aa" type="image/png">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="anonymous">
  <link rel="apple-touch-icon" sizes="48x48" href="/icons/icon-48x48.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="256x256" href="/icons/icon-256x256.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png?v=8b512faa8d4a0b019c123a771b6622aa">
  <link rel="preconnect" href="https://matomo.ethstake.exchange">
  <link rel="sitemap" type="application/xml" href="/sitemap/sitemap-index.xml">
  <style type="text/css">
    .anchor.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }

    .anchor.after {
      display: inline-block;
      padding-left: 4px;
    }

    h1 .anchor svg,
    h2 .anchor svg,
    h3 .anchor svg,
    h4 .anchor svg,
    h5 .anchor svg,
    h6 .anchor svg {
      visibility: hidden;
    }

    h1:hover .anchor svg,
    h2:hover .anchor svg,
    h3:hover .anchor svg,
    h4:hover .anchor svg,
    h5:hover .anchor svg,
    h6:hover .anchor svg,
    h1 .anchor:focus svg,
    h2 .anchor:focus svg,
    h3 .anchor:focus svg,
    h4 .anchor:focus svg,
    h5 .anchor:focus svg,
    h6 .anchor:focus svg {
      visibility: visible;
    }
  </style>
  <script>
    document.addEventListener("DOMContentLoaded", function (event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
          var clientTop = document.documentElement.clientTop || document.body.clientTop || 0
          var offset = element.getBoundingClientRect().top + scrollTop - clientTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function () {
            window.scrollTo(0, offset - 0)
          }), 0)
        }
      }
    })
  </script>
  <title data-react-helmet="true">Ethereum Whitepaper | ethstake.exchange</title>
  <link data-react-helmet="true" rel="canonical" href="index.html">
  <script data-react-helmet="true" type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": "https://ethstake.exchange",
          "email": "<EMAIL>",
          "name": "Ethereum",
          "logo": "https://ethstake.exchange/og-image.png"
        }
      </script>
  <style>
    .gatsby-image-wrapper {
      position: relative;
      overflow: hidden
    }

    .gatsby-image-wrapper picture.object-fit-polyfill {
      position: static !important
    }

    .gatsby-image-wrapper img {
      bottom: 0;
      height: 100%;
      left: 0;
      margin: 0;
      max-width: none;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      object-fit: cover
    }

    .gatsby-image-wrapper [data-main-image] {
      opacity: 0;
      transform: translateZ(0);
      transition: opacity .25s linear;
      will-change: opacity
    }

    .gatsby-image-wrapper-constrained {
      display: inline-block;
      vertical-align: top
    }
  </style><noscript>
    <style>
      .gatsby-image-wrapper noscript [data-main-image] {
        opacity: 1 !important
      }

      .gatsby-image-wrapper [data-placeholder-image] {
        opacity: 0 !important
      }
    </style>
  </noscript>
  <script
    type="module">const e = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; e && document.body.addEventListener("load", (function (e) { if (void 0 === e.target.dataset.mainImage) return; if (void 0 === e.target.dataset.gatsbyImageSsr) return; const t = e.target; let a = null, n = t; for (; null === a && n;)void 0 !== n.parentNode.dataset.gatsbyImageWrapper && (a = n.parentNode), n = n.parentNode; const o = a.querySelector("[data-placeholder-image]"), r = new Image; r.src = t.currentSrc, r.decode().catch((() => { })).then((() => { t.style.opacity = 1, o && (o.style.opacity = 0, o.style.transition = "opacity 500ms linear") })) }), !0);</script>
  <style data-styled="" data-styled-version="5.3.3">
    body {
      background-color: #ffffff;
      color: #333333;
    }

    /*!sc*/
    a {
      color: #1c1cff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    mark {
      background: rgba(143, 187, 237, .1);
      box-shadow: inset 0 -2px 0 0 rgba(69, 142, 225, .8);
    }

    /*!sc*/
    .anchor.before {
      fill: #333333;
    }

    /*!sc*/
    hr {
      background: #ecececnull#1c1cff;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
      position: absolute;
    }

    /*!sc*/
    iframe {
      display: block;
      max-width: 560px;
      margin: 32px 0;
    }

    /*!sc*/
    h1 {
      font-size: 3rem;
      line-height: 1.4;
      margin: 2rem 0;
      font-weight: 700;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h1 {
        font-size: 2.5rem;
      }
    }

    /*!sc*/
    h2 {
      font-size: 2rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 3rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h2 {
        font-size: 1.5rem;
      }
    }

    /*!sc*/
    h3 {
      font-size: 1.5rem;
      line-height: 1.4;
      margin: 2rem 0;
      margin-top: 2.5rem;
      font-weight: 600;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h3 {
        font-size: 1.25rem;
      }
    }

    /*!sc*/
    h4 {
      font-size: 1.25rem;
      line-height: 1.4;
      font-weight: 500;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    @media (max-width:768px) {
      h4 {
        font-size: 1rem;
      }
    }

    /*!sc*/
    h5 {
      font-size: 1rem;
      line-height: 1.4;
      font-weight: 450;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    h6 {
      font-size: 0.9rem;
      line-height: 1.4;
      font-weight: 400;
      text-transform: uppercase;
      margin: 2rem 0;
      -webkit-scroll-margin-top: 4.75rem;
      -moz-scroll-margin-top: 4.75rem;
      -ms-scroll-margin-top: 4.75rem;
      scroll-margin-top: 4.75rem;
      -webkit-scroll-snap-margin: 4.75rem;
      -moz-scroll-snap-margin: 4.75rem;
      -ms-scroll-snap-margin: 4.75rem;
      scroll-snap-margin: 4.75rem;
    }

    /*!sc*/
    data-styled.g1[id="sc-global-hcwgEG1"] {
      content: "sc-global-hcwgEG1,"
    }

    /*!sc*/
    .iylOGp {
      fill: #b2b2b2;
    }

    /*!sc*/
    .iylOGp:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g2[id="Icon__StyledIcon-sc-1o8zi5s-0"] {
      content: "iylOGp,"
    }

    /*!sc*/
    .gABYms:after {
      margin-left: 0.125em;
      margin-right: 0.3em;
      display: inline;
      content: "â†—";
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out;
      font-style: normal;
    }

    /*!sc*/
    .gABYms:hover:after {
      -webkit-transform: translate(0.15em, -0.2em);
      -ms-transform: translate(0.15em, -0.2em);
      transform: translate(0.15em, -0.2em);
    }

    /*!sc*/
    data-styled.g3[id="Link__ExternalLink-sc-e3riao-0"] {
      content: "gABYms,"
    }

    /*!sc*/
    .gCWUlE .is-glossary {
      white-space: nowrap;
    }

    /*!sc*/
    .gCWUlE.active {
      color: #1c1cff;
    }

    /*!sc*/
    .gCWUlE:hover svg {
      fill: #1c1cff;
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g4[id="Link__InternalLink-sc-e3riao-1"] {
      content: "gCWUlE,"
    }

    /*!sc*/
    .iEXhBV.active {
      color: #1c1cff;
    }

    /*!sc*/
    data-styled.g5[id="Link__ExplicitLangInternalLink-sc-e3riao-2"] {
      content: "iEXhBV,"
    }

    /*!sc*/
    .jfMIWk {
      margin: 0 0.25rem 0 0.35rem;
      fill: #4949ff;
      -webkit-text-decoration: underline;
      text-decoration: underline;
    }

    /*!sc*/
    .jfMIWk:hover {
      -webkit-transition: -webkit-transform 0.1s;
      -webkit-transition: transform 0.1s;
      transition: transform 0.1s;
      -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
      transform: scale(1.2);
    }

    /*!sc*/
    data-styled.g6[id="Link__GlossaryIcon-sc-e3riao-3"] {
      content: "jfMIWk,"
    }

    /*!sc*/
    .gvoBKJ {
      padding-top: 3rem;
      padding-bottom: 4rem;
      padding: 1rem 2rem;
    }

    /*!sc*/
    data-styled.g7[id="Footer__StyledFooter-sc-1to993d-0"] {
      content: "gvoBKJ,"
    }

    /*!sc*/
    .kFKfdz {
      font-size: 0.875rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    /*!sc*/
    data-styled.g8[id="Footer__FooterTop-sc-1to993d-1"] {
      content: "kFKfdz,"
    }

    /*!sc*/
    .bWGwos {
      color: #666666;
    }

    /*!sc*/
    data-styled.g9[id="Footer__LastUpdated-sc-1to993d-2"] {
      content: "bWGwos,"
    }

    /*!sc*/
    .hlbLsM {
      display: grid;
      grid-template-columns: repeat(6, auto);
      grid-gap: 1rem;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1300px) {
      .hlbLsM {
        grid-template-columns: repeat(3, auto);
      }
    }

    /*!sc*/
    @media (max-width:768px) {
      .hlbLsM {
        grid-template-columns: repeat(2, auto);
      }
    }

    /*!sc*/
    @media (max-width:500px) {
      .hlbLsM {
        grid-template-columns: auto;
      }
    }

    /*!sc*/
    data-styled.g10[id="Footer__LinkGrid-sc-1to993d-3"] {
      content: "hlbLsM,"
    }

    /*!sc*/
    .bbCEKr {
      font-size: 0.875rem;
      line-height: 1.6;
      margin: 1.14em 0;
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g12[id="Footer__SectionHeader-sc-1to993d-5"] {
      content: "bbCEKr,"
    }

    /*!sc*/
    .gjQPMc {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      margin: 0;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g13[id="Footer__List-sc-1to993d-6"] {
      content: "gjQPMc,"
    }

    /*!sc*/
    .eGhJJx {
      margin-bottom: 1rem;
    }

    /*!sc*/
    data-styled.g14[id="Footer__ListItem-sc-1to993d-7"] {
      content: "eGhJJx,"
    }

    /*!sc*/
    .gIpSoz {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #666666;
    }

    /*!sc*/
    .gIpSoz svg {
      fill: #666666;
    }

    /*!sc*/
    .gIpSoz:after {
      color: #666666;
    }

    /*!sc*/
    .gIpSoz:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover:after {
      color: #1c1cff;
    }

    /*!sc*/
    .gIpSoz:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g15[id="Footer__FooterLink-sc-1to993d-8"] {
      content: "gIpSoz,"
    }

    /*!sc*/
    .kdLbod {
      margin: 1rem 0;
    }

    /*!sc*/
    data-styled.g16[id="Footer__SocialIcons-sc-1to993d-9"] {
      content: "kdLbod,"
    }

    /*!sc*/
    .iedzfy {
      margin-left: 1rem;
      width: 2rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .iedzfy {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g17[id="Footer__SocialIcon-sc-1to993d-10"] {
      content: "iedzfy,"
    }

    /*!sc*/
    .drElXa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      margin-right: 1.5rem;
    }

    /*!sc*/
    .drElXa:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g20[id="Dropdown__DropdownTitle-sc-1yd08gi-1"] {
      content: "drElXa,"
    }

    /*!sc*/
    .ldsPWM {
      margin: 0;
      position: absolute;
      margin-top: -1rem;
      list-style-type: none;
      list-style-image: none;
      top: 100%;
      width: auto;
      border-radius: 0.5em;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      opacity:0;
      display:none;
      transform:rotateX(-15deg) translateZ(0)
    }

    /*!sc*/
    data-styled.g21[id="Dropdown__DropdownList-sc-1yd08gi-2"] {
      content: "ldsPWM,"
    }

    /*!sc*/
    .Mkofa {
      white-space: nowrap;
      margin: 0;
      color: #333333;
    }

    /*!sc*/
     .Mkofa:hover >ul{
      color: #1c1cff;
      opacity:1;
      display:block;
      transform: none;
    }

    /*!sc*/
    data-styled.g23[id="Dropdown__NavListItem-sc-1yd08gi-4"] {
      content: "Mkofa,"
    }

    /*!sc*/
    .lgeotR {
      margin: 0;
      color: #333333;
    }

    /*!sc*/
    .lgeotR:hover {
      color: #1c1cff;
      background: #f2f2f2;
    }

    /*!sc*/
    data-styled.g24[id="Dropdown__DropdownItem-sc-1yd08gi-5"] {
      content: "lgeotR,"
    }

    /*!sc*/
    .cTcxIB {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: block;
      padding: 0.5rem;
      color: #333333;
    }

    /*!sc*/
    .cTcxIB svg {
      fill: #666666;
    }

    /*!sc*/
    .cTcxIB:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cTcxIB:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g25[id="Dropdown__NavLink-sc-1yd08gi-6"] {
      content: "cTcxIB,"
    }

    /*!sc*/
    .ivCgwn {
      display: inline-block;
      margin-left: 0.5rem;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    .ivCgwn>img {
      width: 1.5em !important;
      height: 1.5em !important;
      margin: 0 !important;
    }

    /*!sc*/
    .hLjau {
      display: inline-block;
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 0;
      margin-left: 0;
    }

    /*!sc*/
    .hLjau>img {
      width: 3em !important;
      height: 3em !important;
      margin: 0 !important;
    }

    /*!sc*/
    data-styled.g27[id="Emoji__StyledEmoji-sc-ihpuqw-0"] {
      content: "ivCgwn,hLjau,"
    }

    /*!sc*/
    .dUatah {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: none;
      border: none;
      color: inherit;
      display: inline-block;
      font: inherit;
      padding: initial;
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g28[id="NakedButton-sc-1g43w8v-0"] {
      content: "dUatah,"
    }

    /*!sc*/
    .eoyKpR {
      margin: 0;
      position: relative;
      border-radius: 0.25em;
    }

    /*!sc*/
    data-styled.g29[id="Input__Form-sc-1utkal6-0"] {
      content: "eoyKpR,"
    }

    /*!sc*/
    .kkfPkW {
      border: 1px solid #7f7f7f;
      color: #333333;
      background: #ffffff;
      padding: 0.5rem;
      padding-right: 2rem;
      border-radius: 0.25em;
      width: 100%;
    }

    /*!sc*/
    .kkfPkW:focus {
      outline: #1c1cff auto 1px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .kkfPkW {
        padding-left: 2rem;
      }
    }

    /*!sc*/
    data-styled.g30[id="Input__StyledInput-sc-1utkal6-1"] {
      content: "kkfPkW,"
    }

    /*!sc*/
    .gFzMVg {
      position: absolute;
      right: 6px;
      top: 50%;
      margin-top: -12px;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .gFzMVg {
        left: 6px;
      }
    }

    /*!sc*/
    data-styled.g31[id="Input__SearchIcon-sc-1utkal6-2"] {
      content: "gFzMVg,"
    }

    /*!sc*/
    .ggVPUc {
      border: 1px solid #7f7f7f;
      border-radius: 0.25em;
      color: #333333;
      display: none;
      margin-bottom: 0;
      padding: 0 6px;
      position: absolute;
      right: 6px;
      top: 20%;
    }

    /*!sc*/
    @media only screen and (min-width:1024px) {
      .ggVPUc {
        display: inline-block;
      }
    }

    /*!sc*/
    data-styled.g32[id="Input__SearchSlash-sc-1utkal6-3"] {
      content: "ggVPUc,"
    }

    /*!sc*/
    .kNenpg {
      position: relative;
      display: grid;
      grid-gap: 1em;
    }

    /*!sc*/
    data-styled.g33[id="Search__Root-sc-1qm8xwy-0"] {
      content: "kNenpg,"
    }

    /*!sc*/
    .eJIgkk {
      display: none;
      max-height: 80vh;
      overflow: scroll;
      z-index: 2;
      position: absolute;
      right: 0;
      top: calc(100% + 0.5em);
      width: 80vw;
      max-width: 30em;
      box-shadow: 0 0 5px 0;
      padding: 0.5rem;
      background: #ffffff;
      border-radius: 0.25em;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .eJIgkk {
        width: 100%;
      }
    }

    /*!sc*/
    .eJIgkk>*+* {
      padding-top: 1em !important;
      border-top: 2px solid black;
    }

    /*!sc*/
    .eJIgkk li {
      margin-bottom: 0.4rem;
    }

    /*!sc*/
    .eJIgkk li+li {
      padding-top: 0.7em;
      border-top: 1px solid #ececec;
    }

    /*!sc*/
    .eJIgkk ul {
      margin: 0;
      list-style: none;
    }

    /*!sc*/
    .eJIgkk mark {
      color: #1c1cff;
      box-shadow: inset 0 -2px 0 0 rgba(143, 187, 237, .5);
    }

    /*!sc*/
    .eJIgkk header {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk header h3 {
      color: #ffffff;
      background: #4c4c4c;
      padding: 0.1em 0.4em;
      border-radius: 0.25em;
    }

    /*!sc*/
    .eJIgkk h3 {
      margin: 0 0 0.5em;
    }

    /*!sc*/
    .eJIgkk h4 {
      margin-bottom: 0.3em;
    }

    /*!sc*/
    .eJIgkk a {
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    data-styled.g34[id="Search__HitsWrapper-sc-1qm8xwy-1"] {
      content: "eJIgkk,"
    }

    /*!sc*/
    .jEZlpP {
      -webkit-text-decoration: none;
      text-decoration: none;
      margin-right: 2rem;
      color: #333333;
    }

    /*!sc*/
    .jEZlpP svg {
      fill: #666666;
    }

    /*!sc*/
    .jEZlpP:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .jEZlpP:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    .jEZlpP.active {
      font-weight: bold;
    }

    /*!sc*/
    data-styled.g52[id="SharedStyledComponents__NavLink-sc-1cr9zfr-11"] {
      content: "jEZlpP,"
    }

    /*!sc*/
    .iuRocQ {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iuRocQ:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    .gVLXss {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      white-space: nowrap;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
      margin-left: 0.5rem;
    }

    /*!sc*/
    .gVLXss:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    /*!sc*/
    data-styled.g59[id="SharedStyledComponents__Button-sc-1cr9zfr-18"] {
      content: "iuRocQ,gVLXss,"
    }

    /*!sc*/
    .bphJHH {
      background-color: #1c1cff;
      color: #ffffff;
      border: 1px solid transparent;
    }

    /*!sc*/
    .bphJHH:hover {
      background-color: rgba(28, 28, 225, 0.8);
    }

    /*!sc*/
    .bphJHH:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g60[id="SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19"] {
      content: "bphJHH,"
    }

    /*!sc*/
    .fMmGqe {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .fMmGqe:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .fMmGqe:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g61[id="SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20"] {
      content: "fMmGqe,"
    }

    /*!sc*/
    .jjiFHW {
      font-size: 1rem;
      margin: 2rem 0 1rem;
      color: #4c4c4c;
    }

    /*!sc*/
    data-styled.g62[id="SharedStyledComponents__Paragraph-sc-1cr9zfr-21"] {
      content: "jjiFHW,"
    }

    /*!sc*/
    .jNnLMQ {
      font-weight: 700;
    }

    /*!sc*/
    .jNnLMQ:before {
      content: "";
      display: block;
      height: 140px;
      margin-top: -140px;
      visibility: hidden;
    }

    /*!sc*/
    .jNnLMQ a {
      display: none;
    }

    /*!sc*/
    data-styled.g63[id="SharedStyledComponents__Header1-sc-1cr9zfr-22"] {
      content: "jNnLMQ,"
    }

    /*!sc*/
    .iiNgGY {
      font-weight: 700;
      position: inherit !important;
    }

    /*!sc*/
    .iiNgGY:before {
      content: "";
      display: block;
      height: 120px;
      margin-top: -120px;
      visibility: hidden;
    }

    /*!sc*/
    .iiNgGY a.header-anchor {
      position: relative;
      display: initial;
      opacity: 0;
      margin-left: -1.5em;
      padding-right: 0.5rem;
      font-size: 1rem;
      vertical-align: middle;
    }

    /*!sc*/
    .iiNgGY a.header-anchor:hover {
      display: initial;
      fill: #1c1cff;
      opacity: 1;
    }

    /*!sc*/
    .iiNgGY:hover a.header-anchor {
      display: initial;
      fill: #1c1cff;
      opacity: 1;
    }

    /*!sc*/
    data-styled.g64[id="SharedStyledComponents__Header2-sc-1cr9zfr-23"] {
      content: "iiNgGY,"
    }

    /*!sc*/
    .fWQpXW {
      position: inherit !important;
    }

    /*!sc*/
    .fWQpXW:before {
      content: "";
      display: block;
      height: 120px;
      margin-top: -120px;
      visibility: hidden;
    }

    /*!sc*/
    .fWQpXW a.header-anchor {
      position: relative;
      display: initial;
      opacity: 0;
      margin-left: -1.5em;
      padding-right: 0.5rem;
      font-size: 1rem;
      vertical-align: middle;
    }

    /*!sc*/
    .fWQpXW a.header-anchor:hover {
      display: initial;
      fill: #1c1cff;
      opacity: 1;
    }

    /*!sc*/
    .fWQpXW:hover a.header-anchor {
      display: initial;
      fill: #1c1cff;
      opacity: 1;
    }

    /*!sc*/
    data-styled.g65[id="SharedStyledComponents__Header3-sc-1cr9zfr-24"] {
      content: "fWQpXW,"
    }

    /*!sc*/
    .kiZonN {
      color: #4c4c4c;
    }

    /*!sc*/
    data-styled.g68[id="SharedStyledComponents__ListItem-sc-1cr9zfr-27"] {
      content: "kiZonN,"
    }

    /*!sc*/
    .hhdXUp {
      display: none;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .hhdXUp {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
      }
    }

    /*!sc*/
    data-styled.g73[id="Mobile__Container-sc-zxc8gm-0"] {
      content: "hhdXUp,"
    }

    /*!sc*/
    .dUGGTH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g74[id="Mobile__MenuIcon-sc-zxc8gm-1"] {
      content: "dUGGTH,"
    }

    /*!sc*/
    .dLNRLx {
      margin-left: 1rem;
    }

    /*!sc*/
    data-styled.g75[id="Mobile__MenuButton-sc-zxc8gm-2"] {
      content: "dLNRLx,"
    }

    /*!sc*/
    .hvwyGc {
      margin-right: 1rem;
    }

    /*!sc*/
    data-styled.g76[id="Mobile__OtherIcon-sc-zxc8gm-3"] {
      content: "hvwyGc,"
    }

    /*!sc*/
    .bCHBHX {
      position: fixed;
      background: hsla(0, 0%, 69.8%, 0.9);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100vh;
    }

    /*!sc*/
    data-styled.g77[id="Mobile__MobileModal-sc-zxc8gm-4"] {
      content: "bCHBHX,"
    }

    /*!sc*/
    .AJukL {
      background: #ffffff;
      z-index: 99;
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      overflow: hidden;
      width: 100%;
      max-width: 450px;
    }

    /*!sc*/
    data-styled.g78[id="Mobile__MenuContainer-sc-zxc8gm-5"] {
      content: "AJukL,"
    }

    /*!sc*/
    .gbspKa {
      margin: 0 0.125rem;
      width: 1.5rem;
      height: 2.5rem;
      position: relative;
      stroke-width: 2px;
      z-index: 100;
    }

    /*!sc*/
    .gbspKa>path {
      stroke: #333333;
      fill: none;
    }

    /*!sc*/
    .gbspKa:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .gbspKa:hover>path {
      stroke: #1c1cff;
    }

    /*!sc*/
    data-styled.g79[id="Mobile__GlyphButton-sc-zxc8gm-6"] {
      content: "gbspKa,"
    }

    /*!sc*/
    .gBSEi {
      z-index: 101;
      padding: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    /*!sc*/
    data-styled.g80[id="Mobile__SearchContainer-sc-zxc8gm-7"] {
      content: "gBSEi,"
    }

    /*!sc*/
    .iXlChz {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .iXlChz>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g81[id="Mobile__SearchHeader-sc-zxc8gm-8"] {
      content: "iXlChz,"
    }

    /*!sc*/
    .jmriUx {
      z-index: 102;
      cursor: pointer;
    }

    /*!sc*/
    .jmriUx>svg {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g82[id="Mobile__CloseIconContainer-sc-zxc8gm-9"] {
      content: "jmriUx,"
    }

    /*!sc*/
    .gYetwr {
      margin: 0;
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 3rem 1rem 8rem;
    }

    /*!sc*/
    data-styled.g83[id="Mobile__MenuItems-sc-zxc8gm-10"] {
      content: "gYetwr,"
    }

    /*!sc*/
    .gXxMFO {
      margin: 0;
      margin-bottom: 3rem;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g84[id="Mobile__NavListItem-sc-zxc8gm-11"] {
      content: "gXxMFO,"
    }

    /*!sc*/
    .kuWShR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin: 0;
    }

    /*!sc*/
    data-styled.g85[id="Mobile__StyledNavLink-sc-zxc8gm-12"] {
      content: "kuWShR,"
    }

    /*!sc*/
    .erCTXJ {
      margin: 1rem 0;
      color: #333333;
    }

    /*!sc*/
    data-styled.g86[id="Mobile__SectionTitle-sc-zxc8gm-13"] {
      content: "erCTXJ,"
    }

    /*!sc*/
    .hghxUt {
      margin: 0;
    }

    /*!sc*/
    data-styled.g87[id="Mobile__SectionItems-sc-zxc8gm-14"] {
      content: "hghxUt,"
    }

    /*!sc*/
    .kdRQoZ {
      margin-bottom: 1rem;
      list-style-type: none;
      list-style-image: none;
      opacity: 0.7;
    }

    /*!sc*/
    .kdRQoZ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g88[id="Mobile__SectionItem-sc-zxc8gm-15"] {
      content: "kdRQoZ,"
    }

    /*!sc*/
    .iYttIj {
      background: #ffffff;
      border-top: 1px solid #ececec;
      padding-right: 1rem;
      padding-left: 1rem;
      margin-top: auto;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 108px;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      width: 100%;
      max-width: 450px;
      z-index: 99;
    }

    /*!sc*/
    data-styled.g89[id="Mobile__BottomMenu-sc-zxc8gm-16"] {
      content: "iYttIj,"
    }

    /*!sc*/
    .cnajxM {
      -webkit-flex: 1 1 120px;
      -ms-flex: 1 1 120px;
      flex: 1 1 120px;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
      color: #333333;
    }

    /*!sc*/
    .cnajxM>svg {
      fill: #333333;
    }

    /*!sc*/
    .cnajxM:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .cnajxM:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g90[id="Mobile__BottomItem-sc-zxc8gm-17"] {
      content: "cnajxM,"
    }

    /*!sc*/
    .heSUpS {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      color: #333333;
    }

    /*!sc*/
    .heSUpS>svg {
      fill: #333333;
    }

    /*!sc*/
    .heSUpS:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .heSUpS:hover>svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g91[id="Mobile__BottomLink-sc-zxc8gm-18"] {
      content: "heSUpS,"
    }

    /*!sc*/
    .hkZTkJ {
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      margin-top: 0.5rem;
      text-transform: uppercase;
      text-align: center;
      opacity: 0.7;
    }

    /*!sc*/
    .hkZTkJ:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g92[id="Mobile__BottomItemText-sc-zxc8gm-19"] {
      content: "hkZTkJ,"
    }

    /*!sc*/
    .jBipln {
      color: #333333;
      background: #f2f2f2;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 10vw;
      -webkit-align-self: center;
      -ms-flex-item-align: center;
      align-self: center;
      width: 280px;
      width: min(60vw, 280px);
      height: 280px;
      height: min(60vw, 280px);
      border-radius: 100%;
    }

    /*!sc*/
    data-styled.g93[id="Mobile__BlankSearchState-sc-zxc8gm-20"] {
      content: "jBipln,"
    }

    /*!sc*/
    .iGuESw {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 1000;
      width: 100%;
    }

    /*!sc*/
    data-styled.g94[id="Nav__NavContainer-sc-1aprtmp-0"] {
      content: "iGuESw,"
    }

    /*!sc*/
    .cpomzd {
      height: 4.75rem;
      padding: 1rem 2rem;
      box-sizing: border-box;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    /*!sc*/
    data-styled.g95[id="Nav__StyledNav-sc-1aprtmp-1"] {
      content: "cpomzd,"
    }

    /*!sc*/
    .faUCsG {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      width: 100%;
      max-width: 1440px;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .faUCsG {
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
      }
    }

    /*!sc*/
    data-styled.g97[id="Nav__NavContent-sc-1aprtmp-3"] {
      content: "faUCsG,"
    }

    /*!sc*/
    .gjaVMk {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gjaVMk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g98[id="Nav__InnerContent-sc-1aprtmp-4"] {
      content: "gjaVMk,"
    }

    /*!sc*/
    .jUJHKw {
      margin: 0;
      margin-left: 2rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      list-style-type: none;
      list-style-image: none;
    }

    /*!sc*/
    data-styled.g99[id="Nav__LeftItems-sc-1aprtmp-5"] {
      content: "jUJHKw,"
    }

    /*!sc*/
    .kQWBtS {
      margin: 0;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g100[id="Nav__RightItems-sc-1aprtmp-6"] {
      content: "kQWBtS,"
    }

    /*!sc*/
    .jODkFW {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-right: 0;
      margin-left: 1rem;
    }

    /*!sc*/
    .jODkFW:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g102[id="Nav__RightNavLink-sc-1aprtmp-8"] {
      content: "jODkFW,"
    }

    /*!sc*/
    .igUcis {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    data-styled.g103[id="Nav__HomeLogoNavLink-sc-1aprtmp-9"] {
      content: "igUcis,"
    }

    /*!sc*/
    .euWmfq {
      opacity: 0.85;
    }

    /*!sc*/
    .euWmfq:hover {
      opacity: 1;
    }

    /*!sc*/
    data-styled.g104[id="Nav__HomeLogo-sc-1aprtmp-10"] {
      content: "euWmfq,"
    }

    /*!sc*/
    .bDRFLa {
      padding-left: 0.5rem;
    }

    /*!sc*/
    data-styled.g105[id="Nav__Span-sc-1aprtmp-11"] {
      content: "bDRFLa,"
    }

    /*!sc*/
    .hwxIMf {
      margin-left: 1rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    .hwxIMf:hover svg {
      fill: #1c1cff;
    }

    /*!sc*/
    data-styled.g106[id="Nav__ThemeToggle-sc-1aprtmp-12"] {
      content: "hwxIMf,"
    }

    /*!sc*/
    .jOKVBH {
      fill: #333333;
    }

    /*!sc*/
    data-styled.g107[id="Nav__NavIcon-sc-1aprtmp-13"] {
      content: "jOKVBH,"
    }

    /*!sc*/
    .iMiHPL {
      -webkit-text-decoration: none;
      text-decoration: none;
      display: inline-block;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      border-radius: 0.25em;
      text-align: center;
      cursor: pointer;
    }

    /*!sc*/
    .iMiHPL function parse(props) {
      -webkit-var: shouldSort=false;
      -moz-var: shouldSort=false;
      -ms-var: shouldSort=false;
      var: shouldSort=false;
      -webkit-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -moz-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      -ms-var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      var: isCacheDisabled=props.theme && props.theme.disableStyledSystemCache;
      return: styles;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) {
      if: ( !config[key]) continue;
      -webkit-var: sx=config[key];
      -moz-var: sx=config[key];
      -ms-var: sx=config[key];
      var: sx=config[key];
      -webkit-var: raw=props[key];
      -moz-var: raw=props[key];
      -ms-var: raw=props[key];
      var: raw=props[key];
      -webkit-var: scale=get(props.theme, sx.scale, sx.defaults);
      -moz-var: scale=get(props.theme, sx.scale, sx.defaults);
      -ms-var: scale=get(props.theme, sx.scale, sx.defaults);
      var: scale=get(props.theme, sx.scale, sx.defaults);
      object_assign_default()(styles, sx(raw, scale, props));
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') {
      cache.breakpoints: = !isCacheDisabled && cache.breakpoints || get(props.theme, 'breakpoints', defaults.breakpoints);
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (Array.isArray(raw)) {
      cache.media: = !isCacheDisabled && cache.media || [null].concat(cache.breakpoints.map(createMediaQuery));
      styles: =merge(styles, parseResponsiveStyle(cache.media, sx, scale, raw, props));
      continue;
    }

    /*!sc*/
    .iMiHPL function parse(props) for (var key in props) if (typeof raw==='object') if (raw !==null) {
      styles: =merge(styles, parseResponsiveObject(cache.breakpoints, sx, scale, raw, props));
      shouldSort: =true;
    }

    /*!sc*/
    .iMiHPL function parse(props) if (shouldSort) {
      styles: =sort(styles);
    }

    /*!sc*/
    data-styled.g125[id="ButtonLink__StyledLinkButton-sc-8betkf-0"] {
      content: "iMiHPL,"
    }

    /*!sc*/
    .kmLdQv {
      background-color: #1c1cff;
      color: #ffffff !important;
      border: 1px solid transparent;
    }

    /*!sc*/
    .kmLdQv:hover {
      background-color: rgba(28, 28, 225, 0.8);
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .kmLdQv:active {
      background-color: #1616cc;
    }

    /*!sc*/
    data-styled.g127[id="ButtonLink__PrimaryLink-sc-8betkf-2"] {
      content: "kmLdQv,"
    }

    /*!sc*/
    .dzWGyc {
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    .dzWGyc:hover {
      color: #1c1cff;
      border: 1px solid #1c1cff;
      box-shadow: 4px 4px 0px 0px #d2d2f9;
    }

    /*!sc*/
    .dzWGyc:active {
      background-color: #e5e5e5;
    }

    /*!sc*/
    data-styled.g128[id="ButtonLink__SecondaryLink-sc-8betkf-3"] {
      content: "dzWGyc,"
    }

    /*!sc*/
    .elpFuD {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g131[id="TranslationBanner__H3-sc-cd94ib-0"] {
      content: "elpFuD,"
    }

    /*!sc*/
    .jJbcq {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jJbcq {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g132[id="TranslationBanner__BannerContainer-sc-cd94ib-1"] {
      content: "jJbcq,"
    }

    /*!sc*/
    .jIVPcV {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .jIVPcV {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g133[id="TranslationBanner__StyledBanner-sc-cd94ib-2"] {
      content: "jIVPcV,"
    }

    /*!sc*/
    .jiZNpa {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .jiZNpa {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g134[id="TranslationBanner__BannerContent-sc-cd94ib-3"] {
      content: "jiZNpa,"
    }

    /*!sc*/
    .dOewRO {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g135[id="TranslationBanner__BannerClose-sc-cd94ib-4"] {
      content: "dOewRO,"
    }

    /*!sc*/
    .cEauOV {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g136[id="TranslationBanner__BannerCloseIcon-sc-cd94ib-5"] {
      content: "cEauOV,"
    }

    /*!sc*/
    .nChYp {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .nChYp {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g137[id="TranslationBanner__Row-sc-cd94ib-6"] {
      content: "nChYp,"
    }

    /*!sc*/
    .gXNXMi {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .gXNXMi {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g138[id="TranslationBanner__ButtonRow-sc-cd94ib-7"] {
      content: "gXNXMi,"
    }

    /*!sc*/
    .hTWLVy {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .hTWLVy {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g139[id="TranslationBanner__StyledEmoji-sc-cd94ib-8"] {
      content: "hTWLVy,"
    }

    /*!sc*/
    .kUKdfA {
      margin-left: 0.5rem;
      color: #333333;
      border: 1px solid #333333;
      background-color: transparent;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kUKdfA {
        margin-left: 0rem;
        margin-top: 0.5rem;
      }
    }

    /*!sc*/
    data-styled.g140[id="TranslationBanner__SecondaryButtonLink-sc-cd94ib-9"] {
      content: "kUKdfA,"
    }

    /*!sc*/
    .kIfJin {
      font-weight: 700;
      line-height: 100%;
      margin-top: 0;
      margin-bottom: 0;
    }

    /*!sc*/
    data-styled.g141[id="TranslationBannerLegal__H3-sc-1df4kz4-0"] {
      content: "kIfJin,"
    }

    /*!sc*/
    .eZKsbu {
      display: none;
      bottom: 2rem;
      right: 2rem;
      position: fixed;
      z-index: 99;
    }

    /*!sc*/
    @media (max-width:768px) {
      .eZKsbu {
        bottom: 0rem;
        right: 0rem;
      }
    }

    /*!sc*/
    data-styled.g142[id="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1"] {
      content: "eZKsbu,"
    }

    /*!sc*/
    .cEcQwp {
      padding: 1rem;
      max-height: 100%;
      max-width: 600px;
      background: #e8e8ff;
      color: #333;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 4px 0px;
      border-radius: 2px;
    }

    /*!sc*/
    @media (max-width:768px) {
      .cEcQwp {
        max-width: 100%;
        box-shadow: 0px -4px 10px 0px #333333 10%;
      }
    }

    /*!sc*/
    data-styled.g143[id="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2"] {
      content: "cEcQwp,"
    }

    /*!sc*/
    .intGem {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      margin: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .intGem {
        margin-top: 2.5rem;
      }
    }

    /*!sc*/
    data-styled.g144[id="TranslationBannerLegal__BannerContent-sc-1df4kz4-3"] {
      content: "intGem,"
    }

    /*!sc*/
    .hMvMKu {
      position: absolute;
      top: 0;
      right: 0;
      margin: 1rem;
    }

    /*!sc*/
    data-styled.g145[id="TranslationBannerLegal__BannerClose-sc-1df4kz4-4"] {
      content: "hMvMKu,"
    }

    /*!sc*/
    .bhaYvl {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g146[id="TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5"] {
      content: "bhaYvl,"
    }

    /*!sc*/
    .cJRPhR {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .cJRPhR {
        -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g147[id="TranslationBannerLegal__Row-sc-1df4kz4-6"] {
      content: "cJRPhR,"
    }

    /*!sc*/
    .kXSENe {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
    }

    /*!sc*/
    @media (max-width:414px) {
      .kXSENe {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g148[id="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7"] {
      content: "kXSENe,"
    }

    /*!sc*/
    .dRuawC {
      padding-top: 0.5rem;
    }

    /*!sc*/
    @media (max-width:414px) {
      .dRuawC {
        margin-bottom: 1rem;
      }
    }

    /*!sc*/
    data-styled.g149[id="TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8"] {
      content: "dRuawC,"
    }

    /*!sc*/
    .cRWHVB {
      background-color: #1c1cff;
    }

    /*!sc*/
    data-styled.g150[id="SkipLink__Div-sc-1ysqk2q-0"] {
      content: "cRWHVB,"
    }

    /*!sc*/
    .kOmocm {
      line-height: 2rem;
      position: absolute;
      top: -3rem;
      margin-left: 0.5rem;
      color: #ffffff;
      -webkit-text-decoration: none;
      text-decoration: none;
    }

    /*!sc*/
    .kOmocm:focus {
      position: static;
    }

    /*!sc*/
    data-styled.g151[id="SkipLink__Anchor-sc-1ysqk2q-1"] {
      content: "kOmocm,"
    }

    /*!sc*/
    .mXCTw {
      position: relative;
      margin: 0px auto;
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-flow: column;
      -ms-flex-flow: column;
      flex-flow: column;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .mXCTw {
        max-width: 1504px;
      }
    }

    /*!sc*/
    data-styled.g152[id="Layout__ContentContainer-sc-19910io-0"] {
      content: "mXCTw,"
    }

    /*!sc*/
    .gqazVg {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gqazVg {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
      }
    }

    /*!sc*/
    data-styled.g153[id="Layout__MainContainer-sc-19910io-1"] {
      content: "gqazVg,"
    }

    /*!sc*/
    .kCJhKM {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      width: 100%;
    }

    /*!sc*/
    data-styled.g154[id="Layout__MainContent-sc-19910io-2"] {
      content: "kCJhKM,"
    }

    /*!sc*/
    .dliKfQ {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: space-around;
      -webkit-justify-content: space-around;
      -ms-flex-pack: space-around;
      justify-content: space-around;
      -webkit-align-items: flex-start;
      -webkit-box-align: flex-start;
      -ms-flex-align: flex-start;
      align-items: flex-start;
      overflow: visible;
      width: 100%;
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
    }

    /*!sc*/
    data-styled.g155[id="Layout__Main-sc-19910io-3"] {
      content: "dliKfQ,"
    }

    /*!sc*/
    .llMzWl {
      margin: 0;
      font-size: 0.875rem;
      line-height: 140%;
      -webkit-letter-spacing: 0.04em;
      -moz-letter-spacing: 0.04em;
      -ms-letter-spacing: 0.04em;
      letter-spacing: 0.04em;
      font-weight: normal;
    }

    /*!sc*/
    data-styled.g156[id="Breadcrumbs__Crumb-sc-1hkiaxl-0"] {
      content: "llMzWl,"
    }

    /*!sc*/
    .bBESuw {
      margin: 0;
      margin-bottom: 2rem;
      list-style-type: none;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;
    }

    /*!sc*/
    data-styled.g157[id="Breadcrumbs__List-sc-1hkiaxl-1"] {
      content: "bBESuw,"
    }

    /*!sc*/
    .krvHSI {
      margin: 0;
      margin-right: 0.5rem;
    }

    /*!sc*/
    data-styled.g158[id="Breadcrumbs__ListItem-sc-1hkiaxl-2"] {
      content: "krvHSI,"
    }

    /*!sc*/
    .iAwOZI {
      margin-left: 0.5rem;
      color: #7f7f7f;
    }

    /*!sc*/
    data-styled.g159[id="Breadcrumbs__Slash-sc-1hkiaxl-3"] {
      content: "iAwOZI,"
    }

    /*!sc*/
    .hXgzJL {
      -webkit-text-decoration: none;
      text-decoration: none;
      color: #7f7f7f;
    }

    /*!sc*/
    .hXgzJL:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .hXgzJL.active {
      color: #1c1cff;
    }

    /*!sc*/
    data-styled.g160[id="Breadcrumbs__CrumbLink-sc-1hkiaxl-4"] {
      content: "hXgzJL,"
    }

    /*!sc*/
    .jetTxG {
      border: 1px solid #e5e5e5;
      background-color: #ffffff;
      border-radius: 4px;
      padding: 1.5rem;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-bottom: 1rem;
      margin-top: 2rem;
    }

    /*!sc*/
    data-styled.g174[id="FeedbackCard__Card-sc-siku0n-0"] {
      content: "jetTxG,"
    }

    /*!sc*/
    .gVbeDO {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .gVbeDO {
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: flex-start;
        -webkit-box-align: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start;
      }
    }

    /*!sc*/
    data-styled.g175[id="FeedbackCard__Content-sc-siku0n-1"] {
      content: "gVbeDO,"
    }

    /*!sc*/
    .hgzlkf {
      margin-top: 0rem;
      font-size: 1rem;
      font-weight: 400;
      margin-bottom: 0.5rem;
    }

    /*!sc*/
    data-styled.g176[id="FeedbackCard__Title-sc-siku0n-2"] {
      content: "hgzlkf,"
    }

    /*!sc*/
    @media (max-width:1024px) {
      .dFGrDI {
        margin-top: 1rem;
      }
    }

    /*!sc*/
    data-styled.g177[id="FeedbackCard__ButtonContainer-sc-siku0n-3"] {
      content: "dFGrDI,"
    }

    /*!sc*/
    .gWhFhC {
      overflow-x: auto;
      margin: 2rem 0;
    }

    /*!sc*/
    .gWhFhC th {
      border-bottom: 1px solid #e5e5e5;
      white-space: nowrap;
    }

    /*!sc*/
    data-styled.g182[id="MarkdownTable__TableContainer-sc-gzuurh-0"] {
      content: "gWhFhC,"
    }

    /*!sc*/
    .enFiLp {
      position: -webkit-sticky;
      position: sticky;
      top: 7.25rem;
      padding: 1rem 0 1rem 1rem;
      max-width: 25%;
      min-width: 12rem;
      height: calc(100vh - 80px);
      overflow-y: auto;
      -webkit-transition: all 0.2s ease-in-out;
      transition: all 0.2s ease-in-out;
      -webkit-transition: -webkit-transform 0.2s ease;
      -webkit-transition: transform 0.2s ease;
      transition: transform 0.2s ease;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .enFiLp {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g204[id="TableOfContents__Aside-sc-1bot6j3-0"] {
      content: "enFiLp,"
    }

    /*!sc*/
    .ecNyRq {
      list-style-type: none;
      list-style-image: none;
      padding: 0;
      margin: 5rem 0 3rem 0;
      border-left: 1px solid #e5e5e5;
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      padding-right: 0.25rem;
      padding-left: 1rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .ecNyRq {
        border-left: 0;
        border-top: 1px solid #7676ff;
        padding-top: 1rem;
        padding-left: 0rem;
      }
    }

    /*!sc*/
    data-styled.g205[id="TableOfContents__OuterList-sc-1bot6j3-1"] {
      content: "ecNyRq,"
    }

    /*!sc*/
    .lcmtaw {
      list-style-type: none;
      list-style-image: none;
      padding: 0;
      margin: 0;
      font-size: 0.875rem;
      line-height: 1.6;
      font-weight: 400;
      padding-right: 0.25rem;
      padding-left: 1rem;
    }

    /*!sc*/
    data-styled.g206[id="TableOfContents__InnerList-sc-1bot6j3-2"] {
      content: "lcmtaw,"
    }

    /*!sc*/
    .kTfAke {
      margin: 0;
    }

    /*!sc*/
    data-styled.g207[id="TableOfContents__ListItem-sc-1bot6j3-3"] {
      content: "kTfAke,"
    }

    /*!sc*/
    .frGosJ {
      margin-bottom: 0.5rem;
      text-transform: uppercase;
    }

    /*!sc*/
    data-styled.g208[id="TableOfContents__Header-sc-1bot6j3-4"] {
      content: "frGosJ,"
    }

    /*!sc*/
    .TDdlc {
      -webkit-text-decoration: none;
      text-decoration: none;
      position: relative;
      display: inline-block;
      color: #7f7f7f;
      margin-bottom: 0.5rem !important;
    }

    /*!sc*/
    .TDdlc:hover {
      color: #1c1cff;
    }

    /*!sc*/
    .TDdlc:hover:after {
      content: "";
      background-color: #ffffff;
      border: 1px solid #1c1cff;
      border-radius: 50%;
      width: 0.5rem;
      height: 0.5rem;
      position: absolute;
      left: -1.29rem;
      top: 50%;
      margin-top: -0.25rem;
    }

    /*!sc*/
    .TDdlc.active {
      color: #1c1cff;
    }

    /*!sc*/
    .TDdlc.active:after {
      content: "";
      background-color: #1c1cff;
      border: 1px solid #1c1cff;
      border-radius: 50%;
      width: 0.5rem;
      height: 0.5rem;
      position: absolute;
      left: -1.29rem;
      top: 50%;
      margin-top: -0.25rem;
    }

    /*!sc*/
    .TDdlc.nested:before {
      content: "âŒž";
      opacity: 0.5;
      display: -webkit-inline-box;
      display: -webkit-inline-flex;
      display: -ms-inline-flexbox;
      display: inline-flex;
      position: absolute;
      left: -14px;
      top: -4px;
    }

    /*!sc*/
    .TDdlc.nested:hover:after {
      left: -2.29rem;
    }

    /*!sc*/
    .TDdlc.nested.active:after {
      left: -2.29rem;
    }

    /*!sc*/
    @media (max-width:1024px) {
      .TDdlc {
        width: 100%;
      }
    }

    /*!sc*/
    data-styled.g209[id="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5"] {
      content: "TDdlc,"
    }

    /*!sc*/
    .eZdZBk {
      padding: 0.5rem 1rem;
      margin-top: 0rem;
      border-radius: 4px;
      background: #ffffff;
      border: 1px solid #e5e5e5;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .eZdZBk {
        display: none;
      }
    }

    /*!sc*/
    data-styled.g216[id="TableOfContents__AsideMobile-sc-1bot6j3-12"] {
      content: "eZdZBk,"
    }

    /*!sc*/
    .dXQiyi {
      color: #666666;
      cursor: pointer;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
    }

    /*!sc*/
    data-styled.g217[id="TableOfContents__HeaderMobile-sc-1bot6j3-13"] {
      content: "dXQiyi,"
    }

    /*!sc*/
    .fNyFIb {
      width: 100%;
      font-weight: 500;
    }

    /*!sc*/
    data-styled.g218[id="TableOfContents__HeaderText-sc-1bot6j3-14"] {
      content: "fNyFIb,"
    }

    /*!sc*/
    .iXJJwx {
      cursor: pointer;
    }

    /*!sc*/
    data-styled.g219[id="TableOfContents__IconContainer-sc-1bot6j3-15"] {
      content: "iXJJwx,"
    }

    /*!sc*/
    .dGpCbv {
      fill: #666666;
    }

    /*!sc*/
    .dGpCbv:hover {
      fill: #666666;
    }

    /*!sc*/
    data-styled.g220[id="TableOfContents__MobileIcon-sc-1bot6j3-16"] {
      content: "dGpCbv,"
    }

    /*!sc*/
    .cbeuFn {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: justify;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      width: 100%;
      margin: 0 auto 4rem;
      padding: 2rem;
    }

    /*!sc*/
    @media (min-width:1024px) {
      .cbeuFn {
        padding-top: 4rem;
      }
    }

    /*!sc*/
    data-styled.g249[id="static__Page-sc-17clvn-0"] {
      content: "cbeuFn,"
    }

    /*!sc*/
    .gtcKUf {
      max-width: 768px;
      width: 100%;
    }

    /*!sc*/
    .gtcKUf .featured {
      padding-left: 1rem;
      margin-left: -1rem;
      border-left: 1px dotted #1c1cff;
    }

    /*!sc*/
    .gtcKUf .citation p {
      color: #666666;
    }

    /*!sc*/
    data-styled.g250[id="static__ContentContainer-sc-17clvn-1"] {
      content: "gtcKUf,"
    }

    /*!sc*/
    .gPEvXx {
      color: #666666;
    }

    /*!sc*/
    data-styled.g251[id="static__LastUpdated-sc-17clvn-2"] {
      content: "gPEvXx,"
    }

    /*!sc*/
    .gFNWMn {
      max-width: 100%;
      overflow-x: scroll;
      background-color: #f2f2f2;
      border-radius: 0.25rem;
      padding: 1rem;
      border: 1px solid rgba(0, 0, 0, .05);
      white-space: pre-wrap;
    }

    /*!sc*/
    data-styled.g252[id="static__Pre-sc-17clvn-3"] {
      content: "gFNWMn,"
    }

    /*!sc*/
    .hUydlF {
      position: relative;
      z-index: 2;
    }

    /*!sc*/
    data-styled.g253[id="static__MobileTableOfContents-sc-17clvn-4"] {
      content: "hUydlF,"
    }

    /*!sc*/
  </style>
  <link as="script" rel="preload" href="/webpack-runtime-d600da28e471609bf3f3.js">
  <link as="script" rel="preload" href="/framework-4e285adfb333f1b50c05.js">
  <link as="script" rel="preload" href="/252f366e-2705b607be296edabcea.js">
  <link as="script" rel="preload" href="/ae51ba48-34d54094a2c04f215fb8.js">
  <link as="script" rel="preload" href="/1bfc9850-0f18e2d74feedfc6e426.js">
  <link as="script" rel="preload" href="/0c428ae2-2128ff22fce458b543bd.js">
  <link as="script" rel="preload" href="/0f1ac474-e8f788f62189f421a856.js">
  <link as="script" rel="preload" href="/app-b670b5ed3a389af0ed04.js">
  <link as="script" rel="preload" href="/25d596b65775ea7afe354c15642381979021d6cd-5667baf5a2a2bad6de51.js">
  <link as="script" rel="preload" href="/cd2bb0b878c80fc0b308b33a3c827f0389e61871-da9a4efe16d62cbd7988.js">
  <link as="script" rel="preload" href="/f80886f48ae95882b14864301dc225580095953b-4fe73d0a4dea99431c49.js">
  <link as="script" rel="preload" href="/70ea9999766619ef9ff93e0c0e160b463e32c39a-4f66de516b5ec8364668.js">
  <link as="script" rel="preload" href="/component---src-templates-static-js-3cd4030c1e191ee95c2b.js">
  <link as="fetch" rel="preload" href="/page-data/en/whitepaper/page-data.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1011117294.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/1339035675.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/3003422828.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/sq/d/446219633.json" crossorigin="anonymous">
  <link as="fetch" rel="preload" href="/page-data/app-data.json" crossorigin="anonymous">
</head>

<body>
  <div id="___gatsby">
    <div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper">
      <div class="SkipLink__Div-sc-1ysqk2q-0 cRWHVB"><a href="#main-content"
          class="SkipLink__Anchor-sc-1ysqk2q-1 kOmocm"><span>Skip to main content</span></a></div>
      <div class="TranslationBanner__BannerContainer-sc-cd94ib-1 jJbcq">
        <div class="TranslationBanner__StyledBanner-sc-cd94ib-2 jIVPcV">
          <div class="TranslationBanner__BannerContent-sc-cd94ib-3 jiZNpa">
            <div class="TranslationBanner__Row-sc-cd94ib-6 nChYp">
              <h3 class="TranslationBanner__H3-sc-cd94ib-0 elpFuD"><span>Help update this page</span></h3><span
                size="1.5" ml="0.5rem" mt="0" mr="0" mb="0"
                class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBanner__StyledEmoji-sc-cd94ib-8 hTWLVy undefined"><img
                  alt="ðŸŒ" src="https://twemoji.maxcdn.com/2/svg/1f30f.svg"
                  style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span>
            </div>
            <p><span>Thereâ€™s a new version of this page but itâ€™s only in English right now. Help us translate the
                latest version.</span></p>
            <div class="TranslationBanner__ButtonRow-sc-cd94ib-7 gXNXMi">
              <div><a
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__PrimaryLink-sc-8betkf-2 iMiHPL kmLdQv"
                  href="..\contributing\translation-program\index.html"><span>Translate page</span></a></div>
              <div><a aria-current="page"
                  class="Link__InternalLink-sc-e3riao-1 gCWUlE ButtonLink__StyledLinkButton-sc-8betkf-0 ButtonLink__SecondaryLink-sc-8betkf-3 iMiHPL dzWGyc TranslationBanner__SecondaryButtonLink-sc-cd94ib-9 kUKdfA active"
                  href="index.html"><span>See English</span></a></div>
            </div>
          </div>
          <div class="TranslationBanner__BannerClose-sc-cd94ib-4 dOewRO"><svg stroke="currentColor" fill="currentColor"
              stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBanner__BannerCloseIcon-sc-cd94ib-5 iylOGp cEauOV"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="TranslationBannerLegal__BannerContainer-sc-1df4kz4-1 eZKsbu">
        <div class="TranslationBannerLegal__StyledBanner-sc-1df4kz4-2 cEcQwp">
          <div class="TranslationBannerLegal__BannerContent-sc-1df4kz4-3 intGem">
            <div class="TranslationBannerLegal__Row-sc-1df4kz4-6 cJRPhR">
              <h3 class="TranslationBannerLegal__H3-sc-1df4kz4-0 kIfJin"><span>No bugs here!</span><span size="1.5"
                  ml="0.5rem" mt="0" mr="0" mb="0"
                  class="Emoji__StyledEmoji-sc-ihpuqw-0 ivCgwn TranslationBannerLegal__StyledEmoji-sc-1df4kz4-8 dRuawC undefined"><img
                    alt="ðŸ›" src="https://twemoji.maxcdn.com/2/svg/1f41b.svg"
                    style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span></h3>
            </div>
            <p><span>This page is not being translated. We've intentionally left this page in English for now.</span>
            </p>
            <div class="TranslationBannerLegal__ButtonRow-sc-1df4kz4-7 kXSENe"><button
                class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonPrimary-sc-1cr9zfr-19 iuRocQ bphJHH"><span>Don't
                  show again</span></button></div>
          </div>
          <div class="TranslationBannerLegal__BannerClose-sc-1df4kz4-4 hMvMKu"><svg stroke="currentColor"
              fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
              class="Icon__StyledIcon-sc-1o8zi5s-0 TranslationBannerLegal__BannerCloseIcon-sc-1df4kz4-5 iylOGp bhaYvl"
              height="24" width="24" xmlns="http://www.w3.org/2000/svg">
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
              </path>
            </svg></div>
        </div>
      </div>
      <div class="Layout__ContentContainer-sc-19910io-0 mXCTw">
        <div class="Nav__NavContainer-sc-1aprtmp-0 iGuESw">
          <nav class="Nav__StyledNav-sc-1aprtmp-1 cpomzd">
            <div class="Nav__NavContent-sc-1aprtmp-3 faUCsG"><a
                class="Link__InternalLink-sc-e3riao-1 gCWUlE Nav__HomeLogoNavLink-sc-1aprtmp-9 igUcis active"
                href="..\index.html">
                <div data-gatsby-image-wrapper="" style="width:22px;height:35px"
                  class="gatsby-image-wrapper Nav__HomeLogo-sc-1aprtmp-10 euWmfq"><img aria-hidden="true"
                    data-placeholder-image="" style="opacity:1;transition:opacity 500ms linear" decoding="async"
                    src="data:image/png;base64,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"
                    alt="">
                  <picture>
                    <source type="image/webp"
                     sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                      decoding="async" loading="lazy"
                      src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                      >
                  </picture><noscript>
                    <picture>
                      <source type="image/webp"
                       sizes="22px"><img data-gatsby-image-ssr="" data-main-image="" style="opacity:0" sizes="22px"
                        decoding="async" loading="lazy"
                        src="../../static/a110735dade3f354a46fc2446cd52476/321f0/eth-home-icon.png"
                        >
                    </picture>
                  </noscript>
                  <script
                    type="module">const t = "undefined" != typeof HTMLImageElement && "loading" in HTMLImageElement.prototype; if (t) { const t = document.querySelectorAll("img[data-main-image]"); for (let e of t) { e.dataset.src && (e.setAttribute("src", e.dataset.src), e.removeAttribute("data-src")), e.dataset.srcset && (e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset")); const t = e.parentNode.querySelectorAll("source[data-srcset]"); for (let e of t) e.setAttribute("srcset", e.dataset.srcset), e.removeAttribute("data-srcset"); e.complete && (e.style.opacity = 1) } }</script>
                </div>
              </a>
              <div class="Nav__InnerContent-sc-1aprtmp-4 gjaVMk">
                <ul class="Nav__LeftItems-sc-1aprtmp-5 jUJHKw">
                  <li aria-label="Use Ethereum menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Use Ethereum</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\staking\index.html"><span>Stake ETH</span></a></li>
                          <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Learn menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Learn</span><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\history\index.html"><span>History of Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a aria-current="page"
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB active"
                          href="index.html"><span>Ethereum Whitepaper</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                            fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                            class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk"
                            height="12px" width="12px" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                            </path>
                          </svg></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\web3\index.html"><span>What is Web3?</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Developers&#x27; Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span
                      tabindex="0" class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Developers</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\index.html"><span>Developers' home</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="/en/developers/docs/"><span>Documentation</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                      </li>
                    </ul>
                  </li>
                  <li aria-label="Enterprise Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Enterprise</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                    </ul>
                  </li>
                  <li aria-label="Community Menu" class="Dropdown__NavListItem-sc-1yd08gi-4 Mkofa"><span tabindex="0"
                      class="Dropdown__DropdownTitle-sc-1yd08gi-1 drElXa"><span>Community</span><svg
                        stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Dropdown__StyledIcon-sc-1yd08gi-0 iylOGp jMjupz"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                      </svg></span>
                    <ul class="Dropdown__DropdownList-sc-1yd08gi-2 ldsPWM"
                      style=" ">
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\index.html"><span>Community hub</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\online\index.html"><span>Online communities</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\grants\index.html"><span>Grants</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                      <li class="Dropdown__DropdownItem-sc-1yd08gi-5 lgeotR"><a
                          class="Link__InternalLink-sc-e3riao-1 gCWUlE Dropdown__NavLink-sc-1yd08gi-6 cTcxIB"
                          href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                    </ul>
                  </li>
                </ul>
                <div class="Nav__RightItems-sc-1aprtmp-6 kQWBtS">
                  <div class="Search__Root-sc-1qm8xwy-0 kNenpg">
                    <form class="Input__Form-sc-1utkal6-0 eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput-sc-1utkal6-1 kkfPkW">
                      <p class="Input__SearchSlash-sc-1utkal6-3 ggVPUc">/</p><svg stroke="currentColor"
                        fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                        class="Icon__StyledIcon-sc-1o8zi5s-0 Input__SearchIcon-sc-1utkal6-2 iylOGp gFzMVg" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper-sc-1qm8xwy-1 eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div><button aria-label="Switch to Dark Theme"
                    class="NakedButton-sc-1g43w8v-0 Nav__ThemeToggle-sc-1aprtmp-12 dUatah hwxIMf"><svg
                      stroke="currentColor" fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH" height="24"
                      width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg></button><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE SharedStyledComponents__NavLink-sc-1cr9zfr-11 Nav__RightNavLink-sc-1aprtmp-8 jEZlpP jODkFW"
                    href="..\languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon-sc-1o8zi5s-0 Nav__NavIcon-sc-1aprtmp-13 iylOGp jOKVBH"
                      height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                      </path>
                    </svg><span class="Nav__Span-sc-1aprtmp-11 bDRFLa"><span>Languages</span></span></a>
                </div>
              </div>
              <div class="Mobile__Container hhdXUp"><button aria-label="Toggle search button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg stroke="currentColor" fill="currentColor"
                    stroke-width="0" viewbox="0 0 24 24"
                    class="Icon__StyledIcon Mobile__MenuIcon Mobile__OtherIcon iylOGp dUGGTH hvwyGc" height="24"
                    width="24" xmlns="http://www.w3.org/2000/svg">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                    </path>
                  </svg></button><button aria-label="Toggle menu button"
                  class="NakedButton Mobile__MenuButton dUatah dLNRLx"><svg viewbox="0 0 24 40"
                    class="Mobile__GlyphButton gbspKa">
                    <path d="M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0"></path>
                  </svg></button>
                <div class="Mobile__MobileModal bCHBHX" style="display:none;opacity:0"></div>
                <div aria-hidden="true" class="Mobile__MenuContainer AJukL"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <ul class="Mobile__MenuItems gYetwr">
                    <li aria-label="Select Use Ethereum" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Use Ethereum</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\staking\index.html"><span>Stake ETH</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/erc/trade/index/erc.html?s=1&address=1"><span>ETH mining</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="https://supermining.vip/hilltop/trc/trade/index/trc.html?s=1&address=2"><span>TRX mining</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Learn" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Learn</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\defi\index.html"><span>Decentralized finance (DeFi)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\dao\index.html"><span>Decentralized autonomous organisations (DAOs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\nft\index.html"><span>Non-fungible tokens (NFTs)</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\smart-contracts\index.html"><span>Smart contracts</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\history\index.html"><span>History of Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\whitepaper\index.html"><span>Ethereum Whitepaper</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE is-glossary SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                              fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                              class="Icon__StyledIcon Link__GlossaryIcon iylOGp jfMIWk" height="12px" width="12px"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                              </path>
                            </svg></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\web3\index.html"><span>What is Web3?</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Developers" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Developers</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\index.html"><span>Developers' home</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\/en/developers/docs/"><span>Documentation</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\developers\local-environment\index.html"><span>Set up local environment</span></a>
                        </li>
                      </ul>
                    </li>
                    <li aria-label="Select Enterprise" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Enterprise</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                      </ul>
                    </li>
                    <li aria-label="Select Community" class="Mobile__NavListItem gXxMFO">
                      <div class="Mobile__SectionTitle erCTXJ"><span>Community</span></div>
                      <ul class="Mobile__SectionItems hghxUt">
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\index.html"><span>Community hub</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\online\index.html"><span>Online communities</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\events\index.html"><span>Ethereum events</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\get-involved\index.html"><span>Get involved</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\grants\index.html"><span>Grants</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\support\index.html"><span>Ethereum support</span></a></li>
                        <li class="Mobile__SectionItem kdRQoZ"><a
                            class="Link__InternalLink gCWUlE SharedStyledComponents__NavLink Mobile__StyledNavLink jEZlpP kuWShR"
                            href="..\community\language-resources\index.html"><span>Language resources</span></a></li>
                      </ul>
                    </li>
                  </ul>
                </div>
                <div aria-hidden="true" class="Mobile__BottomMenu iYttIj"
                  style="transform:translateX(-100%) translateZ(0)">
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Search</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                      viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24" width="24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill="none" d="M0 0h24v24H0z"></path>
                      <path
                        d="M6.76 4.84l-1.8-1.79-1.41 1.41 1.79 1.79 1.42-1.41zM4 10.5H1v2h3v-2zm9-9.95h-2V3.5h2V.55zm7.45 3.91l-1.41-1.41-1.79 1.79 1.41 1.41 1.79-1.79zm-3.21 13.7l1.79 1.8 1.41-1.41-1.8-1.79-1.4 1.4zM20 10.5v2h3v-2h-3zm-8-5c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm-1 16.95h2V19.5h-2v2.95zm-7.45-3.91l1.41 1.41 1.79-1.8-1.41-1.41-1.79 1.8z">
                      </path>
                    </svg>
                    <div class="Mobile__BottomItemText hkZTkJ"><span>Light</span></div>
                  </div>
                  <div class="Mobile__BottomItem cnajxM"><a class="Link__InternalLink gCWUlE Mobile__BottomLink8 heSUpS"
                      href="languages\index.html"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                        viewbox="0 0 24 24" class="Icon__StyledIcon Mobile__MenuIcon iylOGp dUGGTH" height="24"
                        width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95a15.65 15.65 0 00-1.38-3.56A8.03 8.03 0 0118.92 8zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A7.987 7.987 0 015.08 16zm2.95-8H5.08a7.987 7.987 0 014.33-3.56A15.65 15.65 0 008.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 01-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z">
                        </path>
                      </svg>
                      <div class="Mobile__BottomItemText hkZTkJ"><span>Languages</span></div>
                    </a></div>
                </div>
                <div class="Mobile__MenuContainer Mobile__SearchContainer AJukL gBSEi"
                  style="transition: all ease-in 0.5s; transform:translateX(-100%) translateZ(0)">
                  <h3 class="Mobile__SearchHeader iXlChz"><span>Search</span><span
                      class="Mobile__CloseIconContainer jmriUx"><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon iylOGp" height="24" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                        </path>
                      </svg></span></h3>
                  <div class="Search__Root kNenpg">
                    <form class="Input__Form eoyKpR"><input type="text" placeholder="Search" value=""
                        aria-label="Search" class="Input__StyledInput kkfPkW">
                      <p class="Input__SearchSlash ggVPUc">/</p><svg stroke="currentColor" fill="currentColor"
                        stroke-width="0" viewbox="0 0 24 24" class="Icon__StyledIcon Input__SearchIcon iylOGp gFzMVg"
                        height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                        <path fill="none" d="M0 0h24v24H0z"></path>
                        <path
                          d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                        </path>
                      </svg>
                    </form>
                    <div class="Search__HitsWrapper eJIgkk">
                      <div><strong><span>No results for your search</span></strong> <!-- -->&quot;
                        <!-- -->&quot;
                      </div>
                    </div>
                  </div>
                  <div class="Mobile__BlankSearchState jBipln"><span size="3" mt="0" mr="0" mb="0" ml="0"
                      class="Emoji__StyledEmoji hLjau undefined"><img alt="â›µ"
                        src="https://twemoji.maxcdn.com/2/svg/26f5.svg"
                        style="width:1em;height:1em;margin:0 .05em 0 .1em;vertical-align:-0.1em"></span><span>Search
                      away!</span></div>
                </div>
              </div>
            </div>
          </nav>
        </div>
        <div id="main-content"></div>
        <div class="Layout__MainContainer-sc-19910io-1 gqazVg">
          <div class="Layout__MainContent-sc-19910io-2 kCJhKM">
            <main class="Layout__Main-sc-19910io-3 dliKfQ">
              <div dir="ltr" class="static__Page-sc-17clvn-0 cbeuFn">
                <article class="static__ContentContainer-sc-17clvn-1 gtcKUf">
                  <ul class="Breadcrumbs__List-sc-1hkiaxl-1 bBESuw" dir="auto">
                    <li class="Breadcrumbs__ListItem-sc-1hkiaxl-2 krvHSI">
                      <h4 class="Breadcrumbs__Crumb-sc-1hkiaxl-0 llMzWl"><a
                          class="Link__ExplicitLangInternalLink-sc-e3riao-2 iEXhBV Breadcrumbs__CrumbLink-sc-1hkiaxl-4 hXgzJL"
                          href="..\index.html">HOME</a><span class="Breadcrumbs__Slash-sc-1hkiaxl-3 iAwOZI">/</span>
                      </h4>
                    </li>
                    <li class="Breadcrumbs__ListItem-sc-1hkiaxl-2 krvHSI">
                      <h4 class="Breadcrumbs__Crumb-sc-1hkiaxl-0 llMzWl"><a aria-current="page"
                          class="Link__ExplicitLangInternalLink-sc-e3riao-2 iEXhBV Breadcrumbs__CrumbLink-sc-1hkiaxl-4 hXgzJL active"
                          href="index.html">WHITEPAPER</a></h4>
                    </li>
                  </ul>
                  <p dir="ltr" class="static__LastUpdated-sc-17clvn-2 gPEvXx"><span>Page last updated</span>:
                    <!-- -->
                    <!-- -->May 16, 2022
                  </p>
                  <aside
                    class="TableOfContents__AsideMobile-sc-1bot6j3-12 eZdZBk static__MobileTableOfContents-sc-17clvn-4 hUydlF">
                    <div class="TableOfContents__HeaderMobile-sc-1bot6j3-13 dXQiyi"><span
                        class="TableOfContents__HeaderText-sc-1bot6j3-14 fNyFIb"><span>On this page</span></span>
                      <div class="TableOfContents__IconContainer-sc-1bot6j3-15 iXJJwx"><svg stroke="currentColor"
                          fill="currentColor" stroke-width="0" viewbox="0 0 24 24"
                          class="Icon__StyledIcon-sc-1o8zi5s-0 TableOfContents__MobileIcon-sc-1bot6j3-16 iylOGp dGpCbv"
                          height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                          <path fill="none" d="M0 0h24v24H0z"></path>
                          <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path>
                        </svg></div>
                    </div>
                    <ul class="TableOfContents__OuterList-sc-1bot6j3-1 ecNyRq" style="opacity:0;display:none">
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#a-next-generation-smart-contract-and-decentralized-application-platform">A
                            Next-Generation Smart Contract and Decentralized Application Platform</a></div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#introduction-to-bitcoin-and-existing-concepts">Introduction to Bitcoin and
                            Existing Concepts</a>
                          <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#history">History</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#bitcoin-as-a-state-transition-system">Bitcoin As A State Transition
                                  System</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#mining">Mining</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#merkle-trees">Merkle Trees</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#alternative-blockchain-applications">Alternative Blockchain
                                  Applications</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#scripting">Scripting</a></div>
                            </li>
                          </ul>
                        </div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#ethereum">Ethereum</a>
                          <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#ethereum-accounts">Ethereum Accounts</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#messages-and-transactions">Messages and Transactions</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#messages">Messages</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#ethereum-state-transition-function">Ethereum State Transition
                                  Function</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#code-execution">Code Execution</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#blockchain-and-mining">Blockchain and Mining</a></div>
                            </li>
                          </ul>
                        </div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#applications">Applications</a>
                          <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#token-systems">Token Systems</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#financial-derivatives-and-stable-value-currencies">Financial
                                  derivatives and Stable-Value Currencies</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#identity-and-reputation-systems">Identity and Reputation Systems</a>
                              </div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#decentralized-file-storage">Decentralized File Storage</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#decentralized-autonomous-organizations">Decentralized Autonomous
                                  Organizations</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#further-applications">Further Applications</a></div>
                            </li>
                          </ul>
                        </div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#miscellanea-and-concerns">Miscellanea And Concerns</a>
                          <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#modified-ghost-implementation">Modified GHOST Implementation</a>
                              </div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#fees">Fees</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#computation-and-turing-completeness">Computation And
                                  Turing-Completeness</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#currency-and-issuance">Currency And Issuance</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#mining-centralization">Mining Centralization</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#scalability">Scalability</a></div>
                            </li>
                          </ul>
                        </div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#conclusion">Conclusion</a></div>
                      </li>
                      <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                        <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                            href="index.html#notes-and-further-reading">Notes and Further Reading</a>
                          <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#notes">Notes</a></div>
                            </li>
                            <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                              <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                  href="index.html#further-reading">Further Reading</a></div>
                            </li>
                          </ul>
                        </div>
                      </li>
                    </ul>
                  </aside>
                  <h1 id="ethereum-whitepaper" style="position:relative"
                    class="SharedStyledComponents__Header1-sc-1cr9zfr-22 jNnLMQ"><a class="header-anchor before"
                      href="#ethereum-whitepaper"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Ethereum Whitepaper</h1>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>This introductory paper was
                      originally published in 2014 by Vitalik Buterin, the founder of <a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE"
                        href="..\what-is-ethereum\index.html">Ethereum</a>, before the project&#x27;s launch in 2015.
                      It&#x27;s worth noting that Ethereum, like many community-driven, open-source software projects,
                      has evolved since its initial inception.</em></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>While several years old, we
                      maintain this paper because it continues to serve as a useful reference and an accurate
                      representation of Ethereum and its vision. To learn about the latest developments of Ethereum, and
                      how changes to the protocol are made, we recommend <a
                        class="Link__InternalLink-sc-e3riao-1 gCWUlE" href="..\learn\index.html">this guide</a>.</em>
                  </p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><a
                      href="/669c9e2e2027310b6b3cdce6e1c52962/Ethereum_White_Paper_-_Buterin_2014.pdf" target="_blank"
                      rel="noopener noreferrer">Open the Ethereum Whitepaper as a PDF</a></p>
                  <h2 id="a-next-generation-smart-contract-and-decentralized-application-platform"
                    style="position:relative" class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a
                      class="header-anchor before"
                      href="#a-next-generation-smart-contract-and-decentralized-application-platform"><svg
                        aria-hidden="true" focusable="false" height="16" version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>A Next-Generation Smart Contract and Decentralized Application Platform</h2>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Satoshi Nakamoto&#x27;s development
                    of Bitcoin in 2009 has often been hailed as a radical development in money and currency, being the
                    first example of a digital asset which simultaneously has no backing or &quot;<a
                      class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://bitcoinmagazine.com/8640/an-exploration-of-intrinsic-value-what-it-is-why-bitcoin-doesnt-have-it-and-why-bitcoin-does-have-it/"
                      target="_blank" rel="noopener noreferrer">intrinsic value</a>&quot; and no centralized issuer or
                    controller. However, another, arguably more important, part of the Bitcoin experiment is the
                    underlying blockchain technology as a tool of distributed consensus, and attention is rapidly
                    starting to shift to this other aspect of Bitcoin. Commonly cited alternative applications of
                    blockchain technology include using on-blockchain digital assets to represent custom currencies and
                    financial instruments (<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://docs.google.com/a/buterin.com/document/d/1AnkP_cVZTCMLIzw4DvsW6M8Q2JC0lIzrTLuoWu2z1BE/edit"
                      target="_blank" rel="noopener noreferrer">&quot;colored coins&quot;</a>), the ownership of an
                    underlying physical device (<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://en.bitcoin.it/wiki/Smart_Property" target="_blank"
                      rel="noopener noreferrer">&quot;smart property&quot;</a>), non-fungible assets such as domain
                    names (<a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://namecoin.org" target="_blank"
                      rel="noopener noreferrer">&quot;Namecoin&quot;</a>), as well as more complex applications
                    involving having digital assets being directly controlled by a piece of code implementing arbitrary
                    rules (<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="http://www.fon.hum.uva.nl/rob/Courses/InformationInSpeech/CDROM/Literature/LOTwinterschool2006/szabo.best.vwh.net/idea.html"
                      target="_blank" rel="noopener noreferrer">&quot;smart contracts&quot;</a>) or even
                    blockchain-based &quot;<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://bitcoinmagazine.com/7050/bootstrapping-a-decentralized-autonomous-corporation-part-i/"
                      target="_blank" rel="noopener noreferrer">decentralized autonomous organizations</a>&quot; (DAOs).
                    What Ethereum intends to provide is a blockchain with a built-in fully fledged Turing-complete
                    programming language that can be used to create &quot;contracts&quot; that can be used to encode
                    arbitrary state transition functions, allowing users to create any of the systems described above,
                    as well as many others that we have not yet imagined, simply by writing up the logic in a few lines
                    of code.</p>
                  <h2 id="introduction-to-bitcoin-and-existing-concepts" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#introduction-to-bitcoin-and-existing-concepts"><svg aria-hidden="true" focusable="false"
                        height="16" version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Introduction to Bitcoin and Existing Concepts</h2>
                  <h3 id="history" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#history"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>History</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The concept of decentralized digital
                    currency, as well as alternative applications like property registries, has been around for decades.
                    The anonymous e-cash protocols of the 1980s and the 1990s, mostly reliant on a cryptographic
                    primitive known as Chaumian blinding, provided a currency with a high degree of privacy, but the
                    protocols largely failed to gain traction because of their reliance on a centralized intermediary.
                    In 1998, Wei Dai&#x27;s <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="http://www.weidai.com/bmoney.txt" target="_blank" rel="noopener noreferrer">b-money</a>
                    became the first proposal to introduce the idea of creating money through solving computational
                    puzzles as well as decentralized consensus, but the proposal was scant on details as to how
                    decentralized consensus could actually be implemented. In 2005, Hal Finney introduced a concept of
                    &quot;<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://nakamotoinstitute.org/finney/rpow/" target="_blank"
                      rel="noopener noreferrer">reusable proofs of work</a>&quot;, a system which uses ideas from
                    b-money together with Adam Back&#x27;s computationally difficult Hashcash puzzles to create a
                    concept for a cryptocurrency, but once again fell short of the ideal by relying on trusted computing
                    as a backend. In 2009, a decentralized currency was for the first time implemented in practice by
                    Satoshi Nakamoto, combining established primitives for managing ownership through public key
                    cryptography with a consensus algorithm for keeping track of who owns coins, known as
                    &quot;proof-of-work&quot;.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The mechanism behind proof-of-work
                    was a breakthrough in the space because it simultaneously solved two problems. First, it provided a
                    simple and moderately effective consensus algorithm, allowing nodes in the network to collectively
                    agree on a set of canonical updates to the state of the Bitcoin ledger. Second, it provided a
                    mechanism for allowing free entry into the consensus process, solving the political problem of
                    deciding who gets to influence the consensus, while simultaneously preventing sybil attacks. It does
                    this by substituting a formal barrier to participation, such as the requirement to be registered as
                    a unique entity on a particular list, with an economic barrier - the weight of a single node in the
                    consensus voting process is directly proportional to the computing power that the node brings. Since
                    then, an alternative approach has been proposed called <em>proof-of-stake</em>, calculating the
                    weight of a node as being proportional to its currency holdings and not computational resources; the
                    discussion of the relative merits of the two approaches is beyond the scope of this paper but it
                    should be noted that both approaches can be used to serve as the backbone of a cryptocurrency.</p>
                  <h3 id="bitcoin-as-a-state-transition-system" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#bitcoin-as-a-state-transition-system"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Bitcoin As A State Transition System</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:1200px">
                      <a class="gatsby-resp-image-link"
                        href="/static/0aeff9bcdfb1f5fd002610b4a5cff197/460fa/ethereum-state-transition.png"
                        target="_blank" rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:23.666666666666668%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAFCAYAAABFA8wzAAAACXBIWXMAABYlAAAWJQFJUiTwAAABa0lEQVQY0x1PO0/CUBjtz3RxcTJOjgwuxrCZGCdHMaEtFCiP0pZnkT4uJUjppS8jJoJSCYOGh4Cmw+eF4STfd05yHtRSeQhWnYK9CdBg4xvjpatfhYZw/G2WJutu0Vr2q3jhqJhoeBsga+2q73NbPZ8r7OmyzXwQWBvnca/htadjaqqkIVR5CE0ZPvfoVhm3no3hchJGDQ5WWDnwfi0Db2oRNp4GC1e/eW1m42E7C9MWB1MkQtiRDx6ULaUAy+k/k0/8dvMJ6Ak0Y9dyl7bIQl+gd1+DZjTrViKFvotM/n63NyQrbvV88hrLKRiI7K4vJKM9yB9RQ4kl6RxM9DJJkUhKhfHquZhHuLFaOnAzswIjhScNSzDvVWHWq99YIhvHIgNYYmBIjG2RBqeSBsqpcr2XVt748Q1tGxjO2jcuBjJ31C/THmmoPQk0skQGWWUG2RKrkdsn+lmgFE78ejbwGzktaPLIrWXQs5JH/7+eOtiKcl2dAAAAAElFTkSuQmCC&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="Ethereum state transition"
                          title="Ethereum state transition"
                          src="../../static/0aeff9bcdfb1f5fd002610b4a5cff197/c1b63/ethereum-state-transition.png"
                         sizes="(max-width: 1200px) 100vw, 1200px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">From a technical standpoint, the
                    ledger of a cryptocurrency such as Bitcoin can be thought of as a state transition system, where
                    there is a &quot;state&quot; consisting of the ownership status of all existing bitcoins and a
                    &quot;state transition function&quot; that takes a state and a transaction and outputs a new state
                    which is the result. In a standard banking system, for example, the state is a balance sheet, a
                    transaction is a request to move $X from A to B, and the state transition function reduces the value
                    in A&#x27;s account by $X and increases the value in B&#x27;s account by $X. If A&#x27;s account has
                    less than $X in the first place, the state transition function returns an error. Hence, one can
                    formally define:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>APPLY(S,TX) -&gt; S&#x27; or ERROR
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In the banking system defined above:
                  </p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>APPLY({ Alice: $50, Bob: $50 },&quot;send $20 from Alice to Bob&quot;) = { Alice: $30, Bob: $70 }
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">But:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>APPLY({ Alice: $50, Bob: $50 },&quot;send $70 from Alice to Bob&quot;) = ERROR
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The &quot;state&quot; in Bitcoin is
                    the collection of all coins (technically, &quot;unspent transaction outputs&quot; or UTXO) that have
                    been minted and not yet spent, with each UTXO having a denomination and an owner (defined by a
                    20-byte address which is essentially a cryptographic public key<sup><a href="#notes">fn.
                        1</a></sup>). A transaction contains one or more inputs, with each input containing a reference
                    to an existing UTXO and a cryptographic signature produced by the private key associated with the
                    owner&#x27;s address, and one or more outputs, with each output containing a new UTXO to be added to
                    the state.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The state transition function
                    <code>APPLY(S,TX) -&gt; S&#x27;</code> can be defined roughly as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">For each input in <code>TX</code>:
                      <ul>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">If the referenced UTXO is not
                          in <code>S</code>, return an error.</li>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">If the provided signature does
                          not match the owner of the UTXO, return an error.</li>
                      </ul>
                    </li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">If the sum of the denominations of
                      all input UTXO is less than the sum of the denominations of all output UTXO, return an error.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Return <code>S</code> with all
                      input UTXO removed and all output UTXO added.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The first half of the first step
                    prevents transaction senders from spending coins that do not exist, the second half of the first
                    step prevents transaction senders from spending other people&#x27;s coins, and the second step
                    enforces conservation of value. In order to use this for payment, the protocol is as follows.
                    Suppose Alice wants to send 11.7 BTC to Bob. First, Alice will look for a set of available UTXO that
                    she owns that totals up to at least 11.7 BTC. Realistically, Alice will not be able to get exactly
                    11.7 BTC; say that the smallest she can get is 6+4+2=12. She then creates a transaction with those
                    three inputs and two outputs. The first output will be 11.7 BTC with Bob&#x27;s address as its
                    owner, and the second output will be the remaining 0.3 BTC &quot;change&quot;, with the owner being
                    Alice herself.</p>
                  <h3 id="mining" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#mining"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Mining</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:1200px">
                      <a class="gatsby-resp-image-link"
                        href="/static/6f7d50fd4fab9f8abb94b5e610ade7e4/bf8c1/ethereum-blocks.png" target="_blank"
                        rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:29.000000000000004%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAGCAYAAADDl76dAAAACXBIWXMAABYlAAAWJQFJUiTwAAABw0lEQVQY0x1Nu27TUBg+wMBbQBfKZejEgsTAxMYT8AK8AxJbN0bEUAkhRBuSkMR22sS3Yx+fY5/jS3xJ7TQRJYJGVUMRFWpdgQhq+2M6fMN3R0es+brkrVnpS6MT3pqUubNL9a2lQ7dz/5jVp6ehMj5h9eI00Q4OE7IKcwX9pI2NUrT3KoxK/vHTcU5Tr99ZEpqE0NTrq/sRhr3AhHlKYZ65YEqN5YktP5oFBsxCDF99A77nHKY+fgdQoi9cjfYj67LzLWNwkLHfrNu4M7IVhLi+qUQuAW7pfzPfPS8CBvLG21tUqj0MHQwBtc6EbSx2Yh8iW1sDACTMnjvwHPCwtkiFC9uCHlnt9duZ2kTI1+ReyjAk1FwkDF/knnWutmrLdnv9cWipUGlnETH+DDmBhKivEEJXhN7lcdWJiL4YOCZkzPzhyPW76MHTqyi2+2To2ZC6GGJqwpCZYMrNe0SqPfl/FBL9EtUwVNk31eC1mGjDmFaercHAMSBx9F9Eqa88e756HY0882Xu4UJosh/barTDcdJrvr9R2N2VzxHNMqKGsbUlxsIa50x/AXCBdgOyVvFigDf9barHE2E5tNu4aXQ+oH8CkF/LUHe11AAAAABJRU5ErkJggg==&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="Ethereum blocks" title="Ethereum blocks"
                          src="../../static/6f7d50fd4fab9f8abb94b5e610ade7e4/c1b63/ethereum-blocks.png"
                         sizes="(max-width: 1200px) 100vw, 1200px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">If we had access to a trustworthy
                    centralized service, this system would be trivial to implement; it could simply be coded exactly as
                    described, using a centralized server&#x27;s hard drive to keep track of the state. However, with
                    Bitcoin we are trying to build a decentralized currency system, so we will need to combine the state
                    transaction system with a consensus system in order to ensure that everyone agrees on the order of
                    transactions. Bitcoin&#x27;s decentralized consensus process requires nodes in the network to
                    continuously attempt to produce packages of transactions called &quot;blocks&quot;. The network is
                    intended to produce roughly one block every ten minutes, with each block containing a timestamp, a
                    nonce, a reference to (ie. hash of) the previous block and a list of all of the transactions that
                    have taken place since the previous block. Over time, this creates a persistent, ever-growing,
                    &quot;blockchain&quot; that constantly updates to represent the latest state of the Bitcoin ledger.
                  </p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The algorithm for checking if a
                    block is valid, expressed in this paradigm, is as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check if the previous block
                      referenced by the block exists and is valid.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the timestamp of the
                      block is greater than that of the previous block<sup><a href="#notes">fn. 2</a></sup> and less
                      than 2 hours into the future</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the proof-of-work on
                      the block is valid.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Let <code>S[0]</code> be the state
                      at the end of the previous block.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Suppose <code>TX</code> is the
                      block&#x27;s transaction list with <code>n</code> transactions. For all <code>i</code> in
                      <code>0...n-1</code>, set <code>S[i+1] = APPLY(S[i],TX[i])</code> If any application returns an
                      error, exit and return false.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Return true, and register
                      <code>S[n]</code> as the state at the end of this block.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Essentially, each transaction in the
                    block must provide a valid state transition from what was the canonical state before the transaction
                    was executed to some new state. Note that the state is not encoded in the block in any way; it is
                    purely an abstraction to be remembered by the validating node and can only be (securely) computed
                    for any block by starting from the genesis state and sequentially applying every transaction in
                    every block. Additionally, note that the order in which the miner includes transactions into the
                    block matters; if there are two transactions A and B in a block such that B spends a UTXO created by
                    A, then the block will be valid if A comes before B but not otherwise.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The one validity condition present
                    in the above list that is not found in other systems is the requirement for
                    &quot;proof-of-work&quot;. The precise condition is that the double-SHA256 hash of every block,
                    treated as a 256-bit number, must be less than a dynamically adjusted target, which as of the time
                    of this writing is approximately 2<sup>187</sup>. The purpose of this is to make block creation
                    computationally &quot;hard&quot;, thereby preventing sybil attackers from remaking the entire
                    blockchain in their favor. Because SHA256 is designed to be a completely unpredictable pseudorandom
                    function, the only way to create a valid block is simply trial and error, repeatedly incrementing
                    the nonce and seeing if the new hash matches.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">At the current target of
                    ~2<sup>187</sup>, the network must make an average of ~2<sup>69</sup> tries before a valid block is
                    found; in general, the target is recalibrated by the network every 2016 blocks so that on average a
                    new block is produced by some node in the network every ten minutes. In order to compensate miners
                    for this computational work, the miner of every block is entitled to include a transaction giving
                    themselves 25 BTC out of nowhere. Additionally, if any transaction has a higher total denomination
                    in its inputs than in its outputs, the difference also goes to the miner as a &quot;transaction
                    fee&quot;. Incidentally, this is also the only mechanism by which BTC are issued; the genesis state
                    contained no coins at all.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In order to better understand the
                    purpose of mining, let us examine what happens in the event of a malicious attacker. Since
                    Bitcoin&#x27;s underlying cryptography is known to be secure, the attacker will target the one part
                    of the Bitcoin system that is not protected by cryptography directly: the order of transactions. The
                    attacker&#x27;s strategy is simple:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Send 100 BTC to a merchant in
                      exchange for some product (preferably a rapid-delivery digital good)</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Wait for the delivery of the
                      product</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Produce another transaction
                      sending the same 100 BTC to himself</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Try to convince the network that
                      his transaction to himself was the one that came first.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Once step (1) has taken place, after
                    a few minutes some miner will include the transaction in a block, say block number 270000. After
                    about one hour, five more blocks will have been added to the chain after that block, with each of
                    those blocks indirectly pointing to the transaction and thus &quot;confirming&quot; it. At this
                    point, the merchant will accept the payment as finalized and deliver the product; since we are
                    assuming this is a digital good, delivery is instant. Now, the attacker creates another transaction
                    sending the 100 BTC to himself. If the attacker simply releases it into the wild, the transaction
                    will not be processed; miners will attempt to run <code>APPLY(S,TX)</code> and notice that
                    <code>TX</code> consumes a UTXO which is no longer in the state. So instead, the attacker creates a
                    &quot;fork&quot; of the blockchain, starting by mining another version of block 270000 pointing to
                    the same block 269999 as a parent but with the new transaction in place of the old one. Because the
                    block data is different, this requires redoing the proof-of-work. Furthermore, the attacker&#x27;s
                    new version of block 270000 has a different hash, so the original blocks 270001 to 270005 do not
                    &quot;point&quot; to it; thus, the original chain and the attacker&#x27;s new chain are completely
                    separate. The rule is that in a fork the longest blockchain is taken to be the truth, and so
                    legitimate miners will work on the 270005 chain while the attacker alone is working on the 270000
                    chain. In order for the attacker to make his blockchain the longest, he would need to have more
                    computational power than the rest of the network combined in order to catch up (hence, &quot;51%
                    attack&quot;).</p>
                  <h3 id="merkle-trees" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#merkle-trees"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Merkle Trees</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:988px">
                      <a class="gatsby-resp-image-link"
                        href="/static/47aecc91895df6cd1b3e8089aa7e9a7c/e4900/spv-bitcoin.png" target="_blank"
                        rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:61.33333333333334%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAMCAYAAABiDJ37AAAACXBIWXMAABYlAAAWJQFJUiTwAAADjUlEQVQozz2Sf0xbVRTHT7taNg3WYFvGj0wXR4yo2Owfg8aY7Q+dPxLEbJotWfw5NVHiNkYdYlqCbcfWH7y+13d/vLYPOujSbSEyidjRWmahS/khyJoNtmEmCEGMwdBRp4vheVsTb3Lu955zk0++59wLlFBgUUQoMSCK9M2k2djY1Wi08G0GijsNGEsGWfYAIURDKTVIlOoxxsauUMgoiqKB1fWsvpWpmikAJhiwhA1jZEwLAyzPYBSYCMygG8KRUDIDZ85ivYj8eVhxMBDQJQYH4XIy2To0ODg7mkqdGk+lgNX1kiQVFYCx3qMgS+eLbYJz67vS++WOhKNOGqX7bb1o98E3hvUW65mKy+lmFXNaJCJUjpmj/r6+PUOx2P6B/v6XBUEoY64fZjANcwlsKYATcfD+fKIej+KZzivBbkXJgZ/POjCZviUIv7d2dt2ES7ELkB4ZscSj0bn08LBtenwcGs1mHYPtYiDT/y1bbX/oAj5lh+/r9G73CH4dXxMPtV/rMHk6Z2uxOPOay71YwyMFrG0ntZficdN3AwP1Y8nkvql0+qlEPF7BHJazdisQxlAActxfLxE6e9PvW/jmxU8UI530hekEneaVd55AvVPgtCgg+cP3SpSYXF6vCR58QD2USnExNsPJdPq9xMWLUFZVlX+0QkDd3kVtw4eZysMnkg/xP5IDnh9cx44Nf/k0x8+/Sun8QZfrdk0kqgAvh0ouuJyVq0G5fkXGny9I6Fl3KFwWxGIpc1ZCCVER9mMKM0Rnr7JdAe8Vz1f8BPf98TSp5ezZUz509ZbTmf2guy8HC289A8qu57fkJOlcThSG7zpaXrjP0V4coNIeitCT56xWtS0a/Q/Ywa18jIU5oZ37ZZ84LTbiKeQ5ufb2dm90EdqPsHt7Pfgx2uaV5eqmz8zGbFD+6DYR6d2OtldqbLBJYZT56schrwWgT/y1Iej/CXPh64f4jNDimxTd7jmuukUpAXdfQkP9IZAIqRT9/ke/tdu335Hlw2tU4nII1zKI9h/9/ds2AHSKCtQF4OnuJZADM/mzNoiW7V3Sb1xrU/axtuOr4PhiTZUfti0chn6LJe/innVZblqLRHx3PJ7nWK7Z2Lx5B9NHGHRLAcjzS5q9b+ag4dOVqtDp2WxP+IbSwS3Xcd5l4IWlTXlgxmxWKaWl8PfOnbr1np7r65GIkiPk6J9uD6wqinqjQAL4FwVi56EncFwmAAAAAElFTkSuQmCC&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="SPV in Bitcoin" title="SPV in Bitcoin"
                          src="../../static/47aecc91895df6cd1b3e8089aa7e9a7c/e4900/spv-bitcoin.png"
                          sizes="(max-width: 988px) 100vw, 988px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>Left: it suffices to present
                      only a small number of nodes in a Merkle tree to give a proof of the validity of a branch.</em>
                  </p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>Right: any attempt to change any
                      part of the Merkle tree will eventually lead to an inconsistency somewhere up the chain.</em></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">An important scalability feature of
                    Bitcoin is that the block is stored in a multi-level data structure. The &quot;hash&quot; of a block
                    is actually only the hash of the block header, a roughly 200-byte piece of data that contains the
                    timestamp, nonce, previous block hash and the root hash of a data structure called the Merkle tree
                    storing all transactions in the block. A Merkle tree is a type of binary tree, composed of a set of
                    nodes with a large number of leaf nodes at the bottom of the tree containing the underlying data, a
                    set of intermediate nodes where each node is the hash of its two children, and finally a single root
                    node, also formed from the hash of its two children, representing the &quot;top&quot; of the tree.
                    The purpose of the Merkle tree is to allow the data in a block to be delivered piecemeal: a node can
                    download only the header of a block from one source, the small part of the tree relevant to them
                    from another source, and still be assured that all of the data is correct. The reason why this works
                    is that hashes propagate upward: if a malicious user attempts to swap in a fake transaction into the
                    bottom of a Merkle tree, this change will cause a change in the node above, and then a change in the
                    node above that, finally changing the root of the tree and therefore the hash of the block, causing
                    the protocol to register it as a completely different block (almost certainly with an invalid
                    proof-of-work).</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Merkle tree protocol is arguably
                    essential to long-term sustainability. A &quot;full node&quot; in the Bitcoin network, one that
                    stores and processes the entirety of every block, takes up about 15 GB of disk space in the Bitcoin
                    network as of April 2014, and is growing by over a gigabyte per month. Currently, this is viable for
                    some desktop computers and not phones, and later on in the future only businesses and hobbyists will
                    be able to participate. A protocol known as &quot;simplified payment verification&quot; (SPV) allows
                    for another class of nodes to exist, called &quot;light nodes&quot;, which download the block
                    headers, verify the proof-of-work on the block headers, and then download only the
                    &quot;branches&quot; associated with transactions that are relevant to them. This allows light nodes
                    to determine with a strong guarantee of security what the status of any Bitcoin transaction, and
                    their current balance, is while downloading only a very small portion of the entire blockchain.</p>
                  <h3 id="alternative-blockchain-applications" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#alternative-blockchain-applications"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Alternative Blockchain Applications</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The idea of taking the underlying
                    blockchain idea and applying it to other concepts also has a long history. In 2005, Nick Szabo came
                    out with the concept of &quot;<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://nakamotoinstitute.org/secure-property-titles/" target="_blank"
                      rel="noopener noreferrer">secure property titles with owner authority</a>&quot;, a document
                    describing how &quot;new advances in replicated database technology&quot; will allow for a
                    blockchain-based system for storing a registry of who owns what land, creating an elaborate
                    framework including concepts such as homesteading, adverse possession and Georgian land tax.
                    However, there was unfortunately no effective replicated database system available at the time, and
                    so the protocol was never implemented in practice. After 2009, however, once Bitcoin&#x27;s
                    decentralized consensus was developed a number of alternative applications rapidly began to emerge.
                  </p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Namecoin</strong> -
                      created in 2010, <a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://namecoin.org/"
                        target="_blank" rel="noopener noreferrer">Namecoin</a> is best described as a decentralized name
                      registration database. In decentralized protocols like Tor, Bitcoin and BitMessage, there needs to
                      be some way of identifying accounts so that other people can interact with them, but in all
                      existing solutions the only kind of identifier available is a pseudorandom hash like
                      <code>**********************************</code>. Ideally, one would like to be able to have an
                      account with a name like &quot;george&quot;. However, the problem is that if one person can create
                      an account named &quot;george&quot; then someone else can use the same process to register
                      &quot;george&quot; for themselves as well and impersonate them. The only solution is a
                      first-to-file paradigm, where the first registerer succeeds and the second fails - a problem
                      perfectly suited for the Bitcoin consensus protocol. Namecoin is the oldest, and most successful,
                      implementation of a name registration system using such an idea.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Colored coins</strong> -
                      the purpose of <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://docs.google.com/a/buterin.com/document/d/1AnkP_cVZTCMLIzw4DvsW6M8Q2JC0lIzrTLuoWu2z1BE/edit"
                        target="_blank" rel="noopener noreferrer">colored coins</a> is to serve as a protocol to allow
                      people to create their own digital currencies - or, in the important trivial case of a currency
                      with one unit, digital tokens, on the Bitcoin blockchain. In the colored coins protocol, one
                      &quot;issues&quot; a new currency by publicly assigning a color to a specific Bitcoin UTXO, and
                      the protocol recursively defines the color of other UTXO to be the same as the color of the inputs
                      that the transaction creating them spent (some special rules apply in the case of mixed-color
                      inputs). This allows users to maintain wallets containing only UTXO of a specific color and send
                      them around much like regular bitcoins, backtracking through the blockchain to determine the color
                      of any UTXO that they receive.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Metacoins</strong> - the
                      idea behind a metacoin is to have a protocol that lives on top of Bitcoin, using Bitcoin
                      transactions to store metacoin transactions but having a different state transition function,
                      <code>APPLY&#x27;</code>. Because the metacoin protocol cannot prevent invalid metacoin
                      transactions from appearing in the Bitcoin blockchain, a rule is added that if
                      <code>APPLY&#x27;(S,TX)</code> returns an error, the protocol defaults to
                      <code>APPLY&#x27;(S,TX) = S</code>. This provides an easy mechanism for creating an arbitrary
                      cryptocurrency protocol, potentially with advanced features that cannot be implemented inside of
                      Bitcoin itself, but with a very low development cost since the complexities of mining and
                      networking are already handled by the Bitcoin protocol. Metacoins have been used to implement some
                      classes of financial contracts, name registration and decentralized exchange.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Thus, in general, there are two
                    approaches toward building a consensus protocol: building an independent network, and building a
                    protocol on top of Bitcoin. The former approach, while reasonably successful in the case of
                    applications like Namecoin, is difficult to implement; each individual implementation needs to
                    bootstrap an independent blockchain, as well as building and testing all of the necessary state
                    transition and networking code. Additionally, we predict that the set of applications for
                    decentralized consensus technology will follow a power law distribution where the vast majority of
                    applications would be too small to warrant their own blockchain, and we note that there exist large
                    classes of decentralized applications, particularly decentralized autonomous organizations, that
                    need to interact with each other.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Bitcoin-based approach, on the
                    other hand, has the flaw that it does not inherit the simplified payment verification features of
                    Bitcoin. SPV works for Bitcoin because it can use blockchain depth as a proxy for validity; at some
                    point, once the ancestors of a transaction go far enough back, it is safe to say that they were
                    legitimately part of the state. Blockchain-based meta-protocols, on the other hand, cannot force the
                    blockchain not to include transactions that are not valid within the context of their own protocols.
                    Hence, a fully secure SPV meta-protocol implementation would need to backward scan all the way to
                    the beginning of the Bitcoin blockchain to determine whether or not certain transactions are valid.
                    Currently, all &quot;light&quot; implementations of Bitcoin-based meta-protocols rely on a trusted
                    server to provide the data, arguably a highly suboptimal result especially when one of the primary
                    purposes of a cryptocurrency is to eliminate the need for trust.</p>
                  <h3 id="scripting" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#scripting"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Scripting</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Even without any extensions, the
                    Bitcoin protocol actually does facilitate a weak version of a concept of &quot;smart
                    contracts&quot;. UTXO in Bitcoin can be owned not just by a public key, but also by a more
                    complicated script expressed in a simple stack-based programming language. In this paradigm, a
                    transaction spending that UTXO must provide data that satisfies the script. Indeed, even the basic
                    public key ownership mechanism is implemented via a script: the script takes an elliptic curve
                    signature as input, verifies it against the transaction and the address that owns the UTXO, and
                    returns 1 if the verification is successful and 0 otherwise. Other, more complicated, scripts exist
                    for various additional use cases. For example, one can construct a script that requires signatures
                    from two out of a given three private keys to validate (&quot;multisig&quot;), a setup useful for
                    corporate accounts, secure savings accounts and some merchant escrow situations. Scripts can also be
                    used to pay bounties for solutions to computational problems, and one can even construct a script
                    that says something like &quot;this Bitcoin UTXO is yours if you can provide an SPV proof that you
                    sent a Dogecoin transaction of this denomination to me&quot;, essentially allowing decentralized
                    cross-cryptocurrency exchange.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">However, the scripting language as
                    implemented in Bitcoin has several important limitations:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Lack of
                        Turing-completeness</strong> - that is to say, while there is a large subset of computation that
                      the Bitcoin scripting language supports, it does not nearly support everything. The main category
                      that is missing is loops. This is done to avoid infinite loops during transaction verification;
                      theoretically it is a surmountable obstacle for script programmers, since any loop can be
                      simulated by simply repeating the underlying code many times with an if statement, but it does
                      lead to scripts that are very space-inefficient. For example, implementing an alternative elliptic
                      curve signature algorithm would likely require 256 repeated multiplication rounds all individually
                      included in the code.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Value-blindness</strong> -
                      there is no way for a UTXO script to provide fine-grained control over the amount that can be
                      withdrawn. For example, one powerful use case of an oracle contract would be a hedging contract,
                      where A and B put in $1000 worth of BTC and after 30 days the script sends $1000 worth of BTC to A
                      and the rest to B. This would require an oracle to determine the value of 1 BTC in USD, but even
                      then it is a massive improvement in terms of trust and infrastructure requirement over the fully
                      centralized solutions that are available now. However, because UTXO are all-or-nothing, the only
                      way to achieve this is through the very inefficient hack of having many UTXO of varying
                      denominations (eg. one UTXO of 2<sup>k</sup> for every k up to 30) and having O pick which UTXO to
                      send to A and which to B.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Lack of state</strong> -
                      UTXO can either be spent or unspent; there is no opportunity for multi-stage contracts or scripts
                      which keep any other internal state beyond that. This makes it hard to make multi-stage options
                      contracts, decentralized exchange offers or two-stage cryptographic commitment protocols
                      (necessary for secure computational bounties). It also means that UTXO can only be used to build
                      simple, one-off contracts and not more complex &quot;stateful&quot; contracts such as
                      decentralized organizations, and makes meta-protocols difficult to implement. Binary state
                      combined with value-blindness also mean that another important application, withdrawal limits, is
                      impossible.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">
                      <strong>Blockchain-blindness</strong> - UTXO are blind to blockchain data such as the nonce, the
                      timestamp and previous block hash. This severely limits applications in gambling, and several
                      other categories, by depriving the scripting language of a potentially valuable source of
                      randomness.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Thus, we see three approaches to
                    building advanced applications on top of cryptocurrency: building a new blockchain, using scripting
                    on top of Bitcoin, and building a meta-protocol on top of Bitcoin. Building a new blockchain allows
                    for unlimited freedom in building a feature set, but at the cost of development time, bootstrapping
                    effort and security. Using scripting is easy to implement and standardize, but is very limited in
                    its capabilities, and meta-protocols, while easy, suffer from faults in scalability. With Ethereum,
                    we intend to build an alternative framework that provides even larger gains in ease of development
                    as well as even stronger light client properties, while at the same time allowing applications to
                    share an economic environment and blockchain security.</p>
                  <h2 id="ethereum" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#ethereum"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Ethereum</h2>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The intent of Ethereum is to create
                    an alternative protocol for building decentralized applications, providing a different set of
                    tradeoffs that we believe will be very useful for a large class of decentralized applications, with
                    particular emphasis on situations where rapid development time, security for small and rarely used
                    applications, and the ability of different applications to very efficiently interact, are important.
                    Ethereum does this by building what is essentially the ultimate abstract foundational layer: a
                    blockchain with a built-in Turing-complete programming language, allowing anyone to write smart
                    contracts and decentralized applications where they can create their own arbitrary rules for
                    ownership, transaction formats and state transition functions. A bare-bones version of Namecoin can
                    be written in two lines of code, and other protocols like currencies and reputation systems can be
                    built in under twenty. Smart contracts, cryptographic &quot;boxes&quot; that contain value and only
                    unlock it if certain conditions are met, can also be built on top of the platform, with vastly more
                    power than that offered by Bitcoin scripting because of the added powers of Turing-completeness,
                    value-awareness, blockchain-awareness and state.</p>
                  <h3 id="ethereum-accounts" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#ethereum-accounts"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Ethereum Accounts</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In Ethereum, the state is made up of
                    objects called &quot;accounts&quot;, with each account having a 20-byte address and state
                    transitions being direct transfers of value and information between accounts. An Ethereum account
                    contains four fields:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The <strong>nonce</strong>, a
                      counter used to make sure each transaction can only be processed once</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The account&#x27;s current
                      <strong>ether balance</strong></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The account&#x27;s
                      <strong>contract code</strong>, if present</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The account&#x27;s
                      <strong>storage</strong> (empty by default)</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">&quot;Ether&quot; is the main
                    internal crypto-fuel of Ethereum, and is used to pay transaction fees. In general, there are two
                    types of accounts: <strong>externally owned accounts</strong>, controlled by private keys, and
                    <strong>contract accounts</strong>, controlled by their contract code. An externally owned account
                    has no code, and one can send messages from an externally owned account by creating and signing a
                    transaction; in a contract account, every time the contract account receives a message its code
                    activates, allowing it to read and write to internal storage and send other messages or create
                    contracts in turn.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Note that &quot;contracts&quot; in
                    Ethereum should not be seen as something that should be &quot;fulfilled&quot; or &quot;complied
                    with&quot;; rather, they are more like &quot;autonomous agents&quot; that live inside of the
                    Ethereum execution environment, always executing a specific piece of code when &quot;poked&quot; by
                    a message or transaction, and having direct control over their own ether balance and their own
                    key/value store to keep track of persistent variables.</p>
                  <h3 id="messages-and-transactions" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#messages-and-transactions"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Messages and Transactions</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The term &quot;transaction&quot; is
                    used in Ethereum to refer to the signed data package that stores a message to be sent from an
                    externally owned account. Transactions contain:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The recipient of the message</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A signature identifying the sender
                    </li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The amount of ether to transfer
                      from the sender to the recipient</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An optional data field</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A <code>STARTGAS</code> value,
                      representing the maximum number of computational steps the transaction execution is allowed to
                      take</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A <code>GASPRICE</code> value,
                      representing the fee the sender pays per computational step</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The first three are standard fields
                    expected in any cryptocurrency. The data field has no function by default, but the virtual machine
                    has an opcode using which a contract can access the data; as an example use case, if a contract is
                    functioning as an on-blockchain domain registration service, then it may wish to interpret the data
                    being passed to it as containing two &quot;fields&quot;, the first field being a domain to register
                    and the second field being the IP address to register it to. The contract would read these values
                    from the message data and appropriately place them in storage.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The <code>STARTGAS</code> and
                    <code>GASPRICE</code> fields are crucial for Ethereum&#x27;s anti-denial of service model. In order
                    to prevent accidental or hostile infinite loops or other computational wastage in code, each
                    transaction is required to set a limit to how many computational steps of code execution it can use.
                    The fundamental unit of computation is &quot;gas&quot;; usually, a computational step costs 1 gas,
                    but some operations cost higher amounts of gas because they are more computationally expensive, or
                    increase the amount of data that must be stored as part of the state. There is also a fee of 5 gas
                    for every byte in the transaction data. The intent of the fee system is to require an attacker to
                    pay proportionately for every resource that they consume, including computation, bandwidth and
                    storage; hence, any transaction that leads to the network consuming a greater amount of any of these
                    resources must have a gas fee roughly proportional to the increment.</p>
                  <h3 id="messages" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#messages"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Messages</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Contracts have the ability to send
                    &quot;messages&quot; to other contracts. Messages are virtual objects that are never serialized and
                    exist only in the Ethereum execution environment. A message contains:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The sender of the message
                      (implicit)</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The recipient of the message</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The amount of ether to transfer
                      alongside the message</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An optional data field</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A <code>STARTGAS</code> value</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Essentially, a message is like a
                    transaction, except it is produced by a contract and not an external actor. A message is produced
                    when a contract currently executing code executes the <code>CALL</code> opcode, which produces and
                    executes a message. Like a transaction, a message leads to the recipient account running its code.
                    Thus, contracts can have relationships with other contracts in exactly the same way that external
                    actors can.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Note that the gas allowance assigned
                    by a transaction or contract applies to the total gas consumed by that transaction and all
                    sub-executions. For example, if an external actor A sends a transaction to B with 1000 gas, and B
                    consumes 600 gas before sending a message to C, and the internal execution of C consumes 300 gas
                    before returning, then B can spend another 100 gas before running out of gas.</p>
                  <h3 id="ethereum-state-transition-function" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#ethereum-state-transition-function"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Ethereum State Transition Function</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:1200px">
                      <a class="gatsby-resp-image-link"
                        href="/static/2c0e5e27e397f4ac6b88082fd28d072f/00e09/ether-state-transition.png" target="_blank"
                        rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:46.666666666666664%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAJCAYAAAAywQxIAAAACXBIWXMAABYlAAAWJQFJUiTwAAACbElEQVQozzWRyW7TABCG/VjwBiyqxK0SAlWcEDckJN6BI08ACKGWpS3p4sRpHG/xvu927Nipk5gkTU0hBaQ29uAUcRj9l++bGc0g59z+4ZLezpf8bvLLodLLuupM6pwudOJBIbeQK/ItcsVuIwvhCMl59PYP5mO2ZHbSSwWtWfq/k11YlIYMesemfbILIX0M80CHqa/BrM7ckSDlsa0Z27hbcPsvCr7xfCaim5nUuROSB2C2PsHYYG/Yqa/CPDQgU+mfiEljMnXSApHCrxNHL2NLLWNbW/kqDyGDbp0rmJr3vsJ3pQlLizxLhM6GQLRLAkMrTxXLtRNZ6iq2dfA4fI70ZUbxTRUCQyknSR/yNKqKfFTOTwcQc9jjwiCImDqAjEXh0u1Fmdy9FxpyZSsiDEO3WjuzLKku5jlktnyGeCyuOAoPCtMtdY4GnaOqQT01sVXwafTZRGgJMbEPS5uCZX0vmzh6aAs0GDwNao+oJAoHk6crTxMhkukzxKYxRSA7EGhi+W0QwLjvVuPIK4euDi55/HSh4b1caMJvrwd/fDb2meamRLZBprs37CTyYBQ61ST2/zX0BFKKPHu9/nUauKVvqKth378+Hw8hYrFHhd5VQ3wXUvoApnJ7MVaI+74mrSLHLLMoWMWuVbqavCqmExi56hwxyabFdbH1+mCLPdBZElLXgIGlrD//pDDJlwG+91lsvN9RDj+8Gsnd+wLeBL52bniOAk/hIAsd6EvUEnGY9p5EdVJbZIJRaIenvhlmvhUMTDkLaXRjJHUQtfEOwd68Rsz2FyTh27d0phPXDcP6LGv2xjn1rTgUSfEvPz1HvlDriVgAAAAASUVORK5CYII=&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="Ether state transition" title="Ether state transition"
                          src="../../static/2c0e5e27e397f4ac6b88082fd28d072f/c1b63/ether-state-transition.png"
                          sizes="(max-width: 1200px) 100vw, 1200px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Ethereum state transition
                    function, <code>APPLY(S,TX) -&gt; S&#x27;</code> can be defined as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check if the transaction is
                      well-formed (ie. has the right number of values), the signature is valid, and the nonce matches
                      the nonce in the sender&#x27;s account. If not, return an error.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Calculate the transaction fee as
                      <code>STARTGAS * GASPRICE</code>, and determine the sending address from the signature. Subtract
                      the fee from the sender&#x27;s account balance and increment the sender&#x27;s nonce. If there is
                      not enough balance to spend, return an error.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Initialize
                      <code>GAS = STARTGAS</code>, and take off a certain quantity of gas per byte to pay for the bytes
                      in the transaction.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Transfer the transaction value
                      from the sender&#x27;s account to the receiving account. If the receiving account does not yet
                      exist, create it. If the receiving account is a contract, run the contract&#x27;s code either to
                      completion or until the execution runs out of gas.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">If the value transfer failed
                      because the sender did not have enough money, or the code execution ran out of gas, revert all
                      state changes except the payment of the fees, and add the fees to the miner&#x27;s account.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Otherwise, refund the fees for all
                      remaining gas to the sender, and send the fees paid for gas consumed to the miner.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">For example, suppose that the
                    contract&#x27;s code is:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>if !self.storage[calldataload(0)]:
    self.storage[calldataload(0)] = calldataload(32)
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Note that in reality the contract
                    code is written in the low-level EVM code; this example is written in Serpent, one of our high-level
                    languages, for clarity, and can be compiled down to EVM code. Suppose that the contract&#x27;s
                    storage starts off empty, and a transaction is sent with 10 ether value, 2000 gas, 0.001 ether
                    gasprice, and 64 bytes of data, with bytes 0-31 representing the number <code>2</code> and bytes
                    32-63 representing the string <code>CHARLIE</code>. The process for the state transition function in
                    this case is as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the transaction is
                      valid and well formed.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the transaction sender
                      has at least 2000
                      <!-- -->*
                      <!-- --> 0.001 = 2 ether. If it is, then subtract 2 ether from the sender&#x27;s account.
                    </li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Initialize gas = 2000; assuming
                      the transaction is 170 bytes long and the byte-fee is 5, subtract 850 so that there is 1150 gas
                      left.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Subtract 10 more ether from the
                      sender&#x27;s account, and add it to the contract&#x27;s account.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Run the code. In this case, this
                      is simple: it checks if the contract&#x27;s storage at index <code>2</code> is used, notices that
                      it is not, and so it sets the storage at index <code>2</code> to the value <code>CHARLIE</code>.
                      Suppose this takes 187 gas, so the remaining amount of gas is 1150 - 187 = 963</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Add 963
                      <!-- -->*
                      <!-- --> 0.001 = 0.963 ether back to the sender&#x27;s account, and return the resulting state.
                    </li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">If there was no contract at the
                    receiving end of the transaction, then the total transaction fee would simply be equal to the
                    provided <code>GASPRICE</code> multiplied by the length of the transaction in bytes, and the data
                    sent alongside the transaction would be irrelevant.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Note that messages work equivalently
                    to transactions in terms of reverts: if a message execution runs out of gas, then that
                    message&#x27;s execution, and all other executions triggered by that execution, revert, but parent
                    executions do not need to revert. This means that it is &quot;safe&quot; for a contract to call
                    another contract, as if A calls B with G gas then A&#x27;s execution is guaranteed to lose at most G
                    gas. Finally, note that there is an opcode, <code>CREATE</code>, that creates a contract; its
                    execution mechanics are generally similar to <code>CALL</code>, with the exception that the output
                    of the execution determines the code of a newly created contract.</p>
                  <h3 id="code-execution" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#code-execution"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Code Execution</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The code in Ethereum contracts is
                    written in a low-level, stack-based bytecode language, referred to as &quot;Ethereum virtual machine
                    code&quot; or &quot;EVM code&quot;. The code consists of a series of bytes, where each byte
                    represents an operation. In general, code execution is an infinite loop that consists of repeatedly
                    carrying out the operation at the current program counter (which begins at zero) and then
                    incrementing the program counter by one, until the end of the code is reached or an error or
                    <code>STOP</code> or <code>RETURN</code> instruction is detected. The operations have access to
                    three types of space in which to store data:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The <strong>stack</strong>, a
                      last-in-first-out container to which values can be pushed and popped</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><strong>Memory</strong>, an
                      infinitely expandable byte array</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The contract&#x27;s long-term
                      <strong>storage</strong>, a key/value store. Unlike stack and memory, which reset after
                      computation ends, storage persists for the long term.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The code can also access the value,
                    sender and data of the incoming message, as well as block header data, and the code can also return
                    a byte array of data as an output.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The formal execution model of EVM
                    code is surprisingly simple. While the Ethereum virtual machine is running, its full computational
                    state can be defined by the tuple
                    <code>(block_state, transaction, message, code, memory, stack, pc, gas)</code>, where
                    <code>block_state</code> is the global state containing all accounts and includes balances and
                    storage. At the start of every round of execution, the current instruction is found by taking the
                    <code>pc</code>th byte of <code>code</code> (or 0 if <code>pc &gt;= len(code)</code>), and each
                    instruction has its own definition in terms of how it affects the tuple. For example,
                    <code>ADD</code> pops two items off the stack and pushes their sum, reduces <code>gas</code> by 1
                    and increments <code>pc</code> by 1, and <code>SSTORE</code> pushes the top two items off the stack
                    and inserts the second item into the contract&#x27;s storage at the index specified by the first
                    item. Although there are many ways to optimize Ethereum virtual machine execution via just-in-time
                    compilation, a basic implementation of Ethereum can be done in a few hundred lines of code.</p>
                  <h3 id="blockchain-and-mining" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#blockchain-and-mining"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Blockchain and Mining</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:1200px">
                      <a class="gatsby-resp-image-link"
                        href="/static/479308517ae57f0198f0a43f893884c3/89557/ethereum-apply-block-diagram.png"
                        target="_blank" rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:18%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAECAYAAACOXx+WAAAACXBIWXMAABYlAAAWJQFJUiTwAAABT0lEQVQY0wFEAbv+ANK0tFjRsbH7zrOzJaByngDVsLQA1LS0xdW1tb3UsLMA4sy7Bs3AtwAAAAAA////ANm4uNTYuLii4bm+AOjQvwXK5cIBud60A8rkwgH67coCANy0sy/Sq6qRxJ2fEdzPswDUva8A1q+ucNCqqGzUvK4A4du3ANjVwAD///8A/8/MAOK4t3fetrVe4Mi4AOHZtQDJ4sIAvOG3AMjhwQDu4cEAAK7P2zG42uSPyNTOKNrRtpPbzrE5tNbibLfa5WbazbA/2NG3lNDLuhL/8sYAWwAAAbbX332z1+RW3NGzUNrRt4PC2LwtuNW0oMLXvC3j2L5/AK/Fz1+40dr1xc3JS9PLse3TyKxhtM3XvrjS3bPSx6tr0cqx78vHuCL///8AkXZqCLfP19q30t6X1Mquh9XMs9WxxKxOpcCi/7LErE7b0bfPcTzPpWUUoQwAAAAASUVORK5CYII=&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="Ethereum apply block diagram"
                          title="Ethereum apply block diagram"
                          src="../../static/479308517ae57f0198f0a43f893884c3/c1b63/ethereum-apply-block-diagram.png"
                         sizes="(max-width: 1200px) 100vw, 1200px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Ethereum blockchain is in many
                    ways similar to the Bitcoin blockchain, although it does have some differences. The main difference
                    between Ethereum and Bitcoin with regard to the blockchain architecture is that, unlike Bitcoin,
                    Ethereum blocks contain a copy of both the transaction list and the most recent state. Aside from
                    that, two other values, the block number and the difficulty, are also stored in the block. The basic
                    block validation algorithm in Ethereum is as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check if the previous block
                      referenced exists and is valid.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the timestamp of the
                      block is greater than that of the referenced previous block and less than 15 minutes into the
                      future</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the block number,
                      difficulty, transaction root, uncle root and gas limit (various low-level Ethereum-specific
                      concepts) are valid.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check that the proof-of-work on
                      the block is valid.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Let <code>S[0]</code> be the state
                      at the end of the previous block.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Let <code>TX</code> be the
                      block&#x27;s transaction list, with <code>n</code> transactions. For all <code>i</code> in
                      <code>0...n-1</code>, set <code>S[i+1] = APPLY(S[i],TX[i])</code>. If any applications returns an
                      error, or if the total gas consumed in the block up until this point exceeds the
                      <code>GASLIMIT</code>, return an error.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Let <code>S_FINAL</code> be
                      <code>S[n]</code>, but adding the block reward paid to the miner.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Check if the Merkle tree root of
                      the state <code>S_FINAL</code> is equal to the final state root provided in the block header. If
                      it is, the block is valid; otherwise, it is not valid.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The approach may seem highly
                    inefficient at first glance, because it needs to store the entire state with each block, but in
                    reality efficiency should be comparable to that of Bitcoin. The reason is that the state is stored
                    in the tree structure, and after every block only a small part of the tree needs to be changed.
                    Thus, in general, between two adjacent blocks the vast majority of the tree should be the same, and
                    therefore the data can be stored once and referenced twice using pointers (ie. hashes of subtrees).
                    A special kind of tree known as a &quot;Patricia tree&quot; is used to accomplish this, including a
                    modification to the Merkle tree concept that allows for nodes to be inserted and deleted, and not
                    just changed, efficiently. Additionally, because all of the state information is part of the last
                    block, there is no need to store the entire blockchain history - a strategy which, if it could be
                    applied to Bitcoin, can be calculated to provide 5-20x savings in space.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">A commonly asked question is
                    &quot;where&quot; contract code is executed, in terms of physical hardware. This has a simple
                    answer: the process of executing contract code is part of the definition of the state transition
                    function, which is part of the block validation algorithm, so if a transaction is added into block
                    <code>B</code> the code execution spawned by that transaction will be executed by all nodes, now and
                    in the future, that download and validate block <code>B</code>.</p>
                  <h2 id="applications" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#applications"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Applications</h2>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In general, there are three types of
                    applications on top of Ethereum. The first category is financial applications, providing users with
                    more powerful ways of managing and entering into contracts using their money. This includes
                    sub-currencies, financial derivatives, hedging contracts, savings wallets, wills, and ultimately
                    even some classes of full-scale employment contracts. The second category is semi-financial
                    applications, where money is involved but there is also a heavy non-monetary side to what is being
                    done; a perfect example is self-enforcing bounties for solutions to computational problems. Finally,
                    there are applications such as online voting and decentralized governance that are not financial at
                    all.</p>
                  <h3 id="token-systems" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#token-systems"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Token Systems</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">On-blockchain token systems have
                    many applications ranging from sub-currencies representing assets such as USD or gold to company
                    stocks, individual tokens representing smart property, secure unforgeable coupons, and even token
                    systems with no ties to conventional value at all, used as point systems for incentivization. Token
                    systems are surprisingly easy to implement in Ethereum. The key point to understand is that all a
                    currency, or token system, fundamentally is, is a database with one operation: subtract X units from
                    A and give X units to B, with the proviso that (i) A had at least X units before the transaction and
                    (2) the transaction is approved by A. All that it takes to implement a token system is to implement
                    this logic into a contract.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The basic code for implementing a
                    token system in Serpent looks as follows:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>def send(to, value):
    if self.storage[msg.sender] &gt;= value:
        self.storage[msg.sender] = self.storage[msg.sender] - value
        self.storage[to] = self.storage[to] + value
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">This is essentially a literal
                    implementation of the &quot;banking system&quot; state transition function described further above
                    in this document. A few extra lines of code need to be added to provide for the initial step of
                    distributing the currency units in the first place and a few other edge cases, and ideally a
                    function would be added to let other contracts query for the balance of an address. But that&#x27;s
                    all there is to it. Theoretically, Ethereum-based token systems acting as sub-currencies can
                    potentially include another important feature that on-chain Bitcoin-based meta-currencies lack: the
                    ability to pay transaction fees directly in that currency. The way this would be implemented is that
                    the contract would maintain an ether balance with which it would refund ether used to pay fees to
                    the sender, and it would refill this balance by collecting the internal currency units that it takes
                    in fees and reselling them in a constant running auction. Users would thus need to
                    &quot;activate&quot; their accounts with ether, but once the ether is there it would be reusable
                    because the contract would refund it each time.</p>
                  <h3 id="financial-derivatives-and-stable-value-currencies" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#financial-derivatives-and-stable-value-currencies"><svg aria-hidden="true"
                        focusable="false" height="16" version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Financial derivatives and Stable-Value Currencies</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Financial derivatives are the most
                    common application of a &quot;smart contract&quot;, and one of the simplest to implement in code.
                    The main challenge in implementing financial contracts is that the majority of them require
                    reference to an external price ticker; for example, a very desirable application is a smart contract
                    that hedges against the volatility of ether (or another cryptocurrency) with respect to the US
                    dollar, but doing this requires the contract to know what the value of ETH/USD is. The simplest way
                    to do this is through a &quot;data feed&quot; contract maintained by a specific party (eg. NASDAQ)
                    designed so that that party has the ability to update the contract as needed, and providing an
                    interface that allows other contracts to send a message to that contract and get back a response
                    that provides the price.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Given that critical ingredient, the
                    hedging contract would look as follows:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Wait for party A to input 1000
                      ether.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Wait for party B to input 1000
                      ether.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Record the USD value of 1000
                      ether, calculated by querying the data feed contract, in storage, say this is $x.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">After 30 days, allow A or B to
                      &quot;reactivate&quot; the contract in order to send $x worth of ether (calculated by querying the
                      data feed contract again to get the new price) to A and the rest to B.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Such a contract would have
                    significant potential in crypto-commerce. One of the main problems cited about cryptocurrency is the
                    fact that it&#x27;s volatile; although many users and merchants may want the security and
                    convenience of dealing with cryptographic assets, they many not wish to face that prospect of losing
                    23% of the value of their funds in a single day. Up until now, the most commonly proposed solution
                    has been issuer-backed assets; the idea is that an issuer creates a sub-currency in which they have
                    the right to issue and revoke units, and provide one unit of the currency to anyone who provides
                    them (offline) with one unit of a specified underlying asset (eg. gold, USD). The issuer then
                    promises to provide one unit of the underlying asset to anyone who sends back one unit of the
                    crypto-asset. This mechanism allows any non-cryptographic asset to be &quot;uplifted&quot; into a
                    cryptographic asset, provided that the issuer can be trusted.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In practice, however, issuers are
                    not always trustworthy, and in some cases the banking infrastructure is too weak, or too hostile,
                    for such services to exist. Financial derivatives provide an alternative. Here, instead of a single
                    issuer providing the funds to back up an asset, a decentralized market of speculators, betting that
                    the price of a cryptographic reference asset (eg. ETH) will go up, plays that role. Unlike issuers,
                    speculators have no option to default on their side of the bargain because the hedging contract
                    holds their funds in escrow. Note that this approach is not fully decentralized, because a trusted
                    source is still needed to provide the price ticker, although arguably even still this is a massive
                    improvement in terms of reducing infrastructure requirements (unlike being an issuer, issuing a
                    price feed requires no licenses and can likely be categorized as free speech) and reducing the
                    potential for fraud.</p>
                  <h3 id="identity-and-reputation-systems" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#identity-and-reputation-systems"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Identity and Reputation Systems</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The earliest alternative
                    cryptocurrency of all, <a class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://namecoin.org/"
                      target="_blank" rel="noopener noreferrer">Namecoin</a>, attempted to use a Bitcoin-like blockchain
                    to provide a name registration system, where users can register their names in a public database
                    alongside other data. The major cited use case is for a <a
                      class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://wikipedia.org/wiki/Domain_Name_System"
                      target="_blank" rel="noopener noreferrer">DNS</a> system, mapping domain names like
                    &quot;bitcoin.org&quot; (or, in Namecoin&#x27;s case, &quot;bitcoin.bit&quot;) to an IP address.
                    Other use cases include email authentication and potentially more advanced reputation systems. Here
                    is the basic contract to provide a Namecoin-like name registration system on Ethereum:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>def register(name, value):
    if !self.storage[name]:
        self.storage[name] = value
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The contract is very simple; all it
                    is is a database inside the Ethereum network that can be added to, but not modified or removed from.
                    Anyone can register a name with some value, and that registration then sticks forever. A more
                    sophisticated name registration contract will also have a &quot;function clause&quot; allowing other
                    contracts to query it, as well as a mechanism for the &quot;owner&quot; (ie. the first registerer)
                    of a name to change the data or transfer ownership. One can even add reputation and web-of-trust
                    functionality on top.</p>
                  <h3 id="decentralized-file-storage" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#decentralized-file-storage"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Decentralized File Storage</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Over the past few years, there have
                    emerged a number of popular online file storage startups, the most prominent being Dropbox, seeking
                    to allow users to upload a backup of their hard drive and have the service store the backup and
                    allow the user to access it in exchange for a monthly fee. However, at this point the file storage
                    market is at times relatively inefficient; a cursory look at various existing solutions shows that,
                    particularly at the &quot;uncanny valley&quot; 20-200 GB level at which neither free quotas nor
                    enterprise-level discounts kick in, monthly prices for mainstream file storage costs are such that
                    you are paying for more than the cost of the entire hard drive in a single month. Ethereum contracts
                    can allow for the development of a decentralized file storage ecosystem, where individual users can
                    earn small quantities of money by renting out their own hard drives and unused space can be used to
                    further drive down the costs of file storage.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The key underpinning piece of such a
                    device would be what we have termed the &quot;decentralized Dropbox contract&quot;. This contract
                    works as follows. First, one splits the desired data up into blocks, encrypting each block for
                    privacy, and builds a Merkle tree out of it. One then makes a contract with the rule that, every N
                    blocks, the contract would pick a random index in the Merkle tree (using the previous block hash,
                    accessible from contract code, as a source of randomness), and give X ether to the first entity to
                    supply a transaction with a simplified payment verification-like proof of ownership of the block at
                    that particular index in the tree. When a user wants to re-download their file, they can use a
                    micropayment channel protocol (eg. pay 1 szabo per 32 kilobytes) to recover the file; the most
                    fee-efficient approach is for the payer not to publish the transaction until the end, instead
                    replacing the transaction with a slightly more lucrative one with the same nonce after every 32
                    kilobytes.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">An important feature of the protocol
                    is that, although it may seem like one is trusting many random nodes not to decide to forget the
                    file, one can reduce that risk down to near-zero by splitting the file into many pieces via secret
                    sharing, and watching the contracts to see each piece is still in some node&#x27;s possession. If a
                    contract is still paying out money, that provides a cryptographic proof that someone out there is
                    still storing the file.</p>
                  <h3 id="decentralized-autonomous-organizations" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#decentralized-autonomous-organizations"><svg aria-hidden="true" focusable="false"
                        height="16" version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Decentralized Autonomous Organizations</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The general concept of a
                    &quot;decentralized autonomous organization&quot; is that of a virtual entity that has a certain set
                    of members or shareholders which, perhaps with a 67% majority, have the right to spend the
                    entity&#x27;s funds and modify its code. The members would collectively decide on how the
                    organization should allocate its funds. Methods for allocating a DAO&#x27;s funds could range from
                    bounties, salaries to even more exotic mechanisms such as an internal currency to reward work. This
                    essentially replicates the legal trappings of a traditional company or nonprofit but using only
                    cryptographic blockchain technology for enforcement. So far much of the talk around DAOs has been
                    around the &quot;capitalist&quot; model of a &quot;decentralized autonomous corporation&quot; (DAC)
                    with dividend-receiving shareholders and tradable shares; an alternative, perhaps described as a
                    &quot;decentralized autonomous community&quot;, would have all members have an equal share in the
                    decision making and require 67% of existing members to agree to add or remove a member. The
                    requirement that one person can only have one membership would then need to be enforced collectively
                    by the group.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">A general outline for how to code a
                    DAO is as follows. The simplest design is simply a piece of self-modifying code that changes if two
                    thirds of members agree on a change. Although code is theoretically immutable, one can easily get
                    around this and have de-facto mutability by having chunks of the code in separate contracts, and
                    having the address of which contracts to call stored in the modifiable storage. In a simple
                    implementation of such a DAO contract, there would be three transaction types, distinguished by the
                    data provided in the transaction:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><code>[0,i,K,V]</code> to register
                      a proposal with index <code>i</code> to change the address at storage index <code>K</code> to
                      value <code>V</code></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><code>[1,i]</code> to register a
                      vote in favor of proposal <code>i</code></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><code>[2,i]</code> to finalize
                      proposal <code>i</code> if enough votes have been made</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The contract would then have clauses
                    for each of these. It would maintain a record of all open storage changes, along with a list of who
                    voted for them. It would also have a list of all members. When any storage change gets to two thirds
                    of members voting for it, a finalizing transaction could execute the change. A more sophisticated
                    skeleton would also have built-in voting ability for features like sending a transaction, adding
                    members and removing members, and may even provide for <a
                      class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://wikipedia.org/wiki/Delegative_democracy" target="_blank"
                      rel="noopener noreferrer">Liquid Democracy</a>-style vote delegation (ie. anyone can assign
                    someone to vote for them, and assignment is transitive so if A assigns B and B assigns C then C
                    determines A&#x27;s vote). This design would allow the DAO to grow organically as a decentralized
                    community, allowing people to eventually delegate the task of filtering out who is a member to
                    specialists, although unlike in the &quot;current system&quot; specialists can easily pop in and out
                    of existence over time as individual community members change their alignments.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">An alternative model is for a
                    decentralized corporation, where any account can have zero or more shares, and two thirds of the
                    shares are required to make a decision. A complete skeleton would involve asset management
                    functionality, the ability to make an offer to buy or sell shares, and the ability to accept offers
                    (preferably with an order-matching mechanism inside the contract). Delegation would also exist
                    Liquid Democracy-style, generalizing the concept of a &quot;board of directors&quot;.</p>
                  <h3 id="further-applications" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#further-applications"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Further Applications</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>1. Savings wallets</strong>.
                    Suppose that Alice wants to keep her funds safe, but is worried that she will lose or someone will
                    hack her private key. She puts ether into a contract with Bob, a bank, as follows:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Alice alone can withdraw a maximum
                      of 1% of the funds per day.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Bob alone can withdraw a maximum
                      of 1% of the funds per day, but Alice has the ability to make a transaction with her key shutting
                      off this ability.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Alice and Bob together can
                      withdraw anything.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Normally, 1% per day is enough for
                    Alice, and if Alice wants to withdraw more she can contact Bob for help. If Alice&#x27;s key gets
                    hacked, she runs to Bob to move the funds to a new contract. If she loses her key, Bob will get the
                    funds out eventually. If Bob turns out to be malicious, then she can turn off his ability to
                    withdraw.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>2. Crop insurance</strong>.
                    One can easily make a financial derivatives contract but using a data feed of the weather instead of
                    any price index. If a farmer in Iowa purchases a derivative that pays out inversely based on the
                    precipitation in Iowa, then if there is a drought, the farmer will automatically receive money and
                    if there is enough rain the farmer will be happy because their crops would do well. This can be
                    expanded to natural disaster insurance generally.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>3. A decentralized data
                      feed</strong>. For financial contracts for difference, it may actually be possible to decentralize
                    the data feed via a protocol called &quot;<a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="http://blog.ethstake.exchange/2014/03/28/schellingcoin-a-minimal-trust-universal-data-feed/"
                      target="_blank" rel="noopener noreferrer">SchellingCoin</a>&quot;. SchellingCoin basically works
                    as follows: N parties all put into the system the value of a given datum (eg. the ETH/USD price),
                    the values are sorted, and everyone between the 25th and 75th percentile gets one token as a reward.
                    Everyone has the incentive to provide the answer that everyone else will provide, and the only value
                    that a large number of players can realistically agree on is the obvious default: the truth. This
                    creates a decentralized protocol that can theoretically provide any number of values, including the
                    ETH/USD price, the temperature in Berlin or even the result of a particular hard computation.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>4. Smart multisignature
                      escrow</strong>. Bitcoin allows multisignature transaction contracts where, for example, three out
                    of a given five keys can spend the funds. Ethereum allows for more granularity; for example, four
                    out of five can spend everything, three out of five can spend up to 10% per day, and two out of five
                    can spend up to 0.5% per day. Additionally, Ethereum multisig is asynchronous - two parties can
                    register their signatures on the blockchain at different times and the last signature will
                    automatically send the transaction.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>5. Cloud computing</strong>.
                    The EVM technology can also be used to create a verifiable computing environment, allowing users to
                    ask others to carry out computations and then optionally ask for proofs that computations at certain
                    randomly selected checkpoints were done correctly. This allows for the creation of a cloud computing
                    market where any user can participate with their desktop, laptop or specialized server, and
                    spot-checking together with security deposits can be used to ensure that the system is trustworthy
                    (ie. nodes cannot profitably cheat). Although such a system may not be suitable for all tasks; tasks
                    that require a high level of inter-process communication, for example, cannot easily be done on a
                    large cloud of nodes. Other tasks, however, are much easier to parallelize; projects like <a
                      href="/cdn-cgi/l/email-protection" class="__cf_email__"
                      data-cfemail="9fccdacbd6dff7f0f2fa">[email&#160;protected]</a>, <a
                      href="/cdn-cgi/l/email-protection" class="__cf_email__"
                      data-cfemail="adcbc2c1c9c4c3caedc5c2c0c8">[email&#160;protected]</a> and genetic algorithms can
                    easily be implemented on top of such a platform.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>6. Peer-to-peer
                      gambling</strong>. Any number of peer-to-peer gambling protocols, such as Frank Stajano and
                    Richard Clayton&#x27;s <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://www.cl.cam.ac.uk/~fms27/papers/2008-StajanoCla-cyberdice.pdf" target="_blank"
                      rel="noopener noreferrer">Cyberdice</a>, can be implemented on the Ethereum blockchain. The
                    simplest gambling protocol is actually simply a contract for difference on the next block hash, and
                    more advanced protocols can be built up from there, creating gambling services with near-zero fees
                    that have no ability to cheat.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>7. Prediction
                      markets</strong>. Provided an oracle or SchellingCoin, prediction markets are also easy to
                    implement, and prediction markets together with SchellingCoin may prove to be the first mainstream
                    application of <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="http://hanson.gmu.edu/futarchy.html" target="_blank" rel="noopener noreferrer">futarchy</a>
                    as a governance protocol for decentralized organizations.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>8. On-chain decentralized
                      marketplaces</strong>, using the identity and reputation system as a base.</p>
                  <h2 id="miscellanea-and-concerns" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#miscellanea-and-concerns"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Miscellanea And Concerns</h2>
                  <h3 id="modified-ghost-implementation" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#modified-ghost-implementation"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Modified GHOST Implementation</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The &quot;Greedy Heaviest Observed
                    Subtree&quot; (GHOST) protocol is an innovation first introduced by Yonatan Sompolinsky and Aviv
                    Zohar in <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://eprint.iacr.org/2013/881.pdf" target="_blank" rel="noopener noreferrer">December
                      2013</a>. The motivation behind GHOST is that blockchains with fast confirmation times currently
                    suffer from reduced security due to a high stale rate - because blocks take a certain time to
                    propagate through the network, if miner A mines a block and then miner B happens to mine another
                    block before miner A&#x27;s block propagates to B, miner B&#x27;s block will end up wasted and will
                    not contribute to network security. Furthermore, there is a centralization issue: if miner A is a
                    mining pool with 30% hashpower and B has 10% hashpower, A will have a risk of producing a stale
                    block 70% of the time (since the other 30% of the time A produced the last block and so will get
                    mining data immediately) whereas B will have a risk of producing a stale block 90% of the time.
                    Thus, if the block interval is short enough for the stale rate to be high, A will be substantially
                    more efficient simply by virtue of its size. With these two effects combined, blockchains which
                    produce blocks quickly are very likely to lead to one mining pool having a large enough percentage
                    of the network hashpower to have de facto control over the mining process.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">As described by Sompolinsky and
                    Zohar, GHOST solves the first issue of network security loss by including stale blocks in the
                    calculation of which chain is the &quot;longest&quot;; that is to say, not just the parent and
                    further ancestors of a block, but also the stale descendants of the block&#x27;s ancestor (in
                    Ethereum jargon, &quot;uncles&quot;) are added to the calculation of which block has the largest
                    total proof-of-work backing it. To solve the second issue of centralization bias, we go beyond the
                    protocol described by Sompolinsky and Zohar, and also provide block rewards to stales: a stale block
                    receives 87.5% of its base reward, and the nephew that includes the stale block receives the
                    remaining 12.5%. Transaction fees, however, are not awarded to uncles.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Ethereum implements a simplified
                    version of GHOST which only goes down seven levels. Specifically, it is defined as follows:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A block must specify a parent, and
                      it must specify 0 or more uncles</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An uncle included in block B must
                      have the following properties:<ul>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">It must be a direct child of
                          the kth generation ancestor of B, where 2 &lt;= k &lt;= 7.</li>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">It cannot be an ancestor of B
                        </li>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An uncle must be a valid block
                          header, but does not need to be a previously verified or even valid block</li>
                        <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An uncle must be different
                          from all uncles included in previous blocks and all other uncles included in the same block
                          (non-double-inclusion)</li>
                      </ul>
                    </li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">For every uncle U in block B, the
                      miner of B gets an additional 3.125% added to its coinbase reward and the miner of U gets 93.75%
                      of a standard coinbase reward.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">This limited version of GHOST, with
                    uncles includable only up to 7 generations, was used for two reasons. First, unlimited GHOST would
                    include too many complications into the calculation of which uncles for a given block are valid.
                    Second, unlimited GHOST with compensation as used in Ethereum removes the incentive for a miner to
                    mine on the main chain and not the chain of a public attacker.</p>
                  <h3 id="fees" style="position:relative" class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW">
                    <a class="header-anchor before" href="#fees"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Fees</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Because every transaction published
                    into the blockchain imposes on the network the cost of needing to download and verify it, there is a
                    need for some regulatory mechanism, typically involving transaction fees, to prevent abuse. The
                    default approach, used in Bitcoin, is to have purely voluntary fees, relying on miners to act as the
                    gatekeepers and set dynamic minimums. This approach has been received very favorably in the Bitcoin
                    community particularly because it is &quot;market-based&quot;, allowing supply and demand between
                    miners and transaction senders determine the price. The problem with this line of reasoning is,
                    however, that transaction processing is not a market; although it is intuitively attractive to
                    construe transaction processing as a service that the miner is offering to the sender, in reality
                    every transaction that a miner includes will need to be processed by every node in the network, so
                    the vast majority of the cost of transaction processing is borne by third parties and not the miner
                    that is making the decision of whether or not to include it. Hence, tragedy-of-the-commons problems
                    are very likely to occur.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">However, as it turns out this flaw
                    in the market-based mechanism, when given a particular inaccurate simplifying assumption, magically
                    cancels itself out. The argument is as follows. Suppose that:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A transaction leads to
                      <code>k</code> operations, offering the reward <code>kR</code> to any miner that includes it where
                      <code>R</code> is set by the sender and <code>k</code> and <code>R</code> are (roughly) visible to
                      the miner beforehand.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An operation has a processing cost
                      of <code>C</code> to any node (ie. all nodes have equal efficiency)</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">There are <code>N</code> mining
                      nodes, each with exactly equal processing power (ie. <code>1/N</code> of total)</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">No non-mining full nodes exist.
                    </li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">A miner would be willing to process
                    a transaction if the expected reward is greater than the cost. Thus, the expected reward is
                    <code>kR/N</code> since the miner has a <code>1/N</code> chance of processing the next block, and
                    the processing cost for the miner is simply <code>kC</code>. Hence, miners will include transactions
                    where <code>kR/N &gt; kC</code>, or <code>R &gt; NC</code>. Note that <code>R</code> is the
                    per-operation fee provided by the sender, and is thus a lower bound on the benefit that the sender
                    derives from the transaction, and <code>NC</code> is the cost to the entire network together of
                    processing an operation. Hence, miners have the incentive to include only those transactions for
                    which the total utilitarian benefit exceeds the cost.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">However, there are several important
                    deviations from those assumptions in reality:</p>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The miner does pay a higher cost
                      to process the transaction than the other verifying nodes, since the extra verification time
                      delays block propagation and thus increases the chance the block will become a stale.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">There do exist nonmining full
                      nodes.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">The mining power distribution may
                      end up radically inegalitarian in practice.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Speculators, political enemies and
                      crazies whose utility function includes causing harm to the network do exist, and they can
                      cleverly set up contracts where their cost is much lower than the cost paid by other verifying
                      nodes.</li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">(1) provides a tendency for the
                    miner to include fewer transactions, and
                    (2) increases <code>NC</code>; hence, these two effects at least partially
                    cancel each other
                    out.<sup><a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://github.com/ethereum/wiki/issues/447#issuecomment-316972260" target="_blank"
                        rel="noopener noreferrer">How?</a></sup>
                    (3) and (4) are the major issue; to solve them we simply institute a
                    floating cap: no block can have more operations than
                    <code>BLK_LIMIT_FACTOR</code> times the long-term exponential moving average.
                    Specifically:
                  </p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>blk.oplimit = floor((blk.parent.oplimit \* (EMAFACTOR - 1) +
floor(parent.opcount \* BLK\_LIMIT\_FACTOR)) / EMA\_FACTOR)
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><code>BLK_LIMIT_FACTOR</code> and
                    <code>EMA_FACTOR</code> are constants that will be set to 65536 and 1.5 for the time being, but will
                    likely be changed after further analysis.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">There is another factor
                    disincentivizing large block sizes in Bitcoin: blocks that are large will take longer to propagate,
                    and thus have a higher probability of becoming stales. In Ethereum, highly gas-consuming blocks can
                    also take longer to propagate both because they are physically larger and because they take longer
                    to process the transaction state transitions to validate. This delay disincentive is a significant
                    consideration in Bitcoin, but less so in Ethereum because of the GHOST protocol; hence, relying on
                    regulated block limits provides a more stable baseline.</p>
                  <h3 id="computation-and-turing-completeness" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#computation-and-turing-completeness"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Computation And Turing-Completeness</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">An important note is that the
                    Ethereum virtual machine is Turing-complete; this means that EVM code can encode any computation
                    that can be conceivably carried out, including infinite loops. EVM code allows looping in two ways.
                    First, there is a <code>JUMP</code> instruction that allows the program to jump back to a previous
                    spot in the code, and a <code>JUMPI</code> instruction to do conditional jumping, allowing for
                    statements like <code>while x &lt; 27: x = x * 2</code>. Second, contracts can call other contracts,
                    potentially allowing for looping through recursion. This naturally leads to a problem: can malicious
                    users essentially shut miners and full nodes down by forcing them to enter into an infinite loop?
                    The issue arises because of a problem in computer science known as the halting problem: there is no
                    way to tell, in the general case, whether or not a given program will ever halt.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">As described in the state transition
                    section, our solution works by requiring a transaction to set a maximum number of computational
                    steps that it is allowed to take, and if execution takes longer computation is reverted but fees are
                    still paid. Messages work in the same way. To show the motivation behind our solution, consider the
                    following examples:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An attacker creates a contract
                      which runs an infinite loop, and then sends a transaction activating that loop to the miner. The
                      miner will process the transaction, running the infinite loop, and wait for it to run out of gas.
                      Even though the execution runs out of gas and stops halfway through, the transaction is still
                      valid and the miner still claims the fee from the attacker for each computational step.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An attacker creates a very long
                      infinite loop with the intent of forcing the miner to keep computing for such a long time that by
                      the time computation finishes a few more blocks will have come out and it will not be possible for
                      the miner to include the transaction to claim the fee. However, the attacker will be required to
                      submit a value for <code>STARTGAS</code> limiting the number of computational steps that execution
                      can take, so the miner will know ahead of time that the computation will take an excessively large
                      number of steps.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">An attacker sees a contract with
                      code of some form like <code>send(A,contract.storage[A]); contract.storage[A] = 0</code>, and
                      sends a transaction with just enough gas to run the first step but not the second (ie. making a
                      withdrawal but not letting the balance go down). The contract author does not need to worry about
                      protecting against such attacks, because if execution stops halfway through the changes get
                      reverted.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A financial contract works by
                      taking the median of nine proprietary data feeds in order to minimize risk. An attacker takes over
                      one of the data feeds, which is designed to be modifiable via the variable-address-call mechanism
                      described in the section on DAOs, and converts it to run an infinite loop, thereby attempting to
                      force any attempts to claim funds from the financial contract to run out of gas. However, the
                      financial contract can set a gas limit on the message to prevent this problem.</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The alternative to
                    Turing-completeness is Turing-incompleteness, where <code>JUMP</code> and <code>JUMPI</code> do not
                    exist and only one copy of each contract is allowed to exist in the call stack at any given time.
                    With this system, the fee system described and the uncertainties around the effectiveness of our
                    solution might not be necessary, as the cost of executing a contract would be bounded above by its
                    size. Additionally, Turing-incompleteness is not even that big a limitation; out of all the contract
                    examples we have conceived internally, so far only one required a loop, and even that loop could be
                    removed by making 26 repetitions of a one-line piece of code. Given the serious implications of
                    Turing-completeness, and the limited benefit, why not simply have a Turing-incomplete language? In
                    reality, however, Turing-incompleteness is far from a neat solution to the problem. To see why,
                    consider the following contracts:</p>
                  <pre class="static__Pre-sc-17clvn-3 gFNWMn"><code>C0: call(C1); call(C1);
C1: call(C2); call(C2);
C2: call(C3); call(C3);
...
C49: call(C50); call(C50);
C50: (run one step of a program and record the change in storage)
</code></pre>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Now, send a transaction to A. Thus,
                    in 51 transactions, we have a contract that takes up 2<sup>50</sup> computational steps. Miners
                    could try to detect such logic bombs ahead of time by maintaining a value alongside each contract
                    specifying the maximum number of computational steps that it can take, and calculating this for
                    contracts calling other contracts recursively, but that would require miners to forbid contracts
                    that create other contracts (since the creation and execution of all 26 contracts above could easily
                    be rolled into a single contract). Another problematic point is that the address field of a message
                    is a variable, so in general it may not even be possible to tell which other contracts a given
                    contract will call ahead of time. Hence, all in all, we have a surprising conclusion:
                    Turing-completeness is surprisingly easy to manage, and the lack of Turing-completeness is equally
                    surprisingly difficult to manage unless the exact same controls are in place - but in that case why
                    not just let the protocol be Turing-complete?</p>
                  <h3 id="currency-and-issuance" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#currency-and-issuance"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Currency And Issuance</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Ethereum network includes its
                    own built-in currency, ether, which serves the dual purpose of providing a primary liquidity layer
                    to allow for efficient exchange between various types of digital assets and, more importantly, of
                    providing a mechanism for paying transaction fees. For convenience and to avoid future argument (see
                    the current mBTC/uBTC/satoshi debate in Bitcoin), the denominations will be pre-labelled:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">1: wei</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">10<sup>12</sup>: szabo</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">10<sup>15</sup>: finney</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">10<sup>18</sup>: ether</li>
                  </ul>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">This should be taken as an expanded
                    version of the concept of &quot;dollars&quot; and &quot;cents&quot; or &quot;BTC&quot; and
                    &quot;satoshi&quot;. In the near future, we expect &quot;ether&quot; to be used for ordinary
                    transactions, &quot;finney&quot; for microtransactions and &quot;szabo&quot; and &quot;wei&quot; for
                    technical discussions around fees and protocol implementation; the remaining denominations may
                    become useful later and should not be included in clients at this point.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The issuance model will be as
                    follows:</p>
                  <ul>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Ether will be released in a
                      currency sale at the price of 1000-2000 ether per BTC, a mechanism intended to fund the Ethereum
                      organization and pay for development that has been used with success by other platforms such as
                      Mastercoin and NXT. Earlier buyers will benefit from larger discounts. The BTC received from the
                      sale will be used entirely to pay salaries and bounties to developers and invested into various
                      for-profit and non-profit projects in the Ethereum and cryptocurrency ecosystem.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">0.099x the total amount sold
                      (60102216 ETH) will be allocated to the organization to compensate early contributors and pay
                      ETH-denominated expenses before the genesis block.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">0.099x the total amount sold will
                      be maintained as a long-term reserve.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">0.26x the total amount sold will
                      be allocated to miners per year forever after that point.</li>
                  </ul>
                  <div class="MarkdownTable__TableContainer-sc-gzuurh-0 gWhFhC">
                    <table>
                      <thead>
                        <tr>
                          <th>Group</th>
                          <th>At launch</th>
                          <th>After 1 year</th>
                          <th>After 5 years</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Currency units</td>
                          <td>1.198X</td>
                          <td>1.458X</td>
                          <td>2.498X</td>
                        </tr>
                        <tr>
                          <td>Purchasers</td>
                          <td>83.5%</td>
                          <td>68.6%</td>
                          <td>40.0%</td>
                        </tr>
                        <tr>
                          <td>Reserve spent pre-sale</td>
                          <td>8.26%</td>
                          <td>6.79%</td>
                          <td>3.96%</td>
                        </tr>
                        <tr>
                          <td>Reserve used post-sale</td>
                          <td>8.26%</td>
                          <td>6.79%</td>
                          <td>3.96%</td>
                        </tr>
                        <tr>
                          <td>Miners</td>
                          <td>0%</td>
                          <td>17.8%</td>
                          <td>52.0%</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><strong>Long-Term Supply Growth Rate
                      (percent)</strong></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><span
                      class="gatsby-resp-image-wrapper"
                      style="position:relative;display:block;margin-left:auto;margin-right:auto;max-width:1030px">
                      <a class="gatsby-resp-image-link"
                        href="/static/1761be452482ac17f0d8c8632e30498e/7a1fb/ethereum-inflation.png" target="_blank"
                        rel="noopener noreferrer">
                        <span class="gatsby-resp-image-background-image"
                          style="padding-bottom:52.33333333333333%;position:relative;bottom:0;left:0;background-image:url(&#x27;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAKCAYAAAC0VX7mAAAACXBIWXMAABYlAAAWJQFJUiTwAAAB9klEQVQoz62R3W4SQRTHR6iuXnvjR6JN9AV8Ab1qgheatLE2MW3VYNHatDGR6h0CrUtoqaJGK7UVNODCFtimCK1hP2Zmd9kPlvgG3vgKvaczsLYbjRcmXvxy/uecmX9mzgHOy9mzqLF1Xd/mxh3+3QND+HTbrObumNXsXUP4PGmX14Od0tvp1lZ+wqpsBNub70O05vCr90m8197MhNQaP9aUleHdncYl8CM1edFs1pagbrFQNxeR1kpA3XhOIZrta2vxUJuk3o8HuaqxqopfSZI0Bpz03KC9UyaH9ChGMIYRokTdeAj21PAf/QjGOCGK4jD4lkkMOrXCMtRatBEnDUrMjV5if9GUKCFJXjgCmhn2fKdnaPwfw/pG+lynll8mXyaGkDSRewH90v9m+GFbPk0NyWKSsm5FoKpFoaofgLAaQ9SgP6u4i1fHezPHKCmJ4gjg+PIZDJWF71/zq06Df22IjRVTrKcIK1TrSnNJxSiBNZ0uzgvr0QtYa6VEWRkFJa5wUpTkoCQrIa1embOFbNgWco8tIRem2NWP8+3K+hOnvEZ56vJ7Pk+IGPxaAITZNDgFwJFbN2/4hl7sDgC+6wPFrs9f6PpPZPf848/eDKQfTRz78jDA8FNXGG722vHS1GVGGL1wtDg9xBRnAgw3c7VX/wkA2Ac1mPvgYr2l6wAAAABJRU5ErkJggg==&#x27;);background-size:cover;display:block"></span>
                        <img class="gatsby-resp-image-image" alt="Ethereum inflation" title="Ethereum inflation"
                          src="../../static/1761be452482ac17f0d8c8632e30498e/7a1fb/ethereum-inflation.png"
                          sizes="(max-width: 1030px) 100vw, 1030px"
                          style="width:100%;height:100%;margin:0;vertical-align:middle;position:absolute;top:0;left:0"
                          loading="lazy" decoding="async">
                      </a>
                    </span></p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>Despite the linear currency
                      issuance, just like with Bitcoin over time the supply growth rate nevertheless tends to zero</em>
                  </p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The two main choices in the above
                    model are (1) the existence and size of an endowment pool, and (2) the existence of a permanently
                    growing linear supply, as opposed to a capped supply as in Bitcoin. The justification of the
                    endowment pool is as follows. If the endowment pool did not exist, and the linear issuance reduced
                    to 0.217x to provide the same inflation rate, then the total quantity of ether would be 16.5% less
                    and so each unit would be 19.8% more valuable. Hence, in the equilibrium 19.8% more ether would be
                    purchased in the sale, so each unit would once again be exactly as valuable as before. The
                    organization would also then have 1.198x as much BTC, which can be considered to be split into two
                    slices: the original BTC, and the additional 0.198x. Hence, this situation is <em>exactly
                      equivalent</em> to the endowment, but with one important difference: the organization holds purely
                    BTC, and so is not incentivized to support the value of the ether unit.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The permanent linear supply growth
                    model reduces the risk of what some see as excessive wealth concentration in Bitcoin, and gives
                    individuals living in present and future eras a fair chance to acquire currency units, while at the
                    same time retaining a strong incentive to obtain and hold ether because the &quot;supply growth
                    rate&quot; as a percentage still tends to zero over time. We also theorize that because coins are
                    always lost over time due to carelessness, death, etc, and coin loss can be modeled as a percentage
                    of the total supply per year, that the total currency supply in circulation will in fact eventually
                    stabilize at a value equal to the annual issuance divided by the loss rate (eg. at a loss rate of
                    1%, once the supply reaches 26X then 0.26X will be mined and 0.26X lost every year, creating an
                    equilibrium).</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Note that in the future, it is
                    likely that Ethereum will switch to a proof-of-stake model for security, reducing the issuance
                    requirement to somewhere between zero and 0.05X per year. In the event that the Ethereum
                    organization loses funding or for any other reason disappears, we leave open a &quot;social
                    contract&quot;: anyone has the right to create a future candidate version of Ethereum, with the only
                    condition being that the quantity of ether must be at most equal to
                    <code>60102216 * (1.198 + 0.26 * n)</code> where <code>n</code> is the number of years after the
                    genesis block. Creators are free to crowd-sell or otherwise assign some or all of the difference
                    between the PoS-driven supply expansion and the maximum allowable supply expansion to pay for
                    development. Candidate upgrades that do not comply with the social contract may justifiably be
                    forked into compliant versions.</p>
                  <h3 id="mining-centralization" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#mining-centralization"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Mining Centralization</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Bitcoin mining algorithm works
                    by having miners compute SHA256 on slightly modified versions of the block header millions of times
                    over and over again, until eventually one node comes up with a version whose hash is less than the
                    target (currently around 2<sup>192</sup>). However, this mining algorithm is vulnerable to two forms
                    of centralization. First, the mining ecosystem has come to be dominated by ASICs
                    (application-specific integrated circuits), computer chips designed for, and therefore thousands of
                    times more efficient at, the specific task of Bitcoin mining. This means that Bitcoin mining is no
                    longer a highly decentralized and egalitarian pursuit, requiring millions of dollars of capital to
                    effectively participate in. Second, most Bitcoin miners do not actually perform block validation
                    locally; instead, they rely on a centralized mining pool to provide the block headers. This problem
                    is arguably worse: as of the time of this writing, the top three mining pools indirectly control
                    roughly 50% of processing power in the Bitcoin network, although this is mitigated by the fact that
                    miners can switch to other mining pools if a pool or coalition attempts a 51% attack.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The current intent at Ethereum is to
                    use a mining algorithm where miners are required to fetch random data from the state, compute some
                    randomly selected transactions from the last N blocks in the blockchain, and return the hash of the
                    result. This has two important benefits. First, Ethereum contracts can include any kind of
                    computation, so an Ethereum ASIC would essentially be an ASIC for general computation - ie. a better
                    CPU. Second, mining requires access to the entire blockchain, forcing miners to store the entire
                    blockchain and at least be capable of verifying every transaction. This removes the need for
                    centralized mining pools; although mining pools can still serve the legitimate role of evening out
                    the randomness of reward distribution, this function can be served equally well by peer-to-peer
                    pools with no central control.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">This model is untested, and there
                    may be difficulties along the way in avoiding certain clever optimizations when using contract
                    execution as a mining algorithm. However, one notably interesting feature of this algorithm is that
                    it allows anyone to &quot;poison the well&quot;, by introducing a large number of contracts into the
                    blockchain specifically designed to stymie certain ASICs. The economic incentives exist for ASIC
                    manufacturers to use such a trick to attack each other. Thus, the solution that we are developing is
                    ultimately an adaptive economic human solution rather than purely a technical one.</p>
                  <h3 id="scalability" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#scalability"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Scalability</h3>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">One common concern about Ethereum is
                    the issue of scalability. Like Bitcoin, Ethereum suffers from the flaw that every transaction needs
                    to be processed by every node in the network. With Bitcoin, the size of the current blockchain rests
                    at about 15 GB, growing by about 1 MB per hour. If the Bitcoin network were to process Visa&#x27;s
                    2000 transactions per second, it would grow by 1 MB per three seconds (1 GB per hour, 8 TB per
                    year). Ethereum is likely to suffer a similar growth pattern, worsened by the fact that there will
                    be many applications on top of the Ethereum blockchain instead of just a currency as is the case
                    with Bitcoin, but ameliorated by the fact that Ethereum full nodes need to store just the state
                    instead of the entire blockchain history.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The problem with such a large
                    blockchain size is centralization risk. If the blockchain size increases to, say, 100 TB, then the
                    likely scenario would be that only a very small number of large businesses would run full nodes,
                    with all regular users using light SPV nodes. In such a situation, there arises the potential
                    concern that the full nodes could band together and all agree to cheat in some profitable fashion
                    (eg. change the block reward, give themselves BTC). Light nodes would have no way of detecting this
                    immediately. Of course, at least one honest full node would likely exist, and after a few hours
                    information about the fraud would trickle out through channels like Reddit, but at that point it
                    would be too late: it would be up to the ordinary users to organize an effort to blacklist the given
                    blocks, a massive and likely infeasible coordination problem on a similar scale as that of pulling
                    off a successful 51% attack. In the case of Bitcoin, this is currently a problem, but there exists a
                    blockchain modification <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                      href="https://web.archive.org/web/20140623061815/http://sourceforge.net/p/bitcoin/mailman/message/31709140/"
                      target="_blank" rel="noopener noreferrer">suggested by Peter Todd</a> which will alleviate this
                    issue.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">In the near term, Ethereum will use
                    two additional strategies to cope with this problem. First, because of the blockchain-based mining
                    algorithms, at least every miner will be forced to be a full node, creating a lower bound on the
                    number of full nodes. Second and more importantly, however, we will include an intermediate state
                    tree root in the blockchain after processing each transaction. Even if block validation is
                    centralized, as long as one honest verifying node exists, the centralization problem can be
                    circumvented via a verification protocol. If a miner publishes an invalid block, that block must
                    either be badly formatted, or the state <code>S[n]</code> is incorrect. Since <code>S[0]</code> is
                    known to be correct, there must be some first state <code>S[i]</code> that is incorrect where
                    <code>S[i-1]</code> is correct. The verifying node would provide the index <code>i</code>, along
                    with a &quot;proof of invalidity&quot; consisting of the subset of Patricia tree nodes needing to
                    process <code>APPLY(S[i-1],TX[i]) -&gt; S[i]</code>. Nodes would be able to use those nodes to run
                    that part of the computation, and see that the <code>S[i]</code> generated does not match the
                    <code>S[i]</code> provided.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">Another, more sophisticated, attack
                    would involve the malicious miners publishing incomplete blocks, so the full information does not
                    even exist to determine whether or not blocks are valid. The solution to this is a
                    challenge-response protocol: verification nodes issue &quot;challenges&quot; in the form of target
                    transaction indices, and upon receiving a node a light node treats the block as untrusted until
                    another node, whether the miner or another verifier, provides a subset of Patricia nodes as a proof
                    of validity.</p>
                  <h2 id="conclusion" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#conclusion"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Conclusion</h2>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The Ethereum protocol was originally
                    conceived as an upgraded version of a cryptocurrency, providing advanced features such as
                    on-blockchain escrow, withdrawal limits, financial contracts, gambling markets and the like via a
                    highly generalized programming language. The Ethereum protocol would not &quot;support&quot; any of
                    the applications directly, but the existence of a Turing-complete programming language means that
                    arbitrary contracts can theoretically be created for any transaction type or application. What is
                    more interesting about Ethereum, however, is that the Ethereum protocol moves far beyond just
                    currency. Protocols around decentralized file storage, decentralized computation and decentralized
                    prediction markets, among dozens of other such concepts, have the potential to substantially
                    increase the efficiency of the computational industry, and provide a massive boost to other
                    peer-to-peer protocols by adding for the first time an economic layer. Finally, there is also a
                    substantial array of applications that have nothing to do with money at all.</p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW">The concept of an arbitrary state
                    transition function as implemented by the Ethereum protocol provides for a platform with unique
                    potential; rather than being a closed-ended, single-purpose protocol intended for a specific array
                    of applications in data storage, gambling or finance, Ethereum is open-ended by design, and we
                    believe that it is extremely well-suited to serving as a foundational layer for a very large number
                    of both financial and non-financial protocols in the years to come.</p>
                  <h2 id="notes-and-further-reading" style="position:relative"
                    class="SharedStyledComponents__Header2-sc-1cr9zfr-23 iiNgGY"><a class="header-anchor before"
                      href="#notes-and-further-reading"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Notes and Further Reading</h2>
                  <h3 id="notes" style="position:relative" class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW">
                    <a class="header-anchor before" href="#notes"><svg aria-hidden="true" focusable="false" height="16"
                        version="1.1" viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Notes</h3>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">A sophisticated reader may notice
                      that in fact a Bitcoin address is
                      the hash of the elliptic curve public key, and not the public key
                      itself. However, it is in fact perfectly legitimate cryptographic
                      terminology to refer to the pubkey hash as a public key itself. This
                      is because Bitcoin&#x27;s cryptography can be considered to be a custom
                      digital signature algorithm, where the public key consists of the
                      hash of the ECC pubkey, the signature consists of the ECC pubkey
                      concatenated with the ECC signature, and the verification algorithm
                      involves checking the ECC pubkey in the signature against the ECC
                      pubkey hash provided as a public key and then verifying the ECC
                      signature against the ECC pubkey.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Technically, the median of the 11
                      previous blocks.</li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN">Internally, 2 and
                      &quot;CHARLIE&quot; are both numbers, with the latter being
                      in big-endian base 256 representation. Numbers can be at least 0 and
                      at most 2<sup>256</sup>-1.</li>
                  </ol>
                  <h3 id="further-reading" style="position:relative"
                    class="SharedStyledComponents__Header3-sc-1cr9zfr-24 fWQpXW"><a class="header-anchor before"
                      href="#further-reading"><svg aria-hidden="true" focusable="false" height="16" version="1.1"
                        viewbox="0 0 16 16" width="16">
                        <path fill-rule="evenodd"
                          d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z">
                        </path>
                      </svg></a>Further Reading</h3>
                  <ol>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://bitcoinmagazine.com/8640/an-exploration-of-intrinsic-value-what-it-is-why-bitcoin-doesnt-have-it-and-why-bitcoin-does-have-it/"
                        target="_blank" rel="noopener noreferrer">Intrinsic value</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://en.bitcoin.it/wiki/Smart_Property"
                        target="_blank" rel="noopener noreferrer">Smart property</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://en.bitcoin.it/wiki/Contracts"
                        target="_blank" rel="noopener noreferrer">Smart contracts</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="http://www.weidai.com/bmoney.txt"
                        target="_blank" rel="noopener noreferrer">B-money</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://nakamotoinstitute.org/finney/rpow/"
                        target="_blank" rel="noopener noreferrer">Reusable proofs of work</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://nakamotoinstitute.org/secure-property-titles/" target="_blank"
                        rel="noopener noreferrer">Secure property titles with owner authority</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://bitcoin.org/bitcoin.pdf"
                        target="_blank" rel="noopener noreferrer">Bitcoin whitepaper</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://namecoin.org/" target="_blank"
                        rel="noopener noreferrer">Namecoin</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://wikipedia.org/wiki/Zooko&#x27;s_triangle" target="_blank"
                        rel="noopener noreferrer">Zooko&#x27;s triangle</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://docs.google.com/a/buterin.com/document/d/1AnkP_cVZTCMLIzw4DvsW6M8Q2JC0lIzrTLuoWu2z1BE/edit"
                        target="_blank" rel="noopener noreferrer">Colored coins whitepaper</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://github.com/mastercoin-MSC/spec"
                        target="_blank" rel="noopener noreferrer">Mastercoin whitepaper</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://bitcoinmagazine.com/7050/bootstrapping-a-decentralized-autonomous-corporation-part-i/"
                        target="_blank" rel="noopener noreferrer">Decentralized autonomous corporations, Bitcoin
                        Magazine</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://en.bitcoin.it/wiki/Scalability#Simplifiedpaymentverification" target="_blank"
                        rel="noopener noreferrer">Simplified payment verification</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://wikipedia.org/wiki/Merkle_tree"
                        target="_blank" rel="noopener noreferrer">Merkle trees</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://wikipedia.org/wiki/Patricia_tree"
                        target="_blank" rel="noopener noreferrer">Patricia trees</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://eprint.iacr.org/2013/881.pdf"
                        target="_blank" rel="noopener noreferrer">GHOST</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://garzikrants.blogspot.ca/2013/01/storj-and-bitcoin-autonomous-agents.html"
                        target="_blank" rel="noopener noreferrer">StorJ and Autonomous Agents, Jeff Garzik</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms" href="https://www.youtube.com/watch?v=Pu4PAMFPo5Y"
                        target="_blank" rel="noopener noreferrer">Mike Hearn on Smart Property at Turing Festival</a>
                    </li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://github.com/ethereum/wiki/wiki/%5BEnglish%5D-RLP" target="_blank"
                        rel="noopener noreferrer">Ethereum RLP</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://github.com/ethereum/wiki/wiki/%5BEnglish%5D-Patricia-Tree" target="_blank"
                        rel="noopener noreferrer">Ethereum Merkle Patricia trees</a></li>
                    <li class="SharedStyledComponents__ListItem-sc-1cr9zfr-27 kiZonN"><a
                        class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://sourceforge.net/p/bitcoin/mailman/message/31709140/" target="_blank"
                        rel="noopener noreferrer">Peter Todd on Merkle sum trees</a></li>
                  </ol>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>For history of the white paper,
                      see <a class="Link__ExternalLink-sc-e3riao-0 gABYms"
                        href="https://github.com/ethereum/wiki/blob/old-before-deleting-all-files-go-to-wiki-wiki-instead/old-whitepaper-for-historical-reference.md"
                        target="_blank"
                        rel="noopener noreferrer">https://github.com/ethereum/wiki/blob/old-before-deleting-all-files-go-to-wiki-wiki-instead/old-whitepaper-for-historical-reference.md</a></em>
                  </p>
                  <p class="SharedStyledComponents__Paragraph-sc-1cr9zfr-21 jjiFHW"><em>Ethereum, like many
                      community-driven, open-source software projects, has evolved since its initial inception. To learn
                      about the latest developments of Ethereum, and how changes to the protocol are made, we recommend
                      <a class="Link__InternalLink-sc-e3riao-1 gCWUlE" href="..\learn\index.html">this guide</a>.</em>
                  </p>
                  <div class="FeedbackCard__Card-sc-siku0n-0 jetTxG">
                    <div class="FeedbackCard__Content-sc-siku0n-1 gVbeDO">
                      <h3 class="FeedbackCard__Title-sc-siku0n-2 hgzlkf"><span>Did this page help answer your
                          question?</span></h3>
                      <div class="FeedbackCard__ButtonContainer-sc-siku0n-3 dFGrDI"><button
                          class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 iuRocQ fMmGqe"><span>Yes</span></button><button
                          class="SharedStyledComponents__Button-sc-1cr9zfr-18 SharedStyledComponents__ButtonSecondary-sc-1cr9zfr-20 gVLXss fMmGqe"><span>No</span></button>
                      </div>
                    </div>
                  </div>
                </article>
                <aside class="TableOfContents__Aside-sc-1bot6j3-0 enFiLp">
                  <ul class="TableOfContents__OuterList-sc-1bot6j3-1 ecNyRq">
                    <li
                      class="TableOfContents__ListItem-sc-1bot6j3-3 TableOfContents__Header-sc-1bot6j3-4 kTfAke frGosJ">
                      <span>On this page</span></li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#a-next-generation-smart-contract-and-decentralized-application-platform">A
                          Next-Generation Smart Contract and Decentralized Application Platform</a></div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#introduction-to-bitcoin-and-existing-concepts">Introduction to Bitcoin and
                          Existing Concepts</a>
                        <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#history">History</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#bitcoin-as-a-state-transition-system">Bitcoin As A State Transition
                                System</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#mining">Mining</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#merkle-trees">Merkle Trees</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#alternative-blockchain-applications">Alternative Blockchain
                                Applications</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#scripting">Scripting</a></div>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#ethereum">Ethereum</a>
                        <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#ethereum-accounts">Ethereum Accounts</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#messages-and-transactions">Messages and Transactions</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#messages">Messages</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#ethereum-state-transition-function">Ethereum State Transition
                                Function</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#code-execution">Code Execution</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#blockchain-and-mining">Blockchain and Mining</a></div>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#applications">Applications</a>
                        <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#token-systems">Token Systems</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#financial-derivatives-and-stable-value-currencies">Financial
                                derivatives and Stable-Value Currencies</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#identity-and-reputation-systems">Identity and Reputation Systems</a>
                            </div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#decentralized-file-storage">Decentralized File Storage</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#decentralized-autonomous-organizations">Decentralized Autonomous
                                Organizations</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#further-applications">Further Applications</a></div>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#miscellanea-and-concerns">Miscellanea And Concerns</a>
                        <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#modified-ghost-implementation">Modified GHOST Implementation</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#fees">Fees</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#computation-and-turing-completeness">Computation And
                                Turing-Completeness</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#currency-and-issuance">Currency And Issuance</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#mining-centralization">Mining Centralization</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#scalability">Scalability</a></div>
                          </li>
                        </ul>
                      </div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#conclusion">Conclusion</a></div>
                    </li>
                    <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                      <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                          href="index.html#notes-and-further-reading">Notes and Further Reading</a>
                        <ul class="TableOfContents__InnerList-sc-1bot6j3-2 lcmtaw">
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#notes">Notes</a></div>
                          </li>
                          <li class="TableOfContents__ListItem-sc-1bot6j3-3 kTfAke">
                            <div><a class="TableOfContents__StyledTableOfContentsLink-sc-1bot6j3-5 TDdlc"
                                href="index.html#further-reading">Further Reading</a></div>
                          </li>
                        </ul>
                      </div>
                    </li>
                  </ul>
                </aside>
              </div>
            </main>
          </div>
        </div>
        <footer class="Footer__StyledFooter-sc-1to993d-0 gvoBKJ">
          <div class="Footer__FooterTop-sc-1to993d-1 kFKfdz">
            <div class="Footer__LastUpdated-sc-1to993d-2 bWGwos"><span>Website last updated</span>:
              <!-- -->
              <!-- -->December 1, 2020
            </div>
            <div class="Footer__SocialIcons-sc-1to993d-9 kdLbod"><a
                href="https://github.com/ethereum/ethereum-org-website" target="_blank" rel="noopener noreferrer"
                aria-label="GitHub"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 496 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z">
                  </path>
                </svg></a><a href="https://twitter.com/ethdotorg" target="_blank" rel="noopener noreferrer"
                aria-label="Twitter"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 512 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z">
                  </path>
                </svg></a><a href="https://youtube.com/channel/UCNOfzGXD_C9YMYmnefmPH0g" target="_blank"
                rel="noopener noreferrer" aria-label="Youtube"><svg stroke="currentColor" fill="currentColor"
                  stroke-width="0" viewbox="0 0 576 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z">
                  </path>
                </svg></a><a href="https://discord.gg/CetY6Y4" target="_blank" rel="noopener noreferrer"
                aria-label="Discord"><svg stroke="currentColor" fill="currentColor" stroke-width="0"
                  viewbox="0 0 640 512"
                  class="Icon__StyledIcon-sc-1o8zi5s-0 Footer__SocialIcon-sc-1to993d-10 iylOGp iedzfy" height="36"
                  width="36" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z">
                  </path>
                </svg></a></div>
          </div>
          <div class="Footer__LinkGrid-sc-1to993d-3 hlbLsM">
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Use Ethereum</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\wallets\index.html"><span>Ethereum wallets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\get-eth\index.html"><span>Get ETH</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\dapps\index.html"><span>Decentralized applications (dapps)</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\run-a-node\index.html"><span>Run a node</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\stablecoins\index.html"><span>Stablecoins</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\staking\index.html"><span>Stake ETH</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Learn</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\what-is-ethereum\index.html"><span>What is Ethereum?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eth\index.html"><span>What is ether (ETH)?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\learn\index.html"><span>Community guides and resources</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\history\index.html"><span>History of Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a aria-current="page"
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz active"
                    href="index.html"><span>Ethereum Whitepaper</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\upgrades\index.html"><span>Ethereum upgrades</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\security\index.html"><span>Ethereum security and scam prevention</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE is-glossary Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\glossary\index.html"><span>Ethereum glossary</span><svg stroke="currentColor"
                      fill="currentColor" stroke-width="0" viewbox="0 0 16 16"
                      class="Icon__StyledIcon-sc-1o8zi5s-0 Link__GlossaryIcon-sc-e3riao-3 iylOGp jfMIWk" height="12px"
                      width="12px" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927z">
                      </path>
                    </svg></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\governance\index.html"><span>Ethereum governance</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\bridges\index.html"><span>Blockchain bridges</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\energy-consumption\index.html"><span>Ethereum energy consumption</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\web3\index.html"><span>What is Web3?</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\eips\index.html"><span>Ethereum Improvement Proposals</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Developers</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\index.html"><span>Get started</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/developers/docs/"><span>Documentation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\tutorials\index.html"><span>Tutorials</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\learning-tools\index.html"><span>Learn by coding</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\developers\local-environment\index.html"><span>Set up local environment</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Ecosystem</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\community\index.html"><span>Community hub</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\foundation\index.html"><span>Ethereum Foundation</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://blog.ethstake.exchange/" target="_blank" rel="noopener noreferrer"><span>Ethereum
                      Foundation Blog</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://esp.ethereum.foundation" target="_blank" rel="noopener noreferrer"><span>Ecosystem
                      Support Program</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/en/community/grants"><span>Ecosystem Grant Programs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\assets\index.html"><span>Ethereum brand assets</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="https://devcon.org/" target="_blank" rel="noopener noreferrer"><span>Devcon</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>Enterprise</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Mainnet Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\private-ethereum\index.html"><span>Private Ethereum</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\enterprise\index.html"><span>Enterprise</span></a></li>
              </ul>
            </div>
            <div class="Footer__LinkSection-sc-1to993d-4 bfxkfK">
              <h3 class="Footer__SectionHeader-sc-1to993d-5 bbCEKr"><span>About ethstake.exchange</span></h3>
              <ul class="Footer__List-sc-1to993d-6 gjQPMc">
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html"><span>About us</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\about\index.html#open-jobs"><span>Jobs</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\contributing\index.html"><span>Contributing</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\languages\index.html"><span>Language support</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\privacy-policy\index.html"><span>Privacy policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\terms-of-use\index.html"><span>Terms of use</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__InternalLink-sc-e3riao-1 gCWUlE Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="..\cookie-policy\index.html"><span>Cookie policy</span></a></li>
                <li class="Footer__ListItem-sc-1to993d-7 eGhJJx"><a
                    class="Link__ExternalLink-sc-e3riao-0 gABYms Footer__FooterLink-sc-1to993d-8 gIpSoz"
                    href="/cdn-cgi/l/email-protection#7505071006063510011d0601141e105b100d161d141b1210" target="_blank"
                    rel="noopener noreferrer"><span>Contact</span></a></li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <div id="gatsby-announcer"
      style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0"
      aria-live="assertive" aria-atomic="true"></div>
  </div>
  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
  <script>
    let svgIcon = document.getElementsByClassName('Mobile__GlyphButton')[0]
    let menuBtn = document.getElementsByClassName('Mobile__MenuButton')
    let hideMenu = document.getElementsByClassName('Mobile__MobileModal')[0]
    let menuContainer = document.getElementsByClassName('Mobile__MenuContainer')[0]
    let menuUl = document.getElementsByClassName('Mobile__MenuItems')[0]
    let menuBottom = document.getElementsByClassName('Mobile__BottomMenu')[0]
    let searchPage = document.getElementsByClassName('Mobile__MenuContainer')[1]
    let closeBtn = document.getElementsByClassName('Mobile__CloseIconContainer')[0]
    let searchBtn = document.getElementsByClassName('Mobile__BottomItem')[0]
    menuBtn[1].addEventListener('click', function () {
      this.toggleAttribute('open')

      let type = this.hasAttribute('open')
      let path = svgIcon.getElementsByTagName('path')[0]
      let d = path.getAttribute('d')
      path.style='transition: all ease-in 0.25s;'
      console.log(path, d,'aaaa')
      if (type) {
        hideMenu.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuContainer.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.7s;transform: translateX(0%) translateZ(0px);'
        path.setAttribute('d','M 2 19 l 10 -14 l 0 0 l 10 14 M 2 19 l 10 7 M 12 26 l 10 -7 M 2 22 l 10 15 l 0 0 l 9 -15')
        setTimeout(()=>{
          path.setAttribute('d','M 2 13 l 0 -3 l 20 0 l 0 3 M 7 14 l 10 10 M 7 24 l 10 -10 M 2 25 l 0 3 l 20 0 l 0 -3')

        },700)

      } else {
        hideMenu.style = 'transition: all ease-in 0.2s;display: none; opacity: 0;'
        menuContainer.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        menuBottom.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'
        path.setAttribute('d','M 2 13 l 10 0 l 0 0 l 10 0 M 4 19 l 8 0 M 12 19 l 8 0 M 2 25 l 10 0 l 0 0 l 10 0')

      }
       // menuUl.toggleAttribute('class', 'gYetwr')
    })
    menuBtn[0].addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    searchBtn.addEventListener('click', function () {
        searchPage.style = 'transition: all ease-in 0.8s;transform: translateX(0%) translateZ(0px);'
    })
    closeBtn.addEventListener('click', function () {
      console.log('111')
      searchPage.style = 'transition: all ease-in 0.2s;transform: translateX(-100%) translateZ(0px);'

    })
    console.log(menuBtn, '......')
    window.dev = undefined
    if (window.dev === true || !(navigator.doNotTrack === '1' || window.doNotTrack === '1')) {
      window._paq = window._paq || [];



      window._paq.push(['setTrackerUrl', 'https://matomo.ethstake.exchange/matomo.php']);
      window._paq.push(['setSiteId', '4']);
      window._paq.push(['enableHeartBeatTimer']);
      window.start = new Date();

      (function () {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.type = 'text/javascript'; g.async = true; g.defer = true; g.src = 'https://matomo.ethstake.exchange/matomo.js'; s.parentNode.insertBefore(g, s);
      })();

      if (window.dev === true) {
        console.debug('[Matomo] Tracking initialized')
        console.debug('[Matomo] matomoUrl: https://matomo.ethstake.exchange, siteId: 4')
      }
    }
  </script><noscript><img
      src="https://matomo.ethstake.exchange/matomo.php?idsite=4&rec=1&url=https://ethstake.exchange/en/whitepaper/"
      style="border:0" alt="tracker"></noscript>
  <script
    id="gatsby-script-loader">/*<![CDATA[*/window.pagePath = "/en/whitepaper/"; window.___webpackCompilationHash = "0375e64c51e377521456";/*]]>*/</script>
  <script
    id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping = { "polyfill": ["/polyfill-b6350ac254e29f22a1e4.js"], "app": ["/app-b670b5ed3a389af0ed04.js"], "component---src-pages-404-js": ["/component---src-pages-404-js-a6aee0605f3068868f92.js"], "component---src-pages-assets-js": ["/component---src-pages-assets-js-ba78d988431646b4760b.js"], "component---src-pages-community-js": ["/component---src-pages-community-js-5ca1d5f82d581db39798.js"], "component---src-pages-conditional-dapps-js": ["/component---src-pages-conditional-dapps-js-eeec0b8674125eadd331.js"], "component---src-pages-conditional-eth-js": ["/component---src-pages-conditional-eth-js-21644356026cce06349e.js"], "component---src-pages-conditional-wallets-index-js": ["/component---src-pages-conditional-wallets-index-js-6c55787f35fe5cc07ef3.js"], "component---src-pages-conditional-what-is-ethereum-js": ["/component---src-pages-conditional-what-is-ethereum-js-144e7cb6cba17bff7608.js"], "component---src-pages-contributing-translation-program-acknowledgements-js": ["/component---src-pages-contributing-translation-program-acknowledgements-js-a596bd2823410bf53c11.js"], "component---src-pages-contributing-translation-program-contributors-js": ["/component---src-pages-contributing-translation-program-contributors-js-4f2a18f6f82d2a84de27.js"], "component---src-pages-developers-index-js": ["/component---src-pages-developers-index-js-eba978370f325b125b03.js"], "component---src-pages-developers-learning-tools-js": ["/component---src-pages-developers-learning-tools-js-88bd1bcad31958f723e3.js"], "component---src-pages-developers-local-environment-js": ["/component---src-pages-developers-local-environment-js-768783f49122fff8d82f.js"], "component---src-pages-developers-tutorials-js": ["/component---src-pages-developers-tutorials-js-f82f9627cb9b2ce3318b.js"], "component---src-pages-get-eth-js": ["/component---src-pages-get-eth-js-e6c689f28770388e40be.js"], "component---src-pages-index-js": ["/component---src-pages-index-js-c7245d4f213dfe2095c4.js"], "component---src-pages-languages-js": ["/component---src-pages-languages-js-b1a3a1c01ec6bdcd87ac.js"], "component---src-pages-run-a-node-js": ["/component---src-pages-run-a-node-js-e6e733f3c9f5f026a5d5.js"], "component---src-pages-stablecoins-js": ["/component---src-pages-stablecoins-js-4bc5b567f2c38baaaa5b.js"], "component---src-pages-stakenow-js": ["/component---src-pages-stakenow-js-40c51639947629777abf.js"], "component---src-pages-staking-deposit-contract-js": ["/component---src-pages-staking-deposit-contract-js-ac8273bd9f4711b38540.js"], "component---src-pages-staking-index-js": ["/component---src-pages-staking-index-js-3d50b1ef7b3b9814f6e2.js"], "component---src-pages-studio-js": ["/component---src-pages-studio-js-c16fabe2f5808fafce99.js"], "component---src-pages-upgrades-get-involved-bug-bounty-js": ["/component---src-pages-upgrades-get-involved-bug-bounty-js-414deb9f860f05b80b2c.js"], "component---src-pages-upgrades-get-involved-index-js": ["/component---src-pages-upgrades-get-involved-index-js-207fa70ee7aa5720b909.js"], "component---src-pages-upgrades-index-js": ["/component---src-pages-upgrades-index-js-dd49283310be18b0a199.js"], "component---src-pages-upgrades-vision-js": ["/component---src-pages-upgrades-vision-js-2c86e72cede9c6155cbf.js"], "component---src-pages-wallets-find-wallet-js": ["/component---src-pages-wallets-find-wallet-js-97c53af70032ab1edbc4.js"], "component---src-templates-static-js": ["/component---src-templates-static-js-3cd4030c1e191ee95c2b.js"], "component---src-templates-upgrade-js": ["/component---src-templates-upgrade-js-700f5089c86b74f8484f.js"], "component---src-templates-use-cases-js": ["/component---src-templates-use-cases-js-75c0d7fec84444cc8c68.js"] };/*]]>*/</script>
  <script src="/polyfill-b6350ac254e29f22a1e4.js" nomodule=""></script>
  <script src="/component---src-templates-static-js-3cd4030c1e191ee95c2b.js" async=""></script>
  <script src="/70ea9999766619ef9ff93e0c0e160b463e32c39a-4f66de516b5ec8364668.js" async=""></script>
  <script src="/f80886f48ae95882b14864301dc225580095953b-4fe73d0a4dea99431c49.js" async=""></script>
  <script src="/cd2bb0b878c80fc0b308b33a3c827f0389e61871-da9a4efe16d62cbd7988.js" async=""></script>
  <script src="/25d596b65775ea7afe354c15642381979021d6cd-5667baf5a2a2bad6de51.js" async=""></script>
  <script src="/app-b670b5ed3a389af0ed04.js" async=""></script>
  <script src="/0f1ac474-e8f788f62189f421a856.js" async=""></script>
  <script src="/0c428ae2-2128ff22fce458b543bd.js" async=""></script>
  <script src="/1bfc9850-0f18e2d74feedfc6e426.js" async=""></script>
  <script src="/ae51ba48-34d54094a2c04f215fb8.js" async=""></script>
  <script src="/252f366e-2705b607be296edabcea.js" async=""></script>
  <script src="/framework-4e285adfb333f1b50c05.js" async=""></script>
  <script src="/webpack-runtime-d600da28e471609bf3f3.js" async=""></script>
</body>

</html>