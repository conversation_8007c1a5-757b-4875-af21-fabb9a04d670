(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{68109:function(e,t,r){"use strict";r.d(t,{t_:function(){return a}});var n=r(11163),i=r(67294),o=r(17498);function a(e){var t,r=e.locale,a=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,["locale"]),s=null==(t=(0,n.useRouter)())?void 0:t.locale;if(!r&&s&&(r=s),!r)throw new Error(void 0);return i.createElement(o.Pj,Object.assign({locale:r},a))}},48418:function(e,t,r){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(u){s=!0,i=u}finally{try{a||null==r.return||r.return()}finally{if(s)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t.default=void 0;var o,a=(o=r(67294))&&o.__esModule?o:{default:o},s=r(76273),u=r(90387),c=r(57190);var l={};function f(e,t,r,n){if(e&&s.isLocalURL(t)){e.prefetch(t,r,n).catch((function(e){0}));var i=n&&"undefined"!==typeof n.locale?n.locale:e&&e.locale;l[t+"%"+r+(i?"%"+i:"")]=!0}}var p=function(e){var t,r=!1!==e.prefetch,n=u.useRouter(),o=a.default.useMemo((function(){var t=i(s.resolveHref(n,e.href,!0),2),r=t[0],o=t[1];return{href:r,as:e.as?s.resolveHref(n,e.as):o||r}}),[n,e.href,e.as]),p=o.href,h=o.as,E=e.children,m=e.replace,g=e.shallow,y=e.scroll,v=e.locale;"string"===typeof E&&(E=a.default.createElement("a",null,E));var d=(t=a.default.Children.only(E))&&"object"===typeof t&&t.ref,_=i(c.useIntersection({rootMargin:"200px"}),2),T=_[0],b=_[1],A=a.default.useCallback((function(e){T(e),d&&("function"===typeof d?d(e):"object"===typeof d&&(d.current=e))}),[d,T]);a.default.useEffect((function(){var e=b&&r&&s.isLocalURL(p),t="undefined"!==typeof v?v:n&&n.locale,i=l[p+"%"+h+(t?"%"+t:"")];e&&!i&&f(n,p,h,{locale:t})}),[h,p,b,v,r,n]);var S={ref:A,onClick:function(e){t.props&&"function"===typeof t.props.onClick&&t.props.onClick(e),e.defaultPrevented||function(e,t,r,n,i,o,a,u){("A"!==e.currentTarget.nodeName.toUpperCase()||!function(e){var t=e.currentTarget.target;return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)&&s.isLocalURL(r))&&(e.preventDefault(),t[i?"replace":"push"](r,n,{shallow:o,locale:u,scroll:a}))}(e,n,p,h,m,g,y,v)},onMouseEnter:function(e){t.props&&"function"===typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),s.isLocalURL(p)&&f(n,p,h,{priority:!0})}};if(e.passHref||"a"===t.type&&!("href"in t.props)){var P="undefined"!==typeof v?v:n&&n.locale,I=n&&n.isLocaleDomain&&s.getDomainLocale(h,P,n&&n.locales,n&&n.domainLocales);S.href=I||s.addBasePath(s.addLocale(h,P,n&&n.defaultLocale))}return a.default.cloneElement(t,S)};t.default=p},57190:function(e,t,r){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(u){s=!0,i=u}finally{try{a||null==r.return||r.return()}finally{if(s)throw i}}return o}}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}Object.defineProperty(t,"__esModule",{value:!0}),t.useIntersection=function(e){var t=e.rootRef,r=e.rootMargin,n=e.disabled||!s,l=o.useRef(),f=i(o.useState(!1),2),p=f[0],h=f[1],E=i(o.useState(t?t.current:null),2),m=E[0],g=E[1],y=o.useCallback((function(e){l.current&&(l.current(),l.current=void 0),n||p||e&&e.tagName&&(l.current=function(e,t,r){var n=function(e){var t,r={root:e.root||null,margin:e.rootMargin||""},n=c.find((function(e){return e.root===r.root&&e.margin===r.margin}));n?t=u.get(n):(t=u.get(r),c.push(r));if(t)return t;var i=new Map,o=new IntersectionObserver((function(e){e.forEach((function(e){var t=i.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)}))}),e);return u.set(r,t={id:r,observer:o,elements:i}),t}(r),i=n.id,o=n.observer,a=n.elements;return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),u.delete(i);var t=c.findIndex((function(e){return e.root===i.root&&e.margin===i.margin}));t>-1&&c.splice(t,1)}}}(e,(function(e){return e&&h(e)}),{root:m,rootMargin:r}))}),[n,m,r,p]);return o.useEffect((function(){if(!s&&!p){var e=a.requestIdleCallback((function(){return h(!0)}));return function(){return a.cancelIdleCallback(e)}}}),[p]),o.useEffect((function(){t&&g(t.current)}),[t]),[y,p]};var o=r(67294),a=r(9311),s="undefined"!==typeof IntersectionObserver;var u=new Map,c=[]},41664:function(e,t,r){e.exports=r(48418)},17498:function(e,t,r){"use strict";r.d(t,{Pj:function(){return xe},T_:function(){return ze}});var n=r(67294),i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},i(e,t)};function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var a=function(){return a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},a.apply(this,arguments)};Object.create;function s(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}Object.create;var u=function(){return u=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},u.apply(this,arguments)};Object.create;var c,l,f;Object.create;function p(e){return e.type===l.literal}function h(e){return e.type===l.argument}function E(e){return e.type===l.number}function m(e){return e.type===l.date}function g(e){return e.type===l.time}function y(e){return e.type===l.select}function v(e){return e.type===l.plural}function d(e){return e.type===l.pound}function _(e){return e.type===l.tag}function T(e){return!(!e||"object"!==typeof e||e.type!==f.number)}function b(e){return!(!e||"object"!==typeof e||e.type!==f.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(c||(c={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(l||(l={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(f||(f={}));var A=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,S=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function P(e){var t={};return e.replace(S,(function(e){var r=e.length;switch(e[0]){case"G":t.era=4===r?"long":5===r?"narrow":"short";break;case"y":t.year=2===r?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=4===r?"short":5===r?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""})),t}var I=function(){return I=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},I.apply(this,arguments)};Object.create;Object.create;var L=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;var N,O=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,R=/^(@+)?(\+|#+)?[rs]?$/g,M=/(\*)(0+)|(#+)(0+)|(0+)/g,w=/^(0+)$/;function C(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(R,(function(e,r,n){return"string"!==typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"===typeof n?n.length:0)),""})),t}function U(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function G(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!w.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function D(e){var t=U(e);return t||{}}function F(e){for(var t={},r=0,n=e;r<n.length;r++){var i=n[r];switch(i.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=i.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=i.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=I(I(I({},t),{notation:"scientific"}),i.options.reduce((function(e,t){return I(I({},e),D(t))}),{}));continue;case"engineering":t=I(I(I({},t),{notation:"engineering"}),i.options.reduce((function(e,t){return I(I({},e),D(t))}),{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(i.options[0]);continue;case"integer-width":if(i.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");i.options[0].replace(M,(function(e,r,n,i,o,a){if(r)t.minimumIntegerDigits=n.length;else{if(i&&o)throw new Error("We currently do not support maximum integer digits");if(a)throw new Error("We currently do not support exact integer digits")}return""}));continue}if(w.test(i.stem))t.minimumIntegerDigits=i.stem.length;else if(O.test(i.stem)){if(i.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");i.stem.replace(O,(function(e,r,n,i,o,a){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&a?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""}));var o=i.options[0];"w"===o?t=I(I({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=I(I({},t),C(o)))}else if(R.test(i.stem))t=I(I({},t),C(i.stem));else{var a=U(i.stem);a&&(t=I(I({},t),a));var s=G(i.stem);s&&(t=I(I({},t),s))}}return t}var k=new RegExp("^".concat(A.source,"*")),j=new RegExp("".concat(A.source,"*$"));function V(e,t){return{start:e,end:t}}var x=!!String.prototype.startsWith,X=!!String.fromCodePoint,K=!!Object.fromEntries,Y=!!String.prototype.codePointAt,B=!!String.prototype.trimStart,z=!!String.prototype.trimEnd,H=!!Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"===typeof e&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},Z=!0;try{Z="a"===(null===(N=re("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))||void 0===N?void 0:N[0])}catch(He){Z=!1}var q,W=x?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},Q=X?String.fromCodePoint:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r,n="",i=e.length,o=0;i>o;){if((r=e[o++])>1114111)throw RangeError(r+" is not a valid code point");n+=r<65536?String.fromCharCode(r):String.fromCharCode(55296+((r-=65536)>>10),r%1024+56320)}return n},$=K?Object.fromEntries:function(e){for(var t={},r=0,n=e;r<n.length;r++){var i=n[r],o=i[0],a=i[1];t[o]=a}return t},J=Y?function(e,t){return e.codePointAt(t)}:function(e,t){var r=e.length;if(!(t<0||t>=r)){var n,i=e.charCodeAt(t);return i<55296||i>56319||t+1===r||(n=e.charCodeAt(t+1))<56320||n>57343?i:n-56320+(i-55296<<10)+65536}},ee=B?function(e){return e.trimStart()}:function(e){return e.replace(k,"")},te=z?function(e){return e.trimEnd()}:function(e){return e.replace(j,"")};function re(e,t){return new RegExp(e,t)}if(Z){var ne=re("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");q=function(e,t){var r;return ne.lastIndex=t,null!==(r=ne.exec(e)[1])&&void 0!==r?r:""}}else q=function(e,t){for(var r=[];;){var n=J(e,t);if(void 0===n||ae(n)||se(n))break;r.push(n),t+=n>=65536?2:1}return Q.apply(void 0,r)};var ie=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var n=[];!this.isEOF();){var i=this.char();if(123===i){if((o=this.parseArgument(e,r)).err)return o;n.push(o.val)}else{if(125===i&&e>0)break;if(35!==i||"plural"!==t&&"selectordinal"!==t){if(60===i&&!this.ignoreTag&&47===this.peek()){if(r)break;return this.error(c.UNMATCHED_CLOSING_TAG,V(this.clonePosition(),this.clonePosition()))}if(60===i&&!this.ignoreTag&&oe(this.peek()||0)){if((o=this.parseTag(e,t)).err)return o;n.push(o.val)}else{var o;if((o=this.parseLiteral(e,t)).err)return o;n.push(o.val)}}else{var a=this.clonePosition();this.bump(),n.push({type:l.pound,location:V(a,this.clonePosition())})}}}return{val:n,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:l.literal,value:"<".concat(n,"/>"),location:V(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(e+1,t,!0);if(i.err)return i;var o=i.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!oe(this.char()))return this.error(c.INVALID_TAG,V(a,this.clonePosition()));var s=this.clonePosition();return n!==this.parseTagName()?this.error(c.UNMATCHED_CLOSING_TAG,V(s,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:l.tag,value:n,children:o,location:V(r,this.clonePosition())},err:null}:this.error(c.INVALID_TAG,V(a,this.clonePosition())))}return this.error(c.UNCLOSED_TAG,V(r,this.clonePosition()))}return this.error(c.INVALID_TAG,V(r,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i)n+=i;else{var o=this.tryParseUnquoted(e,t);if(o)n+=o;else{var a=this.tryParseLeftAngleBracket();if(!a)break;n+=a}}}var s=V(r,this.clonePosition());return{val:{type:l.literal,value:n,location:s},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return this.isEOF()||60!==this.char()||!this.ignoreTag&&(oe(e=this.peek()||0)||47===e)?null:(this.bump(),"<");var e},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39!==this.peek()){this.bump();break}t.push(39),this.bump()}else t.push(r);this.bump()}return Q.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),Q(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(c.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(c.EMPTY_ARGUMENT,V(r,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(c.MALFORMED_ARGUMENT,V(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(c.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:l.argument,value:n,location:V(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(c.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition())):this.parseArgumentOptions(e,t,n,r);default:return this.error(c.MALFORMED_ARGUMENT,V(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=q(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:V(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,n){var i,o=this.clonePosition(),a=this.parseIdentifierIfPossible().value,s=this.clonePosition();switch(a){case"":return this.error(c.EXPECT_ARGUMENT_TYPE,V(o,s));case"number":case"date":case"time":this.bumpSpace();var p=null;if(this.bumpIf(",")){this.bumpSpace();var h=this.clonePosition();if((_=this.parseSimpleArgStyleIfPossible()).err)return _;if(0===(g=te(_.val)).length)return this.error(c.EXPECT_ARGUMENT_STYLE,V(this.clonePosition(),this.clonePosition()));p={style:g,styleLocation:V(h,this.clonePosition())}}if((T=this.tryParseArgumentClose(n)).err)return T;var E=V(n,this.clonePosition());if(p&&W(null===p||void 0===p?void 0:p.style,"::",0)){var m=ee(p.style.slice(2));if("number"===a)return(_=this.parseNumberSkeletonFromString(m,p.styleLocation)).err?_:{val:{type:l.number,value:r,location:E,style:_.val},err:null};if(0===m.length)return this.error(c.EXPECT_DATE_TIME_SKELETON,E);var g={type:f.dateTime,pattern:m,location:p.styleLocation,parsedOptions:this.shouldParseSkeletons?P(m):{}};return{val:{type:"date"===a?l.date:l.time,value:r,location:E,style:g},err:null}}return{val:{type:"number"===a?l.number:"date"===a?l.date:l.time,value:r,location:E,style:null!==(i=null===p||void 0===p?void 0:p.style)&&void 0!==i?i:null},err:null};case"plural":case"selectordinal":case"select":var y=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(c.EXPECT_SELECT_ARGUMENT_OPTIONS,V(y,u({},y)));this.bumpSpace();var v=this.parseIdentifierIfPossible(),d=0;if("select"!==a&&"offset"===v.value){if(!this.bumpIf(":"))return this.error(c.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,V(this.clonePosition(),this.clonePosition()));var _;if(this.bumpSpace(),(_=this.tryParseDecimalInteger(c.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,c.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE)).err)return _;this.bumpSpace(),v=this.parseIdentifierIfPossible(),d=_.val}var T,b=this.tryParsePluralOrSelectOptions(e,a,t,v);if(b.err)return b;if((T=this.tryParseArgumentClose(n)).err)return T;var A=V(n,this.clonePosition());return"select"===a?{val:{type:l.select,value:r,options:$(b.val),location:A},err:null}:{val:{type:l.plural,value:r,options:$(b.val),offset:d,pluralType:"plural"===a?"cardinal":"ordinal",location:A},err:null};default:return this.error(c.INVALID_ARGUMENT_TYPE,V(o,s))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(c.EXPECT_ARGUMENT_CLOSING_BRACE,V(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();){switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(c.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,V(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw new Error("Number skeleton cannot be empty");for(var t=[],r=0,n=e.split(L).filter((function(e){return e.length>0}));r<n.length;r++){var i=n[r].split("/");if(0===i.length)throw new Error("Invalid number skeleton");for(var o=i[0],a=i.slice(1),s=0,u=a;s<u.length;s++)if(0===u[s].length)throw new Error("Invalid number skeleton");t.push({stem:o,options:a})}return t}(e)}catch(n){return this.error(c.INVALID_NUMBER_SKELETON,t)}return{val:{type:f.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?F(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,n){for(var i,o=!1,a=[],s=new Set,u=n.value,l=n.location;;){if(0===u.length){var f=this.clonePosition();if("select"===t||!this.bumpIf("="))break;var p=this.tryParseDecimalInteger(c.EXPECT_PLURAL_ARGUMENT_SELECTOR,c.INVALID_PLURAL_ARGUMENT_SELECTOR);if(p.err)return p;l=V(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}if(s.has(u))return this.error("select"===t?c.DUPLICATE_SELECT_ARGUMENT_SELECTOR:c.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);"other"===u&&(o=!0),this.bumpSpace();var h=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?c.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:c.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,V(this.clonePosition(),this.clonePosition()));var E=this.parseMessage(e+1,t,r);if(E.err)return E;var m=this.tryParseArgumentClose(h);if(m.err)return m;a.push([u,{value:E.val,location:V(h,this.clonePosition())}]),s.add(u),this.bumpSpace(),u=(i=this.parseIdentifierIfPossible()).value,l=i.location}return 0===a.length?this.error("select"===t?c.EXPECT_SELECT_ARGUMENT_SELECTOR:c.EXPECT_PLURAL_ARGUMENT_SELECTOR,V(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!o?this.error(c.MISSING_OTHER_CLAUSE,V(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var a=this.char();if(!(a>=48&&a<=57))break;i=!0,o=10*o+(a-48),this.bump()}var s=V(n,this.clonePosition());return i?H(o*=r)?{val:o,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=J(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(W(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&ae(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!==r&&void 0!==r?r:null},e}();function oe(e){return e>=97&&e<=122||e>=65&&e<=90}function ae(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function se(e){return e>=33&&e<=35||36===e||e>=37&&e<=39||40===e||41===e||42===e||43===e||44===e||45===e||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||91===e||92===e||93===e||94===e||96===e||123===e||124===e||125===e||126===e||161===e||e>=162&&e<=165||166===e||167===e||169===e||171===e||172===e||174===e||176===e||177===e||182===e||187===e||191===e||215===e||247===e||e>=8208&&e<=8213||e>=8214&&e<=8215||8216===e||8217===e||8218===e||e>=8219&&e<=8220||8221===e||8222===e||8223===e||e>=8224&&e<=8231||e>=8240&&e<=8248||8249===e||8250===e||e>=8251&&e<=8254||e>=8257&&e<=8259||8260===e||8261===e||8262===e||e>=8263&&e<=8273||8274===e||8275===e||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||8608===e||e>=8609&&e<=8610||8611===e||e>=8612&&e<=8613||8614===e||e>=8615&&e<=8621||8622===e||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||8658===e||8659===e||8660===e||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||8968===e||8969===e||8970===e||8971===e||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||9001===e||9002===e||e>=9003&&e<=9083||9084===e||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||9655===e||e>=9656&&e<=9664||9665===e||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||9839===e||e>=9840&&e<=10087||10088===e||10089===e||10090===e||10091===e||10092===e||10093===e||10094===e||10095===e||10096===e||10097===e||10098===e||10099===e||10100===e||10101===e||e>=10132&&e<=10175||e>=10176&&e<=10180||10181===e||10182===e||e>=10183&&e<=10213||10214===e||10215===e||10216===e||10217===e||10218===e||10219===e||10220===e||10221===e||10222===e||10223===e||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||10627===e||10628===e||10629===e||10630===e||10631===e||10632===e||10633===e||10634===e||10635===e||10636===e||10637===e||10638===e||10639===e||10640===e||10641===e||10642===e||10643===e||10644===e||10645===e||10646===e||10647===e||10648===e||e>=10649&&e<=10711||10712===e||10713===e||10714===e||10715===e||e>=10716&&e<=10747||10748===e||10749===e||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||11158===e||e>=11159&&e<=11263||e>=11776&&e<=11777||11778===e||11779===e||11780===e||11781===e||e>=11782&&e<=11784||11785===e||11786===e||11787===e||11788===e||11789===e||e>=11790&&e<=11798||11799===e||e>=11800&&e<=11801||11802===e||11803===e||11804===e||11805===e||e>=11806&&e<=11807||11808===e||11809===e||11810===e||11811===e||11812===e||11813===e||11814===e||11815===e||11816===e||11817===e||e>=11818&&e<=11822||11823===e||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||11840===e||11841===e||11842===e||e>=11843&&e<=11855||e>=11856&&e<=11857||11858===e||e>=11859&&e<=11903||e>=12289&&e<=12291||12296===e||12297===e||12298===e||12299===e||12300===e||12301===e||12302===e||12303===e||12304===e||12305===e||e>=12306&&e<=12307||12308===e||12309===e||12310===e||12311===e||12312===e||12313===e||12314===e||12315===e||12316===e||12317===e||e>=12318&&e<=12319||12320===e||12336===e||64830===e||64831===e||e>=65093&&e<=65094}function ue(e){e.forEach((function(e){if(delete e.location,y(e)||v(e))for(var t in e.options)delete e.options[t].location,ue(e.options[t].value);else E(e)&&T(e.style)||(m(e)||g(e))&&b(e.style)?delete e.style.location:_(e)&&ue(e.children)}))}function ce(e,t){void 0===t&&(t={}),t=u({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new ie(e,t).parse();if(r.err){var n=SyntaxError(c[r.err.kind]);throw n.location=r.err.location,n.originalMessage=r.err.message,n}return(null===t||void 0===t?void 0:t.captureLocation)||ue(r.val),r.val}function le(e,t){var r=t&&t.cache?t.cache:ve,n=t&&t.serializer?t.serializer:me;return(t&&t.strategy?t.strategy:Ee)(e,{cache:r,serializer:n})}function fe(e,t,r,n){var i,o=null==(i=n)||"number"===typeof i||"boolean"===typeof i?n:r(n),a=t.get(o);return"undefined"===typeof a&&(a=e.call(this,n),t.set(o,a)),a}function pe(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return"undefined"===typeof o&&(o=e.apply(this,n),t.set(i,o)),o}function he(e,t,r,n,i){return r.bind(t,e,n,i)}function Ee(e,t){return he(e,this,1===e.length?fe:pe,t.cache.create(),t.serializer)}var me=function(){return JSON.stringify(arguments)};function ge(){this.cache=Object.create(null)}ge.prototype.get=function(e){return this.cache[e]},ge.prototype.set=function(e,t){this.cache[e]=t};var ye,ve={create:function(){return new ge}},de={variadic:function(e,t){return he(e,this,pe,t.cache.create(),t.serializer)},monadic:function(e,t){return he(e,this,fe,t.cache.create(),t.serializer)}};!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(ye||(ye={}));var _e,Te=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return o(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),be=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),ye.INVALID_VALUE,i)||this}return o(t,e),t}(Te),Ae=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),ye.INVALID_VALUE,n)||this}return o(t,e),t}(Te),Se=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),ye.MISSING_VALUE,r)||this}return o(t,e),t}(Te);function Pe(e){return"function"===typeof e}function Ie(e,t,r,n,i,o,a){if(1===e.length&&p(e[0]))return[{type:_e.literal,value:e[0].value}];for(var s=[],u=0,c=e;u<c.length;u++){var l=c[u];if(p(l))s.push({type:_e.literal,value:l.value});else if(d(l))"number"===typeof o&&s.push({type:_e.literal,value:r.getNumberFormat(t).format(o)});else{var f=l.value;if(!i||!(f in i))throw new Se(f,a);var A=i[f];if(h(l))A&&"string"!==typeof A&&"number"!==typeof A||(A="string"===typeof A||"number"===typeof A?String(A):""),s.push({type:"string"===typeof A?_e.literal:_e.object,value:A});else if(m(l)){var S="string"===typeof l.style?n.date[l.style]:b(l.style)?l.style.parsedOptions:void 0;s.push({type:_e.literal,value:r.getDateTimeFormat(t,S).format(A)})}else if(g(l)){S="string"===typeof l.style?n.time[l.style]:b(l.style)?l.style.parsedOptions:n.time.medium;s.push({type:_e.literal,value:r.getDateTimeFormat(t,S).format(A)})}else if(E(l)){(S="string"===typeof l.style?n.number[l.style]:T(l.style)?l.style.parsedOptions:void 0)&&S.scale&&(A*=S.scale||1),s.push({type:_e.literal,value:r.getNumberFormat(t,S).format(A)})}else{if(_(l)){var P=l.children,I=l.value,L=i[I];if(!Pe(L))throw new Ae(I,"function",a);var N=L(Ie(P,t,r,n,i,o).map((function(e){return e.value})));Array.isArray(N)||(N=[N]),s.push.apply(s,N.map((function(e){return{type:"string"===typeof e?_e.literal:_e.object,value:e}})))}if(y(l)){if(!(O=l.options[A]||l.options.other))throw new be(l.value,A,Object.keys(l.options),a);s.push.apply(s,Ie(O.value,t,r,n,i))}else if(v(l)){var O;if(!(O=l.options["=".concat(A)])){if(!Intl.PluralRules)throw new Te('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',ye.MISSING_INTL_API,a);var R=r.getPluralRules(t,{type:l.pluralType}).select(A-(l.offset||0));O=l.options[R]||l.options.other}if(!O)throw new be(l.value,A,Object.keys(l.options),a);s.push.apply(s,Ie(O.value,t,r,n,i,A-(l.offset||0)))}else;}}}return function(e){return e.length<2?e:e.reduce((function(e,t){var r=e[e.length-1];return r&&r.type===_e.literal&&t.type===_e.literal?r.value+=t.value:e.push(t),e}),[])}(s)}function Le(e,t){return t?Object.keys(e).reduce((function(r,n){var i,o;return r[n]=(i=e[n],(o=t[n])?a(a(a({},i||{}),o||{}),Object.keys(i).reduce((function(e,t){return e[t]=a(a({},i[t]),o[t]||{}),e}),{})):i),r}),a({},e)):e}function Ne(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(_e||(_e={}));var Oe=function(){function e(t,r,n,i){var o,a=this;if(void 0===r&&(r=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=a.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce((function(e,t){return e.length&&t.type===_e.literal&&"string"===typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e}),[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return Ie(a.ast,a.locales,a.formatters,a.formats,e,void 0,a.message)},this.resolvedOptions=function(){return{locale:Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},"string"===typeof t){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");this.ast=e.__parse(t,{ignoreTag:null===i||void 0===i?void 0:i.ignoreTag})}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Le(e.formats,n),this.locales=r,this.formatters=i&&i.formatters||(void 0===(o=this.formatterCache)&&(o={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:le((function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,s([void 0],t,!1)))}),{cache:Ne(o.number),strategy:de.variadic}),getDateTimeFormat:le((function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,s([void 0],t,!1)))}),{cache:Ne(o.dateTime),strategy:de.variadic}),getPluralRules:le((function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,s([void 0],t,!1)))}),{cache:Ne(o.pluralRules),strategy:de.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=(new Intl.NumberFormat).resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.__parse=ce,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),Re=Oe;function Me(){return Me=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Me.apply(this,arguments)}function we(e){return we=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},we(e)}function Ce(e,t){return Ce=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},Ce(e,t)}function Ue(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function Ge(e,t,r){return Ge=Ue()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);var i=new(Function.bind.apply(e,n));return r&&Ce(i,r.prototype),i},Ge.apply(null,arguments)}function De(e){var t="function"===typeof Map?new Map:void 0;return De=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return Ge(e,arguments,we(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),Ce(n,e)},De(e)}var Fe,ke=(0,n.createContext)(void 0);function je(e){var t=e.key;return[e.namespace,t].filter((function(e){return null!=e})).join(".")}function Ve(e){console.error(e)}function xe(e){var t=e.children,r=e.onError,i=void 0===r?Ve:r,o=e.getMessageFallback,a=void 0===o?je:o,s=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,["children","onError","getMessageFallback"]);return n.createElement(ke.Provider,{value:Me({},s,{onError:i,getMessageFallback:a})},t)}!function(e){e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.FORMATTING_ERROR="FORMATTING_ERROR"}(Fe||(Fe={}));var Xe=function(e){var t,r;function n(t,r){var n,i=t;return r&&(i+=": "+r),(n=e.call(this,i)||this).code=t,r&&(n.originalMessage=r),n}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,t.__proto__=r,n}(De(Error));function Ke(e,t){return e?Object.keys(e).reduce((function(r,n){return r[n]=Me({timeZone:t},e[n]),r}),{}):e}function Ye(){var e=(0,n.useContext)(ke);if(!e)throw new Error(void 0);return e}function Be(e,t,r){if(!e)throw new Error(void 0);var n=e;return t.split(".").forEach((function(e){var t=n[e];if(null==e||null==t)throw new Error(void 0);n=t})),n}function ze(e){var t=Ye(),r=t.defaultTranslationValues,i=t.formats,o=t.getMessageFallback,a=t.locale,s=t.messages,u=t.onError,c=t.timeZone,l=(0,n.useRef)({}),f=(0,n.useMemo)((function(){try{if(!s)throw new Error(void 0);var t=e?Be(s,e):s;if(!t)throw new Error(void 0);return t}catch(n){var r=new Xe(Fe.MISSING_MESSAGE,n.message);return u(r),r}}),[s,e,u]);return(0,n.useMemo)((function(){function t(t,r,n){var i=new Xe(r,n);return u(i),o({error:i,key:t,namespace:e})}function s(s,u,p){var h,E=l.current;if(f instanceof Xe)return o({error:f,key:s,namespace:e});var m,g=f,y=[e,s].filter((function(e){return null!=e})).join(".");if(null!=(h=E[a])&&h[y])m=E[a][y];else{var v;try{v=Be(g,s)}catch(_){return t(s,Fe.MISSING_MESSAGE,_.message)}if("object"===typeof v)return t(s,Fe.INSUFFICIENT_PATH,void 0);try{m=new Re(v,a,function(e,t){var r=t?Me({},e,{dateTime:Ke(e.dateTime,t)}):e;return Me({},r,{date:null==r?void 0:r.dateTime,time:null==r?void 0:r.dateTime})}(Me({},i,p),c))}catch(_){return t(s,Fe.INVALID_MESSAGE,_.message)}E[a]||(E[a]={}),E[a][y]=m}try{var d=m.format(function(e){if(0!==Object.keys(e).length){var t={};return Object.keys(e).forEach((function(r){var i,o=0,a=e[r];i="function"===typeof a?function(e){var t=a(e);return(0,n.isValidElement)(t)?(0,n.cloneElement)(t,{key:r+o++}):t}:a,t[r]=i})),t}}(Me({},r,u)));if(null==d)throw new Error(void 0);return(0,n.isValidElement)(d)||Array.isArray(d)||"string"===typeof d?d:String(d)}catch(_){return t(s,Fe.FORMATTING_ERROR,_.message)}}function p(e,r,n){var i=s(e,r,n);return"string"!==typeof i?t(e,Fe.INVALID_MESSAGE,void 0):i}return p.rich=s,p.raw=function(r){if(f instanceof Xe)return o({error:f,key:r,namespace:e});var n=f;try{return Be(n,r)}catch(i){return t(r,Fe.MISSING_MESSAGE,i.message)}},p}),[o,i,a,f,e,u,c,r])}}}]);