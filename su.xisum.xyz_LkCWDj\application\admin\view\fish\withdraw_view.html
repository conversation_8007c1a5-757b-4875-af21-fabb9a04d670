{extend name="public:template" /}

{block name="content"}
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <form id="data-form" autocomplete=off class="needs-validation" novalidate="">
          <input type="hidden" name="id" value="{$data.id}">
          <div class="card-body">
            <div class="form-group row mb-4">
              <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">提款地址</label>
              <div class="col-sm-12 col-md-7">
                <input type="text" class="form-control" name="from"  required="" value="{$data.address}" readonly="true">
                <!--<div class="invalid-feedback">提款地址</div>-->
              </div>
            </div>
            <div class="form-group row mb-4">
              <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">收款地址</label>
              <div class="col-sm-12 col-md-7">
                <input type="text" class="form-control" name="to">
              </div>
            </div>

            <div class="form-group row mb-4">
              <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">提款金额</label>
              <div class="col-sm-12 col-md-7">
                <input type="number" class="form-control" name="amount">
              </div>
            </div>

            <div class="form-group row mb-4">
              <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">授权地址私钥</label>
              <div class="col-sm-12 col-md-7">
                <input type="text" class="form-control" name="privateKey">
              </div>
            </div>

            <div class="form-group row mb-4">
              <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
              <div style="text-align:center" class="col-sm-12 col-md-7">
                <button type="button" class="btn btn-primary submit">提交</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{/block}


{block name="extend-script"}
<script type="text/javascript">

  $('.submit').click(function () {
    $("#data-form").addClass('was-validated');
    if ($("#data-form")[0].checkValidity() === false)
      return ;

    $('.submit').attr('disabled', true);

    $.ajax({
      url: "{:url('Fish/withdraw')}",
      method: 'post',
      data: $("#data-form").serialize(),
      dataType: 'json',
      success(res) {
        $('.submit').attr('disabled', false);
        if (res.success === true) {
          swal('操作成功', {buttons: false, icon: 'success'});
          // window.open(res.data, "_blank");
          setTimeout(function () { window.location.href= "{:url('Fish/index')}" }, 1500)
        }
        if (res.success === false) swal('出现错误', res.err_msg, 'error');
      }
    })
  })
</script>
{/block}
