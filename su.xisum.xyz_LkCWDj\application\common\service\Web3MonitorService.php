<?php

namespace app\common\service;

use think\Db;
use think\Cache;

/**
 * Web3监控告警服务
 * 监控Web3登录异常、安全威胁和系统状态
 */
class Web3MonitorService
{
    // 告警级别
    const ALERT_LEVEL_INFO = 'info';
    const ALERT_LEVEL_WARNING = 'warning';
    const ALERT_LEVEL_ERROR = 'error';
    const ALERT_LEVEL_CRITICAL = 'critical';

    // 监控指标阈值
    const THRESHOLDS = [
        'failed_login_rate' => 0.3,        // 失败登录率阈值 30%
        'suspicious_ip_count' => 10,       // 可疑IP数量阈值
        'rapid_login_count' => 20,         // 快速登录次数阈值（5分钟内）
        'invalid_signature_rate' => 0.2,   // 无效签名率阈值 20%
        'new_wallet_rate' => 0.8,          // 新钱包比例阈值 80%
        'chain_switch_rate' => 0.5,        // 链切换比例阈值 50%
    ];

    /**
     * 检查登录异常
     * @param int $timeRange 时间范围（秒）
     * @return array
     */
    public static function checkLoginAnomalies($timeRange = 3600)
    {
        $startTime = time() - $timeRange;
        $alerts = [];

        // 1. 检查失败登录率
        $totalLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->count();

        $failedLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->count();

        if ($totalLogins > 0) {
            $failedRate = $failedLogins / $totalLogins;
            if ($failedRate > self::THRESHOLDS['failed_login_rate']) {
                $alerts[] = [
                    'type' => 'high_failure_rate',
                    'level' => self::ALERT_LEVEL_WARNING,
                    'message' => "Web3登录失败率过高: {$failedRate}%",
                    'data' => [
                        'total_logins' => $totalLogins,
                        'failed_logins' => $failedLogins,
                        'failure_rate' => $failedRate
                    ]
                ];
            }
        }

        // 2. 检查可疑IP
        $suspiciousIPs = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->group('ip_address')
            ->having('count(*) >= ?', [5])
            ->field('ip_address, count(*) as attempt_count')
            ->select();

        if (count($suspiciousIPs) > self::THRESHOLDS['suspicious_ip_count']) {
            $alerts[] = [
                'type' => 'suspicious_ips',
                'level' => self::ALERT_LEVEL_ERROR,
                'message' => '检测到大量可疑IP地址',
                'data' => [
                    'suspicious_ip_count' => count($suspiciousIPs),
                    'ips' => array_column($suspiciousIPs, 'ip_address')
                ]
            ];
        }

        // 3. 检查快速登录
        $rapidLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', time() - 300) // 5分钟内
            ->where('login_status', 1)
            ->count();

        if ($rapidLogins > self::THRESHOLDS['rapid_login_count']) {
            $alerts[] = [
                'type' => 'rapid_logins',
                'level' => self::ALERT_LEVEL_WARNING,
                'message' => '检测到异常频繁的登录活动',
                'data' => [
                    'login_count' => $rapidLogins,
                    'time_window' => '5分钟'
                ]
            ];
        }

        return $alerts;
    }

    /**
     * 检查签名安全
     * @param int $timeRange 时间范围（秒）
     * @return array
     */
    public static function checkSignatureSecurity($timeRange = 3600)
    {
        $startTime = time() - $timeRange;
        $alerts = [];

        // 检查无效签名率
        $totalSignatures = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->count();

        $invalidSignatures = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->where('signature', '<>', '')
            ->count();

        if ($totalSignatures > 0) {
            $invalidRate = $invalidSignatures / $totalSignatures;
            if ($invalidRate > self::THRESHOLDS['invalid_signature_rate']) {
                $alerts[] = [
                    'type' => 'high_invalid_signature_rate',
                    'level' => self::ALERT_LEVEL_ERROR,
                    'message' => "无效签名率过高: {$invalidRate}%",
                    'data' => [
                        'total_signatures' => $totalSignatures,
                        'invalid_signatures' => $invalidSignatures,
                        'invalid_rate' => $invalidRate
                    ]
                ];
            }
        }

        // 检查重复签名攻击
        $duplicateSignatures = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->group('signature')
            ->having('count(*) > 1')
            ->field('signature, count(*) as usage_count')
            ->select();

        if (!empty($duplicateSignatures)) {
            $alerts[] = [
                'type' => 'duplicate_signatures',
                'level' => self::ALERT_LEVEL_CRITICAL,
                'message' => '检测到重复签名攻击',
                'data' => [
                    'duplicate_count' => count($duplicateSignatures),
                    'signatures' => $duplicateSignatures
                ]
            ];
        }

        return $alerts;
    }

    /**
     * 检查钱包行为异常
     * @param int $timeRange 时间范围（秒）
     * @return array
     */
    public static function checkWalletBehavior($timeRange = 3600)
    {
        $startTime = time() - $timeRange;
        $alerts = [];

        // 检查新钱包比例
        $totalLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 1)
            ->count();

        $newWalletLogins = Db::table('web3_login_logs l')
            ->join('tbl_users u', 'l.wallet_address = u.wallet_address')
            ->where('l.create_time', '>=', $startTime)
            ->where('l.login_status', 1)
            ->where('u.wallet_bind_time', '>=', $startTime)
            ->count();

        if ($totalLogins > 0) {
            $newWalletRate = $newWalletLogins / $totalLogins;
            if ($newWalletRate > self::THRESHOLDS['new_wallet_rate']) {
                $alerts[] = [
                    'type' => 'high_new_wallet_rate',
                    'level' => self::ALERT_LEVEL_WARNING,
                    'message' => "新钱包登录比例过高: {$newWalletRate}%",
                    'data' => [
                        'total_logins' => $totalLogins,
                        'new_wallet_logins' => $newWalletLogins,
                        'new_wallet_rate' => $newWalletRate
                    ]
                ];
            }
        }

        // 检查单个钱包多次登录
        $frequentWallets = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 1)
            ->group('wallet_address')
            ->having('count(*) >= ?', [10])
            ->field('wallet_address, count(*) as login_count')
            ->select();

        if (!empty($frequentWallets)) {
            $alerts[] = [
                'type' => 'frequent_wallet_logins',
                'level' => self::ALERT_LEVEL_INFO,
                'message' => '检测到频繁登录的钱包地址',
                'data' => [
                    'frequent_wallets' => $frequentWallets
                ]
            ];
        }

        return $alerts;
    }

    /**
     * 检查系统性能
     * @return array
     */
    public static function checkSystemPerformance()
    {
        $alerts = [];

        // 检查过期nonce数量
        $expiredNonces = Db::table('wallet_nonces')
            ->where('expire_time', '<', time())
            ->count();

        if ($expiredNonces > 1000) {
            $alerts[] = [
                'type' => 'too_many_expired_nonces',
                'level' => self::ALERT_LEVEL_WARNING,
                'message' => '过期nonce数量过多，建议清理',
                'data' => [
                    'expired_nonce_count' => $expiredNonces
                ]
            ];
        }

        // 检查登录日志表大小
        $logCount = Db::table('web3_login_logs')->count();
        if ($logCount > 100000) {
            $alerts[] = [
                'type' => 'large_log_table',
                'level' => self::ALERT_LEVEL_INFO,
                'message' => '登录日志表数据量较大，建议归档',
                'data' => [
                    'log_count' => $logCount
                ]
            ];
        }

        return $alerts;
    }

    /**
     * 执行完整的监控检查
     * @param int $timeRange 时间范围（秒）
     * @return array
     */
    public static function runFullMonitoring($timeRange = 3600)
    {
        $allAlerts = [];

        // 执行各项检查
        $allAlerts = array_merge($allAlerts, self::checkLoginAnomalies($timeRange));
        $allAlerts = array_merge($allAlerts, self::checkSignatureSecurity($timeRange));
        $allAlerts = array_merge($allAlerts, self::checkWalletBehavior($timeRange));
        $allAlerts = array_merge($allAlerts, self::checkSystemPerformance());

        // 记录监控结果
        self::logMonitoringResult($allAlerts);

        return $allAlerts;
    }

    /**
     * 记录监控结果
     * @param array $alerts
     */
    private static function logMonitoringResult($alerts)
    {
        $criticalCount = 0;
        $errorCount = 0;
        $warningCount = 0;
        $infoCount = 0;

        foreach ($alerts as $alert) {
            switch ($alert['level']) {
                case self::ALERT_LEVEL_CRITICAL:
                    $criticalCount++;
                    break;
                case self::ALERT_LEVEL_ERROR:
                    $errorCount++;
                    break;
                case self::ALERT_LEVEL_WARNING:
                    $warningCount++;
                    break;
                case self::ALERT_LEVEL_INFO:
                    $infoCount++;
                    break;
            }
        }

        // 存储监控统计
        Cache::set('web3_monitoring_stats', [
            'last_check_time' => time(),
            'total_alerts' => count($alerts),
            'critical_count' => $criticalCount,
            'error_count' => $errorCount,
            'warning_count' => $warningCount,
            'info_count' => $infoCount,
            'alerts' => $alerts
        ], 3600);
    }

    /**
     * 获取监控统计
     * @return array
     */
    public static function getMonitoringStats()
    {
        return Cache::get('web3_monitoring_stats', [
            'last_check_time' => 0,
            'total_alerts' => 0,
            'critical_count' => 0,
            'error_count' => 0,
            'warning_count' => 0,
            'info_count' => 0,
            'alerts' => []
        ]);
    }

    /**
     * 发送告警通知
     * @param array $alerts
     * @return bool
     */
    public static function sendAlertNotifications($alerts)
    {
        $criticalAlerts = array_filter($alerts, function($alert) {
            return $alert['level'] === self::ALERT_LEVEL_CRITICAL;
        });

        $errorAlerts = array_filter($alerts, function($alert) {
            return $alert['level'] === self::ALERT_LEVEL_ERROR;
        });

        // 发送关键和错误级别的告警
        if (!empty($criticalAlerts) || !empty($errorAlerts)) {
            return self::sendEmailAlert($criticalAlerts, $errorAlerts);
        }

        return true;
    }

    /**
     * 发送邮件告警
     * @param array $criticalAlerts
     * @param array $errorAlerts
     * @return bool
     */
    private static function sendEmailAlert($criticalAlerts, $errorAlerts)
    {
        // 这里实现邮件发送逻辑
        // 可以集成PHPMailer或使用系统的邮件服务
        
        $subject = 'Web3登录系统安全告警';
        $body = "检测到Web3登录系统异常：\n\n";
        
        if (!empty($criticalAlerts)) {
            $body .= "关键告警：\n";
            foreach ($criticalAlerts as $alert) {
                $body .= "- " . $alert['message'] . "\n";
            }
            $body .= "\n";
        }
        
        if (!empty($errorAlerts)) {
            $body .= "错误告警：\n";
            foreach ($errorAlerts as $alert) {
                $body .= "- " . $alert['message'] . "\n";
            }
        }
        
        $body .= "\n请及时检查系统状态。";
        
        // 这里应该调用实际的邮件发送函数
        // mail($to, $subject, $body);
        
        return true;
    }
}
