<?php

namespace app\common\service;

use app\common\model\Fish;
use app\common\model\User;

class UserService
{
    public static  function getUsersByEmail($email, $users = [])
    {
        $query = new User();
        if (!empty($users)) {
            $query = $query->whereIn('userId', $users);
        }
        $data = $query->where('email', 'like', '%' . $email . '%')->select()->toArray();
        return $data;
    }

    public static function getUser($userId)
    {
        $model = new User();
        return $model->where('parent_id', $userId)->field('userId')->select()->toArray();
    }

    /**
     * 获取代理下级所有用户
     * @param $user_id int 代理id
     * @return array
     */
    public static function getSubFishIDs($user_id){

        $user = User::where(['userId' => $user_id])->find();
        $users = [];
        if ($user->roleId == 2){
            $users = UserService::getUser($user_id);
            $users = array_column($users, 'userId');
            $users[] = $user_id;


        }
        else if ($user->roleId == 3){
            $users[] = $user['userId'];
        }
        $fids = db('fish')->field('id,employee')->where('employee','in',$users)->select();
        return array_column($fids,'id');
    }
}
