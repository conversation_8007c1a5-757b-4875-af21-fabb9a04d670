-- Web3钱包登录功能数据库迁移脚本
-- 创建时间: 2025-06-16 22:13:47
-- 说明: 为tbl_users表添加Web3钱包相关字段

-- 检查并添加钱包地址字段
ALTER TABLE `tbl_users`
ADD COLUMN `wallet_address` VARCHAR(42) NULL COMMENT 'Web3钱包地址' AFTER `secret`,
ADD COLUMN `wallet_nonce` VARCHAR(32) NULL COMMENT '钱包签名随机数' AFTER `wallet_address`,
ADD COLUMN `wallet_type` VARCHAR(20) NULL COMMENT '钱包类型(MetaMask,WalletConnect等)' AFTER `wallet_nonce`,
ADD COLUMN `wallet_chain` VARCHAR(20) NULL COMMENT '钱包所在链(ethereum,bsc,polygon等)' AFTER `wallet_type`,
ADD COLUMN `wallet_bind_time` INT(10) NULL COMMENT '钱包绑定时间' AFTER `wallet_chain`;

-- 为钱包地址添加索引以提高查询性能
ALTER TABLE `tbl_users` ADD INDEX `idx_wallet_address` (`wallet_address`);

-- 为钱包nonce添加索引
ALTER TABLE `tbl_users` ADD INDEX `idx_wallet_nonce` (`wallet_nonce`);

-- 创建Web3登录日志表
CREATE TABLE `web3_login_logs` (
  `id` INT(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `wallet_address` VARCHAR(42) NOT NULL COMMENT '钱包地址',
  `user_id` INT(10) NULL COMMENT '关联用户ID',
  `nonce` VARCHAR(32) NOT NULL COMMENT '签名随机数',
  `signature` TEXT NOT NULL COMMENT '用户签名',
  `message` TEXT NOT NULL COMMENT '签名消息',
  `login_status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '登录状态:0=失败,1=成功',
  `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
  `user_agent` TEXT NULL COMMENT '用户代理',
  `create_time` INT(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_wallet_address` (`wallet_address`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='Web3登录日志表';

-- 创建钱包nonce缓存表
CREATE TABLE `wallet_nonces` (
  `id` INT(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `wallet_address` VARCHAR(42) NOT NULL COMMENT '钱包地址',
  `nonce` VARCHAR(32) NOT NULL COMMENT '随机数',
  `chain_key` VARCHAR(20) NOT NULL DEFAULT 'ethereum' COMMENT '区块链标识',
  `expire_time` INT(10) NOT NULL COMMENT '过期时间',
  `is_used` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否已使用:0=未使用,1=已使用',
  `create_time` INT(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_wallet_nonce_chain` (`wallet_address`, `nonce`, `chain_key`),
  INDEX `idx_expire_time` (`expire_time`),
  INDEX `idx_is_used` (`is_used`),
  INDEX `idx_chain_key` (`chain_key`)
) ENGINE=InnoDB COMMENT='钱包nonce缓存表';
