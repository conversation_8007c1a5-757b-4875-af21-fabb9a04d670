<?php

namespace app\admin\controller;

use app\common\model\User as UserModel;
use think\Request;
use think\Db;

/**
 * Web3用户管理控制器
 */
class Web3User extends Base
{
    public function __construct()
    {
        parent::__construct();
        $this->checkAdmin();
    }

    /**
     * Web3用户列表
     */
    public function index()
    {
        $data = UserModel::where('wallet_address', '<>', '')
            ->whereNotNull('wallet_address')
            ->order(['userId' => 'desc'])
            ->paginate(20);

        return $this->fetch('', ['data' => $data]);
    }

    /**
     * Web3用户详情
     */
    public function detail(Request $request)
    {
        $id = $request->param('id');
        $user = UserModel::where('userId', $id)->find();

        if (!$user) {
            $this->error('用户不存在');
        }

        // 获取登录日志
        $loginLogs = Db::table('web3_login_logs')
            ->where('user_id', $id)
            ->order('create_time', 'desc')
            ->limit(10)
            ->select();

        return $this->fetch('', [
            'user' => $user,
            'loginLogs' => $loginLogs
        ]);
    }

    /**
     * 解绑钱包
     */
    public function unbindWallet(Request $request)
    {
        if (!$request->isPost()) {
            return $this->errorJson(405, '请求方法不允许');
        }

        $userId = $request->post('user_id');
        
        if (empty($userId)) {
            return $this->errorJson(400, '用户ID不能为空');
        }

        $user = UserModel::where('userId', $userId)->find();
        if (!$user) {
            return $this->errorJson(404, '用户不存在');
        }

        if (empty($user['wallet_address'])) {
            return $this->errorJson(400, '该用户未绑定钱包');
        }

        try {
            $result = UserModel::unbindWallet($userId);
            
            if ($result) {
                // 记录操作日志
                $this->logOperation('解绑钱包', "管理员解绑用户 {$userId} 的钱包地址 {$user['wallet_address']}");
                
                return $this->successJson('钱包解绑成功');
            } else {
                return $this->errorJson(500, '解绑失败');
            }

        } catch (\Exception $e) {
            return $this->errorJson(500, '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 禁用/启用Web3用户
     */
    public function toggleStatus(Request $request)
    {
        if (!$request->isPost()) {
            return $this->errorJson(405, '请求方法不允许');
        }

        $userId = $request->post('user_id');
        
        if (empty($userId)) {
            return $this->errorJson(400, '用户ID不能为空');
        }

        $user = UserModel::where('userId', $userId)->find();
        if (!$user) {
            return $this->errorJson(404, '用户不存在');
        }

        try {
            $newStatus = $user['is_delete'] == 0 ? 1 : 0;
            $result = UserModel::where('userId', $userId)->update(['is_delete' => $newStatus]);
            
            if ($result) {
                $statusText = $newStatus == 0 ? '启用' : '禁用';
                $this->logOperation($statusText . '用户', "管理员{$statusText}Web3用户 {$userId}");
                
                return $this->successJson($statusText . '成功');
            } else {
                return $this->errorJson(500, '操作失败');
            }

        } catch (\Exception $e) {
            return $this->errorJson(500, '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * Web3登录统计
     */
    public function statistics()
    {
        // 今日Web3登录次数
        $todayStart = strtotime(date('Y-m-d'));
        $todayLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $todayStart)
            ->where('login_status', 1)
            ->count();

        // 本周Web3登录次数
        $weekStart = strtotime('this week');
        $weekLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $weekStart)
            ->where('login_status', 1)
            ->count();

        // 本月Web3登录次数
        $monthStart = strtotime(date('Y-m-01'));
        $monthLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $monthStart)
            ->where('login_status', 1)
            ->count();

        // Web3用户总数
        $totalWeb3Users = UserModel::where('wallet_address', '<>', '')
            ->whereNotNull('wallet_address')
            ->count();

        // 活跃Web3用户（最近7天有登录）
        $activeUsers = Db::table('web3_login_logs')
            ->where('create_time', '>=', time() - 7 * 24 * 3600)
            ->where('login_status', 1)
            ->group('user_id')
            ->count();

        // 最近登录记录
        $recentLogins = Db::table('web3_login_logs')
            ->alias('l')
            ->join('tbl_users u', 'l.user_id = u.userId', 'left')
            ->field('l.*, u.email')
            ->where('l.login_status', 1)
            ->order('l.create_time', 'desc')
            ->limit(10)
            ->select();

        return $this->fetch('', [
            'todayLogins' => $todayLogins,
            'weekLogins' => $weekLogins,
            'monthLogins' => $monthLogins,
            'totalWeb3Users' => $totalWeb3Users,
            'activeUsers' => $activeUsers,
            'recentLogins' => $recentLogins
        ]);
    }

    /**
     * 记录操作日志
     */
    private function logOperation($action, $description)
    {
        try {
            Db::table('admin_operation_logs')->insert([
                'admin_id' => session('admin.userId'),
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'create_time' => time()
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 导出Web3用户数据
     */
    public function export()
    {
        $users = UserModel::where('wallet_address', '<>', '')
            ->whereNotNull('wallet_address')
            ->field('userId,email,wallet_address,wallet_type,wallet_bind_time,is_delete,create_time')
            ->select();

        $filename = 'web3_users_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV头部
        fputcsv($output, [
            '用户ID',
            '邮箱',
            '钱包地址',
            '钱包类型',
            '绑定时间',
            '状态',
            '创建时间'
        ]);
        
        // 数据行
        foreach ($users as $user) {
            fputcsv($output, [
                $user['userId'],
                $user['email'],
                $user['wallet_address'],
                $user['wallet_type'],
                $user['wallet_bind_time'] ? date('Y-m-d H:i:s', $user['wallet_bind_time']) : '',
                $user['is_delete'] == 0 ? '启用' : '禁用',
                date('Y-m-d H:i:s', $user['create_time'])
            ]);
        }
        
        fclose($output);
        exit;
    }
}
