{"data": {"exchangesByCountry": {"nodes": [{"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Afghanistan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Albania", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Algeria", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "American Samoa (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Andorra", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Angola", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON><PERSON> (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Antigua and Barbuda", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Argentina", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Armenia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Aruba (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "TRUE", "country": "Australia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Austria", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Azerbaijan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bahamas", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bahrain", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "TRUE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bangladesh", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Barbados", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Belarus", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Belgium", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Belize", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Benin", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bermuda (UK)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bhutan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bolivia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bonaire (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bosnia and Herzegovina", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Botswana", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Brazil", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "British Virgin Islands (UK)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Brunei", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Bulgaria", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Burkina Faso", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Burundi", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cambodia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cameroon", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "TRUE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Canada", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cape Verde", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cayman Islands (UK)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Central African Republic (CAR)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Chad", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Chile", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "China", "cryptocom": "FALSE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Clipperton Island (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Colombia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Comoros", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Congo, Democratic Republic of the", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Congo, Republic of the", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cook Islands (New Zealand)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Costa Rica", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cote d'Ivoire", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Crimea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Croatia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Cuba", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cura<PERSON><PERSON> (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Cyprus", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Czech republic", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Denmark", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Djibouti", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Dominica", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Dominican Republic", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Ecuador", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Egypt", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "El Salvador", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Equatorial Guinea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Eritrea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Estonia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Eswatini (formerly Swaziland)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Ethiopia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Falkland Islands (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Fiji", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Finland", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "France", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "French Guiana (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "French Polynesia (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Gabon", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Gambia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Georgia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Germany", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Ghana", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Gibraltar", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Greece", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Greenland (Denmark)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Grenada", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guadeloupe (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Guam (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guatemala", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guernsey", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guinea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guinea-Bissau", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Guyana", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Haiti", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Honduras", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Hong Kong", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Hungary", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Iceland", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "India", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Indonesia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Iran", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Iraq", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Ireland", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Isle of Man", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Israel", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Italy", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Jamaica", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Japan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Jersey", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Jordan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kazakhstan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kenya", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kiribati", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kosovo", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kuwait", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "TRUE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Kyrgyzstan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Laos", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Latvia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Lebanon", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Lesotho", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Liberia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Libya", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Liechtenstein", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Lithuania", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Luxembourg", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Madagascar", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Malawi", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Malaysia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Maldives", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mali", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Malta", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Marshall Islands", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON> (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mauritania", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mauritius", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mexico", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Micronesia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Moldova", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Monaco", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mongolia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Montenegro", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON><PERSON> (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Morocco", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Mozambique", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Myanmar (formerly Burma)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Namibia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Nauru", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Navassa Island (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Nepal", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Netherlands", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "New Caledonia (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "New Zealand", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Nicaragua", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Niger", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Nigeria", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON> (New Zealand)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Norfolk Island (Australia)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "North Korea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "North Macedonia (formerly Macedonia)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Northern Mariana Islands (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Norway", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Oman", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "TRUE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Pakistan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON>", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Palestine", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Panama", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Papua New Guinea", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Paraguay", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Peru", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Philippines", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Pitcairn Islands (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Poland", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Portugal", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Puerto Rico (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Qatar", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Romania", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Russia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Rwanda", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON>ba (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON> (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Saint Kitts and Nevis", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Saint Lucia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON> (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Saint Pierre and Miquelon (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Saint Vincent and the Grenadines", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Samoa", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "San Marino", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Sao Tome and Principe", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Saudi Arabia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "TRUE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Senegal", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Serbia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Seychelles", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Sierra Leone", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Singapore", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON> (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON> (Netherlands)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Slovakia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Slovenia", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Solomon Islands", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Somalia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "South Africa", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "South Georgia and the South Sandwich Islands (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "South Korea", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "South Sudan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Spain", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Sri Lanka", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Sudan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Suriname", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Sweden", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Switzerland", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "Syria", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Taiwan", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Tajikistan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "FALSE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Tanzania", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Thailand", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Timor-Leste", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Togo", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON><PERSON><PERSON> (New Zealand)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Tonga", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Trinidad and Tobago", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Tunisia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Turkey", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Turkmenistan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Turks and Caicos Islands (UK)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Tuvalu", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Uganda", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Ukraine", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "United Arab Emirates (UAE)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "TRUE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "TRUE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "United Kingdom (UK)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "TRUE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "United States of America (USA)", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Uruguay", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "FALSE", "coinspot": "FALSE", "country": "US Virgin Islands (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Uzbekistan", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Vanuatu", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Venezuela", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Vietnam", "cryptocom": "TRUE", "gemini": "TRUE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "TRUE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Wake Island (USA)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "<PERSON> and <PERSON><PERSON> (France)", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Yemen", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "FALSE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}, {"binance": "TRUE", "bitbuy": "FALSE", "bittrex": "FALSE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Zambia", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "TRUE", "kraken": "TRUE", "moonpay": "TRUE", "rain": "FALSE", "simplex": "TRUE", "wyre": "FALSE"}, {"binance": "FALSE", "bitbuy": "FALSE", "bittrex": "TRUE", "bitvavo": "FALSE", "coinbase": "FALSE", "coinmama": "TRUE", "coinspot": "FALSE", "country": "Zimbabwe", "cryptocom": "TRUE", "gemini": "FALSE", "itezcom": "FALSE", "kraken": "TRUE", "moonpay": "FALSE", "rain": "FALSE", "simplex": "FALSE", "wyre": "FALSE"}]}, "timestamp": {"parent": {"id": "737a00ec-654e-57eb-8a53-61ec6f1c3a13", "name": "exchanges-by-country", "fields": {"gitLogLatestDate": "2022-05-17 03:16:37 +0800"}}}, "argent": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAACMUlEQVQ4y5WUPUtdQRCG71eIGAKCJAjWoiRtwBSpQwJpkkbYRVBR7FOkSWN1zl+wsEzhf7EIBAykkKDEJmkDCfeeu+s7M++cs4ofeOFl956deXZmdnZ7vSu/XEUouFagVegFtcpvup5ge+cPhn06zKYqHGPMUEPJ/Bs049D7AJ9A57mOGUrQVOdVOIPmadO/A6bpDiwdTW2qsCqkZPOMcYz/SwQObo2SwGEy4JqkmOrYpDokRulpfyBQbW+ESZFzHYaQAA/UuY4Tj1LnBtzHHLZxaOBrDkcNqti3KOMc9AtgOLN2Vj+v4yk2fOx1lACuj66KI6a9o+lWGp0fCqLUuae9SeBIgG0tGVEZ3UPoRxFd6iSHA2Atm4XvWH/Q+RKqdBN3insWVZgUqXoNZRMcFNYq/f6ZtRzZ6EAJ29J+ZWlpuqmFEZxaqIwa5RR2L7OXS4Ft3eIidE5YU8Lg+C+Lyg2YOkY0elxwqNfvKXRM2MQMtWXGnH8E9BOjH/NwCtuI6xjntX3w8VlxCGXdJhyPUttS4Wu3xk07qBzSsgBPGdm4gHnKf6Flb16Mz4vUGz+kLNfRvp2I0S5TabjoMKndO7lavD1DHuB7lsNfIb2e7M0tnnI8pMF/h0EbdhVxvdgSdtWkG8J2AXWfL2LHh1QKimepi2w9tYDQu6Q6DhjpZgH9Cc1ZY3fXTdL7jfF1vgl2Cao+b+HzB+Mbf316yTrdi/6odSgAxePLp7+zSeIjrxM5F01jQwWKGoyCAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/1451fcccfdf060df88cdd664c0766e8e/41fb7/argent.png", "srcSet": "/static/1451fcccfdf060df88cdd664c0766e8e/41fb7/argent.png 20w,\n/static/1451fcccfdf060df88cdd664c0766e8e/996dd/argent.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/1451fcccfdf060df88cdd664c0766e8e/4eb25/argent.webp 20w,\n/static/1451fcccfdf060df88cdd664c0766e8e/3a6ff/argent.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "binance": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAAD+UlEQVQ4y02TzY8TZRzHJ/vWdtp56bw/8/4+0+lMO53ptt3ue3e3WzbsC91FFggrBw2JCRxIiIAhhmjCyRhFswZjPEjQo4jewOhB5IaJJvoPQDRcjR7rs1tQv/kdnsN85jvP9/cdJJ2sw0nqKZw4qcGpxHEQVaNK5Acl1/e8kq9bjmnbpmnouq6qqiRJAACO45B/4VqawImTJEnj+ZlyKQxrKXxR1XLcZmp5rmWa1hCWZfk5PPQckvDRoBy2GuH5M65q2CdP724f7wNZO3fCCANDlBSoobMgCM/hQzINwvJKt9c//fLscrdSgc6R5fqm7dmeZzvWVLO5ubXdaE3zHAedIcyyLDK8Z7UWL690w1azP7t4pj1vlwPNDj99E9x6AwiqbxpK1DkVNZd9W0vqLQCE/+BqkgRhBXr2p+duNBeaUVVzyp9cEwcP0cEP6P7rAIiGETScjeuldOHo+jGa4XkewgwCs42T6mQaznW7e+3Zmbiq+MFHV6TBI/TJXfzpVzg8vHtRBDxvlVO/vQUj0hQeCBxNMwjcyuJcdGHPDaPACQNOC2+cVwaPs3/cI3rzxlpHf/YNNnicu/qKVCAkBbAA8Gc3hNDlcIJGwoN9lhXD9mBiYXlmymvU3e/3mf6q4bi2YVrbXfnBTTr01bSiipLM8WI2h5FFiqYoBDah3pg8cXLXdLw7bwt/3i/sHDEo4Neq1q9fED9/RoYlvUAZ2yvi399iH19lCJLpra7quobjOAI7lNST/k7fsN07bwl/PcB2elpRcGsV87fPiV9uk2VfzZN6f1mA8K0rNEbQnc4iXDiGYYhX8kzHESTNdH3Hd6fqVhpb333IbC4psI+aph9bEu/fpHxHjAMg8DxFcxMZFMNwgiAQ2NtW3Xr1uAb76+gGRVvXX5NgQr/fwzttuTstPfsaG/yEXj7LIlmBoBiaIndXGVcn4c0R07J916qExlSjbay1taQks8r7l7jBo/yTL/GndzF4eOcCM4ELkU6v10S7VJEBThXxfD6PmAaUKcnq1tZLznSdOLckRh7LKPuXmcGP+cHD/HsX6Swu+ApzbRFrlZSF7mY2i+YLGIqiiH4oTVUb7emS4VjNWF1pyDwgaAXG88ElKo8JOMWsxWDSk3BWLodR/lDZbBZRXwg2vt6cWj+6k8aTPOwuEEmSwUmaZblikXSCeKG7EZSj8bExSOZyuYmJCUQ+lHQg+JcKNMubhrC3DnIo1uutLnUWxzPoqSOcxBO5HAqxQqEAyUwmMz4+joBDCS8EGy8CruJzJEnpmgb3WcDwklk8SKhQGH7tkBwdHUW4/4k9EAMbD5sA2wc7BJtAEHgmW4CmMKGh59jYGCRHRkb+AWhm4jVNPoRzAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/7b6435ff9a11b0ad7a1c5c2f34b5cc65/7f8fa/binance.jpg", "srcSet": "/static/7b6435ff9a11b0ad7a1c5c2f34b5cc65/7f8fa/binance.jpg 20w,\n/static/7b6435ff9a11b0ad7a1c5c2f34b5cc65/b1fd8/binance.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/7b6435ff9a11b0ad7a1c5c2f34b5cc65/4eb25/binance.webp 20w,\n/static/7b6435ff9a11b0ad7a1c5c2f34b5cc65/3a6ff/binance.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "bitbuy": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABS0lEQVQ4y+WTP2uDYBDG/VAOAdds/tklS0DUoOCSEIcQiGAGERxCpnwGUVxCCFIyBJHkK7i4BLIIKkVEbXv4liidUjqU0md4+eF7z915ntj7D4T9aXPTNAjeWvXhe5XB88j1lDlN06qqAF5bARRFkec5QF3XKFfTqt8mhkjTtDiOAYIgsG37fr8DbDYbZP7SV1cZmSVJ4nneNM0wDOfz+XA4hHQURS0WC8/zHMeBGDhXqxUAZI+iqDPLsny5XI7H42AwmE6nhmH4vq8oSlmWNE0zDAMxuq6TJLnf7yE4SZLOzHHc9Xo9n884ji+Xy+12ezgcxuMxXLEsCy3cbjdRFF3XJQjCsix4DjP6NO92O7hbr9dQ/HQ6wdsCz2azyWTy0koQBFVVYYSj0QjKoLE9uyRoTlmWwVweM+uW5CH0nftnH/orgP3fv+oXzB/AYU/g21NyqwAAAABJRU5ErkJggg=="}, "images": {"fallback": {"src": "/static/2841b3e3f7d870b86cfee0c47d6326c7/7f8fa/bitbuy.jpg", "srcSet": "/static/2841b3e3f7d870b86cfee0c47d6326c7/7f8fa/bitbuy.jpg 20w,\n/static/2841b3e3f7d870b86cfee0c47d6326c7/b1fd8/bitbuy.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/2841b3e3f7d870b86cfee0c47d6326c7/4eb25/bitbuy.webp 20w,\n/static/2841b3e3f7d870b86cfee0c47d6326c7/3a6ff/bitbuy.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "bittrex": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAABhUlEQVQ4y+2Uv0oEMRDG788eiC9gsfsAtr6DrZ3FNbYWIgnI+gYi6oGllZyCdnaCYCHaqHBgIYII2liICRbX+AD6y+7M7inung9wgY+ZTDZfvszOpNGYjDAS6xuxyYEfFTDqu2Cb4buAsSM2rkCSEes8JxhBs5IwU2Vlsw1EGUkHrBLfZH0rIPgQbxCfU9K/VVlXkim59dP4X+CFtXPUXhG7CDFsKoRRBaEfJW0LplC6TM5mErl+ptz6G1Su5d/6duWVQVvUrUPm2PDE/Fqsg/g4sRnJHbFUbhFV/ohAKLaPfcAuypUN2AP3ovAWP9U9tQrj3B6AQ8nnJ6pWsLvgAywEtZrDSoVJnj9R6CB0J3LIgNg7eCSHA+LPxN6Yd1XE2ByCfa529KvmZkFXDg3l1NL6rKlDp4R9yd0Zf/MSewqG+K+J9T8Kv57QOG2nedAD2xzSi4Nv/A7rS1paWquVnVK0laGdjPayK21Z7EVH/etBELIW844+CLGRh8GQY+OLbpqMbHwDgrJPb51fgdQAAAAASUVORK5CYII="}, "images": {"fallback": {"src": "/static/e154f67ef5dc1196c90f6f2ccb977424/41fb7/bittrex.png", "srcSet": "/static/e154f67ef5dc1196c90f6f2ccb977424/41fb7/bittrex.png 20w,\n/static/e154f67ef5dc1196c90f6f2ccb977424/996dd/bittrex.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/e154f67ef5dc1196c90f6f2ccb977424/4eb25/bittrex.webp 20w,\n/static/e154f67ef5dc1196c90f6f2ccb977424/3a6ff/bittrex.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "bitvavo": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAABsElEQVQ4y71UvS5EQRSeuzcSJGQTye4iFHrBG2h1s4UGiSfwBJ6AYp9B4xmQKBUKjU4jiKiRSGTZPeObO9/MnTsWq2CSL+fM3zfnb45S/zbaJkbmIJRY04TVf0cmg3UdnRmKLL6gTUNpaUE2MW+5uV2XIQnd4ZyE25j3IZ+BR8AAu9yrFVZ/ObSUMXMxqkN/IIkUUpsbyAmGIPveQh8jbXLKDsne8Ng79XU+mofzPySiRrkC9AOhk0fRmSxBRF7NYkZ5Shc9mbVwMSkneiRJgsqs5pRbjkR6kXV7PGetGQemgYaDzECOOcKSLOMrk8BdxVVtbrmuePGCFj8BXeAad5spoS+T/eCqNj0Sb4aQ6KJkTDXzshMSlSRiCZskEe/qSRSvBeAlSdR55TPwVZ+lYx7qUtosL0cXDgFrUTeqgFWGKichFW026Kq94Iu4E9XmWnDV7utCP0g8VD5+tvIvk0BfAVN8bAQkZ4wZ9uUVuIc+H3UjVzquhswoFuaKj+/KYbbIatmiQPhpv05XvyrqpMe5h9TAQo5LrZ32xbAgyVcSFVqUjpssG60ODYTZ/aPxAfEF7gyVr56zAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/90218283bacc595608628ffb63a6631c/41fb7/bitvavo.png", "srcSet": "/static/90218283bacc595608628ffb63a6631c/41fb7/bitvavo.png 20w,\n/static/90218283bacc595608628ffb63a6631c/996dd/bitvavo.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/90218283bacc595608628ffb63a6631c/4eb25/bitvavo.webp 20w,\n/static/90218283bacc595608628ffb63a6631c/3a6ff/bitvavo.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "coinbase": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABG0lEQVQ4y2MQC/pANmIYZJrFgz9IBH8QDvgg5P9BNPCDRAjRmoHa+Hze8/q8V4j6qBr7USr0A6fne6Ap4gQ1Ay3h9nrvWPR59cFfj17++/Dl38W7f1oWf1eL+wh0gjgezRCd0a1fv/349////38g4v+fvyBywpofQCk09zMg+1PQ74Neyqdnb0Caztz8k9j11aP8S++qH1uO/1KP/wgMAqAa7JqBfmNze9+46DtQ592nf9XiPrG6vuf1fs/j9R4Scmg6UTQDwwkYSCv3/wRq7lv9g8X1vUwYSBDk1GAsoYWumY9szcjOvgNy9kcSnA0JMH1sAbaZYIBRFFVoiWQNMJG8AiWSS/eISyRoyVMRljy5iEyelGaMoVAYAACYRFluicEoYgAAAABJRU5ErkJggg=="}, "images": {"fallback": {"src": "/static/b3bb8e0bc947ac46ddf9c62446843c75/41fb7/coinbase.png", "srcSet": "/static/b3bb8e0bc947ac46ddf9c62446843c75/41fb7/coinbase.png 20w,\n/static/b3bb8e0bc947ac46ddf9c62446843c75/996dd/coinbase.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/b3bb8e0bc947ac46ddf9c62446843c75/4eb25/coinbase.webp 20w,\n/static/b3bb8e0bc947ac46ddf9c62446843c75/3a6ff/coinbase.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "coinmama": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACQklEQVQ4y2P4Txz49+8fpiADHqXPX3y4fPXx1WuPL115hFU/ds1//vwFkkuXH0tKnV1WuVJALOvAoWtAkb9//xLQ/BcE/l249PDwkeuZOfP1jWu5BTPdvLt//fqFZj8DLjfnFiyeNnNPRMy07r6tplaN3AIZ8xYegjsKi2aIxIZNZ+cvPPThw5eHj17VNqxpatlgYtHIJ5yVlbcQpObvP3yaq+vWZOYu2Lj5jLxKsbBELhN7sqBoDpCRnr0A7CkMzRCn3rr17M2bTxDur1+/eydst3fpKC5bNmHyDk7+jNaOzdidDTHvyLEb3779nL/gEFDboSM39E3qHVw64pJmVdeu4RXOKixZht3ZEJufPXt/4+azhw9fX7v+GBjU7V2bRaXzuQQyWDhTJGQLxGTyDx+5gRxhKJofPX7z5OnbG7eeTZ2xZ/2GU5u3no1JmOnq1T1l+m5gaMkqFu/YeRFs+V8sAfb9x6/Xrz++ePlhw+azd++9SEyZA3S2nVN7Zt5CJ/fOoPDJIGtwOfvmreeTp+0GMu7df/nv39/6pnUMrIm8QpmC4jm8Qlnp2fOxhzZysJ05dz8obPKM2XtjEmaFR09du/6UuXWLmHT+2fMPiNB89p6bV/fNW8+y8xfJKhUlZ8xR066wd2l/9foj4eT5+/efk6fubNh4VkWzfNmK43v3X7167cnXrz/AOgnlqq/ffqxed2rCpJ3FZcvRUgXhLAlx/IGD127deg5k/Pr9B6ifhMIAqP/nz19YNRDWjKfoQQYAiwb+YBk9UVkAAAAASUVORK5CYII="}, "images": {"fallback": {"src": "/static/2eb8d047f952556a08255030faf30246/7f8fa/coinmama.jpg", "srcSet": "/static/2eb8d047f952556a08255030faf30246/7f8fa/coinmama.jpg 20w,\n/static/2eb8d047f952556a08255030faf30246/b1fd8/coinmama.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/2eb8d047f952556a08255030faf30246/4eb25/coinmama.webp 20w,\n/static/2eb8d047f952556a08255030faf30246/3a6ff/coinmama.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "coinspot": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFh0lEQVQ4yzWUaUyUVxSG72BcZp9vnZVhdYYBhmETGcQREBDERmtttaW01Sb+UOtWq8VoTdM2aZpqbURt/dFWg1alMO4RK0YIKhRsLKuhOINaMXGjf7rgN5zTM5h+yZub78+T957zvpfxUhrjZQ+jUyVa3CrJnMwczmStzR6/UrY4grLZcY+kSGa7YrY673G8HFRL9uqR4Km1d9LTQ+HU1Kfh3FznSFYWC2d4VYyTvJOwKFg0uxkBqwUpLmzkLEgQlC32ydNsjUVBsqLV5sTjJxpwfPw5Pvj2EIb8fhz2+VaMejxsNCdnCsHSVUY5i4mih4myZ5+JT8RkVzZWV69SRNmpyJZYIKdAQDCYxInDR+qVpqZTkc1btkVuDt5WRgcGcaz556rBEw2s8+iPUxgne1WclB51uo+X05AT3YrN4Y1kZgVAkBJAMieA1ZYAMzQGWLN2PTQ3X8KNm3dAY7AF3lu3IfJrTx8uXPLqvuPnLrCO/gEVM4k5pOxqTspETspQeDkdONGDelMiiLILzeZk+o9DR5wb+/sH8PTps7Dzo8+g/VoXbti4BQ7/UA+8YKHRxL5u4s2MGQW/1iTOCpvEXDSJWRFO8hHYC4KcjrzkAb05FdVqK3y4bSf+OTYG4fAI1NUdxHXrNsLF5hasWrQkojPwaLE5Q7I5XsMMwpyVBEWjkKcQGMgxkFtSBhhkL9Y4UrBp9hwY6roJd/4Yxb7+2zg4MAR3747Czl2fAsGAYDRrBy3P8TbT84GgQShEg1CgGMV8IDDwYg5OJ9XYPXjDaYee2locevQUe5svQ1fbdei42Yt1B7+jZcXRWOzRpSmTiTA7GpmOL76nF+YRcC6QW3LmB7WQD5mSD0467PC5HI833t8O/U1B6Fm8BNvaOuFS03nITMkFPRdLcUqkxcVORONF4BEClio6vgT1QhFoubkwXZsH04Q5WGtJgjVCEr7syobHRV4YiE+CjnWboPncJWgvK4cKqwu1sgtkOWkyCZI5LupwnGm5CgKWo8ZUAmZHBeQV1KBRDECCNBumagrw661ueLY+CfoSZ8Kd0kI4lJkPHSkuLEvIAo3oJqCLrp1MSqQZxhOQr7prlBeh2lQ2kep7Az7+5BuQ7WXAtPMw0+vHJw1JMFyZDI93x8JqfzruoOAfS0mlFPjQJKWhIHsor+4JUZ6JBB5hU3ULG1lMMTLVXIVNmwfTdEVgkkqRzZgPu9bMAmhzoRJMxG2rMzBFnwlnYm3gt/hwhphNIB9EI0aFUAQpharp/oltrT206osvj+HW2gNK5UsfoGhbDGxqgEYwH7sacgGHPLB7S3bUMcyRsmCpnIrTaWm8FI1Y9v8RU6htlNvUtxgiap+P/x0CeI5jY88i3d29uH7TXrA4q+Di99m4fW0hxGhoaXwRaoUAqqNp4PNRY8yliM2iMuREqBBUBt8wOVUT8Dm7cqVjxf37D3B09GHk4cOHE0+fPMLzF1oha9Y7wFgxGsQFBCwBvVBMUQmAaC2G5JSFBPNPUG4jL1qWvZzALOowhsS6unr2hkMjODwcivT13Y6EQmG4dq0bcvLfhSkagokVoOdLYbo+AK60pbS8/RHROi+i5/0Ey99jFPOYScqNYXX7j7PoF4Vebf3lq97eQXoEBrG7+zelt7dfOXu2ZcIatwymGcpBJ1RMaLkyRS+UKrJ9Aeq4QLQQe4y8nyo8l1TAWDB4mR0+clYVhQL8xZqCl5e3tnYOX7/eje3tnXjr1i3cvuMAbb0cdUIVavkK1HLlqDaW/K4XSl4zSoVMzVVF3wSVUchnrLm5ndUfPc927zk6ef2rVzuYzf6Kur7+TE1j08WT5863hC5caPnXnb7ynymayjs6YdFJLV/5pj1+mZqAzCgWxtBbwF6ogP0HkNSuKWj3N9kAAAAASUVORK5CYII="}, "images": {"fallback": {"src": "/static/9edc22084109f5e5fa469f4a96bcf987/41fb7/coinspot.png", "srcSet": "/static/9edc22084109f5e5fa469f4a96bcf987/41fb7/coinspot.png 20w,\n/static/9edc22084109f5e5fa469f4a96bcf987/996dd/coinspot.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/9edc22084109f5e5fa469f4a96bcf987/4eb25/coinspot.webp 20w,\n/static/9edc22084109f5e5fa469f4a96bcf987/3a6ff/coinspot.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "cryptocom": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAAD5ElEQVQ4y3WQe0xbVRzHf/fcaxHUBpptpIW1vX3QF2w4RhEZUBilDDpY6QP64jWw4NiUKITXsmF4bSAiMqBsdGzQOaBSnCxzdTM6psmc/uNilhhNXNSYEDVTplN5eWgZPnfzyS/fnPv9nJNzgOCm/C8UrUK8FLQ1GYeHdQDxVP+C5KkoQTpsfopi72JwVcCKp+h0kp/23yaWdyNe+sZkCNSIkwpP7JSk2rud54fdb6lMz0NoArEl6RGBmuJvlNcAxM8MQAk0JF8NYYmbYnTNXa5h9+xO7bPcREu3c7LPNS1LK4XQRBS5mxJmbSgAfM0adBZsSSEjM+w1x1wTl9S2Bng0jgjaQTwWD2ibPKN8yD3beNzFUuwDVhJBa9Ytgt6DBNkQqVbmHXK6L1YfGaC4mVp7Y/vgxNFXx4/2jncMTBS/0AURGfqq1rPTV00H2oCnwRYGkEBL0DmUaO+Y911ZugNCk0k6+7PP76yuri4vL6+srODw6/3fRMllwNwVLM6dvDi3fU81sNWUcC+Wc4GvDZHr2/snGXQubM2J0VQvLS39/sfizwu/YBbu3cd+wcFjwM0BVnrrK26l7kUIV5OiPEAiHdB5wTJj1/B0kMwInGxmVL4yv66xe2x06uqpCV/3Sa/K2szeYYOIbAjXtA94lLpaYGeR4nws54NAFyIv6DrpJUX5rFjbyOQ7KnOT3tGx6v86h96gUyvPXbgWm1MDzLSOQSzX4TOoKD0QYgMI9SEKS6fTy4y1ffzpF1hYXFyqbRv98e4Czs3d7jvfzuNwd+He5vjSI73nlYYGiNCSUUYsF4DQFBxt6xmZBdpwYvxt3MPv1NI39cmtL7/6Zh6H+R9+wotzN28DJ7d90KvUN0JEHikpBBCbQVgYHFPUOfgmROrZyoqvv/seV698eCsi6UCQwj7gvhzYTmVtAUZax2sepeEwcHRIYgEksRJiCymxnbvwQZyuCRgZjgZn4LaHXhqN29cUyEPjPnhcE/pkudd3c5u2HrhGSmIDQmJH0iIQmON0h12e9+uOv86M2T82c91z+aOweAeIba0nZt67cXtTQlVhTf/UpRuGg30gsiKpHYsAkpI1pCXALUS01VI7dHbmuuG5fsQxAttI8CwQmpdgajk9fa2hZ4q5vWJtUVqyboGszE8pUuxH8jLgmMLiqupf9oxOzz1tbhNm1vW7r/Se8Ykz67GGoorJ6PIHShkQsvIA4J9UtAOJyyC8UKiu7zntG/HMJVk6gWMm6OK1X/KKQI2Q+hWQOdaR4/kMDoTcQUZXAV2ChKWUpAK4RSi6kpD/vfaAv9I/QYpKJHcQUv9GD+n8CXiqiDy4rGhoAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/3ccc032ee507d0c45d32ffdf9bb2515b/7f8fa/crypto.com.jpg", "srcSet": "/static/3ccc032ee507d0c45d32ffdf9bb2515b/7f8fa/crypto.com.jpg 20w,\n/static/3ccc032ee507d0c45d32ffdf9bb2515b/b1fd8/crypto.com.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/3ccc032ee507d0c45d32ffdf9bb2515b/4eb25/crypto.com.webp 20w,\n/static/3ccc032ee507d0c45d32ffdf9bb2515b/3a6ff/crypto.com.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "gemini": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABp0lEQVQ4y7WUyUoDQRCG57FU3C4RPbhveBA8iiCIHjVEX8Cb93hSSQ5ueIggKOjJp9DZjE40syTdXVU9tmkSJnFcQKzDMFNTH/X3X91txH8I479gjGNKe/8Zlo2nAp4xrpLUGfkbWDe5jHDeZTMun3LE8pMwBcXt/Y1UtSqKAQyY/L6OMpZMyn0f+sy6BRQn+hupZERy0uZOs1QnDwNYK4tWTSesJak+uxU+bIvzEPd9PA2xGOCBj3kfMhbUpGw1NzrIYiBGbZax+aAlch7kKtBl8lmHb3uw8Sx6Le7TJ1iTpUgtjNWITkJUpP6V9eAs+lB6V4cllyc9M5KDWXzi9+yjrhDAThPe9KAUYQWp55Fd17AT1uQbyW5TrL/Algfjjug2edbDTQ/7LDFk8TGbnYWQMioNhyQzFlfeHId0EVEpokJAVxGO2HzvVQjZNqQ22dr91bI4CkB/6roHgRMOYw2HUX6xPXXeBOp/ZPkqqDkD0W0dlX+nISRnm749qcmvlPm0K+ZcWHD5TbtD3x2M1tYPSFZIaZWf1/ndwaCEQvpC7f9fBj/GOx9HeKA4rVjSAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/5e6663b44ef940909fc9490da6f2f9ef/7f8fa/gemini.jpg", "srcSet": "/static/5e6663b44ef940909fc9490da6f2f9ef/7f8fa/gemini.jpg 20w,\n/static/5e6663b44ef940909fc9490da6f2f9ef/b1fd8/gemini.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/5e6663b44ef940909fc9490da6f2f9ef/4eb25/gemini.webp 20w,\n/static/5e6663b44ef940909fc9490da6f2f9ef/3a6ff/gemini.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "imtoken": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEfElEQVQ4y32U309bZRjHn562tIe2tOilN8Z5o5deLDHL/gAvvSK74cJ4ASpZggN/bGMZbshkWgEHdehYHI6JFegPWkrhtKMthba0W2TGZMXQGTfRjSBzRjjnPI/PezhmZhc+yZvmpDmf833e5/l+wflZCuShNMgXUuAMpKX6LxfB9XVOdl1danZ/kw96vluueaZX1IZIQfXEirWGxGrQO19ufipVkX1KBXzJsuRN8684qQqAc0ABhlpcAYYOp4GBTfWXs+uusRy5x5fIPZEnhhJDiaHkiZWIocTQKgOafEoZvJVbwFCLgIJzkBX6F8BQOpz2yxevE0OJoarrSk51jS/pDEWGIkN1U6lqQBfKxFB/Y1JA1wQUwPGpIvEBZ7/iZygxVGOoxlBkKDIUGYr/gWJDpIjeWEnzza1qPhMqlPrmyxLU9c2Do2++yfHJAjFUY6heH0iTa2QRXZcyAkqifYaSh9v3TTJwuoByeIXs0YIuz5Y0Q6nC7Ys7tH+QkOt6k+sMJYZq9QMpsg0oCIMKObl9hnL7GfKYUBjLkvPbPD0XL+PL19fo+YUbmjtRIlZXfTpdkcHenWiuO5Ogut451dk3j9A7h89czNDBa0VyBa6TdThNHoZaL/G9XsnR6/nblN7cpu09lbgwc38HG2Il1cN3ytBmsJ+KB+2nZ8nZM6dCdxxfm1nD739/SL892qXS5g4d+CpPMJSiFycKlNv8g54oLG49RG+kqJrTD4LteKxWdypO0BXTXxjKYHXrER0IZOiVYNl4o2+1RnAuTl/8cNd4/lvTSUMkVUfjOfzLFjoml3VvtEistAbWd2dUx0kGdoaxJbKG2TtbBMcj9NJonu7/tUuvhm4SnE9ShVWLEjBReybw7NrPCONZbAwVeE+LKlg7oqrj/RjBsTAemaggFx2+vMKKZwh6EgQfJuhkbt14Wef/0DyE+/CDsRtoHc+hd3JFLL8KUnukZuuMEnRE9Gc/UlC08ueuSoHSHerOVEmpPXh8YSZsl9sWNfLjPYLRRWy4ltdNR9VAOhoOSu1hsndGVWgP4RnlNj558+Ij4uzp+r46ruy9bWrkxXeOZtA9llMNm7L3wdIWapaOhsjaHlGljgha3onix5mf6P9q9NZdahxZJFsgje795VeN5edAAallSra8Nb1uQN+OaNKxCEF7GA8FlujC8gZlalt089cdSm08IH9hgw5dLRpDsg8qKPbUsKmx/NmqSCmwvDEtThNDiaEaK9Xt4k4ZKgZl64qTzItv4wOnY2JQyGGC8gA7iW1qep+MlOLoA6ktJEkC2jrlt7wpoGEB1RiKPH3kPUVrVxzt3bMo98yhfC6JbFMUNjW9T0ZKcfyJPAVomQI4MgHWNhO6r5R4+qpYKdt7M7r9RAzZUQKq151NqOx91fQ+mSkFrs9FSKcBoHVKQC0CKj1uv2pCiaHEy0+slOzcvrDpvveTVcd5Tql+xchTmUNaBLRQ9S/UaF/cqdTKgzKmL1YqUhNKhaNsJ2I1w/scKCKlHCL6+hVJhLSZ/PAPxorKOv4xkwgAAAAASUVORK5CYII="}, "images": {"fallback": {"src": "/static/81905ce6aa3a09e2bedc9081d5502f04/41fb7/imtoken.png", "srcSet": "/static/81905ce6aa3a09e2bedc9081d5502f04/41fb7/imtoken.png 20w,\n/static/81905ce6aa3a09e2bedc9081d5502f04/996dd/imtoken.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/81905ce6aa3a09e2bedc9081d5502f04/4eb25/imtoken.webp 20w,\n/static/81905ce6aa3a09e2bedc9081d5502f04/3a6ff/imtoken.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "itezcom": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAACtElEQVQ4y11US4iNURz/7v2ux1gpMRZspmzMhmiKLHzf5pYpTdiwspE5BysLSnllCgtZyKM8Ekk2YyGEuoUa5FUsJBubcY6imGnEXHP9/o/zneMufv3P+Z//+3EyU7jMlj6zhSda53Ppa8AgcNaUvmVL9xbnN8BDU7qToGuMyGd4zysbQGUMggwoNyFAyp1u4J0hZ38fcv1stHD1oE8GMvKmXo4ERTXyHfgAzIDXBm2b/538BrZqdHmIsG45Mn/UFhQBG/yF+3FgPnBYI/orBtxm0HNd0W8J6YfIBlWpg/sXnAc0FXp7CRoUW6JI6fltOP+xks0EZPo4QhwaOLxThSlgtUZMWKX8aU11WPk9Ruj2JMobYrB0m5KaHYoKXOSDNq1n4ReGbhoqlXR61EpmU+AtI6XLqvANjCVGUq1pus8Tg3eAeTDSC0qYq8bLZAp208iMMaNwLRVoKF3ONSqos2x0EsbH4fAHzs9Ae1RuAfBZjV4kxkepj7umAnOUjlSeuSku7eqwNI0nhJbghfJvZzpnFOH1EKEWnEbhGPh7xanTwfavwyKIvGvA4Ss1OErMp3p5THWj+oXRSOinJNIhnYKGvveCP642LpCHMKQ/wyzRsBvyLIobbJzDR0Y6XIu1dhuTUuyQ3aVUROmUGpxlYnPOJIO9TnefnIVJaOkbNWupboMbU2YbhW6aGCV18r2me1O3Zzb4vK6g+5PozssuF+xxLdDhXS7cBB6GNJIBy9H7adz7w1BLDd2+xJiD7KLwOeQquItTY7DQadArMgH+hEZNm9KkfzE2iT4Nt173Ow8ew4+zk9IWr1XdZiyNSumfmDjAYTO+AoWVWubxg60+ST6vAO7i3k6a0Y1JyF4CFkvXXW7jB+tT5OHbAlbifAD0FgQf4HwPuArswb0vfmMu15+e+/EPy+I1wfpC5UUAAAAASUVORK5CYII="}, "images": {"fallback": {"src": "/static/abdd3625668b763e7a21ef123de59a5d/41fb7/itezcom.png", "srcSet": "/static/abdd3625668b763e7a21ef123de59a5d/41fb7/itezcom.png 20w,\n/static/abdd3625668b763e7a21ef123de59a5d/996dd/itezcom.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/abdd3625668b763e7a21ef123de59a5d/4eb25/itezcom.webp 20w,\n/static/abdd3625668b763e7a21ef123de59a5d/3a6ff/itezcom.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "kraken": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAACF0lEQVQ4y7VUTUsbURTND3JnorWaGCdSRZukicnEZDGIFBWlhRZaEDcu7cqCXbUEXLkRV9IiNBLBrz+Qzps3Dn4kZJRKTDW2ajMfPc+J06hJxZY+DsN9957z7r3z7oyjv4P+NRz1AnEfTXTKAIx7iCHgPTTQRHobRQAGtnDeIY5z7Ol3kYEe+c14dvbd/uzM/tR4dqCXBlyiTaghRiDmpeFWknyrFg4vTNM0DAYsbJPTeYRAqNZfyxxxk5WlI1umaYBhH7GyVIi0kRqZEz7W5NyHA5Cgsdj2sg7Cmnt/EHCRhK9KjEoibdIYr3wvlQ2zwqsu27xynpbKYzEFZKt4x+XrpX4naxVhXTPrLV1jJyWnVb9ThORK7KPBZpJaZN1upIuoTSvrsD/OfwVY2rIB50b6G+zUYgFkq/KK+MkDsr5cRGxiZPdRQ0bNnRmGLnRRoUuGoebO4ZwY2QFhffkI5JvitRTLPPk8G2oR83tnmqYPh7eGQwrS5rPncCIEwurnYg3xZroiDjZ/UXM/kHAotDUUUnBXyAzn5LM9q6+b4mAT+bRwiNirwe3HTpFmSifHF0K3LHTTk+OfNHMKJ0IggAbybzGA6R30y6+fbmOGom5plFdeCErUIwEvBWU0qsAZ80oggMZ7pOsTxtFouxR+KNnX3tfKLhOAEXFXLhYEvl3q526PJ1c1txy1GfE69p++53/6Gfx38S949NAo/nSYLAAAAABJRU5ErkJggg=="}, "images": {"fallback": {"src": "/static/78a615d69d907c4e10b634e2dce49313/7f8fa/kraken.jpg", "srcSet": "/static/78a615d69d907c4e10b634e2dce49313/7f8fa/kraken.jpg 20w,\n/static/78a615d69d907c4e10b634e2dce49313/b1fd8/kraken.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/78a615d69d907c4e10b634e2dce49313/4eb25/kraken.webp 20w,\n/static/78a615d69d907c4e10b634e2dce49313/3a6ff/kraken.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "myetherwallet": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAADV0lEQVQ4y6VVW0tUURQ+NVGUXSzymmVpYdFDGtVD0UMQBEZF/YL+QUQEPQVFT4Z5mTGvzX10RkYtyrQbIfRiImVZmV0oKnVsdCzTGefMWbv1bc85Xh+CBhaz9zlrfftb61t7HSXrplfZ4wgqq0vsSnqFx7KJ9ylWl8L/eRkVnvNsXn7+BMZrH9sFfrdrI/tksi9ill2vVfLq/IhRlHwGU65alVSb28IvFT4gG4G8nmITsAzd0mcszns/A+QgBrHdoZAElcwkmM3NJ3oK2WEszeYWuqnMVk0ucyZgWDMrFeB4z76/OeYUryXG0qIaRVLGKTqY4RhnI9jWqnpxyHeHYNuq60VSiZ02lDuJgeO6rwAoMHhvkXlzmlv4QUR3UDPZaV2pQxQG27ThiUmKqqqIqQkaGJ8Q9ld9lO8M0ppSuwBbPWaccXJRUykAp+CZxQz1Ik5BZFf56G14lMTMD2sKT0bF8WCbASqZckwjhALDHQwS02tG/JBQo82VPlp145Y43fJAvAiFqXMgpKmaZoKORGO019VE6zl9XTCVsXaD3Tm9dig27awLUHKZQ9YKe85AMkY9jzW2io+RXwaocPf2E4vK9fSoemYXAejCBgpmswDvwhHR/vkbnWl5KFKnUwELsZLZKtdsOFBEojEJOvRnQuTWNAjOygD0Q+VHoMxBiQOeFppKJOTpPcNhk+HRwD26/KxLK+p8KS51dNL7kYgEjLEv1EdL6Wl3zADyw4O+21BUAnYNDmupVresz/aaBjp7/6n2+Mt3M138xxMaHZ4PaKQM2qAf4jaBd4KImJm2vLhO9iYEYqP97mZR/LyH2E+McuoLUjZEQWFRYO+bD2ab9I+O0RH/XZHC6qMD0qdLQyv4EIh3srldlgWCmqIYbaPXkfa5mikSi5GRGmraPfRTtH76itQhnsiq9IIVrS11mAeZbZNidZuNjSZFs55oapd9NrteMGt3r+AskI1kxv/yMsxv7DlXD9cJoAXOJnK87pPXDVeP6y/6WN28Wr9sfoPVgqu32HAAU6SP5sZAMIZDTnWDyJyuGRnMFgwHY3ylzRtfGdLRO2d8QU1DAIPZ7PG1BOOrwMkD9kr5fw/Yzh+DPGADisKKyU9A0r99AuoX+wRY5CcgADLKXx02paM9Z7IiAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/dd1300798d3d2ce87e6b65153f278cc8/41fb7/myetherwallet.png", "srcSet": "/static/dd1300798d3d2ce87e6b65153f278cc8/41fb7/myetherwallet.png 20w,\n/static/dd1300798d3d2ce87e6b65153f278cc8/996dd/myetherwallet.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/dd1300798d3d2ce87e6b65153f278cc8/4eb25/myetherwallet.webp 20w,\n/static/dd1300798d3d2ce87e6b65153f278cc8/3a6ff/myetherwallet.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "mycrypto": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAACsElEQVQ4y21UPWsUURSd3WReJNj4GxSroHZ2/gAVCyO4aGDBRkRQBIsZKwsbUciC+F4hVjZJGoOVllY2dtoEgmFTbRQbC0l2Zieecz9mtrA4zLz73jv33nPvu1lepswRytjPi5gBARgAG8AeUBnGwFZextvAibzAnSL18Z/JPXA4WW/hiRByYxXYAY5Dmf4LEBC7wACEylHEHh3IYpFk6uE5jH7xCA6OcLAmOe3yD5vuyZrE63Y3E0Js9CUykpEIqcHWkWg0NSKZ5U7C6IvY4N5UHaUROSiZsAKrEhl1KlMjJEV8BttNEO17mtj/CfsN4C7OTYNkwyzE0S3VUAuwY2laZPGBaiOansF3gvUf/J/TAMR+BfZZq6sWbzmzatJYGdkjE1kQlOACvhfdRkdmv4ZvjShndnfIzY2gOtDwwtqHhy8D97G/qERSTfynx7Ct5W0G6aEXEue2adgLndjXrQ3umQNolzbpYEntm12h0tOgjgZzhBMapsG0gOEb8EqqXZoMYk+fQPIxN5tkJIVK74FxUMmYdsPUKiNrBQ4aLat3GJy8i4K9eejnVK42oGMSjm3RaOOmqaV/FYdP49K+RUbbL2AFuGQktTV+Y/sHJNyyBaqc2KxM8S9wyip6Ft+vwHfgvL3ZJXx/e2Yktf8PmTRk1zYkrE2jd8FaBPYFQKod5O3H10ZQaxBty93hYUyNuGuRMeUm0KtW8m3w3iu8ndJInx6fospk5JxEJz2CQd69lMZIrVjpDaOyAfDSWsQd82xlRRzS8fwMXDfxayuORqqHvwCfrbIzee8aWWW26GPQh4O/jtHcHGSR2If13HulZpUTmbMYuvfd85R7reBapB9KbGA0ppVqHV2zYejevXBkc9NDZqMOhLismqRtYKIjTdI8YGuwmiyADAmk6WRc/wOwXqbQXDRGLAAAAABJRU5ErkJggg=="}, "images": {"fallback": {"src": "/static/8c45056b7cdd009af22c30dabc573825/41fb7/mycrypto.png", "srcSet": "/static/8c45056b7cdd009af22c30dabc573825/41fb7/mycrypto.png 20w,\n/static/8c45056b7cdd009af22c30dabc573825/996dd/mycrypto.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/8c45056b7cdd009af22c30dabc573825/4eb25/mycrypto.webp 20w,\n/static/8c45056b7cdd009af22c30dabc573825/3a6ff/mycrypto.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "rain": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAIAAAAC64paAAAACXBIWXMAAAsTAAALEwEAmpwYAAABs0lEQVQ4y7VU207CQBDtJ5iYEBOJCrUXeqUFeu8C3RYjCYjvVQP+/z942i1IoUV9MJmHncvpnp6ZWe5xoJ+bqBgX3INx5yF+oN9Lak/WhIEOwwEu/xtwX9Yk1ZhOXNu0gYHhABdBpH4A44ZVFM8dL/PCZVDYwgvmjosg33YzY9gVFNOwZo53w8tdUcmTJE8oDnARRAoFrLIGBiV82DLtD5quo1jVho41jscODAe464i8pxQFfFn8DYYvq0bJ1n+JyJMfUNffZqlhWLBtlsFFECkUrMJYUkz2Cxw43Ikqmbgg1uElRRtSN7juSwAcjLmKanZK/tAPEIHdjGZAUghzKygTa/y5yFLPv3oQNzGB4QB3t8iQQgGEtM1Rr2TOMbXQkucgymkCChAmcf1NPN2mKeyVELgIIgX9ln6IYuHwz3twmFPaAI7r4KAOZrTBh9HetdIendJmgk0rweRSMP9YsPRMMHIsWNWqMJ5VrQrLVmVFq3Rrd9KqKEIx3zIkdB2S/ZC4sGpIQoJUw5C0jecbxpMmbDznF8azaTECthhZsRjepcWoraRSrKTVsJL/+hj86Rn6Au9S4UlIgzk/AAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/735a4cd478eac48f534f3663fc219373/7f8fa/rain.jpg", "srcSet": "/static/735a4cd478eac48f534f3663fc219373/7f8fa/rain.jpg 20w,\n/static/735a4cd478eac48f534f3663fc219373/b1fd8/rain.jpg 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/735a4cd478eac48f534f3663fc219373/4eb25/rain.webp 20w,\n/static/735a4cd478eac48f534f3663fc219373/3a6ff/rain.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "squarelink": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAYEkAAGBJAE9MEcoAAACf0lEQVQ4y41UPWsVQRTd+JTUaaIoQSxSBDEfVrNgn9I/oEKYxc4/kMYI6i8wRWLjE7QxFv4BwSSECKJGC4ngbCOIMVU0vvXl7T7PvXN2ZpM80OJwd+/MPXPvmXsnSbO8MlneFwt0jXVd2AP+i78UfxPGKv7oPpt/N5lbxPdIal2SpJFMbN+TO/3GhrL2h3Xr+kd9IBV8wNpIYkKQ+wp7H1gAHjJLCfgE3AHuDcBd4GMkd4uSYc/oyfkavhPiPNBhxk8b/gTlJcjGQ//zMdgdku4oIUt8jWyHAfFdBgoEC+Fzkp0iEu6JpNa9JGFPTuzxZxMbWjx1CocU9K+QcAg+2X8F33PANewXXIfW24FQS/ZCb8K2eOoUNhUqNgklK+DGsQuJ0Iv9N6F1K7FM98R4eQpqvA/tf3vZVO/qvzNMvW6PmI0QXgXOAeMgemNiyU4JASE8qaVl+TTICt93QUPJst0gvEi9ZW01amglQy1tI7SHdRNaUp2h9e2C4DabWwhnjNVDWrDrrLInDtbv3sJeQMajsLPGZyHN+qxuD6DNajoqC28fsWvGxluuKLQQ/wJ+ImCfZUnJL1Ibeq/NTDqImWSTHyb0U+Kqeia50JzdW6GRQ8l6w5OxP2XKXK2hn2UEfYHNQDrHxr2JtVnqNHTkloXwEglPIGadsmnJZapluNXQbzXiyA2n/vYf+73ae9ONMXxFfymOXWr4AxjzhPExaD4EwBIzFI3PkvAMDvpG/65sWk7DG+e2YG8PwAIC54F3bCV5iB/AzgPv47voluUEaZPPhy/i+Kw2fGXjUW0+usIxWmt1Wlsiy/eAAz7v3QFQvSFRxX95hPdSmSDrhCP5CymSTRmxOqvrAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/862023a2dbf01764d00b09600152e9af/41fb7/squarelink.png", "srcSet": "/static/862023a2dbf01764d00b09600152e9af/41fb7/squarelink.png 20w,\n/static/862023a2dbf01764d00b09600152e9af/996dd/squarelink.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/862023a2dbf01764d00b09600152e9af/4eb25/squarelink.webp 20w,\n/static/862023a2dbf01764d00b09600152e9af/3a6ff/squarelink.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}, "trust": {"childImageSharp": {"gatsbyImageData": {"layout": "fixed", "placeholder": {"fallback": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAACxLAAAsSwGlPZapAAADeElEQVQ4y2VUSUwUURAdMOHsXTwoxn0bGvWCV6MeXWI8mRiXILgM04MEPbmcUEQNRI2JGk+KJsaRZbonEIyicYkYBRVQUQSDGBAUM0z3/LJe/d8wiZ28dHX9X69rDxXG3FChzYi5OeFoMmRBtp18RhWjnTHMZxmGx/I3RoJRyt+zC2NOyIo5OcIhcEKazHZzoeBDyJWMcSvmEsDGVFThCiDP6N1Bxi6x0WQ5AaH5QzKP5XhgwEgXxVx/7dGkWna4RcByhnWepRH8sM6yHfFOCMPlycCzuPFgykKILK+IJNS8kka1/UyH2lbdITJ05p5v6TQQO1RrSYRubsjksDIgW1WeUCsjCcJ786mH6rLzkYLnUqKPNrEu+w6IhdR2tjAk5HxWjiNHSw+1ZMqvd1LP0IQaGv1LaS9DY3+m6MTtLjrZ0EW/JtM0xbpBPsOdyLVXtORQsw9bjvIdE+aholXIxZoKN72gtIlutverVNqn6nvvqeTyS+K80cKyJgFk6HAG4httnxVs2NY3+dwKD9sNoV9woFFdTX5Sr/vHaO6+OP5OYQ6HzwSQF7MOZ2++/KIr7kcFGz7zDOE1eDiMD1RzPh9ebOqlLyOTUkFpl5mqixzoBn5O0vkHPQQbtg08fI6iZLjs04Sn73bTyHhKDMNZfRcgHNUe/5xI0ak73VSgCbk4KIybQsgePviS4jwp+0YnTaY8Kj7Wiir+Rwjd+uOtcgcFhA1sNaEzgZC/mfAyaN6dNU8IReHeQx9OT0gwMcuPtNCOs/oO3rApminKI3iYMH/3OEQcKrQK8jNnb5zWVSanRw8ydMjzKN9hGwUb3eBMGHPqQciDLrnykBuEUNfcK428/9ILQlugieEt5JIrL+XsQmNPVrimuWPOBswftsYgbxkKDlZHHdXQMSCGbW+HaePJh5gQau/6Ibpbj7/KHTNdnqW9e7om2hrSQ207u8Rl20E/ycXFB5vVnvrnqu/7b/L8jKB36DftrnumcGbIMsYzoFiWA/aZJfPMW0MnFgn2ET4MkfRz8Q+q5v4HtZRl6EyYniYVsgj2KGNWSAtmp9lOreknMqQw8heVNatFTDStM6kxrRIxCzZHloOQ2exlFNtXFuUWxvtguZqxFGTr+P2EUWzZM2TZhALss0K9LPMw6JhNjBMmgDGBPkNqGBuK7KS24TAteWv8Az93pfIiivGAAAAAAElFTkSuQmCC"}, "images": {"fallback": {"src": "/static/463b977caa0282f5c1ff85daa2100852/41fb7/trust.png", "srcSet": "/static/463b977caa0282f5c1ff85daa2100852/41fb7/trust.png 20w,\n/static/463b977caa0282f5c1ff85daa2100852/996dd/trust.png 40w", "sizes": "20px"}, "sources": [{"srcSet": "/static/463b977caa0282f5c1ff85daa2100852/4eb25/trust.webp 20w,\n/static/463b977caa0282f5c1ff85daa2100852/3a6ff/trust.webp 40w", "type": "image/webp", "sizes": "20px"}]}, "width": 20, "height": 20}}}}}