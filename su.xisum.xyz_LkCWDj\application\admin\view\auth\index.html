{include file="public:header"}
<style>
    .lyear-wrapper {
        position: relative;
    }
    .lyear-login {
        display: flex !important;
        min-height: 100vh;
        align-items: center !important;
        justify-content: center !important;
    }
    .login-center {
        background: #fff;
        min-width: 38.25rem;
        padding: 2.14286em 3.57143em;
        border-radius: 5px;
        margin: 2.85714em 0;
    }
    .login-header {
        margin-bottom: 1.5rem !important;
    }
    .login-center .has-feedback.feedback-left .form-control {
        padding-left: 38px;
        padding-right: 12px;
    }
    .login-center .has-feedback.feedback-left .form-control-feedback {
        left: 0;
        right: auto;
        width: 38px;
        height: 38px;
        line-height: 38px;
        z-index: 4;
        color: #dcdcdc;
    }
    .login-center .has-feedback.feedback-left.row .form-control-feedback {
        left: 15px;
    }
</style>

<html>
	<head>
		<meta charset="utf-8">
		<title>U2E</title>
		<style>
			body{
				background-size: 100% ;
			}
		</style>
	</head>
	<body background="/static/img/R.jpg" >
	</body>
</html>



    <div class="row lyear-wrapper">
        <div class="lyear-login">
            <div class="login-center">
                <div class="login-header text-center" style="font-size: 32px">
                    HILLTOP<br/>
                </div>
                <form action="{:url('Auth/login')}" method="post">
                    <div class="form-group has-feedback feedback-left">
                        <input type="text" placeholder="请输入您的用户名" class="form-control" name="username" id="username" />
                        <span class="mdi mdi-account form-control-feedback" aria-hidden="true"></span>
                    </div>
                    <div class="form-group has-feedback feedback-left">
                        <input type="password" placeholder="请输入密码" class="form-control" id="password" name="password" />
                        <span class="mdi mdi-lock form-control-feedback" aria-hidden="true"></span>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-block btn-primary" type="submit" >立即登录</button>
                    </div>
                </form>

                <!-- Web3钱包登录分隔线 -->
                <div class="text-center my-3">
                    <span class="text-muted">或</span>
                </div>

                <!-- Web3钱包登录按钮 -->
                <div class="form-group">
                    <button class="btn btn-block btn-warning" type="button" id="connectWalletBtn" onclick="connectWallet()">
                        <i class="mdi mdi-wallet"></i> 连接钱包登录
                    </button>
                </div>

                <!-- Web3登录状态显示 -->
                <div id="web3Status" class="alert" style="display: none;"></div>

                <hr>
                <footer class="col-sm-12 text-center">

                </footer>
            </div>
        </div>
    </div>

<!-- 引入ethers.js库 -->
<script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>

<!-- Web3钱包连接JavaScript -->
<script>
let web3Provider = null;
let userAccount = null;

// 检查是否安装了MetaMask
function checkMetaMask() {
    if (typeof window.ethereum !== 'undefined') {
        return true;
    }
    return false;
}

// 显示状态消息
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('web3Status');
    statusDiv.className = `alert alert-${type}`;
    statusDiv.innerHTML = message;
    statusDiv.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// 连接钱包
async function connectWallet() {
    if (!checkMetaMask()) {
        showStatus('请安装MetaMask钱包插件', 'danger');
        return;
    }

    try {
        showStatus('正在连接钱包...', 'info');

        // 请求连接钱包
        const accounts = await window.ethereum.request({
            method: 'eth_requestAccounts'
        });

        if (accounts.length === 0) {
            showStatus('未选择钱包账户', 'warning');
            return;
        }

        userAccount = accounts[0];
        web3Provider = new ethers.providers.Web3Provider(window.ethereum);

        showStatus(`钱包已连接: ${userAccount.substring(0, 6)}...${userAccount.substring(38)}`, 'success');

        // 开始Web3登录流程
        await startWeb3Login();

    } catch (error) {
        console.error('连接钱包失败:', error);
        showStatus('连接钱包失败: ' + error.message, 'danger');
    }
}

// 开始Web3登录流程
async function startWeb3Login() {
    try {
        showStatus('正在获取签名信息...', 'info');

        // 获取nonce
        const nonceResponse = await fetch('{:url("Web3Auth/getNonce")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                wallet_address: userAccount
            })
        });

        const nonceData = await nonceResponse.json();

        if (nonceData.code !== 200) {
            showStatus('获取签名信息失败: ' + nonceData.msg, 'danger');
            return;
        }

        showStatus('请在钱包中确认签名...', 'warning');

        // 请求用户签名
        const signer = web3Provider.getSigner();
        const signature = await signer.signMessage(nonceData.data.message);

        showStatus('正在验证签名...', 'info');

        // 验证签名并登录
        const verifyResponse = await fetch('{:url("Web3Auth/verify")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                wallet_address: userAccount,
                signature: signature,
                nonce: nonceData.data.nonce
            })
        });

        const verifyData = await verifyResponse.json();

        if (verifyData.code === 200) {
            showStatus('登录成功，正在跳转...', 'success');
            setTimeout(() => {
                window.location.href = verifyData.data.redirect_url;
            }, 1000);
        } else {
            showStatus('登录失败: ' + verifyData.msg, 'danger');
        }

    } catch (error) {
        console.error('Web3登录失败:', error);
        if (error.code === 4001) {
            showStatus('用户取消了签名', 'warning');
        } else {
            showStatus('登录失败: ' + error.message, 'danger');
        }
    }
}

// 监听账户变化
if (typeof window.ethereum !== 'undefined') {
    window.ethereum.on('accountsChanged', function (accounts) {
        if (accounts.length === 0) {
            userAccount = null;
            web3Provider = null;
            showStatus('钱包已断开连接', 'warning');
        } else {
            userAccount = accounts[0];
            showStatus('钱包账户已切换', 'info');
        }
    });

    window.ethereum.on('chainChanged', function (chainId) {
        // 网络切换时重新加载页面
        window.location.reload();
    });
}
</script>

{include file="public:footer"}