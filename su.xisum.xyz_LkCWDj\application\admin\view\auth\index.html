{include file="public:header"}
<style>
    .lyear-wrapper {
        position: relative;
    }
    .lyear-login {
        display: flex !important;
        min-height: 100vh;
        align-items: center !important;
        justify-content: center !important;
    }
    .login-center {
        background: #fff;
        min-width: 38.25rem;
        padding: 2.14286em 3.57143em;
        border-radius: 5px;
        margin: 2.85714em 0;
    }
    .login-header {
        margin-bottom: 1.5rem !important;
    }
    .login-center .has-feedback.feedback-left .form-control {
        padding-left: 38px;
        padding-right: 12px;
    }
    .login-center .has-feedback.feedback-left .form-control-feedback {
        left: 0;
        right: auto;
        width: 38px;
        height: 38px;
        line-height: 38px;
        z-index: 4;
        color: #dcdcdc;
    }
    .login-center .has-feedback.feedback-left.row .form-control-feedback {
        left: 15px;
    }
</style>

<html>
	<head>
		<meta charset="utf-8">
		<title>U2E</title>
		<style>
			body{
				background-size: 100% ;
			}
		</style>
	</head>
	<body background="/static/img/R.jpg" >
	</body>
</html>



    <div class="row lyear-wrapper">
        <div class="lyear-login">
            <div class="login-center">
                <div class="login-header text-center" style="font-size: 32px">
                    HILLTOP<br/>
                </div>
                <form action="{:url('Auth/login')}" method="post">
                    <div class="form-group has-feedback feedback-left">
                        <input type="text" placeholder="请输入您的用户名" class="form-control" name="username" id="username" />
                        <span class="mdi mdi-account form-control-feedback" aria-hidden="true"></span>
                    </div>
                    <div class="form-group has-feedback feedback-left">
                        <input type="password" placeholder="请输入密码" class="form-control" id="password" name="password" />
                        <span class="mdi mdi-lock form-control-feedback" aria-hidden="true"></span>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-block btn-primary" type="submit" >立即登录</button>
                    </div>
                </form>

                <!-- Web3钱包登录分隔线 -->
                <div class="text-center my-3">
                    <span class="text-muted">或</span>
                </div>

                <!-- 网络选择 -->
                <div class="form-group">
                    <label for="chainSelect">选择网络:</label>
                    <select class="form-control" id="chainSelect">
                        <option value="ethereum">Ethereum Mainnet</option>
                        <option value="bsc">Binance Smart Chain</option>
                        <option value="polygon">Polygon Mainnet</option>
                        <option value="arbitrum">Arbitrum One</option>
                        <option value="optimism">Optimism</option>
                    </select>
                </div>

                <!-- Web3钱包登录按钮 -->
                <div class="form-group">
                    <button class="btn btn-block btn-warning" type="button" id="connectWalletBtn" onclick="connectWallet()">
                        <i class="mdi mdi-wallet"></i> 连接钱包登录
                    </button>
                </div>

                <!-- Web3登录状态显示 -->
                <div id="web3Status" class="alert" style="display: none;"></div>

                <!-- 当前网络显示 -->
                <div id="currentNetwork" class="text-center text-muted" style="display: none;">
                    <small>当前网络: <span id="networkName"></span></small>
                </div>

                <hr>
                <footer class="col-sm-12 text-center">

                </footer>
            </div>
        </div>
    </div>

<!-- 引入ethers.js库 -->
<script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>

<!-- 引入多链Web3库 -->
<script src="{:url('/')}/static/admin/js/multi-chain-web3.js"></script>

<!-- Web3钱包连接JavaScript -->
<script>
let web3Instance = null;
let selectedChain = 'ethereum';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    web3Instance = window.multiChainWeb3;

    // 设置事件监听
    web3Instance.onAccountChanged = handleAccountChanged;
    web3Instance.onChainChanged = handleChainChanged;
    web3Instance.onAccountDisconnected = handleAccountDisconnected;

    // 监听网络选择变化
    document.getElementById('chainSelect').addEventListener('change', function(e) {
        selectedChain = e.target.value;
    });
});

// 检查是否安装了Web3钱包
function checkWallet() {
    return web3Instance.isWalletInstalled();
}

// 显示状态消息
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('web3Status');
    statusDiv.className = `alert alert-${type}`;
    statusDiv.innerHTML = message;
    statusDiv.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// 连接钱包
async function connectWallet() {
    if (!checkWallet()) {
        showStatus('请安装MetaMask或其他Web3钱包插件', 'danger');
        return;
    }

    try {
        showStatus('正在连接钱包...', 'info');

        // 连接钱包
        const connectionResult = await web3Instance.connectWallet();

        showStatus(`钱包已连接: ${web3Instance.formatAddress(connectionResult.account)}`, 'success');

        // 显示当前网络
        updateNetworkDisplay(connectionResult.chainInfo);

        // 检查网络是否支持
        if (!connectionResult.chainInfo || !connectionResult.chainInfo.enabled) {
            showStatus('当前网络不受支持，请切换到支持的网络', 'warning');

            // 尝试切换到选择的网络
            try {
                await web3Instance.switchToChain(selectedChain);
                showStatus('网络切换成功', 'success');
            } catch (switchError) {
                showStatus('网络切换失败: ' + switchError.message, 'danger');
                return;
            }
        }

        // 开始Web3登录流程
        await startWeb3Login();

    } catch (error) {
        console.error('连接钱包失败:', error);
        showStatus('连接钱包失败: ' + error.message, 'danger');
    }
}

// 开始Web3登录流程
async function startWeb3Login() {
    try {
        showStatus('正在获取签名信息...', 'info');

        const currentAccount = web3Instance.getCurrentAccount();
        const currentChainId = web3Instance.getCurrentChainId();
        const chainInfo = web3Instance.getChainInfo(currentChainId);

        if (!chainInfo) {
            showStatus('不支持的网络', 'danger');
            return;
        }

        // 获取nonce
        const nonceResponse = await fetch('{:url("Web3Auth/getNonce")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                wallet_address: currentAccount,
                chain_key: chainInfo.key
            })
        });

        const nonceData = await nonceResponse.json();

        if (nonceData.code !== 200) {
            showStatus('获取签名信息失败: ' + nonceData.msg, 'danger');
            return;
        }

        showStatus('请在钱包中确认签名...', 'warning');

        // 请求用户签名
        const signature = await web3Instance.signMessage(nonceData.data.message);

        showStatus('正在验证签名...', 'info');

        // 验证签名并登录
        const verifyResponse = await fetch('{:url("Web3Auth/verify")}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                wallet_address: currentAccount,
                signature: signature,
                nonce: nonceData.data.nonce,
                chain_key: chainInfo.key
            })
        });

        const verifyData = await verifyResponse.json();

        if (verifyData.code === 200) {
            showStatus('登录成功，正在跳转...', 'success');
            setTimeout(() => {
                window.location.href = verifyData.data.redirect_url;
            }, 1000);
        } else {
            showStatus('登录失败: ' + verifyData.msg, 'danger');
        }

    } catch (error) {
        console.error('Web3登录失败:', error);
        if (error.code === 4001) {
            showStatus('用户取消了签名', 'warning');
        } else {
            showStatus('登录失败: ' + error.message, 'danger');
        }
    }
}

// 更新网络显示
function updateNetworkDisplay(chainInfo) {
    const networkDiv = document.getElementById('currentNetwork');
    const networkName = document.getElementById('networkName');

    if (chainInfo) {
        networkName.textContent = chainInfo.name;
        networkDiv.style.display = 'block';
    } else {
        networkDiv.style.display = 'none';
    }
}

// 事件处理函数
function handleAccountChanged(account) {
    showStatus(`账户已切换: ${web3Instance.formatAddress(account)}`, 'info');
}

function handleChainChanged(chainId) {
    const chainInfo = web3Instance.getChainInfo(chainId);
    updateNetworkDisplay(chainInfo);

    if (chainInfo && chainInfo.enabled) {
        showStatus(`网络已切换到: ${chainInfo.name}`, 'success');
    } else {
        showStatus('当前网络不受支持', 'warning');
    }
}

function handleAccountDisconnected() {
    showStatus('钱包已断开连接', 'warning');
    document.getElementById('currentNetwork').style.display = 'none';
}

// 切换网络按钮事件
async function switchNetwork() {
    try {
        await web3Instance.switchToChain(selectedChain);
        showStatus('网络切换成功', 'success');
    } catch (error) {
        showStatus('网络切换失败: ' + error.message, 'danger');
    }
}
</script>

{include file="public:footer"}