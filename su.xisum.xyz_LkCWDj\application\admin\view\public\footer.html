


<script type="text/javascript" src="__LY__/js/jquery.min.js"></script>
<script type="text/javascript" src="__LY__/js/bootstrap.min.js"></script>
<script type="text/javascript" src="__LY__/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="__LY__/js/main.min.js"></script>
<script src="__VENDOR__/sweetalert/js/sweetalert.min.js"></script>
<script src="__ADMIN__/js/stisla.js"></script>
<script type="text/javascript">
    $(function() {
        var controller = '{:request()->controller()}';
        var action = '{:request()->action()}';
        if (action.toLowerCase()  !== 'index'){
            controller = controller + '-' + action;
        }
        controller = controller.toLowerCase();
        console.log('active','.' + controller + '-li');
        $('.' + controller + '-li').addClass('active');
    })
</script>
</body>
</html>



