<?php

namespace app\admin\controller;

use app\common\service\Web3MonitorService;
use app\common\service\MultiChainService;
use think\Request;
use think\Db;

/**
 * Web3监控API控制器
 * 提供实时监控数据接口
 */
class Web3MonitorApi extends Base
{
    public function __construct()
    {
        parent::__construct();
        $this->checkAdmin();
    }

    /**
     * 获取实时监控数据
     */
    public function realTimeData()
    {
        $timeRange = input('time_range', 300); // 默认5分钟

        $data = [
            'timestamp' => time(),
            'login_stats' => $this->getRealtimeLoginStats($timeRange),
            'chain_stats' => $this->getRealtimeChainStats($timeRange),
            'alert_stats' => $this->getRealtimeAlertStats(),
            'system_stats' => $this->getRealtimeSystemStats()
        ];

        return json(['code' => 200, 'data' => $data]);
    }

    /**
     * 获取告警统计
     */
    public function alertStats()
    {
        $stats = Web3MonitorService::getMonitoringStats();
        
        return json([
            'code' => 200,
            'data' => [
                'total_alerts' => $stats['total_alerts'],
                'critical_count' => $stats['critical_count'],
                'error_count' => $stats['error_count'],
                'warning_count' => $stats['warning_count'],
                'info_count' => $stats['info_count'],
                'last_check_time' => $stats['last_check_time'],
                'recent_alerts' => array_slice($stats['alerts'], 0, 5)
            ]
        ]);
    }

    /**
     * 获取登录趋势数据
     */
    public function loginTrend()
    {
        $hours = input('hours', 24);
        $interval = input('interval', 3600); // 默认1小时间隔

        $startTime = time() - ($hours * 3600);
        $trendData = [];

        for ($i = 0; $i < $hours; $i++) {
            $periodStart = $startTime + ($i * $interval);
            $periodEnd = $periodStart + $interval;

            $successCount = Db::table('web3_login_logs')
                ->where('create_time', '>=', $periodStart)
                ->where('create_time', '<', $periodEnd)
                ->where('login_status', 1)
                ->count();

            $failedCount = Db::table('web3_login_logs')
                ->where('create_time', '>=', $periodStart)
                ->where('create_time', '<', $periodEnd)
                ->where('login_status', 0)
                ->count();

            $trendData[] = [
                'timestamp' => $periodStart,
                'time_label' => date('H:i', $periodStart),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'total_count' => $successCount + $failedCount
            ];
        }

        return json(['code' => 200, 'data' => $trendData]);
    }

    /**
     * 获取链使用分布
     */
    public function chainDistribution()
    {
        $timeRange = input('time_range', 86400); // 默认24小时
        $startTime = time() - $timeRange;

        $chainStats = Db::table('web3_login_logs l')
            ->join('wallet_nonces n', 'l.nonce = n.nonce', 'left')
            ->where('l.create_time', '>=', $startTime)
            ->where('l.login_status', 1)
            ->group('n.chain_key')
            ->field('n.chain_key, count(*) as login_count')
            ->select();

        // 添加链的显示名称
        foreach ($chainStats as &$stat) {
            $stat['chain_name'] = MultiChainService::getChainDisplayName($stat['chain_key']);
        }

        return json(['code' => 200, 'data' => $chainStats]);
    }

    /**
     * 获取安全事件
     */
    public function securityEvents()
    {
        $timeRange = input('time_range', 3600); // 默认1小时
        $startTime = time() - $timeRange;

        // 获取可疑IP
        $suspiciousIPs = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 0)
            ->group('ip_address')
            ->having('count(*) >= ?', [5])
            ->field('ip_address, count(*) as attempt_count, max(create_time) as last_attempt')
            ->order('attempt_count', 'desc')
            ->limit(10)
            ->select();

        // 获取频繁登录的钱包
        $frequentWallets = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 1)
            ->group('wallet_address')
            ->having('count(*) >= ?', [10])
            ->field('wallet_address, count(*) as login_count, max(create_time) as last_login')
            ->order('login_count', 'desc')
            ->limit(10)
            ->select();

        return json([
            'code' => 200,
            'data' => [
                'suspicious_ips' => $suspiciousIPs,
                'frequent_wallets' => $frequentWallets
            ]
        ]);
    }

    /**
     * 获取系统健康状态
     */
    public function systemHealth()
    {
        $health = [
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'storage' => $this->checkStorageHealth(),
            'performance' => $this->checkPerformanceHealth()
        ];

        $overallStatus = 'healthy';
        foreach ($health as $component) {
            if ($component['status'] === 'error') {
                $overallStatus = 'error';
                break;
            } elseif ($component['status'] === 'warning') {
                $overallStatus = 'warning';
            }
        }

        return json([
            'code' => 200,
            'data' => [
                'overall_status' => $overallStatus,
                'components' => $health,
                'check_time' => time()
            ]
        ]);
    }

    /**
     * 触发监控检查
     */
    public function triggerCheck(Request $request)
    {
        if (!$request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $timeRange = $request->post('time_range', 3600);

        try {
            $alerts = Web3MonitorService::runFullMonitoring($timeRange);

            return json([
                'code' => 200,
                'msg' => '监控检查完成',
                'data' => [
                    'alert_count' => count($alerts),
                    'alerts' => $alerts,
                    'check_time' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '监控检查失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取监控配置
     */
    public function getConfig()
    {
        $config = config('web3_monitor');
        
        // 隐藏敏感信息
        if (isset($config['notifications']['email']['smtp_password'])) {
            $config['notifications']['email']['smtp_password'] = '***';
        }
        if (isset($config['notifications']['sms']['access_secret'])) {
            $config['notifications']['sms']['access_secret'] = '***';
        }

        return json(['code' => 200, 'data' => $config]);
    }

    /**
     * 更新监控配置
     */
    public function updateConfig(Request $request)
    {
        if (!$request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        $config = $request->post('config');
        
        if (empty($config)) {
            return json(['code' => 400, 'msg' => '配置数据不能为空']);
        }

        try {
            // 这里应该验证配置的有效性
            // 然后保存到配置文件或数据库
            
            return json(['code' => 200, 'msg' => '配置更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '配置更新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取实时登录统计
     * @param int $timeRange
     * @return array
     */
    private function getRealtimeLoginStats($timeRange)
    {
        $startTime = time() - $timeRange;

        $totalLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->count();

        $successLogins = Db::table('web3_login_logs')
            ->where('create_time', '>=', $startTime)
            ->where('login_status', 1)
            ->count();

        $failedLogins = $totalLogins - $successLogins;
        $successRate = $totalLogins > 0 ? ($successLogins / $totalLogins) * 100 : 0;

        return [
            'total' => $totalLogins,
            'success' => $successLogins,
            'failed' => $failedLogins,
            'success_rate' => round($successRate, 2),
            'time_range' => $timeRange
        ];
    }

    /**
     * 获取实时链统计
     * @param int $timeRange
     * @return array
     */
    private function getRealtimeChainStats($timeRange)
    {
        $startTime = time() - $timeRange;

        $chainStats = Db::table('web3_login_logs l')
            ->join('wallet_nonces n', 'l.nonce = n.nonce', 'left')
            ->where('l.create_time', '>=', $startTime)
            ->where('l.login_status', 1)
            ->group('n.chain_key')
            ->field('n.chain_key, count(*) as login_count')
            ->select();

        $total = array_sum(array_column($chainStats, 'login_count'));

        foreach ($chainStats as &$stat) {
            $stat['chain_name'] = MultiChainService::getChainDisplayName($stat['chain_key']);
            $stat['percentage'] = $total > 0 ? round(($stat['login_count'] / $total) * 100, 2) : 0;
        }

        return $chainStats;
    }

    /**
     * 获取实时告警统计
     * @return array
     */
    private function getRealtimeAlertStats()
    {
        $stats = Web3MonitorService::getMonitoringStats();
        
        return [
            'total' => $stats['total_alerts'],
            'critical' => $stats['critical_count'],
            'error' => $stats['error_count'],
            'warning' => $stats['warning_count'],
            'info' => $stats['info_count']
        ];
    }

    /**
     * 获取实时系统统计
     * @return array
     */
    private function getRealtimeSystemStats()
    {
        $expiredNonces = Db::table('wallet_nonces')
            ->where('expire_time', '<', time())
            ->count();

        $activeNonces = Db::table('wallet_nonces')
            ->where('expire_time', '>=', time())
            ->where('is_used', 0)
            ->count();

        return [
            'expired_nonces' => $expiredNonces,
            'active_nonces' => $activeNonces,
            'memory_usage' => $this->formatBytes(memory_get_usage(true)),
            'uptime' => $this->getSystemUptime()
        ];
    }

    /**
     * 检查数据库健康状态
     * @return array
     */
    private function checkDatabaseHealth()
    {
        try {
            $start = microtime(true);
            Db::query('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => $responseTime < 100 ? 'healthy' : 'warning',
                'response_time' => round($responseTime, 2)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查缓存健康状态
     * @return array
     */
    private function checkCacheHealth()
    {
        try {
            $testKey = 'health_check_' . time();
            cache($testKey, 'test', 60);
            $result = cache($testKey);
            
            return [
                'status' => $result === 'test' ? 'healthy' : 'error'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查存储健康状态
     * @return array
     */
    private function checkStorageHealth()
    {
        $logCount = Db::table('web3_login_logs')->count();
        $nonceCount = Db::table('wallet_nonces')->count();

        return [
            'status' => 'healthy',
            'log_count' => $logCount,
            'nonce_count' => $nonceCount
        ];
    }

    /**
     * 检查性能健康状态
     * @return array
     */
    private function checkPerformanceHealth()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryUsagePercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;

        return [
            'status' => $memoryUsagePercent < 80 ? 'healthy' : 'warning',
            'memory_usage' => $this->formatBytes($memoryUsage),
            'memory_usage_percent' => round($memoryUsagePercent, 2)
        ];
    }

    /**
     * 格式化字节数
     * @param int $bytes
     * @return string
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 解析内存限制
     * @param string $memoryLimit
     * @return int
     */
    private function parseMemoryLimit($memoryLimit)
    {
        if ($memoryLimit === '-1') {
            return 0; // 无限制
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int)$memoryLimit;
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    /**
     * 获取系统运行时间
     * @return string
     */
    private function getSystemUptime()
    {
        // 这里可以实现获取系统运行时间的逻辑
        return 'N/A';
    }
}
