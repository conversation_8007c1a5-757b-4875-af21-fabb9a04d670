/**
 * 多链Web3钱包连接JavaScript库
 * 支持以太坊、BSC、Polygon等多个区块链网络
 */

class MultiChainWeb3 {
    constructor() {
        this.provider = null;
        this.signer = null;
        this.currentAccount = null;
        this.currentChainId = null;
        this.supportedChains = {};
        this.init();
    }

    // 支持的区块链网络配置
    static SUPPORTED_CHAINS = {
        ethereum: {
            chainId: '0x1',
            chainIdDecimal: 1,
            name: 'Ethereum Mainnet',
            symbol: 'ETH',
            decimals: 18,
            rpcUrls: ['https://mainnet.infura.io/v3/YOUR_INFURA_KEY'],
            blockExplorerUrls: ['https://etherscan.io'],
            enabled: true
        },
        bsc: {
            chainId: '0x38',
            chainIdDecimal: 56,
            name: 'Binance Smart Chain',
            symbol: 'BNB',
            decimals: 18,
            rpcUrls: ['https://bsc-dataseed1.binance.org'],
            blockExplorerUrls: ['https://bscscan.com'],
            enabled: true
        },
        polygon: {
            chainId: '0x89',
            chainIdDecimal: 137,
            name: 'Polygon Mainnet',
            symbol: 'MATIC',
            decimals: 18,
            rpcUrls: ['https://polygon-rpc.com'],
            blockExplorerUrls: ['https://polygonscan.com'],
            enabled: true
        },
        arbitrum: {
            chainId: '0xa4b1',
            chainIdDecimal: 42161,
            name: 'Arbitrum One',
            symbol: 'ETH',
            decimals: 18,
            rpcUrls: ['https://arb1.arbitrum.io/rpc'],
            blockExplorerUrls: ['https://arbiscan.io'],
            enabled: true
        },
        optimism: {
            chainId: '0xa',
            chainIdDecimal: 10,
            name: 'Optimism',
            symbol: 'ETH',
            decimals: 18,
            rpcUrls: ['https://mainnet.optimism.io'],
            blockExplorerUrls: ['https://optimistic.etherscan.io'],
            enabled: true
        }
    };

    init() {
        this.supportedChains = MultiChainWeb3.SUPPORTED_CHAINS;
        this.setupEventListeners();
    }

    // 设置事件监听器
    setupEventListeners() {
        if (typeof window.ethereum !== 'undefined') {
            window.ethereum.on('accountsChanged', (accounts) => {
                this.handleAccountsChanged(accounts);
            });

            window.ethereum.on('chainChanged', (chainId) => {
                this.handleChainChanged(chainId);
            });

            window.ethereum.on('disconnect', () => {
                this.handleDisconnect();
            });
        }
    }

    // 检查是否安装了Web3钱包
    isWalletInstalled() {
        return typeof window.ethereum !== 'undefined';
    }

    // 连接钱包
    async connectWallet() {
        if (!this.isWalletInstalled()) {
            throw new Error('请安装MetaMask或其他Web3钱包');
        }

        try {
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });

            if (accounts.length === 0) {
                throw new Error('未选择钱包账户');
            }

            this.currentAccount = accounts[0];
            this.provider = new ethers.providers.Web3Provider(window.ethereum);
            this.signer = this.provider.getSigner();
            
            // 获取当前网络
            const network = await this.provider.getNetwork();
            this.currentChainId = '0x' + network.chainId.toString(16);

            return {
                account: this.currentAccount,
                chainId: this.currentChainId,
                chainInfo: this.getChainInfo(this.currentChainId)
            };

        } catch (error) {
            throw new Error('连接钱包失败: ' + error.message);
        }
    }

    // 获取链信息
    getChainInfo(chainId) {
        for (const [key, chain] of Object.entries(this.supportedChains)) {
            if (chain.chainId.toLowerCase() === chainId.toLowerCase()) {
                return { ...chain, key };
            }
        }
        return null;
    }

    // 检查当前网络是否支持
    isCurrentChainSupported() {
        const chainInfo = this.getChainInfo(this.currentChainId);
        return chainInfo && chainInfo.enabled;
    }

    // 切换到指定网络
    async switchToChain(chainKey) {
        if (!this.supportedChains[chainKey]) {
            throw new Error('不支持的网络');
        }

        const chain = this.supportedChains[chainKey];

        try {
            // 尝试切换网络
            await window.ethereum.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: chain.chainId }]
            });

            this.currentChainId = chain.chainId;
            return true;

        } catch (switchError) {
            // 如果网络不存在，尝试添加网络
            if (switchError.code === 4902) {
                try {
                    await window.ethereum.request({
                        method: 'wallet_addEthereumChain',
                        params: [{
                            chainId: chain.chainId,
                            chainName: chain.name,
                            nativeCurrency: {
                                name: chain.symbol,
                                symbol: chain.symbol,
                                decimals: chain.decimals
                            },
                            rpcUrls: chain.rpcUrls,
                            blockExplorerUrls: chain.blockExplorerUrls
                        }]
                    });

                    this.currentChainId = chain.chainId;
                    return true;

                } catch (addError) {
                    throw new Error('添加网络失败: ' + addError.message);
                }
            } else {
                throw new Error('切换网络失败: ' + switchError.message);
            }
        }
    }

    // 签名消息
    async signMessage(message) {
        if (!this.signer) {
            throw new Error('请先连接钱包');
        }

        try {
            return await this.signer.signMessage(message);
        } catch (error) {
            throw new Error('签名失败: ' + error.message);
        }
    }

    // 获取支持的网络列表
    getSupportedChains() {
        return Object.entries(this.supportedChains)
            .filter(([key, chain]) => chain.enabled)
            .map(([key, chain]) => ({ ...chain, key }));
    }

    // 获取当前账户
    getCurrentAccount() {
        return this.currentAccount;
    }

    // 获取当前链ID
    getCurrentChainId() {
        return this.currentChainId;
    }

    // 事件处理器
    handleAccountsChanged(accounts) {
        if (accounts.length === 0) {
            this.currentAccount = null;
            this.provider = null;
            this.signer = null;
            this.onAccountDisconnected && this.onAccountDisconnected();
        } else {
            this.currentAccount = accounts[0];
            this.onAccountChanged && this.onAccountChanged(accounts[0]);
        }
    }

    handleChainChanged(chainId) {
        this.currentChainId = chainId;
        this.onChainChanged && this.onChainChanged(chainId);
        
        // 重新加载页面以确保状态一致
        window.location.reload();
    }

    handleDisconnect() {
        this.currentAccount = null;
        this.provider = null;
        this.signer = null;
        this.currentChainId = null;
        this.onDisconnected && this.onDisconnected();
    }

    // 设置事件回调
    onAccountChanged(callback) {
        this.onAccountChanged = callback;
    }

    onChainChanged(callback) {
        this.onChainChanged = callback;
    }

    onAccountDisconnected(callback) {
        this.onAccountDisconnected = callback;
    }

    onDisconnected(callback) {
        this.onDisconnected = callback;
    }

    // 获取网络显示名称
    getChainDisplayName(chainId) {
        const chainInfo = this.getChainInfo(chainId);
        return chainInfo ? chainInfo.name : 'Unknown Network';
    }

    // 获取区块浏览器链接
    getExplorerUrl(chainId, address) {
        const chainInfo = this.getChainInfo(chainId);
        if (chainInfo && chainInfo.blockExplorerUrls.length > 0) {
            return `${chainInfo.blockExplorerUrls[0]}/address/${address}`;
        }
        return null;
    }

    // 格式化地址显示
    formatAddress(address) {
        if (!address) return '';
        return `${address.substring(0, 6)}...${address.substring(38)}`;
    }

    // 检查地址格式
    isValidAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
}

// 全局实例
window.multiChainWeb3 = new MultiChainWeb3();
