module.exports = function (e) { var t = {}; function r(n) { if (t[n]) return t[n].exports; var i = t[n] = { i: n, l: !1, exports: {} }; return e[n].call(i.exports, i, i.exports, r), i.l = !0, i.exports } return r.m = e, r.c = t, r.d = function (e, t, n) { r.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: n }) }, r.r = function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, r.t = function (e, t) { if (1 & t && (e = r(e)), 8 & t) return e; if (4 & t && "object" == typeof e && e && e.__esModule) return e; var n = Object.create(null); if (r.r(n), Object.defineProperty(n, "default", { enumerable: !0, value: e }), 2 & t && "string" != typeof e) for (var i in e) r.d(n, i, function (t) { return e[t] }.bind(null, i)); return n }, r.n = function (e) { var t = e && e.__esModule ? function () { return e.default } : function () { return e }; return r.d(t, "a", t), t }, r.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, r.p = "", r(r.s = 22) }([function (e, t) { e.exports = require("@babel/runtime/regenerator") }, function (e, t) { e.exports = require("@babel/runtime/helpers/asyncToGenerator") }, function (e, t) { e.exports = require("@babel/runtime/helpers/classCallCheck") }, function (e, t) { e.exports = require("@babel/runtime/helpers/createClass") }, function (e, t) { e.exports = require("@babel/runtime/helpers/defineProperty") }, function (e, t) { e.exports = require("injectpromise") }, function (e, t) { e.exports = require("@babel/runtime/helpers/typeof") }, function (e, t) { e.exports = require("@babel/runtime/helpers/toConsumableArray") }, function (e, t) { e.exports = require("@babel/runtime/helpers/slicedToArray") }, function (e, t) { e.exports = require("ethers") }, function (e, t) { e.exports = require("bignumber.js") }, function (e, t) { e.exports = require("@babel/runtime/helpers/assertThisInitialized") }, function (e, t) { e.exports = require("semver") }, function (e, t) { e.exports = require("elliptic") }, function (e, t) { e.exports = require("@babel/runtime/helpers/getPrototypeOf") }, function (e, t) { e.exports = require("@babel/runtime/helpers/inherits") }, function (e, t) { e.exports = require("@babel/runtime/helpers/possibleConstructorReturn") }, function (e, t) { e.exports = require("axios") }, function (e, t) { e.exports = require("validator") }, function (e, t) { e.exports = require("eventemitter3") }, function (e) { e.exports = JSON.parse('{"a":"4.0.1"}') }, function (e, t) { e.exports = require("querystring") }, function (e, t, r) { "use strict"; r.r(t), r.d(t, "default", (function () { return Pt })); var n = {}; r.r(n), r.d(n, "byte2hexStr", (function () { return O })), r.d(n, "bytesToString", (function () { return C })), r.d(n, "hextoString", (function () { return B })), r.d(n, "byteArray2hexStr", (function () { return E })), r.d(n, "base64DecodeFromString", (function () { return q })), r.d(n, "base64EncodeToString", (function () { return D })); var i = {}; r.r(i), r.d(i, "bin2String", (function () { return V })), r.d(i, "arrayEquals", (function () { return L })), r.d(i, "stringToBytes", (function () { return U })), r.d(i, "byte2hexStr", (function () { return O })), r.d(i, "bytesToString", (function () { return C })), r.d(i, "hextoString", (function () { return B })), r.d(i, "byteArray2hexStr", (function () { return E })), r.d(i, "base64DecodeFromString", (function () { return q })), r.d(i, "base64EncodeToString", (function () { return D })), r.d(i, "hexChar2byte", (function () { return z })), r.d(i, "isHexChar", (function () { return M })), r.d(i, "hexStr2byteArray", (function () { return G })), r.d(i, "strToDate", (function () { return J })), r.d(i, "isNumber", (function () { return Y })), r.d(i, "getStringType", (function () { return $ })); var a = {}; r.r(a), r.d(a, "encode58", (function () { return ee })), r.d(a, "decode58", (function () { return te })); var s = {}; r.r(s), r.d(s, "keccak256", (function () { return ie })), r.d(s, "sha256", (function () { return ae })), r.d(s, "toUtf8Bytes", (function () { return se })), r.d(s, "toUtf8String", (function () { return oe })), r.d(s, "recoverAddress", (function () { return ue })), r.d(s, "SigningKey", (function () { return ce })), r.d(s, "AbiCoder", (function () { return de })); var o = {}; r.r(o), r.d(o, "getBase58CheckAddress", (function () { return le })), r.d(o, "decodeBase58Address", (function () { return he })), r.d(o, "signTransaction", (function () { return fe })), r.d(o, "arrayToBase64String", (function () { return ve })), r.d(o, "signBytes", (function () { return pe })), r.d(o, "getRowBytesFromTransactionBase64", (function () { return ge })), r.d(o, "genPriKey", (function () { return be })), r.d(o, "computeAddress", (function () { return me })), r.d(o, "getAddressFromPriKey", (function () { return ye })), r.d(o, "decode58Check", (function () { return ke })), r.d(o, "isAddressValid", (function () { return xe })), r.d(o, "getBase58CheckAddressFromPriKeyBase64String", (function () { return we })), r.d(o, "getHexStrAddressFromPriKeyBase64String", (function () { return Ie })), r.d(o, "getAddressFromPriKeyBase64String", (function () { return Ae })), r.d(o, "getPubKeyFromPriKey", (function () { return Pe })), r.d(o, "ECKeySign", (function () { return We })), r.d(o, "SHA256", (function () { return _e })), r.d(o, "passwordToAddress", (function () { return Se })), r.d(o, "pkToAddress", (function () { return Ne })); var u = {}; r.r(u), r.d(u, "generateAccount", (function () { return Te })); var c = {}; r.r(c), r.d(c, "decodeParams", (function () { return Fe })), r.d(c, "encodeParams", (function () { return Oe })); var d = r(1), l = r.n(d), h = r(6), f = r.n(h), v = r(2), p = r.n(v), g = r(3), b = r.n(g), m = r(11), y = r.n(m), k = r(15), x = r.n(k), w = r(16), I = r.n(w), A = r(14), P = r.n(A), W = r(4), _ = r.n(W), S = r(0), N = r.n(S), T = r(17), j = r.n(T); function F() { var e = this; this._keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", this.encode = function (t) { for (var r, n, i, a, s, o, u, c = "", d = 0; d < t.length;)a = (r = t.charCodeAt(d++)) >> 2, s = (3 & r) << 4 | (n = t.charCodeAt(d++)) >> 4, o = (15 & n) << 2 | (i = t.charCodeAt(d++)) >> 6, u = 63 & i, isNaN(n) ? o = u = 64 : isNaN(i) && (u = 64), c = c + e._keyStr.charAt(a) + e._keyStr.charAt(s) + e._keyStr.charAt(o) + e._keyStr.charAt(u); return c }, this.encodeIgnoreUtf8 = function (t) { for (var r, n, i, a, s, o, u, c = "", d = 0; d < t.length;)a = (r = t[d++]) >> 2, s = (3 & r) << 4 | (n = t[d++]) >> 4, o = (15 & n) << 2 | (i = t[d++]) >> 6, u = 63 & i, isNaN(n) ? o = u = 64 : isNaN(i) && (u = 64), c = c + e._keyStr.charAt(a) + e._keyStr.charAt(s) + e._keyStr.charAt(o) + e._keyStr.charAt(u); return c }, this.decode = function (t) { var r, n, i, a, s, o, u = "", c = 0; for (t = t.replace(/[^A-Za-z0-9\+\/\=]/g, ""); c < t.length;)r = e._keyStr.indexOf(t.charAt(c++)) << 2 | (a = e._keyStr.indexOf(t.charAt(c++))) >> 4, n = (15 & a) << 4 | (s = e._keyStr.indexOf(t.charAt(c++))) >> 2, i = (3 & s) << 6 | (o = e._keyStr.indexOf(t.charAt(c++))), u += String.fromCharCode(r), 64 != s && (u += String.fromCharCode(n)), 64 != o && (u += String.fromCharCode(i)); return e._utf8_decode(u) }, this.decodeToByteArray = function (t) { var r, n, i, a, s, o, u = "", c = 0; for (t = t.replace(/[^A-Za-z0-9\+\/\=]/g, ""); c < t.length;)r = e._keyStr.indexOf(t.charAt(c++)) << 2 | (a = e._keyStr.indexOf(t.charAt(c++))) >> 4, n = (15 & a) << 4 | (s = e._keyStr.indexOf(t.charAt(c++))) >> 2, i = (3 & s) << 6 | (o = e._keyStr.indexOf(t.charAt(c++))), u += String.fromCharCode(r), 64 != s && (u += String.fromCharCode(n)), 64 != o && (u += String.fromCharCode(i)); return e._out2ByteArray(u) }, this._out2ByteArray = function (e) { for (var t = new Array(e.length), r = 0, n = 0; r < e.length;)n = e.charCodeAt(r), t[r] = n, r++; return t }, this._utf8_encode = function (e) { e = e.replace(/\r\n/g, "\n"); for (var t = "", r = 0; r < e.length; r++) { var n = e.charCodeAt(r); n < 128 ? t += String.fromCharCode(n) : n > 127 && n < 2048 ? (t += String.fromCharCode(n >> 6 | 192), t += String.fromCharCode(63 & n | 128)) : (t += String.fromCharCode(n >> 12 | 224), t += String.fromCharCode(n >> 6 & 63 | 128), t += String.fromCharCode(63 & n | 128)) } return t }, this._utf8_decode = function (e) { for (var t = "", r = 0, n = 0, i = 0, a = 0; r < e.length;)(n = e.charCodeAt(r)) < 128 ? (t += String.fromCharCode(n), r++) : n > 191 && n < 224 ? (i = e.charCodeAt(r + 1), t += String.fromCharCode((31 & n) << 6 | 63 & i), r += 2) : (i = e.charCodeAt(r + 1), a = e.charCodeAt(r + 2), t += String.fromCharCode((15 & n) << 12 | (63 & i) << 6 | 63 & a), r += 3); return t } } function O(e) { if ("number" != typeof e) throw new Error("Input must be a number"); if (e < 0 || e > 255) throw new Error("Input must be a byte"); var t = ""; return t += "0123456789ABCDEF".charAt(e >> 4), t += "0123456789ABCDEF".charAt(15 & e) } function C(e) { if ("string" == typeof e) return e; for (var t = "", r = 0; r < e.length; r++) { var n = e[r].toString(2), i = n.match(/^1+?(?=0)/); if (i && 8 === n.length) { for (var a = i[0].length, s = e[r].toString(2).slice(7 - a), o = 1; o < a; o++)s += e[o + r].toString(2).slice(2); t += String.fromCharCode(parseInt(s, 2)), r += a - 1 } else t += String.fromCharCode(e[r]) } return t } function B(e) { for (var t = e.replace(/^0x/, "").split(""), r = "", n = 0; n < t.length / 2; n++) { var i = "0x".concat(t[2 * n]).concat(t[2 * n + 1]); r += String.fromCharCode(i) } return r } function E(e) { for (var t = "", r = 0; r < e.length; r++)t += O(e[r]); return t } function q(e) { return (new F).decodeToByteArray(e) } function D(e) { return (new F).encodeIgnoreUtf8(e) } var K = r(7), R = r.n(K), H = /^(41)/; function V(e) { return C(e) } function L(e, t, r) { if (e.length != t.length) return !1; var n; for (n = 0; n < e.length; n++)if (r) { if (e[n] != t[n]) return !1 } else if (JSON.stringify(e[n]) != JSON.stringify(t[n])) return !1; return !0 } function U(e) { if ("string" != typeof e) throw new Error("The passed string is not a string"); var t, r, n = new Array; t = e.length; for (var i = 0; i < t; i++)(r = e.charCodeAt(i)) >= 65536 && r <= 1114111 ? (n.push(r >> 18 & 7 | 240), n.push(r >> 12 & 63 | 128), n.push(r >> 6 & 63 | 128), n.push(63 & r | 128)) : r >= 2048 && r <= 65535 ? (n.push(r >> 12 & 15 | 224), n.push(r >> 6 & 63 | 128), n.push(63 & r | 128)) : r >= 128 && r <= 2047 ? (n.push(r >> 6 & 31 | 192), n.push(63 & r | 128)) : n.push(255 & r); return n } function z(e) { var t; if (e >= "A" && e <= "F" ? t = e.charCodeAt(0) - "A".charCodeAt(0) + 10 : e >= "a" && e <= "f" ? t = e.charCodeAt(0) - "a".charCodeAt(0) + 10 : e >= "0" && e <= "9" && (t = e.charCodeAt(0) - "0".charCodeAt(0)), "number" == typeof t) return t; throw new Error("The passed hex char is not a valid hex char") } function M(e) { return e >= "A" && e <= "F" || e >= "a" && e <= "f" || e >= "0" && e <= "9" ? 1 : 0 } function G(e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if ("string" != typeof e) throw new Error("The passed string is not a string"); var r = e.length; t && r % 2 && (e = "0".concat(e), r++); for (var n = Array(), i = 0, a = 0, s = 0, o = 0; o < r; o++) { var u = e.charAt(o); if (!M(u)) throw new Error("The passed hex char is not a valid hex string"); i <<= 4, i += z(u), 0 == ++a % 2 && (n[s++] = i, i = 0) } return n } function J(e) { if (!/^\d{4}-\d{2}-\d{2}( \d{2}-\d{2}-\d{2}|)/.test(e)) throw new Error("The passed date string is not valid"); var t = e.split(" "), r = t[0].split("-"), n = parseInt(r[0], 10), i = parseInt(r[1], 10) - 1, a = parseInt(r[2], 10); if (t.length > 1) { var s = t[1].split("-"), o = parseInt(s[0], 10), u = parseInt(s[1], 10), c = parseInt(s[2], 10); return new Date(n, i, a, o, u, c) } return new Date(n, i, a) } function Y(e) { return e >= "0" && e <= "9" ? 1 : 0 } function $(e) { if (null == e) return -1; if ("string" != typeof e) return -1; if (0 == e.length || "" == e) return -1; var t = 0; if (40 == e.length) for (; t < 40; t++) { if (!M(e.charAt(t))) break } if (40 == t) return 1; for (t = 0; t < e.length; t++) { if (!Y(e.charAt(t))) break } if (t == e.length) return 2; for (t = 0; t < e.length; t++) { if (e.charAt(t) > " ") return 3 } return -1 } for (var X = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz", Z = {}, Q = 0; Q < X.length; Q++)Z[X.charAt(Q)] = Q; function ee(e) { if (0 === e.length) return ""; var t, r, n = [0]; for (t = 0; t < e.length; t++) { for (r = 0; r < n.length; r++)n[r] <<= 8; n[0] += e[t]; var i = 0; for (r = 0; r < n.length; ++r)n[r] += i, i = n[r] / 58 | 0, n[r] %= 58; for (; i;)n.push(i % 58), i = i / 58 | 0 } for (t = 0; 0 === e[t] && t < e.length - 1; t++)n.push(0); return n.reverse().map((function (e) { return X[e] })).join("") } function te(e) { if (0 === e.length) return []; var t, r, n = [0]; for (t = 0; t < e.length; t++) { var i = e[t]; if (!(i in Z)) throw new Error("Non-base58 character"); for (r = 0; r < n.length; r++)n[r] *= 58; n[0] += Z[i]; var a = 0; for (r = 0; r < n.length; ++r)n[r] += a, a = n[r] >> 8, n[r] &= 255; for (; a;)n.push(255 & a), a >>= 8 } for (t = 0; "1" === e[t] && t < e.length - 1; t++)n.push(0); return n.reverse() } var re = r(13), ne = r(9), ie = ne.utils.keccak256, ae = ne.utils.sha256, se = ne.utils.toUtf8Bytes, oe = ne.utils.toUtf8String, ue = ne.utils.recoverAddress, ce = ne.utils.SigningKey, de = ne.utils.AbiCoder; function le(e) { var t = _e(e), r = _e(t).slice(0, 4); return ee(r = e.concat(r)) } function he(e) { if ("string" != typeof e) return !1; if (e.length <= 4) return !1; var t = te(e); if (e.length <= 4) return !1; var r = t.length - 4, n = t.slice(r), i = _e(t = t.slice(0, r)), a = _e(i).slice(0, 4); if (n[0] == a[0] && n[1] == a[1] && n[2] == a[2] && n[3] == a[3]) return t; throw new Error("Invalid address provided") } function fe(e, t) { "string" == typeof e && (e = G(e)); var r = We(G(t.txID), e); return Array.isArray(t.signature) ? t.signature.includes(r) || t.signature.push(r) : t.signature = [r], t } function ve(e) { return btoa(String.fromCharCode.apply(String, R()(e))) } function pe(e, t) { return "string" == typeof e && (e = G(e)), We(_e(t), e) } function ge(e) { var t = q(e); return proto.protocol.Transaction.deserializeBinary(t).getRawData().serializeBinary() } function be() { for (var e = new re.ec("secp256k1").genKeyPair().getPrivate().toString("hex"); e.length < 64;)e = "0".concat(e); return G(e) } function me(e) { return 65 === e.length && (e = e.slice(1)), G("41" + ie(e).toString().substring(2).substring(24)) } function ye(e) { return me(Pe(e)) } function ke(e) { var t = te(e); if (t.length <= 4) return !1; var r = t.slice(0, t.length - 4), n = _e(r), i = _e(n); return i[0] === t[r.length] && i[1] === t[r.length + 1] && i[2] === t[r.length + 2] && i[3] === t[r.length + 3] && r } function xe(e) { if ("string" != typeof e) return !1; if (34 !== e.length) return !1; var t = te(e); if (25 !== t.length) return !1; if (65 !== t[0]) return !1; var r = t.slice(21), n = _e(t = t.slice(0, 21)), i = _e(n).slice(0, 4); return r[0] == i[0] && r[1] == i[1] && r[2] == i[2] && r[3] == i[3] } function we(e) { return le(me(Pe(q(e)))) } function Ie(e) { return E(me(Pe(q(e)))) } function Ae(e) { return D(me(Pe(q(e)))) } function Pe(e) { for (var t = new re.ec("secp256k1").keyFromPrivate(e, "bytes").getPublic(), r = t.x, n = t.y, i = r.toString("hex"); i.length < 64;)i = "0".concat(i); for (var a = n.toString("hex"); a.length < 64;)a = "0".concat(a); return G("04".concat(i).concat(a)) } function We(e, t) { for (var r = new re.ec("secp256k1").keyFromPrivate(t, "bytes").sign(e), n = r.r, i = r.s, a = r.recoveryParam, s = n.toString("hex"); s.length < 64;)s = "0".concat(s); for (var o = i.toString("hex"); o.length < 64;)o = "0".concat(o); return s + o + O(a) } function _e(e) { var t = E(e); return G(ae("0x" + t).replace(/^0x/, "")) } function Se(e) { return le(ye(q(e))) } function Ne(e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = G(e, t), n = ye(r); return le(n) } function Te() { var e = be(), t = Pe(e), r = ye(e); return { privateKey: E(e), publicKey: E(t), address: { base58: le(r), hex: E(r) } } } var je = new de; function Fe(e, t, r, n) { if (r && "boolean" != typeof r || (n = r, r = t, t = e, e = []), n && r.replace(/^0x/, "").length % 64 == 8 && (r = "0x" + r.replace(/^0x/, "").substring(8)), r.replace(/^0x/, "").length % 64) throw new Error("The encoded string is not valid. Its length must be a multiple of 64."); return t = t.map((function (e) { return /trcToken/.test(e) && (e = e.replace(/trcToken/, "uint256")), e })), je.decode(t, r).reduce((function (r, n, i) { return "address" == t[i] && (n = "41" + n.substr(2).toLowerCase()), e.length ? r[e[i]] = n : r.push(n), r }), e.length ? {} : []) } function Oe(e, t) { for (var r = 0; r < e.length; r++)"address" === e[r] && (t[r] = Pt.address.toHex(t[r]).replace(H, "0x")); return je.encode(e, t) } var Ce = r(18), Be = r.n(Ce), Ee = r(10), qe = r.n(Ee); function De(e, t) { var r = Object.keys(e); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); t && (n = n.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), r.push.apply(r, n) } return r } function Ke(e) { for (var t = 1; t < arguments.length; t++) { var r = null != arguments[t] ? arguments[t] : {}; t % 2 ? De(Object(r), !0).forEach((function (t) { _()(e, t, r[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : De(Object(r)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t)) })) } return e } var Re = Ke(Ke({}, { isValidURL: function (e) { return "string" == typeof e && Be.a.isURL(e.toString(), { protocols: ["http", "https"], require_tld: !1 }) }, isObject: function (e) { return e === Object(e) && "[object Array]" !== Object.prototype.toString.call(e) }, isArray: function (e) { return Array.isArray(e) }, isJson: function (e) { try { return !!JSON.parse(e) } catch (e) { return !1 } }, isBoolean: function (e) { return "boolean" == typeof e }, isBigNumber: function (e) { return e && (e instanceof qe.a || e.constructor && "BigNumber" === e.constructor.name) }, isString: function (e) { return "string" == typeof e || e && e.constructor && "String" === e.constructor.name }, isFunction: function (e) { return "function" == typeof e }, isHex: function (e) { return "string" == typeof e && !isNaN(parseInt(e, 16)) && /^(0x|)[a-fA-F0-9]+$/.test(e) }, isInteger: function (e) { return null !== e && Number.isInteger(Number(e)) }, hasProperty: function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, hasProperties: function (e) { for (var t = this, r = arguments.length, n = new Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++)n[i - 1] = arguments[i]; return n.length && !n.map((function (r) { return t.hasProperty(e, r) })).includes(!1) }, mapEvent: function (e) { var t = { block: e.block_number, timestamp: e.block_timestamp, contract: e.contract_address, name: e.event_name, transaction: e.transaction_id, result: e.result, resourceNode: e.resource_Node || (e._unconfirmed ? "fullNode" : "solidityNode") }; return e._unconfirmed && (t.unconfirmed = e._unconfirmed), e._fingerprint && (t.fingerprint = e._fingerprint), t }, parseEvent: function (e, t) { var r = t.inputs; if (!e.result) return e; if (this.isObject(e.result)) for (var n = 0; n < r.length; n++) { var i = r[n]; "address" == i.type && i.name in e.result && (e.result[i.name] = "41" + e.result[i.name].substr(2).toLowerCase()) } else this.isArray(e.result) && (e.result = e.result.reduce((function (e, t, n) { var i = r[n], a = i.name; return "address" == i.type && (t = "41" + t.substr(2).toLowerCase()), e[a] = t, e }), {})); return e }, padLeft: function (e, t, r) { for (var n = e.toString(); n.length < r;)n = t + n; return n }, isNotNullOrUndefined: function (e) { return null != e }, sleep: function () { var e = arguments; return l()(N.a.mark((function t() { var r; return N.a.wrap((function (t) { for (; ;)switch (t.prev = t.next) { case 0: return r = e.length > 0 && void 0 !== e[0] ? e[0] : 1e3, t.abrupt("return", new Promise((function (e) { return setTimeout(e, r) }))); case 2: case "end": return t.stop() } }), t) })))() } }), {}, { code: i, accounts: u, base58: a, bytes: n, crypto: o, abi: c, ethersUtils: s }), He = { HttpProvider: function () { function e(t) { var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3e4, n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3], a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : {}, s = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : "/"; if (p()(this, e), !Re.isValidURL(t)) throw new Error("Invalid URL provided to HttpProvider"); if (isNaN(r) || r < 0) throw new Error("Invalid timeout duration provided"); if (!Re.isObject(a)) throw new Error("Invalid headers object provided"); t = t.replace(/\/+$/, ""), this.host = t, this.timeout = r, this.user = n, this.password = i, this.headers = a, this.statusPage = s, this.instance = j.a.create({ baseURL: t, timeout: r, headers: a, auth: n && { user: n, password: i } }) } var t; return b()(e, [{ key: "setStatusPage", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "/"; this.statusPage = e } }, { key: "isConnected", value: (t = l()(N.a.mark((function e() { var t, r = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return t = r.length > 0 && void 0 !== r[0] ? r[0] : this.statusPage, e.abrupt("return", this.request(t).then((function (e) { return Re.hasProperties(e, "blockID", "block_header") })).catch((function () { return !1 }))); case 2: case "end": return e.stop() } }), e, this) }))), function () { return t.apply(this, arguments) }) }, { key: "request", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "get"; return r = r.toLowerCase(), this.instance.request({ data: "post" == r && Object.keys(t).length ? t : null, params: "get" == r && t, url: e, method: r }).then((function (e) { return e.data })) } }]), e }() }, Ve = r(19), Le = r.n(Ve), Ue = r(20), ze = r(12), Me = r.n(ze), Ge = r(5), Je = r.n(Ge), Ye = r(8), $e = r.n(Ye); function Xe(e, t) { var r = "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (!r) { if (Array.isArray(e) || (r = function (e, t) { if (!e) return; if ("string" == typeof e) return Ze(e, t); var r = Object.prototype.toString.call(e).slice(8, -1); "Object" === r && e.constructor && (r = e.constructor.name); if ("Map" === r || "Set" === r) return Array.from(e); if ("Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)) return Ze(e, t) }(e)) || t && e && "number" == typeof e.length) { r && (e = r); var n = 0, i = function () { }; return { s: i, n: function () { return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] } }, e: function (e) { throw e }, f: i } } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } var a, s = !0, o = !1; return { s: function () { r = r.call(e) }, n: function () { var e = r.next(); return s = e.done, e }, e: function (e) { o = !0, a = e }, f: function () { try { s || null == r.return || r.return() } finally { if (o) throw a } } } } function Ze(e, t) { (null == t || t > e.length) && (t = e.length); for (var r = 0, n = new Array(t); r < t; r++)n[r] = e[r]; return n } var Qe, et = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (p()(this, e), !t || !t instanceof Pt) throw new Error("Expected instance of TronWeb"); this.tronWeb = t } return b()(e, [{ key: "invalid", value: function (e) { return e.msg || "Invalid ".concat(e.name).concat("address" === e.type ? " address" : "", " provided") } }, { key: "notPositive", value: function (e) { return "".concat(e.name, " must be a positive integer") } }, { key: "notEqual", value: function (e) { return e.msg || "".concat(e.names[0], " can not be equal to ").concat(e.names[1]) } }, { key: "notValid", value: function () { var e, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Function, n = {}, i = !1, a = Xe(t); try { for (a.s(); !(e = a.n()).done;) { var s = e.value, o = s.name, u = s.names, c = s.value, d = s.type, l = s.gt, h = s.lt, f = s.gte, v = s.lte, p = (s.se, s.optional); if (!p || Re.isNotNullOrUndefined(c) && ("boolean" === d || !1 !== c)) { switch (n[s.name] = s.value, d) { case "address": this.tronWeb.isAddress(c) ? n[o] = this.tronWeb.address.toHex(c) : i = !0; break; case "integer": (!Re.isInteger(c) || "number" == typeof l && c <= s.gt || "number" == typeof h && c >= s.lt || "number" == typeof f && c < s.gte || "number" == typeof v && c > s.lte) && (i = !0); break; case "positive-integer": if (!Re.isInteger(c) || c <= 0) return void r(this.notPositive(s)); break; case "tokenId": Re.isString(c) && c.length || (i = !0); break; case "notEmptyObject": Re.isObject(c) && Object.keys(c).length || (i = !0); break; case "notEqual": if (n[u[0]] === n[u[1]]) return r(this.notEqual(s)), !0; break; case "resource": ["BANDWIDTH", "ENERGY"].includes(c) || (i = !0); break; case "url": Re.isValidURL(c) || (i = !0); break; case "hex": Re.isHex(c) || (i = !0); break; case "array": Array.isArray(c) || (i = !0); break; case "not-empty-string": Re.isString(c) && c.length || (i = !0); break; case "boolean": Re.isBoolean(c) || (i = !0); break; case "string": (!Re.isString(c) || "number" == typeof l && c.length <= s.gt || "number" == typeof h && c.length >= s.lt || "number" == typeof f && c.length < s.gte || "number" == typeof v && c.length > s.lte) && (i = !0) }if (i) return r(this.invalid(s)), !0 } } } catch (e) { a.e(e) } finally { a.f() } return !1 } }]), e }(); function tt(e, t) { var r = "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (!r) { if (Array.isArray(e) || (r = function (e, t) { if (!e) return; if ("string" == typeof e) return rt(e, t); var r = Object.prototype.toString.call(e).slice(8, -1); "Object" === r && e.constructor && (r = e.constructor.name); if ("Map" === r || "Set" === r) return Array.from(e); if ("Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)) return rt(e, t) }(e)) || t && e && "number" == typeof e.length) { r && (e = r); var n = 0, i = function () { }; return { s: i, n: function () { return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] } }, e: function (e) { throw e }, f: i } } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } var a, s = !0, o = !1; return { s: function () { r = r.call(e) }, n: function () { var e = r.next(); return s = e.done, e }, e: function (e) { o = !0, a = e }, f: function () { try { s || null == r.return || r.return() } finally { if (o) throw a } } } } function rt(e, t) { (null == t || t > e.length) && (t = e.length); for (var r = 0, n = new Array(t); r < t; r++)n[r] = e[r]; return n } function nt(e) { return Pt.address.toHex(e) } function it(e) { return Qe.tronWeb.fromUtf8(e) } function at(e, t) { return e.Error ? t(e.Error) : e.result && e.result.message ? t(Qe.tronWeb.toUtf8(e.result.message)) : t(null, e) } var st = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (p()(this, e), !t || !t instanceof Pt) throw new Error("Expected instance of TronWeb"); Qe = this, this.tronWeb = t, this.injectPromise = Je()(this), this.validator = new et(t) } var t, r, n, i; return b()(e, [{ key: "sendTrx", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this.tronWeb.defaultAddress.hex, n = arguments.length > 3 ? arguments[3] : void 0, i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = this.tronWeb.defaultAddress.hex) : Re.isObject(r) && (n = r, r = this.tronWeb.defaultAddress.hex), !i) return this.injectPromise(this.sendTrx, e, t, r, n); if (t = parseInt(t), !this.validator.notValid([{ name: "recipient", type: "address", value: e }, { name: "origin", type: "address", value: r }, { names: ["recipient", "origin"], type: "notEqual", msg: "Cannot transfer TRX to the same account" }, { name: "amount", type: "integer", gt: 0, value: t }], i)) { var a = { to_address: nt(e), owner_address: nt(r), amount: t }; n && n.permissionId && (a.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/createtransaction", a, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } }, { key: "sendToken", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 ? arguments[4] : void 0, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = {}), Re.isFunction(n) ? (a = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (i = n, n = this.tronWeb.defaultAddress.hex), !a) return this.injectPromise(this.sendToken, e, t, r, n, i); if (t = parseInt(t), !this.validator.notValid([{ name: "recipient", type: "address", value: e }, { name: "origin", type: "address", value: n }, { names: ["recipient", "origin"], type: "notEqual", msg: "Cannot transfer tokens to the same account" }, { name: "amount", type: "integer", gt: 0, value: t }, { name: "token ID", type: "tokenId", value: r }], a)) { var s = { to_address: nt(e), owner_address: nt(n), asset_name: it(r), amount: parseInt(t) }; i && i.permissionId && (s.Permission_id = i.permissionId), this.tronWeb.fullNode.request("wallet/transferasset", s, "post").then((function (e) { return at(e, a) })).catch((function (e) { return a(e) })) } } }, { key: "purchaseToken", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 ? arguments[4] : void 0, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = {}), Re.isFunction(n) ? (a = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (i = n, n = this.tronWeb.defaultAddress.hex), !a) return this.injectPromise(this.purchaseToken, e, t, r, n, i); if (!this.validator.notValid([{ name: "buyer", type: "address", value: n }, { name: "issuer", type: "address", value: e }, { names: ["buyer", "issuer"], type: "notEqual", msg: "Cannot purchase tokens from same account" }, { name: "amount", type: "integer", gt: 0, value: r }, { name: "token ID", type: "tokenId", value: t }], a)) { var s = { to_address: nt(e), owner_address: nt(n), asset_name: it(t), amount: parseInt(r) }; i && i.permissionId && (s.Permission_id = i.permissionId), this.tronWeb.fullNode.request("wallet/participateassetissue", s, "post").then((function (e) { return at(e, a) })).catch((function (e) { return a(e) })) } } }, { key: "freezeBalance", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "BANDWIDTH", n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : void 0, a = arguments.length > 5 ? arguments[5] : void 0, s = arguments.length > 6 && void 0 !== arguments[6] && arguments[6]; if (Re.isFunction(a) && (s = a, a = {}), Re.isFunction(i) ? (s = i, i = void 0) : Re.isObject(i) && (a = i, i = void 0), Re.isFunction(n) ? (s = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (a = n, n = this.tronWeb.defaultAddress.hex), Re.isFunction(t) && (s = t, t = 3), Re.isFunction(r) && (s = r, r = "BANDWIDTH"), !s) return this.injectPromise(this.freezeBalance, e, t, r, n, i, a); if (!this.validator.notValid([{ name: "origin", type: "address", value: n }, { name: "receiver", type: "address", value: i, optional: !0 }, { name: "amount", type: "integer", gt: 0, value: e }, { name: "duration", type: "integer", gte: 3, value: t }, { name: "resource", type: "resource", value: r, msg: 'Invalid resource provided: Expected "BANDWIDTH" or "ENERGY' }], s)) { var o = { owner_address: nt(n), frozen_balance: parseInt(e), frozen_duration: parseInt(t), resource: r }; Re.isNotNullOrUndefined(i) && nt(i) !== nt(n) && (o.receiver_address = nt(i)), a && a.permissionId && (o.Permission_id = a.permissionId), this.tronWeb.fullNode.request("wallet/freezebalance", o, "post").then((function (e) { return at(e, s) })).catch((function (e) { return s(e) })) } } }, { key: "unfreezeBalance", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "BANDWIDTH", t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0, n = arguments.length > 3 ? arguments[3] : void 0, i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = void 0) : Re.isObject(r) && (n = r, r = void 0), Re.isFunction(t) ? (i = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (n = t, t = this.tronWeb.defaultAddress.hex), Re.isFunction(e) && (i = e, e = "BANDWIDTH"), !i) return this.injectPromise(this.unfreezeBalance, e, t, r, n); if (!this.validator.notValid([{ name: "origin", type: "address", value: t }, { name: "receiver", type: "address", value: r, optional: !0 }, { name: "resource", type: "resource", value: e, msg: 'Invalid resource provided: Expected "BANDWIDTH" or "ENERGY' }], i)) { var a = { owner_address: nt(t), resource: e }; Re.isNotNullOrUndefined(r) && nt(r) !== nt(t) && (a.receiver_address = nt(r)), n && n.permissionId && (a.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/unfreezebalance", a, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } }, { key: "withdrawBlockRewards", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 ? arguments[1] : void 0, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = {}), Re.isFunction(e) ? (r = e, e = this.tronWeb.defaultAddress.hex) : Re.isObject(e) && (t = e, e = this.tronWeb.defaultAddress.hex), !r) return this.injectPromise(this.withdrawBlockRewards, e, t); if (!this.validator.notValid([{ name: "origin", type: "address", value: e }], r)) { var n = { owner_address: nt(e) }; t && t.permissionId && (n.Permission_id = t.permissionId), this.tronWeb.fullNode.request("wallet/withdrawbalance", n, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } } }, { key: "applyForSR", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 ? arguments[2] : void 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; if (Re.isFunction(r) && (n = r, r = {}), Re.isObject(t) && Re.isValidURL(e) && (r = t, t = e, e = this.tronWeb.defaultAddress.hex), !n) return this.injectPromise(this.applyForSR, e, t, r); if (!this.validator.notValid([{ name: "origin", type: "address", value: e }, { name: "url", type: "url", value: t, msg: "Invalid url provided" }], n)) { var i = { owner_address: nt(e), url: it(t) }; r && r.permissionId && (i.Permission_id = r.permissionId), this.tronWeb.fullNode.request("wallet/createwitness", i, "post").then((function (e) { return at(e, n) })).catch((function (e) { return n(e) })) } } }, { key: "vote", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, n = arguments.length > 2 ? arguments[2] : void 0, i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = this.tronWeb.defaultAddress.hex) : Re.isObject(r) && (n = r, r = this.tronWeb.defaultAddress.hex), !i) return this.injectPromise(this.vote, t, r, n); if (!this.validator.notValid([{ name: "voter", type: "address", value: r }, { name: "votes", type: "notEmptyObject", value: t }], i)) { var a = !1; if (t = Object.entries(t).map((function (t) { var r = $e()(t, 2), n = r[0], i = r[1]; if (!a) return e.validator.notValid([{ name: "SR", type: "address", value: n }, { name: "vote count", type: "integer", gt: 0, value: i, msg: "Invalid vote count provided for SR: " + n }]) ? a = !0 : { vote_address: nt(n), vote_count: parseInt(i) } })), !a) { var s = { owner_address: nt(r), votes: t }; n && n.permissionId && (s.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/votewitnessaccount", s, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } } }, { key: "createSmartContract", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !r) return this.injectPromise(this.createSmartContract, e, t); var n = e.feeLimit || this.tronWeb.feeLimit, i = e.userFeePercentage; "number" == typeof i || i || (i = 100); var a = e.originEnergyLimit || 1e7, s = e.callValue || 0, o = e.tokenValue, u = e.tokenId || e.token_id, c = e.abi, d = void 0 !== c && c, l = e.bytecode, h = void 0 !== l && l, f = e.parameters, v = void 0 === f ? [] : f, p = e.name, g = void 0 === p ? "" : p; if (d && Re.isString(d)) try { d = JSON.parse(d) } catch (e) { return r("Invalid options.abi provided") } if (d.entrys && (d = d.entrys), !Re.isArray(d)) return r("Invalid options.abi provided"); var b = d.some((function (e) { return "constructor" === e.type && "payable" === e.stateMutability.toLowerCase() })); if (!this.validator.notValid([{ name: "bytecode", type: "hex", value: h }, { name: "feeLimit", type: "integer", value: n, gt: 0 }, { name: "callValue", type: "integer", value: s, gte: 0 }, { name: "userFeePercentage", type: "integer", value: i, gte: 0, lte: 100 }, { name: "originEnergyLimit", type: "integer", value: a, gte: 0, lte: 1e7 }, { name: "parameters", type: "array", value: v }, { name: "issuer", type: "address", value: t }, { name: "tokenValue", type: "integer", value: o, gte: 0, optional: !0 }, { name: "tokenId", type: "integer", value: u, gte: 0, optional: !0 }], r)) { if (b && 0 == s && 0 == o) return r("When contract is payable, options.callValue or options.tokenValue must be a positive integer"); if (!b && (s > 0 || o > 0)) return r("When contract is not payable, options.callValue and options.tokenValue must be 0"); if (e.rawParameter && Re.isString(e.rawParameter)) v = e.rawParameter.replace(/^(0x)/, ""); else { var m = d.find((function (e) { return "constructor" === e.type })); if (void 0 !== m && m) { var y = new de, k = [], x = []; if (m = m.inputs, v.length != m.length) return r("constructor needs ".concat(m.length, " but ").concat(v.length, " provided")); for (var w = 0; w < v.length; w++) { var I = m[w].type, A = v[w]; if (!I || !Re.isString(I) || !I.length) return r("Invalid parameter type provided: " + I); "address" === I ? A = nt(A).replace(H, "0x") : "address[" === I.match(/^([^\x5b]*)(\x5b|$)/)[0] ? A = A.map((function (e) { return nt(e).replace(H, "0x") })) : /trcToken/.test(I) && (I = I.replace(/trcToken/, "uint256")), k.push(I), x.push(A) } try { v = y.encode(k, x).replace(/^(0x)/, "") } catch (e) { return r(e) } } else v = "" } var P = { owner_address: nt(t), fee_limit: parseInt(n), call_value: parseInt(s), consume_user_resource_percent: i, origin_energy_limit: a, abi: JSON.stringify(d), bytecode: h, parameter: v, name: g }; Re.isNotNullOrUndefined(o) && (P.call_token_value = parseInt(o)), Re.isNotNullOrUndefined(u) && (P.token_id = parseInt(u)), e && e.permissionId && (P.Permission_id = e.permissionId), this.tronWeb.fullNode.request("wallet/deploycontract", P, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } } }, { key: "triggerSmartContract", value: function () { for (var e = arguments.length, t = new Array(e), r = 0; r < e; r++)t[r] = arguments[r]; return "object" !== f()(t[2]) && (t[2] = { feeLimit: t[2], callValue: t[3] }, t.splice(3, 1)), this._triggerSmartContract.apply(this, t) } }, { key: "triggerConstantContract", value: function () { for (var e = arguments.length, t = new Array(e), r = 0; r < e; r++)t[r] = arguments[r]; return t[2]._isConstant = !0, this.triggerSmartContract.apply(this, t) } }, { key: "triggerConfirmedConstantContract", value: function () { for (var e = arguments.length, t = new Array(e), r = 0; r < e; r++)t[r] = arguments[r]; return t[2]._isConstant = !0, t[2].confirmed = !0, this.triggerSmartContract.apply(this, t) } }, { key: "_triggerSmartContract", value: function (e, t) { var r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}, n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : [], i = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : this.tronWeb.defaultAddress.hex, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = this.tronWeb.defaultAddress.hex), Re.isFunction(n) && (a = n, n = []), !a) return this.injectPromise(this._triggerSmartContract, e, t, r, n, i); var s = Object.assign({ callValue: 0, feeLimit: this.tronWeb.feeLimit }, r), o = s.tokenValue, u = s.tokenId, c = s.callValue, d = s.feeLimit; if (!this.validator.notValid([{ name: "feeLimit", type: "integer", value: d, gt: 0 }, { name: "callValue", type: "integer", value: c, gte: 0 }, { name: "parameters", type: "array", value: n }, { name: "contract", type: "address", value: e }, { name: "issuer", type: "address", value: i, optional: !0 }, { name: "tokenValue", type: "integer", value: o, gte: 0, optional: !0 }, { name: "tokenId", type: "integer", value: u, gte: 0, optional: !0 }], a)) { var l = { contract_address: nt(e), owner_address: nt(i) }; if (t && Re.isString(t)) { if (t = t.replace("/s*/g", ""), n.length) { for (var h = new de, f = [], v = [], p = 0; p < n.length; p++) { var g = n[p], b = g.type, m = g.value; if (!b || !Re.isString(b) || !b.length) return a("Invalid parameter type provided: " + b); "address" === b ? m = nt(m).replace(H, "0x") : "address[" === b.match(/^([^\x5b]*)(\x5b|$)/)[0] && (m = m.map((function (e) { return nt(e).replace(H, "0x") }))), f.push(b), v.push(m) } try { f = f.map((function (e) { return /trcToken/.test(e) && (e = e.replace(/trcToken/, "uint256")), e })), n = h.encode(f, v).replace(/^(0x)/, "") } catch (e) { return a(e) } } else n = ""; r.shieldedParameter && Re.isString(r.shieldedParameter) && (n = r.shieldedParameter.replace(/^(0x)/, "")), r.rawParameter && Re.isString(r.rawParameter) && (n = r.rawParameter.replace(/^(0x)/, "")), l.function_selector = t, l.parameter = n } r._isConstant || (l.call_value = parseInt(c), l.fee_limit = parseInt(d), Re.isNotNullOrUndefined(o) && (l.call_token_value = parseInt(o)), Re.isNotNullOrUndefined(u) && (l.token_id = parseInt(u))), r.permissionId && (l.Permission_id = r.permissionId), this.tronWeb[r.confirmed ? "solidityNode" : "fullNode"].request("wallet".concat(r.confirmed ? "solidity" : "", "/trigger").concat(r._isConstant ? "constant" : "smart", "contract"), l, "post").then((function (e) { return at(e, a) })).catch((function (e) { return a(e) })) } } }, { key: "clearABI", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (!r) return this.injectPromise(this.clearABI, e, t); if (!this.tronWeb.isAddress(e)) return r("Invalid contract address provided"); if (!this.tronWeb.isAddress(t)) return r("Invalid owner address provided"); var n = { contract_address: nt(e), owner_address: nt(t) }; this.tronWeb.trx.cache.contracts[e] && delete this.tronWeb.trx.cache.contracts[e], this.tronWeb.fullNode.request("wallet/clearabi", n, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } }, { key: "updateBrokerage", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (!r) return this.injectPromise(this.updateBrokerage, e, t); if (!Re.isNotNullOrUndefined(e)) return r("Invalid brokerage provided"); if (!Re.isInteger(e) || e < 0 || e > 100) return r("Brokerage must be an integer between 0 and 100"); if (!this.tronWeb.isAddress(t)) return r("Invalid owner address provided"); var n = { brokerage: parseInt(e), owner_address: nt(t) }; this.tronWeb.fullNode.request("wallet/updateBrokerage", n, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } }, { key: "createToken", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !r) return this.injectPromise(this.createToken, e, t); var n = e.name, i = void 0 !== n && n, a = e.abbreviation, s = void 0 !== a && a, o = e.description, u = void 0 !== o && o, c = e.url, d = void 0 !== c && c, l = e.totalSupply, h = void 0 === l ? 0 : l, f = e.trxRatio, v = void 0 === f ? 1 : f, p = e.tokenRatio, g = void 0 === p ? 1 : p, b = e.saleStart, m = void 0 === b ? Date.now() : b, y = e.saleEnd, k = void 0 !== y && y, x = e.freeBandwidth, w = void 0 === x ? 0 : x, I = e.freeBandwidthLimit, A = void 0 === I ? 0 : I, P = e.frozenAmount, W = void 0 === P ? 0 : P, _ = e.frozenDuration, S = void 0 === _ ? 0 : _, N = e.voteScore, T = e.precision; if (!this.validator.notValid([{ name: "Supply amount", type: "positive-integer", value: h }, { name: "TRX ratio", type: "positive-integer", value: v }, { name: "Token ratio", type: "positive-integer", value: g }, { name: "token abbreviation", type: "not-empty-string", value: s }, { name: "token name", type: "not-empty-string", value: i }, { name: "token description", type: "not-empty-string", value: u }, { name: "token url", type: "url", value: d }, { name: "issuer", type: "address", value: t }, { name: "sale start timestamp", type: "integer", value: m, gte: Date.now() }, { name: "sale end timestamp", type: "integer", value: k, gt: m }, { name: "Free bandwidth amount", type: "integer", value: w, gte: 0 }, { name: "Free bandwidth limit", type: "integer", value: A, gte: 0 }, { name: "Frozen supply", type: "integer", value: W, gte: 0 }, { name: "Frozen duration", type: "integer", value: S, gte: 0 }], r)) { if (Re.isNotNullOrUndefined(N) && (!Re.isInteger(N) || N <= 0)) return r("voteScore must be a positive integer greater than 0"); if (Re.isNotNullOrUndefined(T) && (!Re.isInteger(T) || T < 0 || T > 6)) return r("precision must be a positive integer >= 0 and <= 6"); var j = { owner_address: nt(t), name: it(i), abbr: it(s), description: it(u), url: it(d), total_supply: parseInt(h), trx_num: parseInt(v), num: parseInt(g), start_time: parseInt(m), end_time: parseInt(k), free_asset_net_limit: parseInt(w), public_free_asset_net_limit: parseInt(A), frozen_supply: { frozen_amount: parseInt(W), frozen_days: parseInt(S) } }; parseInt(W) > 0 || delete j.frozen_supply, T && !isNaN(parseInt(T)) && (j.precision = parseInt(T)), N && !isNaN(parseInt(N)) && (j.vote_score = parseInt(N)), e && e.permissionId && (j.Permission_id = e.permissionId), this.tronWeb.fullNode.request("wallet/createassetissue", j, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } } }, { key: "updateAccount", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 ? arguments[2] : void 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; if (Re.isFunction(r) && (n = r, r = {}), Re.isFunction(t) ? (n = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !n) return this.injectPromise(this.updateAccount, e, t, r); if (!this.validator.notValid([{ name: "Name", type: "not-empty-string", value: e }, { name: "origin", type: "address", value: t }], n)) { var i = { account_name: it(e), owner_address: nt(t) }; r && r.permissionId && (i.Permission_id = r.permissionId), this.tronWeb.fullNode.request("wallet/updateaccount", i, "post").then((function (e) { return at(e, n) })).catch((function (e) { return n(e) })) } } }, { key: "setAccountId", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !r) return this.injectPromise(this.setAccountId, e, t); e && Re.isString(e) && e.startsWith("0x") && (e = e.slice(2)), this.validator.notValid([{ name: "accountId", type: "hex", value: e }, { name: "accountId", type: "string", lte: 32, gte: 8, value: e }, { name: "origin", type: "address", value: t }], r) || this.tronWeb.fullNode.request("wallet/setaccountid", { account_id: e, owner_address: nt(t) }, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } }, { key: "updateToken", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) ? (r = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (e = t, t = this.tronWeb.defaultAddress.hex), !r) return this.injectPromise(this.updateToken, e, t); var n = e, i = n.description, a = void 0 !== i && i, s = n.url, o = void 0 !== s && s, u = n.freeBandwidth, c = void 0 === u ? 0 : u, d = n.freeBandwidthLimit, l = void 0 === d ? 0 : d; if (!this.validator.notValid([{ name: "token description", type: "not-empty-string", value: a }, { name: "token url", type: "url", value: o }, { name: "issuer", type: "address", value: t }, { name: "Free bandwidth amount", type: "positive-integer", value: c }, { name: "Free bandwidth limit", type: "positive-integer", value: l }], r)) { var h = { owner_address: nt(t), description: it(a), url: it(o), new_limit: parseInt(c), new_public_limit: parseInt(l) }; e && e.permissionId && (h.Permission_id = e.permissionId), this.tronWeb.fullNode.request("wallet/updateasset", h, "post").then((function (e) { return at(e, r) })).catch((function (e) { return r(e) })) } } }, { key: "sendAsset", value: function () { return this.sendToken.apply(this, arguments) } }, { key: "purchaseAsset", value: function () { return this.purchaseToken.apply(this, arguments) } }, { key: "createAsset", value: function () { return this.createToken.apply(this, arguments) } }, { key: "updateAsset", value: function () { return this.updateToken.apply(this, arguments) } }, { key: "createProposal", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 ? arguments[2] : void 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; if (Re.isFunction(r) && (n = r, r = {}), Re.isFunction(t) ? (n = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !n) return this.injectPromise(this.createProposal, e, t, r); if (!this.validator.notValid([{ name: "issuer", type: "address", value: t }], n)) { var i = "Invalid proposal parameters provided"; if (!e) return n(i); Re.isArray(e) || (e = [e]); var a, s = tt(e); try { for (s.s(); !(a = s.n()).done;) { var o = a.value; if (!Re.isObject(o)) return n(i) } } catch (e) { s.e(e) } finally { s.f() } var u = { owner_address: nt(t), parameters: e }; r && r.permissionId && (u.Permission_id = r.permissionId), this.tronWeb.fullNode.request("wallet/proposalcreate", u, "post").then((function (e) { return at(e, n) })).catch((function (e) { return n(e) })) } } }, { key: "deleteProposal", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.tronWeb.defaultAddress.hex, r = arguments.length > 2 ? arguments[2] : void 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; if (Re.isFunction(r) && (n = r, r = {}), Re.isFunction(t) ? (n = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (r = t, t = this.tronWeb.defaultAddress.hex), !n) return this.injectPromise(this.deleteProposal, e, t, r); if (!this.validator.notValid([{ name: "issuer", type: "address", value: t }, { name: "proposalID", type: "integer", value: e, gte: 0 }], n)) { var i = { owner_address: nt(t), proposal_id: parseInt(e) }; r && r.permissionId && (i.Permission_id = r.permissionId), this.tronWeb.fullNode.request("wallet/proposaldelete", i, "post").then((function (e) { return at(e, n) })).catch((function (e) { return n(e) })) } } }, { key: "voteProposal", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this.tronWeb.defaultAddress.hex, n = arguments.length > 3 ? arguments[3] : void 0, i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = this.tronWeb.defaultAddress.hex) : Re.isObject(r) && (n = r, r = this.tronWeb.defaultAddress.hex), !i) return this.injectPromise(this.voteProposal, e, t, r, n); if (!this.validator.notValid([{ name: "voter", type: "address", value: r }, { name: "proposalID", type: "integer", value: e, gte: 0 }, { name: "has approval", type: "boolean", value: t }], i)) { var a = { owner_address: nt(r), proposal_id: parseInt(e), is_add_approval: t }; n && n.permissionId && (a.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/proposalapprove", a, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } }, { key: "createTRXExchange", value: function (e, t, r) { var n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 ? arguments[4] : void 0, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = {}), Re.isFunction(n) ? (a = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (i = n, n = this.tronWeb.defaultAddress.hex), !a) return this.injectPromise(this.createTRXExchange, e, t, r, n, i); if (!this.validator.notValid([{ name: "owner", type: "address", value: n }, { name: "token name", type: "not-empty-string", value: e }, { name: "token balance", type: "positive-integer", value: t }, { name: "trx balance", type: "positive-integer", value: r }], a)) { var s = { owner_address: nt(n), first_token_id: it(e), first_token_balance: t, second_token_id: "5f", second_token_balance: r }; i && i.permissionId && (s.Permission_id = i.permissionId), this.tronWeb.fullNode.request("wallet/exchangecreate", s, "post").then((function (e) { a(null, e) })).catch((function (e) { return a(e) })) } } }, { key: "createTokenExchange", value: function (e, t, r, n) { var i = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : this.tronWeb.defaultAddress.hex, a = arguments.length > 5 ? arguments[5] : void 0, s = arguments.length > 6 && void 0 !== arguments[6] && arguments[6]; if (Re.isFunction(a) && (s = a, a = {}), Re.isFunction(i) ? (s = i, i = this.tronWeb.defaultAddress.hex) : Re.isObject(i) && (a = i, i = this.tronWeb.defaultAddress.hex), !s) return this.injectPromise(this.createTokenExchange, e, t, r, n, i, a); if (!this.validator.notValid([{ name: "owner", type: "address", value: i }, { name: "first token name", type: "not-empty-string", value: e }, { name: "second token name", type: "not-empty-string", value: r }, { name: "first token balance", type: "positive-integer", value: t }, { name: "second token balance", type: "positive-integer", value: n }], s)) { var o = { owner_address: nt(i), first_token_id: it(e), first_token_balance: t, second_token_id: it(r), second_token_balance: n }; a && a.permissionId && (o.Permission_id = a.permissionId), this.tronWeb.fullNode.request("wallet/exchangecreate", o, "post").then((function (e) { s(null, e) })).catch((function (e) { return s(e) })) } } }, { key: "injectExchangeTokens", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 ? arguments[4] : void 0, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = {}), Re.isFunction(n) ? (a = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (i = n, n = this.tronWeb.defaultAddress.hex), !a) return this.injectPromise(this.injectExchangeTokens, e, t, r, n, i); if (!this.validator.notValid([{ name: "owner", type: "address", value: n }, { name: "token name", type: "not-empty-string", value: t }, { name: "token amount", type: "integer", value: r, gte: 1 }, { name: "exchangeID", type: "integer", value: e, gte: 0 }], a)) { var s = { owner_address: nt(n), exchange_id: parseInt(e), token_id: it(t), quant: parseInt(r) }; i && i.permissionId && (s.Permission_id = i.permissionId), this.tronWeb.fullNode.request("wallet/exchangeinject", s, "post").then((function (e) { return at(e, a) })).catch((function (e) { return a(e) })) } } }, { key: "withdrawExchangeTokens", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : this.tronWeb.defaultAddress.hex, i = arguments.length > 4 ? arguments[4] : void 0, a = arguments.length > 5 && void 0 !== arguments[5] && arguments[5]; if (Re.isFunction(i) && (a = i, i = {}), Re.isFunction(n) ? (a = n, n = this.tronWeb.defaultAddress.hex) : Re.isObject(n) && (i = n, n = this.tronWeb.defaultAddress.hex), !a) return this.injectPromise(this.withdrawExchangeTokens, e, t, r, n, i); if (!this.validator.notValid([{ name: "owner", type: "address", value: n }, { name: "token name", type: "not-empty-string", value: t }, { name: "token amount", type: "integer", value: r, gte: 1 }, { name: "exchangeID", type: "integer", value: e, gte: 0 }], a)) { var s = { owner_address: nt(n), exchange_id: parseInt(e), token_id: it(t), quant: parseInt(r) }; i && i.permissionId && (s.Permission_id = i.permissionId), this.tronWeb.fullNode.request("wallet/exchangewithdraw", s, "post").then((function (e) { return at(e, a) })).catch((function (e) { return a(e) })) } } }, { key: "tradeExchangeTokens", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0, i = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : this.tronWeb.defaultAddress.hex, a = arguments.length > 5 ? arguments[5] : void 0, s = arguments.length > 6 && void 0 !== arguments[6] && arguments[6]; if (Re.isFunction(a) && (s = a, a = {}), Re.isFunction(i) ? (s = i, i = this.tronWeb.defaultAddress.hex) : Re.isObject(i) && (a = i, i = this.tronWeb.defaultAddress.hex), !s) return this.injectPromise(this.tradeExchangeTokens, e, t, r, n, i, a); if (!this.validator.notValid([{ name: "owner", type: "address", value: i }, { name: "token name", type: "not-empty-string", value: t }, { name: "tokenAmountSold", type: "integer", value: r, gte: 1 }, { name: "tokenAmountExpected", type: "integer", value: n, gte: 1 }, { name: "exchangeID", type: "integer", value: e, gte: 0 }], s)) { var o = { owner_address: nt(i), exchange_id: parseInt(e), token_id: this.tronWeb.fromAscii(t), quant: parseInt(r), expected: parseInt(n) }; a && a.permissionId && (o.Permission_id = a.permissionId), this.tronWeb.fullNode.request("wallet/exchangetransaction", o, "post").then((function (e) { return at(e, s) })).catch((function (e) { return s(e) })) } } }, { key: "updateSetting", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this.tronWeb.defaultAddress.hex, n = arguments.length > 3 ? arguments[3] : void 0, i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = this.tronWeb.defaultAddress.hex) : Re.isObject(r) && (n = r, r = this.tronWeb.defaultAddress.hex), !i) return this.injectPromise(this.updateSetting, e, t, r, n); if (!this.validator.notValid([{ name: "owner", type: "address", value: r }, { name: "contract", type: "address", value: e }, { name: "userFeePercentage", type: "integer", value: t, gte: 0, lte: 100 }], i)) { var a = { owner_address: nt(r), contract_address: nt(e), consume_user_resource_percent: t }; n && n.permissionId && (a.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/updatesetting", a, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } }, { key: "updateEnergyLimit", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this.tronWeb.defaultAddress.hex, n = arguments.length > 3 ? arguments[3] : void 0, i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = {}), Re.isFunction(r) ? (i = r, r = this.tronWeb.defaultAddress.hex) : Re.isObject(r) && (n = r, r = this.tronWeb.defaultAddress.hex), !i) return this.injectPromise(this.updateEnergyLimit, e, t, r, n); if (!this.validator.notValid([{ name: "owner", type: "address", value: r }, { name: "contract", type: "address", value: e }, { name: "originEnergyLimit", type: "integer", value: t, gte: 0, lte: 1e7 }], i)) { var a = { owner_address: nt(r), contract_address: nt(e), origin_energy_limit: t }; n && n.permissionId && (a.Permission_id = n.permissionId), this.tronWeb.fullNode.request("wallet/updateenergylimit", a, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } } }, { key: "checkPermissions", value: function (e, t) { if (e) { if (e.type !== t || !e.permission_name || !Re.isString(e.permission_name) || !Re.isInteger(e.threshold) || e.threshold < 1 || !e.keys) return !1; var r, n = tt(e.keys); try { for (n.s(); !(r = n.n()).done;) { var i = r.value; if (!this.tronWeb.isAddress(i.address) || !Re.isInteger(i.weight) || i.weight > e.threshold || i.weight < 1 || 2 === t && !e.operations) return !1 } } catch (e) { n.e(e) } finally { n.f() } } return !0 } }, { key: "updateAccountPermissions", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3], i = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (Re.isFunction(n) && (i = n, n = !1), Re.isFunction(r) && (i = r, r = n = !1), Re.isFunction(t) && (i = t, t = r = n = !1), !i) return this.injectPromise(this.updateAccountPermissions, e, t, r, n); if (!this.tronWeb.isAddress(e)) return i("Invalid ownerAddress provided"); if (!this.checkPermissions(t, 0)) return i("Invalid ownerPermissions provided"); if (!this.checkPermissions(r, 1)) return i("Invalid witnessPermissions provided"); Array.isArray(n) || (n = [n]); var a, s = tt(n); try { for (s.s(); !(a = s.n()).done;) { var o = a.value; if (!this.checkPermissions(o, 2)) return i("Invalid activesPermissions provided") } } catch (e) { s.e(e) } finally { s.f() } var u = { owner_address: e }; t && (u.owner = t), r && (u.witness = r), n && (u.actives = 1 === n.length ? n[0] : n), this.tronWeb.fullNode.request("wallet/accountpermissionupdate", u, "post").then((function (e) { return at(e, i) })).catch((function (e) { return i(e) })) } }, { key: "newTxID", value: (i = l()(N.a.mark((function e(t, r) { return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (r) { e.next = 2; break } return e.abrupt("return", this.injectPromise(this.newTxID, t)); case 2: this.tronWeb.fullNode.request("wallet/getsignweight", t, "post").then((function (e) { e = e.transaction.transaction, "boolean" == typeof t.visible && (e.visible = t.visible), r(null, e) })).catch((function (e) { return r("Error generating a new transaction id.") })); case 3: case "end": return e.stop() } }), e, this) }))), function (e, t) { return i.apply(this, arguments) }) }, { key: "alterTransaction", value: (n = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (r = i.length > 1 && void 0 !== i[1] ? i[1] : {}, n = i.length > 2 && void 0 !== i[2] && i[2]) { e.next = 4; break } return e.abrupt("return", this.injectPromise(this.alterTransaction, t, r)); case 4: if (!t.signature) { e.next = 6; break } return e.abrupt("return", n("You can not extend the expiration of a signed transaction.")); case 6: if (!r.data) { e.next = 12; break } if ("hex" !== r.dataFormat && (r.data = this.tronWeb.toHex(r.data)), r.data = r.data.replace(/^0x/, ""), 0 !== r.data.length) { e.next = 11; break } return e.abrupt("return", n("Invalid data provided")); case 11: t.raw_data.data = r.data; case 12: if (!r.extension) { e.next = 17; break } if (r.extension = parseInt(1e3 * r.extension), !(isNaN(r.extension) || t.raw_data.expiration + r.extension <= Date.now() + 3e3)) { e.next = 16; break } return e.abrupt("return", n("Invalid extension provided")); case 16: t.raw_data.expiration += r.extension; case 17: this.newTxID(t, n); case 18: case "end": return e.stop() } }), e, this) }))), function (e) { return n.apply(this, arguments) }) }, { key: "extendExpiration", value: (r = l()(N.a.mark((function e(t, r) { var n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (n = i.length > 2 && void 0 !== i[2] && i[2]) { e.next = 3; break } return e.abrupt("return", this.injectPromise(this.extendExpiration, t, r)); case 3: this.alterTransaction(t, { extension: r }, n); case 4: case "end": return e.stop() } }), e, this) }))), function (e, t) { return r.apply(this, arguments) }) }, { key: "addUpdateData", value: (t = l()(N.a.mark((function e(t, r) { var n, i, a = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (n = a.length > 2 && void 0 !== a[2] ? a[2] : "utf8", i = a.length > 3 && void 0 !== a[3] && a[3], Re.isFunction(n) && (i = n, n = "utf8"), i) { e.next = 5; break } return e.abrupt("return", this.injectPromise(this.addUpdateData, t, r, n)); case 5: this.alterTransaction(t, { data: r, dataFormat: n }, i); case 6: case "end": return e.stop() } }), e, this) }))), function (e, r) { return t.apply(this, arguments) }) }]), e }(); function ot(e, t) { var r = Object.keys(e); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); t && (n = n.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), r.push.apply(r, n) } return r } function ut(e) { for (var t = 1; t < arguments.length; t++) { var r = null != arguments[t] ? arguments[t] : {}; t % 2 ? ot(Object(r), !0).forEach((function (t) { _()(e, t, r[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : ot(Object(r)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t)) })) } return e } function ct(e) { return Pt.address.toHex(e) } var dt = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (p()(this, e), !t || !t instanceof Pt) throw new Error("Expected instance of TronWeb"); this.tronWeb = t, this.injectPromise = Je()(this), this.cache = { contracts: {} }, this.validator = new et(t) } var t, r, n, i, a, s, o, u, c, d, h, f, v, g, m, y, k; return b()(e, [{ key: "_parseToken", value: function (e) { return ut(ut({}, e), {}, { name: this.tronWeb.toUtf8(e.name), abbr: e.abbr && this.tronWeb.toUtf8(e.abbr), description: e.description && this.tronWeb.toUtf8(e.description), url: e.url && this.tronWeb.toUtf8(e.url) }) } }, { key: "getCurrentBlock", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.getCurrentBlock); this.tronWeb.fullNode.request("wallet/getnowblock").then((function (t) { e(null, t) })).catch((function (t) { return e(t) })) } }, { key: "getConfirmedCurrentBlock", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.getConfirmedCurrentBlock); this.tronWeb.solidityNode.request("walletsolidity/getnowblock").then((function (t) { e(null, t) })).catch((function (t) { return e(t) })) } }, { key: "getBlock", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultBlock, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return Re.isFunction(e) && (t = e, e = this.tronWeb.defaultBlock), t ? !1 === e ? t("No block identifier provided") : ("earliest" == e && (e = 0), "latest" == e ? this.getCurrentBlock(t) : isNaN(e) && Re.isHex(e) ? this.getBlockByHash(e, t) : void this.getBlockByNumber(e, t)) : this.injectPromise(this.getBlock, e) } }, { key: "getBlockByHash", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!t) return this.injectPromise(this.getBlockByHash, e); this.tronWeb.fullNode.request("wallet/getblockbyid", { value: e }, "post").then((function (e) { if (!Object.keys(e).length) return t("Block not found"); t(null, e) })).catch((function (e) { return t(e) })) } }, { key: "getBlockByNumber", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return t ? !Re.isInteger(e) || e < 0 ? t("Invalid block number provided") : void this.tronWeb.fullNode.request("wallet/getblockbynum", { num: parseInt(e) }, "post").then((function (e) { if (!Object.keys(e).length) return t("Block not found"); t(null, e) })).catch((function (e) { return t(e) })) : this.injectPromise(this.getBlockByNumber, e) } }, { key: "getBlockTransactionCount", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultBlock, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (Re.isFunction(e) && (t = e, e = this.tronWeb.defaultBlock), !t) return this.injectPromise(this.getBlockTransactionCount, e); this.getBlock(e).then((function (e) { var r = e.transactions; t(null, (void 0 === r ? [] : r).length) })).catch((function (e) { return t(e) })) } }, { key: "getTransactionFromBlock", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultBlock, t = arguments.length > 1 ? arguments[1] : void 0, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = 0), Re.isFunction(e) && (r = e, e = this.tronWeb.defaultBlock), !r) return this.injectPromise(this.getTransactionFromBlock, e, t); this.getBlock(e).then((function (e) { var n = e.transactions, i = void 0 !== n && n; i ? "number" == typeof t ? t >= 0 && t < i.length ? r(null, i[t]) : r("Invalid transaction index provided") : r(null, i) : r("Transaction not found in block") })).catch((function (e) { return r(e) })) } }, { key: "getTransaction", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!t) return this.injectPromise(this.getTransaction, e); this.tronWeb.fullNode.request("wallet/gettransactionbyid", { value: e }, "post").then((function (e) { if (!Object.keys(e).length) return t("Transaction not found"); t(null, e) })).catch((function (e) { return t(e) })) } }, { key: "getConfirmedTransaction", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!t) return this.injectPromise(this.getConfirmedTransaction, e); this.tronWeb.solidityNode.request("walletsolidity/gettransactionbyid", { value: e }, "post").then((function (e) { if (!Object.keys(e).length) return t("Transaction not found"); t(null, e) })).catch((function (e) { return t(e) })) } }, { key: "getUnconfirmedTransactionInfo", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return this._getTransactionInfoById(e, { confirmed: !1 }, t) } }, { key: "getTransactionInfo", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return this._getTransactionInfoById(e, { confirmed: !0 }, t) } }, { key: "_getTransactionInfoById", value: function (e, t) { var r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (!r) return this.injectPromise(this._getTransactionInfoById, e, t); this.tronWeb[t.confirmed ? "solidityNode" : "fullNode"].request("wallet".concat(t.confirmed ? "solidity" : "", "/gettransactioninfobyid"), { value: e }, "post").then((function (e) { r(null, e) })).catch((function (e) { return r(e) })) } }, { key: "getTransactionsToAddress", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 30, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; return Re.isFunction(r) && (n = r, r = 0), Re.isFunction(t) && (n = t, t = 30), n ? (e = this.tronWeb.address.toHex(e), this.getTransactionsRelated(e, "to", t, r, n)) : this.injectPromise(this.getTransactionsToAddress, e, t, r) } }, { key: "getTransactionsFromAddress", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 30, r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, n = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; return Re.isFunction(r) && (n = r, r = 0), Re.isFunction(t) && (n = t, t = 30), n ? (e = this.tronWeb.address.toHex(e), this.getTransactionsRelated(e, "from", t, r, n)) : this.injectPromise(this.getTransactionsFromAddress, e, t, r) } }, { key: "getTransactionsRelated", value: (k = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u, c, d = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = d.length > 0 && void 0 !== d[0] ? d[0] : this.tronWeb.defaultAddress.hex, r = d.length > 1 && void 0 !== d[1] ? d[1] : "all", n = d.length > 2 && void 0 !== d[2] ? d[2] : 30, i = d.length > 3 && void 0 !== d[3] ? d[3] : 0, a = d.length > 4 && void 0 !== d[4] && d[4], Re.isFunction(i) && (a = i, i = 0), Re.isFunction(n) && (a = n, n = 30), Re.isFunction(r) && (a = r, r = "all"), Re.isFunction(t) && (a = t, t = this.tronWeb.defaultAddress.hex), a) { e.next = 11; break } return e.abrupt("return", this.injectPromise(this.getTransactionsRelated, t, r, n, i)); case 11: if (["to", "from", "all"].includes(r)) { e.next = 13; break } return e.abrupt("return", a('Invalid direction provided: Expected "to", "from" or "all"')); case 13: if ("all" != r) { e.next = 27; break } return e.prev = 14, e.next = 17, Promise.all([this.getTransactionsRelated(t, "from", n, i), this.getTransactionsRelated(t, "to", n, i)]); case 17: return s = e.sent, o = $e()(s, 2), u = o[0], c = o[1], e.abrupt("return", a(null, [].concat(R()(u.map((function (e) { return e.direction = "from", e }))), R()(c.map((function (e) { return e.direction = "to", e })))).sort((function (e, t) { return t.raw_data.timestamp - e.raw_data.timestamp })))); case 24: return e.prev = 24, e.t0 = e.catch(14), e.abrupt("return", a(e.t0)); case 27: if (this.tronWeb.isAddress(t)) { e.next = 29; break } return e.abrupt("return", a("Invalid address provided")); case 29: if (!(!Re.isInteger(n) || n < 0 || i && n < 1)) { e.next = 31; break } return e.abrupt("return", a("Invalid limit provided")); case 31: if (Re.isInteger(i) && !(i < 0)) { e.next = 33; break } return e.abrupt("return", a("Invalid offset provided")); case 33: t = this.tronWeb.address.toHex(t), this.tronWeb.solidityNode.request("walletextension/gettransactions".concat(r, "this"), { account: { address: t }, offset: i, limit: n }, "post").then((function (e) { var t = e.transaction; a(null, t) })).catch((function (e) { return a(e) })); case 35: case "end": return e.stop() } }), e, this, [[14, 24]]) }))), function () { return k.apply(this, arguments) }) }, { key: "getAccount", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return Re.isFunction(e) && (t = e, e = this.tronWeb.defaultAddress.hex), t ? this.tronWeb.isAddress(e) ? (e = this.tronWeb.address.toHex(e), void this.tronWeb.solidityNode.request("walletsolidity/getaccount", { address: e }, "post").then((function (e) { t(null, e) })).catch((function (e) { return t(e) }))) : t("Invalid address provided") : this.injectPromise(this.getAccount, e) } }, { key: "getAccountById", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!t) return this.injectPromise(this.getAccountById, e); this.getAccountInfoById(e, { confirmed: !0 }, t) } }, { key: "getAccountInfoById", value: function (e, t, r) { this.validator.notValid([{ name: "accountId", type: "hex", value: e }, { name: "accountId", type: "string", lte: 32, gte: 8, value: e }], r) || (e.startsWith("0x") && (e = e.slice(2)), this.tronWeb[t.confirmed ? "solidityNode" : "fullNode"].request("wallet".concat(t.confirmed ? "solidity" : "", "/getaccountbyid"), { account_id: e }, "post").then((function (e) { r(null, e) })).catch((function (e) { return r(e) }))) } }, { key: "getBalance", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (Re.isFunction(e) && (t = e, e = this.tronWeb.defaultAddress.hex), !t) return this.injectPromise(this.getBalance, e); this.getAccount(e).then((function (e) { var r = e.balance; t(null, void 0 === r ? 0 : r) })).catch((function (e) { return t(e) })) } }, { key: "getUnconfirmedAccount", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return Re.isFunction(e) && (t = e, e = this.tronWeb.defaultAddress.hex), t ? this.tronWeb.isAddress(e) ? (e = this.tronWeb.address.toHex(e), void this.tronWeb.fullNode.request("wallet/getaccount", { address: e }, "post").then((function (e) { t(null, e) })).catch((function (e) { return t(e) }))) : t("Invalid address provided") : this.injectPromise(this.getUnconfirmedAccount, e) } }, { key: "getUnconfirmedAccountById", value: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!t) return this.injectPromise(this.getUnconfirmedAccountById, e); this.getAccountInfoById(e, { confirmed: !1 }, t) } }, { key: "getUnconfirmedBalance", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (Re.isFunction(e) && (t = e, e = this.tronWeb.defaultAddress.hex), !t) return this.injectPromise(this.getUnconfirmedBalance, e); this.getUnconfirmedAccount(e).then((function (e) { var r = e.balance; t(null, void 0 === r ? 0 : r) })).catch((function (e) { return t(e) })) } }, { key: "getBandwidth", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return Re.isFunction(e) && (t = e, e = this.tronWeb.defaultAddress.hex), t ? this.tronWeb.isAddress(e) ? (e = this.tronWeb.address.toHex(e), void this.tronWeb.fullNode.request("wallet/getaccountnet", { address: e }, "post").then((function (e) { var r = e.freeNetUsed, n = void 0 === r ? 0 : r, i = e.freeNetLimit, a = void 0 === i ? 0 : i, s = e.NetUsed, o = void 0 === s ? 0 : s, u = e.NetLimit; t(null, a - n + ((void 0 === u ? 0 : u) - o)) })).catch((function (e) { return t(e) }))) : t("Invalid address provided") : this.injectPromise(this.getBandwidth, e) } }, { key: "getTokensIssuedByAddress", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return Re.isFunction(t) && (r = t, t = this.tronWeb.defaultAddress.hex), r ? this.tronWeb.isAddress(t) ? (t = this.tronWeb.address.toHex(t), void this.tronWeb.fullNode.request("wallet/getassetissuebyaccount", { address: t }, "post").then((function (t) { var n = t.assetIssue, i = void 0 !== n && n; if (!i) return r(null, {}); var a = i.map((function (t) { return e._parseToken(t) })).reduce((function (e, t) { return e[t.name] = t, e }), {}); r(null, a) })).catch((function (e) { return r(e) }))) : r("Invalid address provided") : this.injectPromise(this.getTokensIssuedByAddress, t) } }, { key: "getTokenFromID", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return r ? (Re.isInteger(t) && (t = t.toString()), Re.isString(t) && t.length ? void this.tronWeb.fullNode.request("wallet/getassetissuebyname", { value: this.tronWeb.fromUtf8(t) }, "post").then((function (t) { if (!t.name) return r("Token does not exist"); r(null, e._parseToken(t)) })).catch((function (e) { return r(e) })) : r("Invalid token ID provided")) : this.injectPromise(this.getTokenFromID, t) } }, { key: "listNodes", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!t) return this.injectPromise(this.listNodes); this.tronWeb.fullNode.request("wallet/listnodes").then((function (r) { var n = r.nodes; t(null, (void 0 === n ? [] : n).map((function (t) { var r = t.address, n = r.host, i = r.port; return "".concat(e.tronWeb.toUtf8(n), ":").concat(i) }))) })).catch((function (e) { return t(e) })) } }, { key: "getBlockRange", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 30, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; return Re.isFunction(t) && (r = t, t = 30), Re.isFunction(e) && (r = e, e = 0), r ? !Re.isInteger(e) || e < 0 ? r("Invalid start of range provided") : !Re.isInteger(t) || t <= e ? r("Invalid end of range provided") : void this.tronWeb.fullNode.request("wallet/getblockbylimitnext", { startNum: parseInt(e), endNum: parseInt(t) + 1 }, "post").then((function (e) { var t = e.block; r(null, void 0 === t ? [] : t) })).catch((function (e) { return r(e) })) : this.injectPromise(this.getBlockRange, e, t) } }, { key: "listSuperRepresentatives", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.listSuperRepresentatives); this.tronWeb.fullNode.request("wallet/listwitnesses").then((function (t) { var r = t.witnesses; e(null, void 0 === r ? [] : r) })).catch((function (t) { return e(t) })) } }, { key: "listTokens", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; return Re.isFunction(r) && (n = r, r = 0), Re.isFunction(t) && (n = t, t = 0), n ? !Re.isInteger(t) || t < 0 || r && t < 1 ? n("Invalid limit provided") : !Re.isInteger(r) || r < 0 ? n("Invalid offset provided") : t ? void this.tronWeb.fullNode.request("wallet/getpaginatedassetissuelist", { offset: parseInt(r), limit: parseInt(t) }, "post").then((function (t) { var r = t.assetIssue; n(null, (void 0 === r ? [] : r).map((function (t) { return e._parseToken(t) }))) })).catch((function (e) { return n(e) })) : this.tronWeb.fullNode.request("wallet/getassetissuelist").then((function (t) { var r = t.assetIssue; n(null, (void 0 === r ? [] : r).map((function (t) { return e._parseToken(t) }))) })).catch((function (e) { return n(e) })) : this.injectPromise(this.listTokens, t, r) } }, { key: "timeUntilNextVoteCycle", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.timeUntilNextVoteCycle); this.tronWeb.fullNode.request("wallet/getnextmaintenancetime").then((function (t) { var r = t.num, n = void 0 === r ? -1 : r; if (-1 == n) return e("Failed to get time until next vote cycle"); e(null, Math.floor(n / 1e3)) })).catch((function (t) { return e(t) })) } }, { key: "getContract", value: function (e) { var t = this, r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return r ? this.tronWeb.isAddress(e) ? void (this.cache.contracts[e] ? r(null, this.cache.contracts[e]) : (e = this.tronWeb.address.toHex(e), this.tronWeb.fullNode.request("wallet/getcontract", { value: e }).then((function (n) { if (n.Error) return r("Contract does not exist"); t.cache.contracts[e] = n, r(null, n) })).catch((function (e) { return r(e) })))) : r("Invalid contract address provided") : this.injectPromise(this.getContract, e) } }, { key: "verifyMessage", value: (y = l()(N.a.mark((function t() { var r, n, i, a, s, o = arguments; return N.a.wrap((function (t) { for (; ;)switch (t.prev = t.next) { case 0: if (r = o.length > 0 && void 0 !== o[0] && o[0], n = o.length > 1 && void 0 !== o[1] && o[1], i = o.length > 2 && void 0 !== o[2] ? o[2] : this.tronWeb.defaultAddress.base58, a = !(o.length > 3 && void 0 !== o[3]) || o[3], s = o.length > 4 && void 0 !== o[4] && o[4], Re.isFunction(i) && (s = i, i = this.tronWeb.defaultAddress.base58, a = !0), Re.isFunction(a) && (s = a, a = !0), s) { t.next = 9; break } return t.abrupt("return", this.injectPromise(this.verifyMessage, r, n, i, a)); case 9: if (Re.isHex(r)) { t.next = 11; break } return t.abrupt("return", s("Expected hex message input")); case 11: if (!e.verifySignature(r, i, n, a)) { t.next = 13; break } return t.abrupt("return", s(null, !0)); case 13: s("Signature does not match"); case 14: case "end": return t.stop() } }), t, this) }))), function () { return y.apply(this, arguments) }) }, { key: "sign", value: (m = l()(N.a.mark((function t() { var r, n, i, a, s, o, u = arguments; return N.a.wrap((function (t) { for (; ;)switch (t.prev = t.next) { case 0: if (r = u.length > 0 && void 0 !== u[0] && u[0], n = u.length > 1 && void 0 !== u[1] ? u[1] : this.tronWeb.defaultPrivateKey, i = !(u.length > 2 && void 0 !== u[2]) || u[2], a = u.length > 3 && void 0 !== u[3] && u[3], s = u.length > 4 && void 0 !== u[4] && u[4], Re.isFunction(a) && (s = a, a = !1), Re.isFunction(i) && (s = i, i = !0, a = !1), Re.isFunction(n) && (s = n, n = this.tronWeb.defaultPrivateKey, i = !0, a = !1), s) { t.next = 10; break } return t.abrupt("return", this.injectPromise(this.sign, r, n, i, a)); case 10: if (!Re.isString(r)) { t.next = 21; break } if (Re.isHex(r)) { t.next = 13; break } return t.abrupt("return", s("Expected hex message input")); case 13: return t.prev = 13, o = e.signString(r, n, i), t.abrupt("return", s(null, o)); case 18: t.prev = 18, t.t0 = t.catch(13), s(t.t0); case 21: if (Re.isObject(r)) { t.next = 23; break } return t.abrupt("return", s("Invalid transaction provided")); case 23: if (a || !r.signature) { t.next = 25; break } return t.abrupt("return", s("Transaction is already signed")); case 25: if (t.prev = 25, a) { t.next = 30; break } if (this.tronWeb.address.toHex(this.tronWeb.address.fromPrivateKey(n)).toLowerCase() === this.tronWeb.address.toHex(r.raw_data.contract[0].parameter.value.owner_address)) { t.next = 30; break } return t.abrupt("return", s("Private key does not match address in transaction")); case 30: return t.abrupt("return", s(null, Re.crypto.signTransaction(n, r))); case 33: t.prev = 33, t.t1 = t.catch(25), s(t.t1); case 36: case "end": return t.stop() } }), t, this, [[13, 18], [25, 33]]) }))), function () { return m.apply(this, arguments) }) }, { key: "multiSign", value: (g = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = u.length > 0 && void 0 !== u[0] && u[0], r = u.length > 1 && void 0 !== u[1] ? u[1] : this.tronWeb.defaultPrivateKey, n = u.length > 2 && void 0 !== u[2] && u[2], i = u.length > 3 && void 0 !== u[3] && u[3], Re.isFunction(n) && (i = n, n = 0), Re.isFunction(r) && (i = r, r = this.tronWeb.defaultPrivateKey, n = 0), i) { e.next = 8; break } return e.abrupt("return", this.injectPromise(this.multiSign, t, r, n)); case 8: if (Re.isObject(t) && t.raw_data && t.raw_data.contract) { e.next = 10; break } return e.abrupt("return", i("Invalid transaction provided")); case 10: if (t.raw_data.contract[0].Permission_id || !(n > 0)) { e.next = 30; break } return t.raw_data.contract[0].Permission_id = n, a = this.tronWeb.address.toHex(this.tronWeb.address.fromPrivateKey(r)).toLowerCase(), e.next = 15, this.getSignWeight(t, n); case 15: if ("PERMISSION_ERROR" !== (s = e.sent).result.code) { e.next = 18; break } return e.abrupt("return", i(s.result.message)); case 18: if (o = !1, s.permission.keys.map((function (e) { e.address === a && (o = !0) })), o) { e.next = 22; break } return e.abrupt("return", i(r + " has no permission to sign")); case 22: if (!s.approved_list || -1 == s.approved_list.indexOf(a)) { e.next = 24; break } return e.abrupt("return", i(r + " already sign transaction")); case 24: if (!s.transaction || !s.transaction.transaction) { e.next = 29; break } t = s.transaction.transaction, n > 0 && (t.raw_data.contract[0].Permission_id = n), e.next = 30; break; case 29: return e.abrupt("return", i("Invalid transaction provided")); case 30: return e.prev = 30, e.abrupt("return", i(null, Re.crypto.signTransaction(r, t))); case 34: e.prev = 34, e.t0 = e.catch(30), i(e.t0); case 37: case "end": return e.stop() } }), e, this, [[30, 34]]) }))), function () { return g.apply(this, arguments) }) }, { key: "getApprovedList", value: (v = l()(N.a.mark((function e(t) { var r, n = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (r = n.length > 1 && void 0 !== n[1] && n[1]) { e.next = 3; break } return e.abrupt("return", this.injectPromise(this.getApprovedList, t)); case 3: if (Re.isObject(t)) { e.next = 5; break } return e.abrupt("return", r("Invalid transaction provided")); case 5: this.tronWeb.fullNode.request("wallet/getapprovedlist", t, "post").then((function (e) { r(null, e) })).catch((function (e) { return r(e) })); case 6: case "end": return e.stop() } }), e, this) }))), function (e) { return v.apply(this, arguments) }) }, { key: "getSignWeight", value: (f = l()(N.a.mark((function e(t, r) { var n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (n = i.length > 2 && void 0 !== i[2] && i[2], Re.isFunction(r) && (n = r, r = void 0), n) { e.next = 4; break } return e.abrupt("return", this.injectPromise(this.getSignWeight, t, r)); case 4: if (Re.isObject(t) && t.raw_data && t.raw_data.contract) { e.next = 6; break } return e.abrupt("return", n("Invalid transaction provided")); case 6: if (Re.isInteger(r) ? t.raw_data.contract[0].Permission_id = parseInt(r) : "number" != typeof t.raw_data.contract[0].Permission_id && (t.raw_data.contract[0].Permission_id = 0), Re.isObject(t)) { e.next = 9; break } return e.abrupt("return", n("Invalid transaction provided")); case 9: this.tronWeb.fullNode.request("wallet/getsignweight", t, "post").then((function (e) { n(null, e) })).catch((function (e) { return n(e) })); case 10: case "end": return e.stop() } }), e, this) }))), function (e, t) { return f.apply(this, arguments) }) }, { key: "sendRawTransaction", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; return Re.isFunction(t) && (r = t, t = {}), r ? Re.isObject(e) ? Re.isObject(t) ? e.signature && Re.isArray(e.signature) ? void this.tronWeb.fullNode.request("wallet/broadcasttransaction", e, "post").then((function (t) { t.result && (t.transaction = e), r(null, t) })).catch((function (e) { return r(e) })) : r("Transaction is not signed") : r("Invalid options provided") : r("Invalid transaction provided") : this.injectPromise(this.sendRawTransaction, e, t) } }, { key: "sendHexTransaction", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = {}), !r) return this.injectPromise(this.sendHexTransaction, e, t); if (!Re.isHex(e)) return r("Invalid hex transaction provided"); if (!Re.isObject(t)) return r("Invalid options provided"); var n = { transaction: e }; this.tronWeb.fullNode.request("wallet/broadcasthex", n, "post").then((function (t) { t.result && (t.transaction = JSON.parse(t.transaction), t.hexTransaction = e), r(null, t) })).catch((function (e) { return r(e) })) } }, { key: "sendTransaction", value: (h = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = c.length > 0 && void 0 !== c[0] && c[0], r = c.length > 1 && void 0 !== c[1] && c[1], n = c.length > 2 && void 0 !== c[2] ? c[2] : {}, i = c.length > 3 && void 0 !== c[3] && c[3], Re.isFunction(n) && (i = n, n = {}), "string" == typeof n && (n = { privateKey: n }), i) { e.next = 8; break } return e.abrupt("return", this.injectPromise(this.sendTransaction, t, r, n)); case 8: if (this.tronWeb.isAddress(t)) { e.next = 10; break } return e.abrupt("return", i("Invalid recipient provided")); case 10: if (Re.isInteger(r) && !(r <= 0)) { e.next = 12; break } return e.abrupt("return", i("Invalid amount provided")); case 12: if ((n = ut({ privateKey: this.tronWeb.defaultPrivateKey, address: this.tronWeb.defaultAddress.hex }, n)).privateKey || n.address) { e.next = 15; break } return e.abrupt("return", i("Function requires either a private key or address to be set")); case 15: return e.prev = 15, a = n.privateKey ? this.tronWeb.address.fromPrivateKey(n.privateKey) : n.address, e.next = 19, this.tronWeb.transactionBuilder.sendTrx(t, r, a); case 19: return s = e.sent, e.next = 22, this.sign(s, n.privateKey || void 0); case 22: return o = e.sent, e.next = 25, this.sendRawTransaction(o); case 25: return u = e.sent, e.abrupt("return", i(null, u)); case 29: return e.prev = 29, e.t0 = e.catch(15), e.abrupt("return", i(e.t0)); case 32: case "end": return e.stop() } }), e, this, [[15, 29]]) }))), function () { return h.apply(this, arguments) }) }, { key: "sendToken", value: (d = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u, c, d = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = d.length > 0 && void 0 !== d[0] && d[0], r = d.length > 1 && void 0 !== d[1] && d[1], n = d.length > 2 && void 0 !== d[2] && d[2], i = d.length > 3 && void 0 !== d[3] ? d[3] : {}, a = d.length > 4 && void 0 !== d[4] && d[4], Re.isFunction(i) && (a = i, i = {}), "string" == typeof i && (i = { privateKey: i }), a) { e.next = 9; break } return e.abrupt("return", this.injectPromise(this.sendToken, t, r, n, i)); case 9: if (this.tronWeb.isAddress(t)) { e.next = 11; break } return e.abrupt("return", a("Invalid recipient provided")); case 11: if (Re.isInteger(r) && !(r <= 0)) { e.next = 13; break } return e.abrupt("return", a("Invalid amount provided")); case 13: if (Re.isInteger(n) && (n = n.toString()), Re.isString(n)) { e.next = 16; break } return e.abrupt("return", a("Invalid token ID provided")); case 16: if ((i = ut({ privateKey: this.tronWeb.defaultPrivateKey, address: this.tronWeb.defaultAddress.hex }, i)).privateKey || i.address) { e.next = 19; break } return e.abrupt("return", a("Function requires either a private key or address to be set")); case 19: return e.prev = 19, s = i.privateKey ? this.tronWeb.address.fromPrivateKey(i.privateKey) : i.address, e.next = 23, this.tronWeb.transactionBuilder.sendToken(t, r, n, s); case 23: return o = e.sent, e.next = 26, this.sign(o, i.privateKey || void 0); case 26: return u = e.sent, e.next = 29, this.sendRawTransaction(u); case 29: return c = e.sent, e.abrupt("return", a(null, c)); case 33: return e.prev = 33, e.t0 = e.catch(19), e.abrupt("return", a(e.t0)); case 36: case "end": return e.stop() } }), e, this, [[19, 33]]) }))), function () { return d.apply(this, arguments) }) }, { key: "freezeBalance", value: (c = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u, c, d, l = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = l.length > 0 && void 0 !== l[0] ? l[0] : 0, r = l.length > 1 && void 0 !== l[1] ? l[1] : 3, n = l.length > 2 && void 0 !== l[2] ? l[2] : "BANDWIDTH", i = l.length > 3 && void 0 !== l[3] ? l[3] : {}, a = l.length > 4 && void 0 !== l[4] ? l[4] : void 0, s = l.length > 5 && void 0 !== l[5] && l[5], Re.isFunction(a) && (s = a, a = void 0), Re.isFunction(r) && (s = r, r = 3), Re.isFunction(n) && (s = n, n = "BANDWIDTH"), Re.isFunction(i) && (s = i, i = {}), "string" == typeof i && (i = { privateKey: i }), s) { e.next = 13; break } return e.abrupt("return", this.injectPromise(this.freezeBalance, t, r, n, i, a)); case 13: if (["BANDWIDTH", "ENERGY"].includes(n)) { e.next = 15; break } return e.abrupt("return", s('Invalid resource provided: Expected "BANDWIDTH" or "ENERGY"')); case 15: if (Re.isInteger(t) && !(t <= 0)) { e.next = 17; break } return e.abrupt("return", s("Invalid amount provided")); case 17: if (Re.isInteger(r) && !(r < 3)) { e.next = 19; break } return e.abrupt("return", s("Invalid duration provided, minimum of 3 days")); case 19: if ((i = ut({ privateKey: this.tronWeb.defaultPrivateKey, address: this.tronWeb.defaultAddress.hex }, i)).privateKey || i.address) { e.next = 22; break } return e.abrupt("return", s("Function requires either a private key or address to be set")); case 22: return e.prev = 22, o = i.privateKey ? this.tronWeb.address.fromPrivateKey(i.privateKey) : i.address, e.next = 26, this.tronWeb.transactionBuilder.freezeBalance(t, r, n, o, a); case 26: return u = e.sent, e.next = 29, this.sign(u, i.privateKey || void 0); case 29: return c = e.sent, e.next = 32, this.sendRawTransaction(c); case 32: return d = e.sent, e.abrupt("return", s(null, d)); case 36: return e.prev = 36, e.t0 = e.catch(22), e.abrupt("return", s(e.t0)); case 39: case "end": return e.stop() } }), e, this, [[22, 36]]) }))), function () { return c.apply(this, arguments) }) }, { key: "unfreezeBalance", value: (u = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = c.length > 0 && void 0 !== c[0] ? c[0] : "BANDWIDTH", r = c.length > 1 && void 0 !== c[1] ? c[1] : {}, n = c.length > 2 && void 0 !== c[2] ? c[2] : void 0, i = c.length > 3 && void 0 !== c[3] && c[3], Re.isFunction(n) && (i = n, n = void 0), Re.isFunction(t) && (i = t, t = "BANDWIDTH"), Re.isFunction(r) && (i = r, r = {}), "string" == typeof r && (r = { privateKey: r }), i) { e.next = 10; break } return e.abrupt("return", this.injectPromise(this.unfreezeBalance, t, r, n)); case 10: if (["BANDWIDTH", "ENERGY"].includes(t)) { e.next = 12; break } return e.abrupt("return", i('Invalid resource provided: Expected "BANDWIDTH" or "ENERGY"')); case 12: if ((r = ut({ privateKey: this.tronWeb.defaultPrivateKey, address: this.tronWeb.defaultAddress.hex }, r)).privateKey || r.address) { e.next = 15; break } return e.abrupt("return", i("Function requires either a private key or address to be set")); case 15: return e.prev = 15, a = r.privateKey ? this.tronWeb.address.fromPrivateKey(r.privateKey) : r.address, e.next = 19, this.tronWeb.transactionBuilder.unfreezeBalance(t, a, n); case 19: return s = e.sent, e.next = 22, this.sign(s, r.privateKey || void 0); case 22: return o = e.sent, e.next = 25, this.sendRawTransaction(o); case 25: return u = e.sent, e.abrupt("return", i(null, u)); case 29: return e.prev = 29, e.t0 = e.catch(15), e.abrupt("return", i(e.t0)); case 32: case "end": return e.stop() } }), e, this, [[15, 29]]) }))), function () { return u.apply(this, arguments) }) }, { key: "updateAccount", value: (o = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = u.length > 0 && void 0 !== u[0] && u[0], r = u.length > 1 && void 0 !== u[1] ? u[1] : {}, n = u.length > 2 && void 0 !== u[2] && u[2], Re.isFunction(r) && (n = r, r = {}), "string" == typeof r && (r = { privateKey: r }), n) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.updateAccount, t, r)); case 7: if (Re.isString(t) && t.length) { e.next = 9; break } return e.abrupt("return", n("Name must be a string")); case 9: if ((r = ut({ privateKey: this.tronWeb.defaultPrivateKey, address: this.tronWeb.defaultAddress.hex }, r)).privateKey || r.address) { e.next = 12; break } return e.abrupt("return", n("Function requires either a private key or address to be set")); case 12: return e.prev = 12, i = r.privateKey ? this.tronWeb.address.fromPrivateKey(r.privateKey) : r.address, e.next = 16, this.tronWeb.transactionBuilder.updateAccount(t, i); case 16: return a = e.sent, e.next = 19, this.sign(a, r.privateKey || void 0); case 19: return s = e.sent, e.next = 22, this.sendRawTransaction(s); case 22: return o = e.sent, e.abrupt("return", n(null, o)); case 26: return e.prev = 26, e.t0 = e.catch(12), e.abrupt("return", n(e.t0)); case 29: case "end": return e.stop() } }), e, this, [[12, 26]]) }))), function () { return o.apply(this, arguments) }) }, { key: "signMessage", value: function () { return this.sign.apply(this, arguments) } }, { key: "sendAsset", value: function () { return this.sendToken.apply(this, arguments) } }, { key: "send", value: function () { return this.sendTransaction.apply(this, arguments) } }, { key: "sendTrx", value: function () { return this.sendTransaction.apply(this, arguments) } }, { key: "broadcast", value: function () { return this.sendRawTransaction.apply(this, arguments) } }, { key: "broadcastHex", value: function () { return this.sendHexTransaction.apply(this, arguments) } }, { key: "signTransaction", value: function () { return this.sign.apply(this, arguments) } }, { key: "getProposal", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return t ? !Re.isInteger(e) || e < 0 ? t("Invalid proposalID provided") : void this.tronWeb.fullNode.request("wallet/getproposalbyid", { id: parseInt(e) }, "post").then((function (e) { t(null, e) })).catch((function (e) { return t(e) })) : this.injectPromise(this.getProposal, e) } }, { key: "listProposals", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.listProposals); this.tronWeb.fullNode.request("wallet/listproposals", {}, "post").then((function (t) { var r = t.proposals; e(null, void 0 === r ? [] : r) })).catch((function (t) { return e(t) })) } }, { key: "getChainParameters", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.getChainParameters); this.tronWeb.fullNode.request("wallet/getchainparameters", {}, "post").then((function (t) { var r = t.chainParameter; e(null, void 0 === r ? [] : r) })).catch((function (t) { return e(t) })) } }, { key: "getAccountResources", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.tronWeb.defaultAddress.hex, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return t ? this.tronWeb.isAddress(e) ? void this.tronWeb.fullNode.request("wallet/getaccountresource", { address: this.tronWeb.address.toHex(e) }, "post").then((function (e) { t(null, e) })).catch((function (e) { return t(e) })) : t("Invalid address provided") : this.injectPromise(this.getAccountResources, e) } }, { key: "getExchangeByID", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return t ? !Re.isInteger(e) || e < 0 ? t("Invalid exchangeID provided") : void this.tronWeb.fullNode.request("wallet/getexchangebyid", { id: e }, "post").then((function (e) { t(null, e) })).catch((function (e) { return t(e) })) : this.injectPromise(this.getExchangeByID, e) } }, { key: "listExchanges", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.listExchanges); this.tronWeb.fullNode.request("wallet/listexchanges", {}, "post").then((function (t) { var r = t.exchanges; e(null, void 0 === r ? [] : r) }), "post").catch((function (t) { return e(t) })) } }, { key: "listExchangesPaginated", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 10, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (Re.isFunction(t) && (r = t, t = 0), Re.isFunction(e) && (r = e, e = 10), !r) return this.injectPromise(this.listExchangesPaginated, e, t); this.tronWeb.fullNode.request("wallet/getpaginatedexchangelist", { limit: e, offset: t }, "post").then((function (e) { var t = e.exchanges; r(null, void 0 === t ? [] : t) })).catch((function (e) { return r(e) })) } }, { key: "getNodeInfo", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!e) return this.injectPromise(this.getNodeInfo); this.tronWeb.fullNode.request("wallet/getnodeinfo", {}, "post").then((function (t) { e(null, t) }), "post").catch((function (t) { return e(t) })) } }, { key: "getTokenListByName", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return r ? (Re.isInteger(t) && (t = t.toString()), Re.isString(t) && t.length ? void this.tronWeb.fullNode.request("wallet/getassetissuelistbyname", { value: this.tronWeb.fromUtf8(t) }, "post").then((function (t) { if (Array.isArray(t.assetIssue)) r(null, t.assetIssue.map((function (t) { return e._parseToken(t) }))); else if (!t.name) return r("Token does not exist"); r(null, e._parseToken(t)) })).catch((function (e) { return r(e) })) : r("Invalid token ID provided")) : this.injectPromise(this.getTokenListByName, t) } }, { key: "getTokenByID", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return r ? (Re.isInteger(t) && (t = t.toString()), Re.isString(t) && t.length ? void this.tronWeb.fullNode.request("wallet/getassetissuebyid", { value: t }, "post").then((function (t) { if (!t.name) return r("Token does not exist"); r(null, e._parseToken(t)) })).catch((function (e) { return r(e) })) : r("Invalid token ID provided")) : this.injectPromise(this.getTokenByID, t) } }, { key: "getReward", value: (s = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return r = i.length > 1 && void 0 !== i[1] ? i[1] : {}, n = i.length > 2 && void 0 !== i[2] && i[2], r.confirmed = !0, e.abrupt("return", this._getReward(t, r, n)); case 4: case "end": return e.stop() } }), e, this) }))), function (e) { return s.apply(this, arguments) }) }, { key: "getUnconfirmedReward", value: (a = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return r = i.length > 1 && void 0 !== i[1] ? i[1] : {}, n = i.length > 2 && void 0 !== i[2] && i[2], r.confirmed = !1, e.abrupt("return", this._getReward(t, r, n)); case 4: case "end": return e.stop() } }), e, this) }))), function (e) { return a.apply(this, arguments) }) }, { key: "getBrokerage", value: (i = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return r = i.length > 1 && void 0 !== i[1] ? i[1] : {}, n = i.length > 2 && void 0 !== i[2] && i[2], r.confirmed = !0, e.abrupt("return", this._getBrokerage(t, r, n)); case 4: case "end": return e.stop() } }), e, this) }))), function (e) { return i.apply(this, arguments) }) }, { key: "getUnconfirmedBrokerage", value: (n = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return r = i.length > 1 && void 0 !== i[1] ? i[1] : {}, n = i.length > 2 && void 0 !== i[2] && i[2], r.confirmed = !1, e.abrupt("return", this._getBrokerage(t, r, n)); case 4: case "end": return e.stop() } }), e, this) }))), function (e) { return n.apply(this, arguments) }) }, { key: "_getReward", value: (r = l()(N.a.mark((function e() { var t, r, n, i, a = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = a.length > 0 && void 0 !== a[0] ? a[0] : this.tronWeb.defaultAddress.hex, r = a.length > 1 ? a[1] : void 0, n = a.length > 2 && void 0 !== a[2] && a[2], Re.isFunction(r) && (n = r, r = {}), Re.isFunction(t) ? (n = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (r = t, t = this.tronWeb.defaultAddress.hex), n) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this._getReward, t, r)); case 7: if (!this.validator.notValid([{ name: "origin", type: "address", value: t }], n)) { e.next = 9; break } return e.abrupt("return"); case 9: i = { address: ct(t) }, this.tronWeb[r.confirmed ? "solidityNode" : "fullNode"].request("wallet".concat(r.confirmed ? "solidity" : "", "/getReward"), i, "post").then((function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; if (void 0 === e.reward) return n("Not found."); n(null, e.reward) })).catch((function (e) { return n(e) })); case 11: case "end": return e.stop() } }), e, this) }))), function () { return r.apply(this, arguments) }) }, { key: "_getBrokerage", value: (t = l()(N.a.mark((function e() { var t, r, n, i, a = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = a.length > 0 && void 0 !== a[0] ? a[0] : this.tronWeb.defaultAddress.hex, r = a.length > 1 ? a[1] : void 0, n = a.length > 2 && void 0 !== a[2] && a[2], Re.isFunction(r) && (n = r, r = {}), Re.isFunction(t) ? (n = t, t = this.tronWeb.defaultAddress.hex) : Re.isObject(t) && (r = t, t = this.tronWeb.defaultAddress.hex), n) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this._getBrokerage, t, r)); case 7: if (!this.validator.notValid([{ name: "origin", type: "address", value: t }], n)) { e.next = 9; break } return e.abrupt("return"); case 9: i = { address: ct(t) }, this.tronWeb[r.confirmed ? "solidityNode" : "fullNode"].request("wallet".concat(r.confirmed ? "solidity" : "", "/getBrokerage"), i, "post").then((function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; if (void 0 === e.brokerage) return n("Not found."); n(null, e.brokerage) })).catch((function (e) { return n(e) })); case 11: case "end": return e.stop() } }), e, this) }))), function () { return t.apply(this, arguments) }) }], [{ key: "verifySignature", value: function (e, t, r) { var n = !(arguments.length > 3 && void 0 !== arguments[3]) || arguments[3]; e = e.replace(/^0x/, ""), r = r.replace(/^0x/, ""); var i = [].concat(R()(se(n ? "TRON Signed Message:\n32" : "Ethereum Signed Message:\n32")), R()(Re.code.hexStr2byteArray(e))), a = ie(i), s = ue(a, { recoveryParam: "1c" == r.substring(128, 130) ? 1 : 0, r: "0x" + r.substring(0, 64), s: "0x" + r.substring(64, 128) }), o = "41" + s.substr(2), u = Pt.address.fromHex(o); return u == Pt.address.fromHex(t) } }, { key: "signString", value: function (e, t) { var r = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2]; e = e.replace(/^0x/, ""); var n = { toHexString: function () { return "0x" + t }, value: t }, i = new ce(n), a = [].concat(R()(se(r ? "TRON Signed Message:\n32" : "Ethereum Signed Message:\n32")), R()(Re.code.hexStr2byteArray(e))), s = ie(a), o = i.signDigest(s), u = ["0x", o.r.substring(2), o.s.substring(2), Number(o.v).toString(16)].join(""); return u } }]), e }(); function lt(e, t) { var r = Object.keys(e); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); t && (n = n.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), r.push.apply(r, n) } return r } function ht(e) { for (var t = 1; t < arguments.length; t++) { var r = null != arguments[t] ? arguments[t] : {}; t % 2 ? lt(Object(r), !0).forEach((function (t) { _()(e, t, r[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : lt(Object(r)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t)) })) } return e } var ft = function (e) { return e.map((function (e) { return e.type })) }, vt = function (e, t) { var r = e.map((function (e) { return e.name })).filter((function (e) { return !!e })), n = e.map((function (e) { return e.type })); return Re.abi.decodeParams(r, n, t) }, pt = function () { function e(t, r) { p()(this, e), this.tronWeb = t.tronWeb, this.contract = t, this.abi = r, this.name = r.name || (r.name = r.type), this.inputs = r.inputs || [], this.outputs = r.outputs || [], this.functionSelector = function (e) { return e.name + "(" + ft(e.inputs || []).join(",") + ")" }(r), this.signature = this.tronWeb.sha3(this.functionSelector, !1).slice(0, 8), this.injectPromise = Je()(this), this.defaultOptions = { feeLimit: this.tronWeb.feeLimit, callValue: 0, userFeePercentage: 100, shouldPollResponse: !1 } } var t, r, n; return b()(e, [{ key: "decodeInput", value: function (e) { return vt(this.inputs, "0x" + e) } }, { key: "onMethod", value: function () { for (var e = this, t = arguments.length, r = new Array(t), n = 0; n < t; n++)r[n] = arguments[n]; var i = ft(this.inputs); return r.forEach((function (t, n) { "address" === i[n] && (r[n] = e.tronWeb.address.toHex(t).replace(H, "0x")), "address[" === i[n].match(/^([^\x5b]*)(\x5b|$)/)[0] && (r[n] = r[n].map((function (t) { return e.tronWeb.address.toHex(t).replace(H, "0x") }))) })), { call: function () { for (var t = arguments.length, n = new Array(t), a = 0; a < t; a++)n[a] = arguments[a]; return e._call.apply(e, [i, r].concat(n)) }, send: function () { for (var t = arguments.length, n = new Array(t), a = 0; a < t; a++)n[a] = arguments[a]; return e._send.apply(e, [i, r].concat(n)) }, watch: function () { return e._watch.apply(e, arguments) } } } }, { key: "_call", value: (n = l()(N.a.mark((function e(t, r) { var n, i, a, s, o = this, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (n = u.length > 2 && void 0 !== u[2] ? u[2] : {}, i = u.length > 3 && void 0 !== u[3] && u[3], Re.isFunction(n) && (i = n, n = {}), i) { e.next = 5; break } return e.abrupt("return", this.injectPromise(this._call, t, r, n)); case 5: if (t.length === r.length) { e.next = 7; break } return e.abrupt("return", i("Invalid argument count provided")); case 7: if (this.contract.address) { e.next = 9; break } return e.abrupt("return", i("Smart contract is missing address")); case 9: if (this.contract.deployed) { e.next = 11; break } return e.abrupt("return", i("Calling smart contracts requires you to load the contract first")); case 11: if (a = this.abi.stateMutability, ["pure", "view"].includes(a.toLowerCase())) { e.next = 14; break } return e.abrupt("return", i('Methods with state mutability "'.concat(a, '" must use send()'))); case 14: n = ht(ht(ht({}, this.defaultOptions), {}, { from: this.tronWeb.defaultAddress.hex }, n), {}, { _isConstant: !0 }), s = r.map((function (e, r) { return { type: t[r], value: e } })), this.tronWeb.transactionBuilder.triggerSmartContract(this.contract.address, this.functionSelector, n, s, !!n.from && this.tronWeb.address.toHex(n.from), (function (e, t) { if (e) return i(e); if (!Re.hasProperty(t, "constant_result")) return i("Failed to execute"); try { var r = t.constant_result[0].length; if (0 === r || r % 64 == 8) { var n = "The call has been reverted or has thrown an error."; if (0 !== r) { n += " Error message: "; for (var a = "", s = t.constant_result[0].substring(8), u = 0; u < r - 8; u += 64)a += o.tronWeb.toUtf8(s.substring(u, u + 64)); n += a.replace(/(\u0000|\u000b|\f)+/g, " ").replace(/ +/g, " ").replace(/\s+$/g, "") } return i(n) } var c = vt(o.outputs, "0x" + t.constant_result[0]); return 1 === c.length && (c = c[0]), i(null, c) } catch (e) { return i(e) } })); case 17: case "end": return e.stop() } }), e, this) }))), function (e, t) { return n.apply(this, arguments) }) }, { key: "_send", value: (r = l()(N.a.mark((function e(t, r) { var n, i, a, s, o, u, c, d, h, f, v, p = this, g = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (n = g.length > 2 && void 0 !== g[2] ? g[2] : {}, i = g.length > 3 && void 0 !== g[3] ? g[3] : this.tronWeb.defaultPrivateKey, a = g.length > 4 && void 0 !== g[4] && g[4], Re.isFunction(i) && (a = i, i = this.tronWeb.defaultPrivateKey), Re.isFunction(n) && (a = n, n = {}), a) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this._send, t, r, n, i)); case 7: if (t.length === r.length) { e.next = 9; break } throw new Error("Invalid argument count provided"); case 9: if (this.contract.address) { e.next = 11; break } return e.abrupt("return", a("Smart contract is missing address")); case 11: if (this.contract.deployed) { e.next = 13; break } return e.abrupt("return", a("Calling smart contracts requires you to load the contract first")); case 13: if (s = this.abi.stateMutability, !["pure", "view"].includes(s.toLowerCase())) { e.next = 16; break } return e.abrupt("return", a('Methods with state mutability "'.concat(s, '" must use call()'))); case 16: return ["payable"].includes(s.toLowerCase()) || (n.callValue = 0), n = ht(ht({}, this.defaultOptions), {}, { from: this.tronWeb.defaultAddress.hex }, n), o = r.map((function (e, r) { return { type: t[r], value: e } })), e.prev = 19, u = i ? this.tronWeb.address.fromPrivateKey(i) : this.tronWeb.defaultAddress.base58, e.next = 23, this.tronWeb.transactionBuilder.triggerSmartContract(this.contract.address, this.functionSelector, n, o, this.tronWeb.address.toHex(u)); case 23: if ((c = e.sent).result && c.result.result) { e.next = 26; break } return e.abrupt("return", a("Unknown error: " + JSON.stringify(c, null, 2))); case 26: return e.next = 28, this.tronWeb.trx.sign(c.transaction, i); case 28: if ((d = e.sent).signature) { e.next = 33; break } if (i) { e.next = 32; break } return e.abrupt("return", a("Transaction was not signed properly")); case 32: return e.abrupt("return", a("Invalid private key provided")); case 33: return e.next = 35, this.tronWeb.trx.sendRawTransaction(d); case 35: if (!(h = e.sent).code) { e.next = 40; break } return f = { error: h.code, message: h.code }, h.message && (f.message = this.tronWeb.toUtf8(h.message)), e.abrupt("return", a(f)); case 40: if (n.shouldPollResponse) { e.next = 42; break } return e.abrupt("return", a(null, d.txID)); case 42: (v = function () { var e = l()(N.a.mark((function e() { var t, r, i, s = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (20 !== (t = s.length > 0 && void 0 !== s[0] ? s[0] : 0)) { e.next = 3; break } return e.abrupt("return", a({ error: "Cannot find result in solidity node", transaction: d })); case 3: return e.next = 5, p.tronWeb.trx.getTransactionInfo(d.txID); case 5: if (r = e.sent, Object.keys(r).length) { e.next = 8; break } return e.abrupt("return", setTimeout((function () { v(t + 1) }), 3e3)); case 8: if (!r.result || "FAILED" !== r.result) { e.next = 10; break } return e.abrupt("return", a({ error: p.tronWeb.toUtf8(r.resMessage), transaction: d, output: r })); case 10: if (Re.hasProperty(r, "contractResult")) { e.next = 12; break } return e.abrupt("return", a({ error: "Failed to execute: " + JSON.stringify(r, null, 2), transaction: d, output: r })); case 12: if (!n.rawResponse) { e.next = 14; break } return e.abrupt("return", a(null, r)); case 14: if (1 === (i = vt(p.outputs, "0x" + r.contractResult[0])).length && (i = i[0]), !n.keepTxID) { e.next = 18; break } return e.abrupt("return", a(null, [d.txID, i])); case 18: return e.abrupt("return", a(null, i)); case 19: case "end": return e.stop() } }), e) }))); return function () { return e.apply(this, arguments) } }())(), e.next = 49; break; case 46: return e.prev = 46, e.t0 = e.catch(19), e.abrupt("return", a(e.t0)); case 49: case "end": return e.stop() } }), e, this, [[19, 46]]) }))), function (e, t) { return r.apply(this, arguments) }) }, { key: "_watch", value: (t = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u = this, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = c.length > 0 && void 0 !== c[0] ? c[0] : {}, r = c.length > 1 && void 0 !== c[1] && c[1], Re.isFunction(t) && (r = t, t = {}), Re.isFunction(r)) { e.next = 5; break } throw new Error("Expected callback to be provided"); case 5: if (this.contract.address) { e.next = 7; break } return e.abrupt("return", r("Smart contract is missing address")); case 7: if (this.abi.type && /event/i.test(this.abi.type)) { e.next = 9; break } return e.abrupt("return", r("Invalid method type for event watching")); case 9: if (this.tronWeb.eventServer) { e.next = 11; break } return e.abrupt("return", r("No event server configured")); case 11: return n = !1, i = !1, a = Date.now() - 1e3, s = function () { var e = l()(N.a.mark((function e() { var r, n, s, o, c, d; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return e.prev = 0, r = { since: a, eventName: u.name, sort: "block_timestamp", blockNumber: "latest", filters: t.filters }, t.resourceNode && (/full/i.test(t.resourceNode) ? r.onlyUnconfirmed = !0 : r.onlyConfirmed = !0), e.next = 5, u.tronWeb.event.getEventsByContractAddress(u.contract.address, r); case 5: return n = e.sent, s = n.sort((function (e, t) { return t.block - e.block })), o = $e()(s, 1), c = o[0], d = n.filter((function (e, r) { return !(t.resourceNode && e.resourceNode && t.resourceNode.toLowerCase() !== e.resourceNode.toLowerCase() || n.slice(0, r).some((function (t) { return JSON.stringify(t) == JSON.stringify(e) })) || i && !(e.block > i)) })), c && (i = c.block), e.abrupt("return", d); case 12: return e.prev = 12, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0)); case 15: case "end": return e.stop() } }), e, null, [[0, 12]]) }))); return function () { return e.apply(this, arguments) } }(), o = function () { n && clearInterval(n), n = setInterval((function () { s().then((function (e) { return e.forEach((function (e) { r(null, Re.parseEvent(e, u.abi)) })) })).catch((function (e) { return r(e) })) }), 3e3) }, e.next = 18, s(); case 18: return o(), e.abrupt("return", { start: o, stop: function () { n && (clearInterval(n), n = !1) } }); case 20: case "end": return e.stop() } }), e, this) }))), function () { return t.apply(this, arguments) }) }]), e }(), gt = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [], n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (p()(this, e), !t || !t instanceof Pt) throw new Error("Expected instance of TronWeb"); this.tronWeb = t, this.injectPromise = Je()(this), this.address = n, this.abi = r, this.eventListener = !1, this.bytecode = !1, this.deployed = !1, this.lastBlock = !1, this.methods = {}, this.methodInstances = {}, this.props = [], this.tronWeb.isAddress(n) ? this.deployed = !0 : this.address = !1, this.loadAbi(r) } var t, r, n, i; return b()(e, [{ key: "_getEvents", value: (i = l()(N.a.mark((function e() { var t, r, n, i, a, s, o = this, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return t = u.length > 0 && void 0 !== u[0] ? u[0] : {}, e.next = 3, this.tronWeb.event.getEventsByContractAddress(this.address, t); case 3: return r = e.sent, n = r.sort((function (e, t) { return t.block - e.block })), i = $e()(n, 1), a = i[0], s = r.filter((function (e, n) { return !(t.resourceNode && e.resourceNode && t.resourceNode.toLowerCase() !== e.resourceNode.toLowerCase() || r.slice(0, n).some((function (t) { return JSON.stringify(t) == JSON.stringify(e) })) || o.lastBlock && !(e.block > o.lastBlock)) })), a && (this.lastBlock = a.block), e.abrupt("return", s); case 8: case "end": return e.stop() } }), e, this) }))), function () { return i.apply(this, arguments) }) }, { key: "_startEventListener", value: (n = l()(N.a.mark((function e() { var t, r, n = this, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = i.length > 0 && void 0 !== i[0] ? i[0] : {}, r = i.length > 1 ? i[1] : void 0, Re.isFunction(t) && (r = t, t = {}), this.eventListener && clearInterval(this.eventListener), this.tronWeb.eventServer) { e.next = 6; break } throw new Error("Event server is not configured"); case 6: if (this.address) { e.next = 8; break } throw new Error("Contract is not configured with an address"); case 8: return this.eventCallback = r, e.next = 11, this._getEvents(t); case 11: this.eventListener = setInterval((function () { n._getEvents(t).then((function (e) { return e.forEach((function (e) { n.eventCallback && n.eventCallback(e) })) })).catch((function (e) { console.error("Failed to get event list", e) })) }), 3e3); case 12: case "end": return e.stop() } }), e, this) }))), function () { return n.apply(this, arguments) }) }, { key: "_stopEventListener", value: function () { this.eventListener && (clearInterval(this.eventListener), this.eventListener = !1, this.eventCallback = !1) } }, { key: "hasProperty", value: function (e) { return this.hasOwnProperty(e) || this.__proto__.hasOwnProperty(e) } }, { key: "loadAbi", value: function (e) { var t = this; this.abi = e, this.methods = {}, this.props.forEach((function (e) { return delete t[e] })), e.forEach((function (e) { if (e.type && !/constructor/i.test(e.type)) { var r = new pt(t, e), n = r.onMethod.bind(r), i = r.name, a = r.functionSelector, s = r.signature; t.methods[i] = n, t.methods[a] = n, t.methods[s] = n, t.methodInstances[i] = r, t.methodInstances[a] = r, t.methodInstances[s] = r, t.hasProperty(i) || (t[i] = n, t.props.push(i)), t.hasProperty(a) || (t[a] = n, t.props.push(a)), t.hasProperty(s) || (t[s] = n, t.props.push(s)) } })) } }, { key: "decodeInput", value: function (e) { var t = e.substring(0, 8), r = e.substring(8); if (!this.methodInstances[t]) throw new Error("Contract method " + t + " not found"); return { name: this.methodInstances[t].name, params: this.methodInstances[t].decodeInput(r) } } }, { key: "new", value: (r = l()(N.a.mark((function e(t) { var r, n, i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (r = u.length > 1 && void 0 !== u[1] ? u[1] : this.tronWeb.defaultPrivateKey, n = u.length > 2 && void 0 !== u[2] && u[2], Re.isFunction(r) && (n = r, r = this.tronWeb.defaultPrivateKey), n) { e.next = 5; break } return e.abrupt("return", this.injectPromise(this.new, t, r)); case 5: return e.prev = 5, i = this.tronWeb.address.fromPrivateKey(r), e.next = 9, this.tronWeb.transactionBuilder.createSmartContract(t, i); case 9: return a = e.sent, e.next = 12, this.tronWeb.trx.sign(a, r); case 12: return s = e.sent, e.next = 15, this.tronWeb.trx.sendRawTransaction(s); case 15: if (!(o = e.sent).code) { e.next = 18; break } return e.abrupt("return", n({ error: o.code, message: this.tronWeb.toUtf8(o.message) })); case 18: return e.next = 20, Re.sleep(3e3); case 20: return e.abrupt("return", this.at(s.contract_address, n)); case 23: return e.prev = 23, e.t0 = e.catch(5), e.abrupt("return", n(e.t0)); case 26: case "end": return e.stop() } }), e, this, [[5, 23]]) }))), function (e) { return r.apply(this, arguments) }) }, { key: "at", value: (t = l()(N.a.mark((function e(t) { var r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (r = i.length > 1 && void 0 !== i[1] && i[1]) { e.next = 3; break } return e.abrupt("return", this.injectPromise(this.at, t)); case 3: return e.prev = 3, e.next = 6, this.tronWeb.trx.getContract(t); case 6: if ((n = e.sent).contract_address) { e.next = 9; break } return e.abrupt("return", r("Unknown error: " + JSON.stringify(n, null, 2))); case 9: return this.address = n.contract_address, this.bytecode = n.bytecode, this.deployed = !0, this.loadAbi(n.abi && n.abi.entrys ? n.abi.entrys : []), e.abrupt("return", r(null, this)); case 16: if (e.prev = 16, e.t0 = e.catch(3), !e.t0.toString().includes("does not exist")) { e.next = 20; break } return e.abrupt("return", r("Contract has not been deployed on the network")); case 20: return e.abrupt("return", r(e.t0)); case 21: case "end": return e.stop() } }), e, this, [[3, 16]]) }))), function (e) { return t.apply(this, arguments) }) }, { key: "events", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (Re.isFunction(e) && (t = e, e = {}), !Re.isFunction(t)) throw new Error("Callback function expected"); var r = this; return { start: function () { var n = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; return n ? (r._startEventListener(e, t).then((function () { n() })).catch((function (e) { n(e) })), this) : (r._startEventListener(e, t), this) }, stop: function () { r._stopEventListener() } } } }]), e }(), bt = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; if (p()(this, e), !t || !t instanceof Pt) throw new Error("Expected instance of TronWeb"); this.tronWeb = t, this.pluginNoOverride = ["register"], this.disablePlugins = r.disablePlugins } return b()(e, [{ key: "register", value: function (e, t) { var r = { requires: "0.0.0", components: {} }, n = { libs: [], plugged: [], skipped: [] }; if (this.disablePlugins) return n.error = "This instance of TronWeb has plugins disabled.", n; var i = new e(this.tronWeb); if (Re.isFunction(i.pluginInterface) && (r = i.pluginInterface(t)), !Me.a.satisfies(Pt.version, r.requires)) throw new Error("The plugin is not compatible with this version of TronWeb"); if (r.fullClass) { var a = i.constructor.name, s = a.substring(0, 1).toLowerCase() + a.substring(1); a !== s && (Pt[a] = e, this.tronWeb[s] = i, n.libs.push(a)) } else for (var o in r.components) if (this.tronWeb.hasOwnProperty(o)) { var u = r.components[o], c = this.tronWeb[o].pluginNoOverride || []; for (var d in u) "constructor" === d || this.tronWeb[o][d] && (c.includes(d) || /^_/.test(d)) ? n.skipped.push(d) : (this.tronWeb[o][d] = u[d].bind(this.tronWeb[o]), n.plugged.push(d)) } return n } }]), e }(), mt = r(21), yt = r.n(mt), kt = function () { function e() { var t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (p()(this, e), !(t && t instanceof Pt)) throw new Error("Expected instance of TronWeb"); this.tronWeb = t, this.injectPromise = Je()(this) } return b()(e, [{ key: "setServer", value: function () { var e = this, t = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "healthcheck"; if (!t) return this.tronWeb.eventServer = !1; if (Re.isString(t) && (t = new He.HttpProvider(t)), !this.tronWeb.isValidProvider(t)) throw new Error("Invalid event server provided"); this.tronWeb.eventServer = t, this.tronWeb.eventServer.isConnected = function () { return e.tronWeb.eventServer.request(r).then((function () { return !0 })).catch((function () { return !1 })) } } }, { key: "getEventsByContractAddress", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], n = Object.assign({ sinceTimestamp: 0, eventName: !1, blockNumber: !1, size: 20, page: 1 }, t), i = n.sinceTimestamp, a = n.since, s = n.fromTimestamp, o = n.eventName, u = n.blockNumber, c = n.size, d = n.page, l = n.onlyConfirmed, h = n.onlyUnconfirmed, v = n.previousLastEventFingerprint, p = n.previousFingerprint, g = n.fingerprint, b = n.rawResponse, m = n.sort, y = n.filters; if (!r) return this.injectPromise(this.getEventsByContractAddress, e, t); if (s = s || i || a, !this.tronWeb.eventServer) return r("No event server configured"); var k = []; if (!this.tronWeb.isAddress(e)) return r("Invalid contract address provided"); if (o && !e) return r("Usage of event name filtering requires a contract address"); if (void 0 !== s && !Re.isInteger(s)) return r("Invalid fromTimestamp provided"); if (!Re.isInteger(c)) return r("Invalid size provided"); if (c > 200 && (console.warn("Defaulting to maximum accepted size: 200"), c = 200), !Re.isInteger(d)) return r("Invalid page provided"); if (u && !o) return r("Usage of block number filtering requires an event name"); e && k.push(this.tronWeb.address.fromHex(e)), o && k.push(o), u && k.push(u); var x = { size: c, page: d }; return "object" === f()(y) && Object.keys(y).length > 0 && (x.filters = JSON.stringify(y)), s && (x.fromTimestamp = x.since = s), l && (x.only_confirmed = l), h && !l && (x.only_unconfirmed = h), m && (x.sort = m), (g = g || p || v) && (x.fingerprint = g), this.tronWeb.eventServer.request("event/contract/".concat(k.join("/"), "?").concat(yt.a.stringify(x))).then((function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; return e ? Re.isArray(e) ? r(null, !0 === b ? e : e.map((function (e) { return Re.mapEvent(e) }))) : r(e) : r("Unknown error occurred") })).catch((function (e) { return r(e.response && e.response.data || e) })) } }, { key: "getEventsByTransactionID", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, r = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; return Re.isFunction(t) && (r = t, t = {}), r ? this.tronWeb.eventServer ? this.tronWeb.eventServer.request("event/transaction/".concat(e)).then((function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; return e ? Re.isArray(e) ? r(null, !0 === t.rawResponse ? e : e.map((function (e) { return Re.mapEvent(e) }))) : r(e) : r("Unknown error occurred") })).catch((function (e) { return r(e.response && e.response.data || e) })) : r("No event server configured") : this.injectPromise(this.getEventsByTransactionID, e, t) } }]), e }(); function xt(e, t) { var r = Object.keys(e); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); t && (n = n.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), r.push.apply(r, n) } return r } function wt(e) { for (var t = 1; t < arguments.length; t++) { var r = null != arguments[t] ? arguments[t] : {}; t % 2 ? xt(Object(r), !0).forEach((function (t) { _()(e, t, r[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : xt(Object(r)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t)) })) } return e } var It = function () { function e(t) { var r = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3]; p()(this, e), this.mainchain = n; var a = t.fullHost, s = t.fullNode, o = t.solidityNode, u = t.eventServer, c = t.mainGatewayAddress, d = t.sideGatewayAddress, l = t.sideChainId; this.sidechain = new r(a || s, a || o, a || u, i), this.isAddress = this.mainchain.isAddress, this.utils = this.mainchain.utils, this.setMainGatewayAddress(c), this.setSideGatewayAddress(d), this.setChainId(l), this.injectPromise = Je()(this), this.validator = new et(this.sidechain); var h = this; this.sidechain.trx.sign = function () { return h.sign.apply(h, arguments) }, this.sidechain.trx.multiSign = function () { return h.multiSign.apply(h, arguments) } } var t, r, n, i, a, s, o, u, c, d, h, f, v, g, m, y, k, x, w, I, A; return b()(e, [{ key: "setMainGatewayAddress", value: function (e) { if (!this.isAddress(e)) throw new Error("Invalid main gateway address provided"); this.mainGatewayAddress = e } }, { key: "setSideGatewayAddress", value: function (e) { if (!this.isAddress(e)) throw new Error("Invalid side gateway address provided"); this.sideGatewayAddress = e } }, { key: "setChainId", value: function (e) { if (!this.utils.isString(e) || !e) throw new Error("Invalid side chainId provided"); this.chainId = e } }, { key: "signTransaction", value: function (e, t) { "string" == typeof e && (e = this.utils.code.hexStr2byteArray(e)); var r = this.utils.code.hexStr2byteArray(this.chainId), n = this.utils.code.hexStr2byteArray(t.txID).concat(r), i = this.sidechain.utils.ethersUtils.sha256(n), a = this.utils.crypto.ECKeySign(this.utils.code.hexStr2byteArray(i.replace(/^0x/, "")), e); return Array.isArray(t.signature) ? t.signature.includes(a) || t.signature.push(a) : t.signature = [a], t } }, { key: "multiSign", value: (A = l()(N.a.mark((function e() { var t, r, n, i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = u.length > 0 && void 0 !== u[0] && u[0], r = u.length > 1 && void 0 !== u[1] ? u[1] : this.sidechain.defaultPrivateKey, n = u.length > 2 && void 0 !== u[2] && u[2], i = u.length > 3 && void 0 !== u[3] && u[3], this.utils.isFunction(n) && (i = n, n = 0), this.utils.isFunction(r) && (i = r, r = this.mainchain.defaultPrivateKey, n = 0), i) { e.next = 8; break } return e.abrupt("return", this.injectPromise(this.multiSign, t, r, n)); case 8: if (this.utils.isObject(t) && t.raw_data && t.raw_data.contract) { e.next = 10; break } return e.abrupt("return", i("Invalid transaction provided")); case 10: if (t.raw_data.contract[0].Permission_id || !(n > 0)) { e.next = 30; break } return t.raw_data.contract[0].Permission_id = n, a = this.sidechain.address.toHex(this.sidechain.address.fromPrivateKey(r)).toLowerCase(), e.next = 15, this.sidechain.trx.getSignWeight(t, n); case 15: if ("PERMISSION_ERROR" !== (s = e.sent).result.code) { e.next = 18; break } return e.abrupt("return", i(s.result.message)); case 18: if (o = !1, s.permission.keys.map((function (e) { e.address === a && (o = !0) })), o) { e.next = 22; break } return e.abrupt("return", i(r + " has no permission to sign")); case 22: if (!s.approved_list || -1 == s.approved_list.indexOf(a)) { e.next = 24; break } return e.abrupt("return", i(r + " already sign transaction")); case 24: if (!s.transaction || !s.transaction.transaction) { e.next = 29; break } (t = s.transaction.transaction).raw_data.contract[0].Permission_id = n, e.next = 30; break; case 29: return e.abrupt("return", i("Invalid transaction provided")); case 30: return e.prev = 30, e.abrupt("return", i(null, this.signTransaction(r, t))); case 34: e.prev = 34, e.t0 = e.catch(30), i(e.t0); case 37: case "end": return e.stop() } }), e, this, [[30, 34]]) }))), function () { return A.apply(this, arguments) }) }, { key: "sign", value: (I = l()(N.a.mark((function e() { var t, r, n, i, a, s, o = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = o.length > 0 && void 0 !== o[0] && o[0], r = o.length > 1 && void 0 !== o[1] ? o[1] : this.sidechain.defaultPrivateKey, n = !(o.length > 2 && void 0 !== o[2]) || o[2], i = o.length > 3 && void 0 !== o[3] && o[3], a = o.length > 4 && void 0 !== o[4] && o[4], this.utils.isFunction(i) && (a = i, i = !1), this.utils.isFunction(n) && (a = n, n = !0, i = !1), this.utils.isFunction(r) && (a = r, r = this.sidechain.defaultPrivateKey, n = !0, i = !1), a) { e.next = 10; break } return e.abrupt("return", this.injectPromise(this.sign, t, r, n, i)); case 10: if (!this.utils.isString(t)) { e.next = 21; break } if (this.utils.isHex(t)) { e.next = 13; break } return e.abrupt("return", a("Expected hex message input")); case 13: return e.prev = 13, s = this.sidechain.trx.signString(t, r, n), e.abrupt("return", a(null, s)); case 18: e.prev = 18, e.t0 = e.catch(13), a(e.t0); case 21: if (this.utils.isObject(t)) { e.next = 23; break } return e.abrupt("return", a("Invalid transaction provided")); case 23: if (i || !t.signature) { e.next = 25; break } return e.abrupt("return", a("Transaction is already signed")); case 25: if (e.prev = 25, i) { e.next = 30; break } if (this.sidechain.address.toHex(this.sidechain.address.fromPrivateKey(r)).toLowerCase() === this.sidechain.address.toHex(t.raw_data.contract[0].parameter.value.owner_address)) { e.next = 30; break } return e.abrupt("return", a("Private key does not match address in transaction")); case 30: return e.abrupt("return", a(null, this.signTransaction(r, t))); case 33: e.prev = 33, e.t1 = e.catch(25), a(e.t1); case 36: case "end": return e.stop() } }), e, this, [[13, 18], [25, 33]]) }))), function () { return I.apply(this, arguments) }) }, { key: "depositTrx", value: (w = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (i = c.length > 3 && void 0 !== c[3] ? c[3] : {}, a = c.length > 4 && void 0 !== c[4] ? c[4] : this.mainchain.defaultPrivateKey, s = c.length > 5 && void 0 !== c[5] && c[5], this.utils.isFunction(a) && (s = a, a = this.mainchain.defaultPrivateKey), this.utils.isFunction(i) && (s = i, i = {}), s) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.depositTrx, t, r, n, i, a)); case 7: if (!this.validator.notValid([{ name: "callValue", type: "integer", value: t, gte: 0 }, { name: "depositFee", type: "integer", value: r, gte: 0 }, { name: "feeLimit", type: "integer", value: n, gte: 0 }], s)) { e.next = 9; break } return e.abrupt("return"); case 9: return i = wt({ callValue: Number(t) + Number(r), feeLimit: n }, i), e.prev = 10, e.next = 13, this.mainchain.contract().at(this.mainGatewayAddress); case 13: return o = e.sent, e.next = 16, o.depositTRX().send(i, a); case 16: return u = e.sent, e.abrupt("return", s(null, u)); case 20: return e.prev = 20, e.t0 = e.catch(10), e.abrupt("return", s(e.t0)); case 23: case "end": return e.stop() } }), e, this, [[10, 20]]) }))), function (e, t, r) { return w.apply(this, arguments) }) }, { key: "depositTrc10", value: (x = l()(N.a.mark((function e(t, r, n, i) { var a, s, o, u, c, d = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (a = d.length > 4 && void 0 !== d[4] ? d[4] : {}, s = d.length > 5 && void 0 !== d[5] ? d[5] : this.mainchain.defaultPrivateKey, o = d.length > 6 && void 0 !== d[6] && d[6], this.utils.isFunction(s) && (o = s, s = this.mainchain.defaultPrivateKey), this.utils.isFunction(a) && (o = a, a = {}), o) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.depositTrc10, t, r, n, i, a, s)); case 7: if (!this.validator.notValid([{ name: "tokenValue", type: "integer", value: r, gte: 0 }, { name: "depositFee", type: "integer", value: n, gte: 0 }, { name: "feeLimit", type: "integer", value: i, gte: 0 }, { name: "tokenId", type: "integer", value: t, gte: 0 }], o)) { e.next = 9; break } return e.abrupt("return"); case 9: return a = wt(wt({ tokenId: t, tokenValue: r, feeLimit: i }, a), {}, { callValue: n }), e.prev = 10, e.next = 13, this.mainchain.contract().at(this.mainGatewayAddress); case 13: return u = e.sent, e.next = 16, u.depositTRC10(t, r).send(a, s); case 16: c = e.sent, o(null, c), e.next = 23; break; case 20: return e.prev = 20, e.t0 = e.catch(10), e.abrupt("return", o(e.t0)); case 23: case "end": return e.stop() } }), e, this, [[10, 20]]) }))), function (e, t, r, n) { return x.apply(this, arguments) }) }, { key: "depositTrc", value: (k = l()(N.a.mark((function e(t, r, n, i, a) { var s, o, u, c, d, l, h = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (s = h.length > 5 && void 0 !== h[5] ? h[5] : {}, o = h.length > 6 && void 0 !== h[6] ? h[6] : this.mainchain.defaultPrivateKey, u = h.length > 7 && void 0 !== h[7] && h[7], this.utils.isFunction(o) && (u = o, o = this.mainchain.defaultPrivateKey), this.utils.isFunction(s) && (u = s, s = {}), u) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.depositTrc, t, r, n, i, a, s, o)); case 7: if (!this.validator.notValid([{ name: "functionSelector", type: "not-empty-string", value: t }, { name: "num", type: "integer", value: r, gte: 0 }, { name: "fee", type: "integer", value: n, gte: 0 }, { name: "feeLimit", type: "integer", value: i, gte: 0 }, { name: "contractAddress", type: "address", value: a }], u)) { e.next = 9; break } return e.abrupt("return"); case 9: if (s = wt(wt({ feeLimit: i }, s), {}, { callValue: n, tokenId: "", tokenValue: 0 }), e.prev = 10, c = null, "approve" !== t) { e.next = 21; break } return e.next = 15, this.mainchain.contract().at(a); case 15: return d = e.sent, e.next = 18, d.approve(this.mainGatewayAddress, r).send(s, o); case 18: c = e.sent, e.next = 44; break; case 21: return e.next = 23, this.mainchain.contract().at(this.mainGatewayAddress); case 23: l = e.sent, e.t0 = t, e.next = "depositTRC20" === e.t0 ? 27 : "depositTRC721" === e.t0 ? 31 : "retryDeposit" === e.t0 ? 35 : "retryMapping" === e.t0 ? 39 : 43; break; case 27: return e.next = 29, l.depositTRC20(a, r).send(s, o); case 29: return c = e.sent, e.abrupt("break", 44); case 31: return e.next = 33, l.depositTRC721(a, r).send(s, o); case 33: return c = e.sent, e.abrupt("break", 44); case 35: return e.next = 37, l.retryDeposit(r).send(s, o); case 37: return c = e.sent, e.abrupt("break", 44); case 39: return e.next = 41, l.retryMapping(r).send(s, o); case 41: return c = e.sent, e.abrupt("break", 44); case 43: return e.abrupt("break", 44); case 44: u(null, c), e.next = 50; break; case 47: return e.prev = 47, e.t1 = e.catch(10), e.abrupt("return", u(e.t1)); case 50: case "end": return e.stop() } }), e, this, [[10, 47]]) }))), function (e, t, r, n, i) { return k.apply(this, arguments) }) }, { key: "approveTrc20", value: (y = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "approve", e.abrupt("return", this.depositTrc(o, t, 0, r, n, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r) { return y.apply(this, arguments) }) }, { key: "approveTrc721", value: (m = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "approve", e.abrupt("return", this.depositTrc(o, t, 0, r, n, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r) { return m.apply(this, arguments) }) }, { key: "depositTrc20", value: (g = l()(N.a.mark((function e(t, r, n, i) { var a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return a = c.length > 4 && void 0 !== c[4] ? c[4] : {}, s = c.length > 5 && void 0 !== c[5] ? c[5] : this.mainchain.defaultPrivateKey, o = c.length > 6 && void 0 !== c[6] && c[6], u = "depositTRC20", e.abrupt("return", this.depositTrc(u, t, r, n, i, a, s, o)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r, n) { return g.apply(this, arguments) }) }, { key: "depositTrc721", value: (v = l()(N.a.mark((function e(t, r, n, i) { var a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return a = c.length > 4 && void 0 !== c[4] ? c[4] : {}, s = c.length > 5 && void 0 !== c[5] ? c[5] : this.mainchain.defaultPrivateKey, o = c.length > 6 && void 0 !== c[6] && c[6], u = "depositTRC721", e.abrupt("return", this.depositTrc(u, t, r, n, i, a, s, o)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r, n) { return v.apply(this, arguments) }) }, { key: "mappingTrc", value: (f = l()(N.a.mark((function e(t, r, n, i) { var a, s, o, u, c, d = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (a = d.length > 4 && void 0 !== d[4] ? d[4] : {}, s = d.length > 5 && void 0 !== d[5] ? d[5] : this.mainchain.defaultPrivateKey, o = d.length > 6 ? d[6] : void 0, this.utils.isFunction(s) && (o = s, s = this.mainchain.defaultPrivateKey), this.utils.isFunction(a) && (o = a, a = {}), o) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.mappingTrc, t, r, n, i, a, s)); case 7: if (!this.validator.notValid([{ name: "trxHash", type: "not-empty-string", value: t }, { name: "mappingFee", type: "integer", value: r, gte: 0 }, { name: "feeLimit", type: "integer", value: n, gte: 0 }], o)) { e.next = 9; break } return e.abrupt("return"); case 9: return t = t.startsWith("0x") ? t : "0x" + t, a = wt(wt({ feeLimit: n }, a), {}, { callValue: r }), e.prev = 11, e.next = 14, this.mainchain.contract().at(this.mainGatewayAddress); case 14: if (u = e.sent, c = null, "mappingTRC20" !== i) { e.next = 22; break } return e.next = 19, u.mappingTRC20(t).send(a, s); case 19: c = e.sent, e.next = 29; break; case 22: if ("mappingTRC721" !== i) { e.next = 28; break } return e.next = 25, u.mappingTRC721(t).send(a, s); case 25: c = e.sent, e.next = 29; break; case 28: o(new Error("type must be trc20 or trc721")); case 29: o(null, c), e.next = 35; break; case 32: return e.prev = 32, e.t0 = e.catch(11), e.abrupt("return", o(e.t0)); case 35: case "end": return e.stop() } }), e, this, [[11, 32]]) }))), function (e, t, r, n) { return f.apply(this, arguments) }) }, { key: "mappingTrc20", value: (h = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "mappingTRC20", e.abrupt("return", this.mappingTrc(t, r, n, o, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r) { return h.apply(this, arguments) }) }, { key: "mappingTrc721", value: (d = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "mappingTRC721", e.abrupt("return", this.mappingTrc(t, r, n, o, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r) { return d.apply(this, arguments) }) }, { key: "withdrawTrx", value: (c = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (i = c.length > 3 && void 0 !== c[3] ? c[3] : {}, a = c.length > 4 && void 0 !== c[4] ? c[4] : this.mainchain.defaultPrivateKey, s = c.length > 5 && void 0 !== c[5] && c[5], this.utils.isFunction(a) && (s = a, a = this.mainchain.defaultPrivateKey), this.utils.isFunction(i) && (s = i, i = {}), s) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.withdrawTrx, t, r, n, i, a)); case 7: if (!this.validator.notValid([{ name: "callValue", type: "integer", value: t, gte: 0 }, { name: "withdrawFee", type: "integer", value: r, gte: 0 }, { name: "feeLimit", type: "integer", value: n, gte: 0 }], s)) { e.next = 9; break } return e.abrupt("return"); case 9: return i = wt({ callValue: Number(t) + Number(r), feeLimit: n }, i), e.prev = 10, e.next = 13, this.sidechain.contract().at(this.sideGatewayAddress); case 13: return o = e.sent, e.next = 16, o.withdrawTRX().send(i, a); case 16: return u = e.sent, e.abrupt("return", s(null, u)); case 20: return e.prev = 20, e.t0 = e.catch(10), e.abrupt("return", s(e.t0)); case 23: case "end": return e.stop() } }), e, this, [[10, 20]]) }))), function (e, t, r) { return c.apply(this, arguments) }) }, { key: "withdrawTrc10", value: (u = l()(N.a.mark((function e(t, r, n, i) { var a, s, o, u, c, d = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (a = d.length > 4 && void 0 !== d[4] ? d[4] : {}, s = d.length > 5 && void 0 !== d[5] ? d[5] : this.mainchain.defaultPrivateKey, o = d.length > 6 && void 0 !== d[6] && d[6], this.utils.isFunction(s) && (o = s, s = this.mainchain.defaultPrivateKey), this.utils.isFunction(a) && (o = a, a = {}), o) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.withdrawTrc10, t, r, n, i, a, s)); case 7: if (!this.validator.notValid([{ name: "tokenId", type: "integer", value: t, gte: 0 }, { name: "tokenValue", type: "integer", value: r, gte: 0 }, { name: "withdrawFee", type: "integer", value: n, gte: 0 }, { name: "feeLimit", type: "integer", value: i, gte: 0 }], o)) { e.next = 9; break } return e.abrupt("return"); case 9: return a = wt({ tokenValue: r, tokenId: t, callValue: n, feeLimit: i }, a), e.prev = 10, e.next = 13, this.sidechain.contract().at(this.sideGatewayAddress); case 13: return u = e.sent, e.next = 16, u.withdrawTRC10(t, r).send(a, s); case 16: return c = e.sent, e.abrupt("return", o(null, c)); case 20: return e.prev = 20, e.t0 = e.catch(10), e.abrupt("return", o(e.t0)); case 23: case "end": return e.stop() } }), e, this, [[10, 20]]) }))), function (e, t, r, n) { return u.apply(this, arguments) }) }, { key: "withdrawTrc", value: (o = l()(N.a.mark((function e(t, r, n, i, a) { var s, o, u, c, d, h, f, v, p, g, b = this, m = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (s = m.length > 5 && void 0 !== m[5] ? m[5] : {}, o = m.length > 6 && void 0 !== m[6] ? m[6] : this.mainchain.defaultPrivateKey, u = m.length > 7 && void 0 !== m[7] && m[7], this.utils.isFunction(o) && (u = o, o = this.mainchain.defaultPrivateKey), this.utils.isFunction(s) && (u = s, s = {}), u) { e.next = 7; break } return e.abrupt("return", this.injectPromise(this.withdrawTrc, t, r, n, i, a, s, o)); case 7: if (!this.validator.notValid([{ name: "functionSelector", type: "not-empty-string", value: t }, { name: "numOrId", type: "integer", value: r, gte: 0 }, { name: "withdrawFee", type: "integer", value: n, gte: 0 }, { name: "feeLimit", type: "integer", value: i, gte: 0 }, { name: "contractAddress", type: "address", value: a }], u)) { e.next = 9; break } return e.abrupt("return"); case 9: return s = wt(wt({ feeLimit: i }, s), {}, { callValue: n }), c = [{ type: "uint256", value: r }], e.prev = 11, d = o ? this.sidechain.address.fromPrivateKey(o) : this.sidechain.defaultAddress.base58, e.next = 15, this.sidechain.transactionBuilder.triggerSmartContract(a, t, s, c, this.sidechain.address.toHex(d)); case 15: if ((h = e.sent).result && h.result.result) { e.next = 18; break } return e.abrupt("return", u("Unknown error: " + JSON.stringify(h.transaction, null, 2))); case 18: return e.next = 20, this.sidechain.trx.sign(h.transaction, o); case 20: if ((f = e.sent).signature) { e.next = 25; break } if (o) { e.next = 24; break } return e.abrupt("return", u("Transaction was not signed properly")); case 24: return e.abrupt("return", u("Invalid private key provided")); case 25: return e.next = 27, this.sidechain.trx.sendRawTransaction(f); case 27: if (!(v = e.sent).code) { e.next = 32; break } return p = { error: v.code, message: v.code }, v.message && (p.message = this.sidechain.toUtf8(v.message)), e.abrupt("return", u(p)); case 32: if (s.shouldPollResponse) { e.next = 34; break } return e.abrupt("return", u(null, f.txID)); case 34: (g = function () { var e = l()(N.a.mark((function e() { var t, r, n, i = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (20 != (t = i.length > 0 && void 0 !== i[0] ? i[0] : 0)) { e.next = 3; break } return e.abrupt("return", u({ error: "Cannot find result in solidity node", transaction: f })); case 3: return e.next = 5, b.sidechain.trx.getTransactionInfo(f.txID); case 5: if (r = e.sent, Object.keys(r).length) { e.next = 8; break } return e.abrupt("return", setTimeout((function () { g(t + 1) }), 3e3)); case 8: if (!r.result || "FAILED" != r.result) { e.next = 10; break } return e.abrupt("return", u({ error: b.sidechain.toUtf8(r.resMessage), transaction: f, output: r })); case 10: if (b.utils.hasProperty(r, "contractResult")) { e.next = 12; break } return e.abrupt("return", u({ error: "Failed to execute: " + JSON.stringify(r, null, 2), transaction: f, output: r })); case 12: if (!s.rawResponse) { e.next = 14; break } return e.abrupt("return", u(null, r)); case 14: return 1 === (n = decodeOutput(b.outputs, "0x" + r.contractResult[0])).length && (n = n[0]), e.abrupt("return", u(null, n)); case 17: case "end": return e.stop() } }), e) }))); return function () { return e.apply(this, arguments) } }())(), e.next = 41; break; case 38: return e.prev = 38, e.t0 = e.catch(11), e.abrupt("return", u(e.t0)); case 41: case "end": return e.stop() } }), e, this, [[11, 38]]) }))), function (e, t, r, n, i) { return o.apply(this, arguments) }) }, { key: "withdrawTrc20", value: (s = l()(N.a.mark((function e(t, r, n, i, a) { var s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return s = c.length > 5 && void 0 !== c[5] ? c[5] : this.mainchain.defaultPrivateKey, o = c.length > 6 && void 0 !== c[6] && c[6], u = "withdrawal(uint256)", e.abrupt("return", this.withdrawTrc(u, t, r, n, i, a, s, o)); case 4: case "end": return e.stop() } }), e, this) }))), function (e, t, r, n, i) { return s.apply(this, arguments) }) }, { key: "withdrawTrc721", value: (a = l()(N.a.mark((function e(t, r, n, i, a) { var s, o, u, c = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return s = c.length > 5 && void 0 !== c[5] ? c[5] : this.mainchain.defaultPrivateKey, o = c.length > 6 && void 0 !== c[6] && c[6], u = "withdrawal(uint256)", e.abrupt("return", this.withdrawTrc(u, t, r, n, i, a, s, o)); case 4: case "end": return e.stop() } }), e, this) }))), function (e, t, r, n, i) { return a.apply(this, arguments) }) }, { key: "injectFund", value: (i = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u, c, d, l, h = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (i = h.length > 3 && void 0 !== h[3] ? h[3] : this.mainchain.defaultPrivateKey, a = h.length > 4 && void 0 !== h[4] && h[4], this.utils.isFunction(i) && (a = i, i = this.mainchain.defaultPrivateKey), this.utils.isFunction(n) && (a = n, n = {}), a) { e.next = 6; break } return e.abrupt("return", this.injectPromise(this.injectFund, t, r, n, i)); case 6: if (!this.validator.notValid([{ name: "num", type: "integer", value: t, gte: 0 }, { name: "feeLimit", type: "integer", value: r, gte: 0 }], a)) { e.next = 8; break } return e.abrupt("return"); case 8: return e.prev = 8, s = this.sidechain.address.fromPrivateKey(i), o = this.sidechain.address.toHex(s), e.next = 13, this.sidechain.fullNode.request("/wallet/fundinject", { owner_address: o, amount: t }, "post"); case 13: return u = e.sent, e.next = 16, this.sidechain.trx.sign(u, i); case 16: if ((c = e.sent).signature) { e.next = 21; break } if (i) { e.next = 20; break } return e.abrupt("return", a("Transaction was not signed properly")); case 20: return e.abrupt("return", a("Invalid private key provided")); case 21: return e.next = 23, this.sidechain.trx.sendRawTransaction(c); case 23: if (!(d = e.sent).code) { e.next = 28; break } return l = { error: d.code, message: d.code }, d.message && (l.message = this.mainchain.toUtf8(d.message)), e.abrupt("return", a(l)); case 28: return e.abrupt("return", a(null, c.txID)); case 31: return e.prev = 31, e.t0 = e.catch(8), e.abrupt("return", a(e.t0)); case 34: case "end": return e.stop() } }), e, this, [[8, 31]]) }))), function (e, t, r) { return i.apply(this, arguments) }) }, { key: "retryWithdraw", value: (n = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.sidechain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "retryWithdraw(uint256)", e.abrupt("return", this.withdrawTrc(o, t, r, n, this.sideGatewayAddress, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, r) { return n.apply(this, arguments) }) }, { key: "retryDeposit", value: (r = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "retryDeposit", e.abrupt("return", this.depositTrc(o, t, r, n, this.mainGatewayAddress, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, t, n) { return r.apply(this, arguments) }) }, { key: "retryMapping", value: (t = l()(N.a.mark((function e(t, r, n) { var i, a, s, o, u = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return i = u.length > 3 && void 0 !== u[3] ? u[3] : {}, a = u.length > 4 && void 0 !== u[4] ? u[4] : this.mainchain.defaultPrivateKey, s = u.length > 5 && void 0 !== u[5] && u[5], o = "retryMapping", e.abrupt("return", this.depositTrc(o, t, r, n, this.mainGatewayAddress, i, a, s)); case 5: case "end": return e.stop() } }), e, this) }))), function (e, r, n) { return t.apply(this, arguments) }) }]), e }(); function At(e) { var t = function () { if ("undefined" == typeof Reflect || !Reflect.construct) return !1; if (Reflect.construct.sham) return !1; if ("function" == typeof Proxy) return !0; try { return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function () { }))), !0 } catch (e) { return !1 } }(); return function () { var r, n = P()(e); if (t) { var i = P()(this).constructor; r = Reflect.construct(n, arguments, i) } else r = n.apply(this, arguments); return I()(this, r) } } var Pt = function (e) { x()(a, e); var t, r, n, i = At(a); function a() { var e, t, r = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], n = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], s = arguments.length > 2 && void 0 !== arguments[2] && arguments[2], o = arguments.length > 3 && void 0 !== arguments[3] && arguments[3], u = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; p()(this, a), e = i.call(this); var c = !1, d = !1; return "object" === f()(r) && (r.fullNode || r.fullHost) ? (t = r.fullNode || r.fullHost, o = n, n = r.solidityNode || r.fullHost, s = r.eventServer || r.fullHost, c = r.headers || !1, d = r.eventHeaders || c, u = r.privateKey) : t = r, Re.isString(t) && (t = new He.HttpProvider(t)), Re.isString(n) && (n = new He.HttpProvider(n)), Re.isString(s) && (s = new He.HttpProvider(s)), e.event = new kt(y()(e)), e.transactionBuilder = new st(y()(e)), e.trx = new dt(y()(e)), e.plugin = new bt(y()(e), r), e.utils = Re, e.setFullNode(t), e.setSolidityNode(n), e.setEventServer(s), e.providers = He, e.BigNumber = qe.a, e.defaultBlock = !1, e.defaultPrivateKey = !1, e.defaultAddress = { hex: !1, base58: !1 }, ["sha3", "toHex", "toUtf8", "fromUtf8", "toAscii", "fromAscii", "toDecimal", "fromDecimal", "toSun", "fromSun", "toBigNumber", "isAddress", "createAccount", "address", "version"].forEach((function (t) { e[t] = a[t] })), "object" === f()(o) && (o.fullNode || o.fullHost) ? e.sidechain = new It(o, a, y()(e), u) : u = u || o, u && e.setPrivateKey(u), e.fullnodeVersion = "3.5.0", e.feeLimit = 15e7, e.injectPromise = Je()(y()(e)), c && e.setFullNodeHeader(c), d && e.setEventHeader(d), e } return b()(a, [{ key: "getFullnodeVersion", value: (n = l()(N.a.mark((function e() { var t; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return e.prev = 0, e.next = 3, this.trx.getNodeInfo(); case 3: t = e.sent, this.fullnodeVersion = t.configNodeInfo.codeVersion, 2 === this.fullnodeVersion.split(".").length && (this.fullnodeVersion += ".0"), e.next = 11; break; case 8: e.prev = 8, e.t0 = e.catch(0), this.fullnodeVersion = "3.5.0"; case 11: case "end": return e.stop() } }), e, this, [[0, 8]]) }))), function () { return n.apply(this, arguments) }) }, { key: "setDefaultBlock", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if ([!1, "latest", "earliest", 0].includes(e)) return this.defaultBlock = e; if (!Re.isInteger(e) || !e) throw new Error("Invalid block ID provided"); this.defaultBlock = Math.abs(e) } }, { key: "setPrivateKey", value: function (e) { try { this.setAddress(this.address.fromPrivateKey(e)) } catch (e) { throw new Error("Invalid private key provided") } this.defaultPrivateKey = e, this.emit("privateKeyChanged", e) } }, { key: "setAddress", value: function (e) { if (!this.isAddress(e)) throw new Error("Invalid address provided"); var t = this.address.toHex(e), r = this.address.fromHex(e); this.defaultPrivateKey && this.address.fromPrivateKey(this.defaultPrivateKey) !== r && (this.defaultPrivateKey = !1), this.defaultAddress = { hex: t, base58: r }, this.emit("addressChanged", { hex: t, base58: r }) } }, { key: "fullnodeSatisfies", value: function (e) { return Me.a.satisfies(this.fullnodeVersion, e) } }, { key: "isValidProvider", value: function (e) { return Object.values(He).some((function (t) { return e instanceof t })) } }, { key: "setFullNode", value: function (e) { if (Re.isString(e) && (e = new He.HttpProvider(e)), !this.isValidProvider(e)) throw new Error("Invalid full node provided"); this.fullNode = e, this.fullNode.setStatusPage("wallet/getnowblock"), this.getFullnodeVersion() } }, { key: "setSolidityNode", value: function (e) { if (Re.isString(e) && (e = new He.HttpProvider(e)), !this.isValidProvider(e)) throw new Error("Invalid solidity node provided"); this.solidityNode = e, this.solidityNode.setStatusPage("walletsolidity/getnowblock") } }, { key: "setEventServer", value: function () { var e; (e = this.event).setServer.apply(e, arguments) } }, { key: "setHeader", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = new He.HttpProvider(this.fullNode.host, 3e4, !1, !1, e), r = new He.HttpProvider(this.solidityNode.host, 3e4, !1, !1, e), n = new He.HttpProvider(this.eventServer.host, 3e4, !1, !1, e); this.setFullNode(t), this.setSolidityNode(r), this.setEventServer(n) } }, { key: "setFullNodeHeader", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = new He.HttpProvider(this.fullNode.host, 3e4, !1, !1, e), r = new He.HttpProvider(this.solidityNode.host, 3e4, !1, !1, e); this.setFullNode(t), this.setSolidityNode(r) } }, { key: "setEventHeader", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = new He.HttpProvider(this.eventServer.host, 3e4, !1, !1, e); this.setEventServer(t) } }, { key: "currentProviders", value: function () { return { fullNode: this.fullNode, solidityNode: this.solidityNode, eventServer: this.eventServer } } }, { key: "currentProvider", value: function () { return this.currentProviders() } }, { key: "getEventResult", value: function () { for (var e, t = arguments.length, r = new Array(t), n = 0; n < t; n++)r[n] = arguments[n]; return "object" !== f()(r[1]) && (r[1] = { sinceTimestamp: r[1] || 0, eventName: r[2] || !1, blockNumber: r[3] || !1, size: r[4] || 20, page: r[5] || 1 }, r.splice(2, 4), Re.isFunction(r[2]) || (Re.isFunction(r[1].page) ? (r[2] = r[1].page, r[1].page = 1) : Re.isFunction(r[1].size) && (r[2] = r[1].size, r[1].size = 20, r[1].page = 1))), (e = this.event).getEventsByContractAddress.apply(e, r) } }, { key: "getEventByTransactionID", value: function () { var e; return (e = this.event).getEventsByTransactionID.apply(e, arguments) } }, { key: "contract", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; return new gt(this, e, t) } }, { key: "isConnected", value: (r = l()(N.a.mark((function e() { var t, r = arguments; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: if (t = r.length > 0 && void 0 !== r[0] && r[0]) { e.next = 3; break } return e.abrupt("return", this.injectPromise(this.isConnected)); case 3: return e.t0 = t, e.next = 6, this.fullNode.isConnected(); case 6: return e.t1 = e.sent, e.next = 9, this.solidityNode.isConnected(); case 9: if (e.t2 = e.sent, e.t3 = this.eventServer, !e.t3) { e.next = 15; break } return e.next = 14, this.eventServer.isConnected(); case 14: e.t3 = e.sent; case 15: return e.t4 = e.t3, e.t5 = { fullNode: e.t1, solidityNode: e.t2, eventServer: e.t4 }, e.abrupt("return", (0, e.t0)(null, e.t5)); case 18: case "end": return e.stop() } }), e, this) }))), function () { return r.apply(this, arguments) }) }], [{ key: "address", get: function () { return { fromHex: function (e) { return Re.isHex(e) ? Re.crypto.getBase58CheckAddress(Re.code.hexStr2byteArray(e.replace(/^0x/, "41"))) : e }, toHex: function (e) { return Re.isHex(e) ? e.toLowerCase().replace(/^0x/, "41") : Re.code.byteArray2hexStr(Re.crypto.decodeBase58Address(e)).toLowerCase() }, fromPrivateKey: function (e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; try { return Re.crypto.pkToAddress(e, t) } catch (e) { return !1 } } } } }, { key: "sha3", value: function (e) { var t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1]; return (t ? "0x" : "") + ie(Buffer.from(e, "utf-8")).toString().substring(2) } }, { key: "toHex", value: function (e) { if (Re.isBoolean(e)) return a.fromDecimal(+e); if (Re.isBigNumber(e)) return a.fromDecimal(e); if ("object" === f()(e)) return a.fromUtf8(JSON.stringify(e)); if (Re.isString(e)) { if (/^(-|)0x/.test(e)) return e; if (!isFinite(e) || /^\s*$/.test(e)) return a.fromUtf8(e) } var t = a.fromDecimal(e); if ("0xNaN" === t) throw new Error("The passed value is not convertible to a hex string"); return t } }, { key: "toUtf8", value: function (e) { if (Re.isHex(e)) return e = e.replace(/^0x/, ""), Buffer.from(e, "hex").toString("utf8"); throw new Error("The passed value is not a valid hex string") } }, { key: "fromUtf8", value: function (e) { if (!Re.isString(e)) throw new Error("The passed value is not a valid utf-8 string"); return "0x" + Buffer.from(e, "utf8").toString("hex") } }, { key: "toAscii", value: function (e) { if (Re.isHex(e)) { var t = "", r = 0, n = e.length; for ("0x" === e.substring(0, 2) && (r = 2); r < n; r += 2) { var i = parseInt(e.substr(r, 2), 16); t += String.fromCharCode(i) } return t } throw new Error("The passed value is not a valid hex string") } }, { key: "fromAscii", value: function (e, t) { if (!Re.isString(e)) throw new Error("The passed value is not a valid utf-8 string"); return "0x" + Buffer.from(e, "ascii").toString("hex").padEnd(t, "0") } }, { key: "toDecimal", value: function (e) { return a.toBigNumber(e).toNumber() } }, { key: "fromDecimal", value: function (e) { var t = a.toBigNumber(e), r = t.toString(16); return t.isLessThan(0) ? "-0x" + r.substr(1) : "0x" + r } }, { key: "fromSun", value: function (e) { var t = a.toBigNumber(e).div(1e6); return Re.isBigNumber(e) ? t : t.toString(10) } }, { key: "toSun", value: function (e) { var t = a.toBigNumber(e).times(1e6); return Re.isBigNumber(e) ? t : t.toString(10) } }, { key: "toBigNumber", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0; return Re.isBigNumber(e) ? e : Re.isString(e) && /^(-|)0x/.test(e) ? new qe.a(e.replace("0x", ""), 16) : new qe.a(e.toString(10), 10) } }, { key: "isAddress", value: function () { var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0]; if (!Re.isString(e)) return !1; if (42 === e.length) try { return a.isAddress(Re.crypto.getBase58CheckAddress(Re.code.hexStr2byteArray(e))) } catch (e) { return !1 } try { return Re.crypto.isAddressValid(e) } catch (e) { return !1 } } }, { key: "createAccount", value: (t = l()(N.a.mark((function e() { var t; return N.a.wrap((function (e) { for (; ;)switch (e.prev = e.next) { case 0: return t = Re.accounts.generateAccount(), e.abrupt("return", t); case 2: case "end": return e.stop() } }), e) }))), function () { return t.apply(this, arguments) }) }]), a }(Le.a); _()(Pt, "providers", He), _()(Pt, "BigNumber", qe.a), _()(Pt, "TransactionBuilder", st), _()(Pt, "Trx", dt), _()(Pt, "Contract", gt), _()(Pt, "Plugin", bt), _()(Pt, "Event", kt), _()(Pt, "version", Ue.a), _()(Pt, "utils", Re) }]).default;
