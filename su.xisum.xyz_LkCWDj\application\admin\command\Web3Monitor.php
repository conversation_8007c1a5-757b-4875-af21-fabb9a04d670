<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\service\Web3MonitorService;

/**
 * Web3监控定时任务
 * 
 * 使用方法：
 * php think web3:monitor
 * php think web3:monitor --time-range=7200
 */
class Web3Monitor extends Command
{
    protected function configure()
    {
        $this->setName('web3:monitor')
            ->addOption('time-range', 't', \think\console\input\Option::VALUE_OPTIONAL, '监控时间范围（秒）', 3600)
            ->addOption('send-alerts', 's', \think\console\input\Option::VALUE_NONE, '发送告警通知')
            ->setDescription('执行Web3登录系统监控检查');
    }

    protected function execute(Input $input, Output $output)
    {
        $timeRange = $input->getOption('time-range');
        $sendAlerts = $input->getOption('send-alerts');

        $output->writeln('开始Web3监控检查...');
        $output->writeln("监控时间范围: {$timeRange}秒");

        try {
            // 执行监控检查
            $alerts = Web3MonitorService::runFullMonitoring($timeRange);

            $output->writeln("检查完成，发现 " . count($alerts) . " 个告警");

            // 按级别统计告警
            $levelCounts = [
                'critical' => 0,
                'error' => 0,
                'warning' => 0,
                'info' => 0
            ];

            foreach ($alerts as $alert) {
                if (isset($levelCounts[$alert['level']])) {
                    $levelCounts[$alert['level']]++;
                }
            }

            // 显示统计信息
            $output->writeln("告警统计:");
            $output->writeln("  关键: {$levelCounts['critical']}");
            $output->writeln("  错误: {$levelCounts['error']}");
            $output->writeln("  警告: {$levelCounts['warning']}");
            $output->writeln("  信息: {$levelCounts['info']}");

            // 显示详细告警信息
            if (!empty($alerts)) {
                $output->writeln("\n详细告警:");
                foreach ($alerts as $alert) {
                    $levelText = strtoupper($alert['level']);
                    $output->writeln("  [{$levelText}] {$alert['message']}");
                }
            }

            // 发送告警通知
            if ($sendAlerts && !empty($alerts)) {
                $output->writeln("\n发送告警通知...");
                $result = Web3MonitorService::sendAlertNotifications($alerts);
                if ($result) {
                    $output->writeln("告警通知发送成功");
                } else {
                    $output->writeln("告警通知发送失败");
                }
            }

            return 0; // 成功

        } catch (\Exception $e) {
            $output->writeln('监控检查失败: ' . $e->getMessage());
            return 1; // 失败
        }
    }
}
